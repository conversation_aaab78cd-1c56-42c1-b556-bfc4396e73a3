#!/usr/bin/env node

/**
 * Test script to verify certificate structure matches code expectations
 */

const fs = require('fs');
const path = require('path');

// Test environments
const environments = ['local', 'staging'];

// Expected certificate paths based on the code analysis
const expectedPaths = {
  ca: 'ca/ca.crt',
  services: {
    'public-api': {
      server: 'services/public-api/server.crt',
      serverKey: 'services/public-api/server.key',
      client: 'services/public-api/client.crt',
      clientKey: 'services/public-api/client.key'
    },
    'ffmpeg': {
      server: 'services/ffmpeg/server.crt',
      serverKey: 'services/ffmpeg/server.key'
    },
    'pyannote': {
      server: 'services/pyannote/server.crt',
      serverKey: 'services/pyannote/server.key'
    },
    'open-parse': {
      server: 'services/open-parse/server.crt',
      serverKey: 'services/open-parse/server.key'
    }
  }
};

function testCertificateStructure() {
  console.log('🔍 Testing Certificate Structure...\n');
  
  let totalTests = 0;
  let passedTests = 0;
  
  for (const env of environments) {
    console.log(`📁 Testing ${env} environment:`);
    
    const baseDir = `./private-keys/${env}/certs/mtls`;
    const deployDir = `./deploy/certificates/${env}`;
    
    // Test CA certificate
    const caCertPath = path.join(baseDir, expectedPaths.ca);
    totalTests++;
    if (fs.existsSync(caCertPath)) {
      console.log(`  ✅ CA certificate: ${caCertPath}`);
      passedTests++;
    } else {
      console.log(`  ❌ CA certificate missing: ${caCertPath}`);
    }
    
    // Test deploy directory CA certificate
    const deployCaCertPath = path.join(deployDir, expectedPaths.ca);
    totalTests++;
    if (fs.existsSync(deployCaCertPath)) {
      console.log(`  ✅ Deploy CA certificate: ${deployCaCertPath}`);
      passedTests++;
    } else {
      console.log(`  ❌ Deploy CA certificate missing: ${deployCaCertPath}`);
    }
    
    // Test service certificates
    for (const [serviceName, servicePaths] of Object.entries(expectedPaths.services)) {
      for (const [certType, certPath] of Object.entries(servicePaths)) {
        const fullPath = path.join(baseDir, certPath);
        const deployPath = path.join(deployDir, certPath);
        
        totalTests += 2;
        
        // Test in private-keys directory
        if (fs.existsSync(fullPath)) {
          console.log(`  ✅ ${serviceName} ${certType}: ${fullPath}`);
          passedTests++;
        } else {
          console.log(`  ❌ ${serviceName} ${certType} missing: ${fullPath}`);
        }
        
        // Test in deploy directory
        if (fs.existsSync(deployPath)) {
          console.log(`  ✅ Deploy ${serviceName} ${certType}: ${deployPath}`);
          passedTests++;
        } else {
          console.log(`  ❌ Deploy ${serviceName} ${certType} missing: ${deployPath}`);
        }
      }
    }
    
    console.log('');
  }
  
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All certificate structure tests passed!');
    return true;
  } else {
    console.log('⚠️  Some certificate structure tests failed.');
    return false;
  }
}

// Test certificate validity
function testCertificateValidity() {
  console.log('\n🔐 Testing Certificate Validity...\n');
  
  const { execSync } = require('child_process');
  let validCerts = 0;
  let totalCerts = 0;
  
  for (const env of environments) {
    console.log(`🔍 Testing ${env} certificates:`);
    
    const baseDir = `./private-keys/${env}/certs/mtls`;
    const caCertPath = path.join(baseDir, 'ca/ca.crt');
    
    if (!fs.existsSync(caCertPath)) {
      console.log(`  ❌ CA certificate not found: ${caCertPath}`);
      continue;
    }
    
    // Test each service certificate against the CA
    for (const serviceName of Object.keys(expectedPaths.services)) {
      const serverCertPath = path.join(baseDir, `services/${serviceName}/server.crt`);
      
      if (fs.existsSync(serverCertPath)) {
        totalCerts++;
        try {
          execSync(`openssl verify -CAfile "${caCertPath}" "${serverCertPath}"`, { stdio: 'pipe' });
          console.log(`  ✅ ${serviceName} server certificate is valid`);
          validCerts++;
        } catch (error) {
          console.log(`  ❌ ${serviceName} server certificate is invalid`);
        }
      }
      
      // Test client certificate for public-api
      if (serviceName === 'public-api') {
        const clientCertPath = path.join(baseDir, `services/${serviceName}/client.crt`);
        if (fs.existsSync(clientCertPath)) {
          totalCerts++;
          try {
            execSync(`openssl verify -CAfile "${caCertPath}" "${clientCertPath}"`, { stdio: 'pipe' });
            console.log(`  ✅ ${serviceName} client certificate is valid`);
            validCerts++;
          } catch (error) {
            console.log(`  ❌ ${serviceName} client certificate is invalid`);
          }
        }
      }
    }
    
    console.log('');
  }
  
  console.log(`📊 Certificate Validity: ${validCerts}/${totalCerts} certificates are valid`);
  
  if (validCerts === totalCerts && totalCerts > 0) {
    console.log('🎉 All certificates are valid!');
    return true;
  } else {
    console.log('⚠️  Some certificates are invalid or missing.');
    return false;
  }
}

// Run tests
const structureTest = testCertificateStructure();
const validityTest = testCertificateValidity();

if (structureTest && validityTest) {
  console.log('\n🎉 Certificate structure is ready for mTLS!');
  process.exit(0);
} else {
  console.log('\n❌ Certificate structure needs fixes.');
  process.exit(1);
}
