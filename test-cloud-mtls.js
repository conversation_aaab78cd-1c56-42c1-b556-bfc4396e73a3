#!/usr/bin/env node

/**
 * Test mTLS connections in the cloud development environment
 * This script tests server-to-server mTLS connections between cloud services
 */

const https = require("https");
const fs = require("fs");
const path = require("path");

// Development environment endpoints
const DEV_ENDPOINTS = {
  api: "https://api.dev.divinci.app",
  live: "https://live.dev.divinci.app",
  pyannote: "https://pyannote.dev.divinci.app",
  openparse: "https://openparse.dev.divinci.app",
  ffmpeg: "https://ffmpeg.dev.divinci.app",
};

// Test endpoints
const TEST_ENDPOINTS = [
  { name: "API Health", url: `${DEV_ENDPOINTS.api}/health` },
  { name: "Live API Health", url: `${DEV_ENDPOINTS.live}/health` },
  { name: "Pyannote Health", url: `${DEV_ENDPOINTS.pyannote}/health` },
  { name: "Open Parse Health", url: `${DEV_ENDPOINTS.openparse}/health` },
  { name: "FFmpeg Health", url: `${DEV_ENDPOINTS.ffmpeg}/health` },
];

// Colors for console output
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test regular HTTPS connection (no mTLS)
async function testRegularHTTPS(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.url);

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: "GET",
      timeout: 10000,
      rejectUnauthorized: true,
    };

    log(`🌐 Testing regular HTTPS: ${endpoint.name}`, "blue");

    const req = https.request(options, (res) => {
      let data = "";
      res.on("data", (chunk) => (data += chunk));
      res.on("end", () => {
        resolve({
          success: true,
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200), // Limit data for display
        });
      });
    });

    req.on("error", (error) => {
      resolve({
        success: false,
        error: error.message,
        code: error.code,
      });
    });

    req.on("timeout", () => {
      req.destroy();
      resolve({
        success: false,
        error: "Request timeout",
        code: "TIMEOUT",
      });
    });

    req.end();
  });
}

// Test mTLS connection (with client certificates)
async function testMTLSConnection(endpoint) {
  return new Promise((resolve) => {
    // Try to load mTLS certificates - use local certificates for testing
    // since develop environment uses Cloudflare-managed certificates
    const certPaths = [
      "./private-keys/local/certs/mtls/services/public-api/client.crt",
      "./private-keys/local/certs/mtls/client.crt",
    ];

    const keyPaths = [
      "./private-keys/local/certs/mtls/services/public-api/client.key",
      "./private-keys/local/certs/mtls/client.key",
    ];

    const caPaths = ["./private-keys/local/certs/mtls/ca/ca.crt"];

    let cert, key, ca;

    // Find valid certificate files
    for (const certPath of certPaths) {
      if (fs.existsSync(certPath)) {
        try {
          cert = fs.readFileSync(certPath);
          log(`📜 Found client certificate: ${certPath}`, "cyan");
          break;
        } catch (err) {
          log(
            `❌ Error reading certificate ${certPath}: ${err.message}`,
            "red"
          );
        }
      }
    }

    for (const keyPath of keyPaths) {
      if (fs.existsSync(keyPath)) {
        try {
          key = fs.readFileSync(keyPath);
          log(`🔑 Found client key: ${keyPath}`, "cyan");
          break;
        } catch (err) {
          log(`❌ Error reading key ${keyPath}: ${err.message}`, "red");
        }
      }
    }

    for (const caPath of caPaths) {
      if (fs.existsSync(caPath)) {
        try {
          ca = fs.readFileSync(caPath);
          log(`🏛️ Found CA certificate: ${caPath}`, "cyan");
          break;
        } catch (err) {
          log(`❌ Error reading CA ${caPath}: ${err.message}`, "red");
        }
      }
    }

    if (!cert || !key) {
      resolve({
        success: false,
        error: "Client certificate or key not found",
        code: "NO_CERT",
      });
      return;
    }

    const url = new URL(endpoint.url);

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: "GET",
      timeout: 10000,
      cert: cert,
      key: key,
      ca: ca,
      rejectUnauthorized: true,
      requestCert: true,
    };

    log(`🔐 Testing mTLS connection: ${endpoint.name}`, "blue");

    const req = https.request(options, (res) => {
      let data = "";
      res.on("data", (chunk) => (data += chunk));
      res.on("end", () => {
        resolve({
          success: true,
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200),
          mtls: true,
        });
      });
    });

    req.on("error", (error) => {
      resolve({
        success: false,
        error: error.message,
        code: error.code,
        mtls: true,
      });
    });

    req.on("timeout", () => {
      req.destroy();
      resolve({
        success: false,
        error: "Request timeout",
        code: "TIMEOUT",
        mtls: true,
      });
    });

    req.end();
  });
}

// Main test function
async function runTests() {
  log("🚀 Starting Cloud Development Environment mTLS Tests", "bold");
  log("=".repeat(60), "cyan");

  const results = [];

  for (const endpoint of TEST_ENDPOINTS) {
    log(`\n📍 Testing endpoint: ${endpoint.name}`, "yellow");
    log(`   URL: ${endpoint.url}`, "cyan");

    // Test regular HTTPS first
    const httpsResult = await testRegularHTTPS(endpoint);

    if (httpsResult.success) {
      log(
        `✅ Regular HTTPS: ${httpsResult.status} - ${httpsResult.data.substring(
          0,
          50
        )}...`,
        "green"
      );
    } else {
      log(
        `❌ Regular HTTPS failed: ${httpsResult.error} (${httpsResult.code})`,
        "red"
      );
    }

    // Test mTLS connection
    const mtlsResult = await testMTLSConnection(endpoint);

    if (mtlsResult.success) {
      log(
        `🔐 mTLS: ${mtlsResult.status} - ${mtlsResult.data.substring(
          0,
          50
        )}...`,
        "green"
      );
    } else {
      log(`🔒 mTLS failed: ${mtlsResult.error} (${mtlsResult.code})`, "yellow");
    }

    results.push({
      endpoint: endpoint.name,
      url: endpoint.url,
      https: httpsResult,
      mtls: mtlsResult,
    });
  }

  // Summary
  log("\n" + "=".repeat(60), "cyan");
  log("📊 Test Results Summary", "bold");
  log("=".repeat(60), "cyan");

  const httpsSuccesses = results.filter((r) => r.https.success).length;
  const mtlsSuccesses = results.filter((r) => r.mtls.success).length;

  log(
    `\n🌐 Regular HTTPS: ${httpsSuccesses}/${results.length} successful`,
    httpsSuccesses === results.length ? "green" : "yellow"
  );
  log(
    `🔐 mTLS: ${mtlsSuccesses}/${results.length} successful`,
    mtlsSuccesses > 0 ? "green" : "yellow"
  );

  // Detailed results
  log("\n📋 Detailed Results:", "bold");
  results.forEach((result) => {
    log(`\n${result.endpoint}:`, "yellow");
    log(
      `  HTTPS: ${result.https.success ? "✅" : "❌"} ${
        result.https.success ? result.https.status : result.https.error
      }`,
      result.https.success ? "green" : "red"
    );
    log(
      `  mTLS:  ${result.mtls.success ? "✅" : "❌"} ${
        result.mtls.success ? result.mtls.status : result.mtls.error
      }`,
      result.mtls.success ? "green" : "yellow"
    );
  });

  log("\n🎯 Next Steps:", "bold");
  if (httpsSuccesses === results.length) {
    log("✅ All services are accessible via regular HTTPS", "green");
  } else {
    log(
      "⚠️  Some services are not accessible - check service deployment",
      "yellow"
    );
  }

  if (mtlsSuccesses === 0) {
    log(
      "🔧 mTLS not configured on cloud services - this is expected for development",
      "yellow"
    );
    log(
      "   Cloud services typically use regular HTTPS with other security measures",
      "cyan"
    );
  } else {
    log(`✅ ${mtlsSuccesses} services support mTLS`, "green");
  }

  log("\n🏁 Cloud mTLS testing complete!", "bold");
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRegularHTTPS, testMTLSConnection };
