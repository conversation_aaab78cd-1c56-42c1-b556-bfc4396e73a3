#!/usr/bin/env node

/**
 * Test script to verify mTLS configuration is properly enabled
 */

const fs = require('fs');
const path = require('path');

// Test environments
const environments = ['local', 'staging'];

// Expected mTLS configuration values
const expectedConfig = {
  ENABLE_MTLS: ['true', '1'],
  DEBUG_MTLS: ['true', '1'],
  MTLS_ENABLED: ['true', '1'],
  API_MTLS_ENABLED: ['1'],
  MTLS_VERIFY_SERVER: ['true', '1'],
  MTLS_VERIFY_CLIENT: ['false', '0']
};

function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function testEnvironmentConfig(environment) {
  console.log(`🔍 Testing ${environment} environment configuration:`);
  
  const envDir = `./private-keys/${environment}`;
  const configFiles = [
    'endpoints.shared.env',
    'mtls.env',
    'pyannote.env',
    'web-client-dev.env',
    'api-dev.env'
  ];
  
  let totalTests = 0;
  let passedTests = 0;
  const allConfig = {};
  
  // Load all configuration files
  for (const configFile of configFiles) {
    const filePath = path.join(envDir, configFile);
    if (fs.existsSync(filePath)) {
      const config = parseEnvFile(filePath);
      Object.assign(allConfig, config);
      console.log(`  📄 Loaded ${configFile}`);
    }
  }
  
  // Test each expected configuration
  for (const [key, expectedValues] of Object.entries(expectedConfig)) {
    totalTests++;
    
    if (allConfig[key] && expectedValues.includes(allConfig[key])) {
      console.log(`  ✅ ${key}=${allConfig[key]}`);
      passedTests++;
    } else if (allConfig[key]) {
      console.log(`  ⚠️  ${key}=${allConfig[key]} (expected: ${expectedValues.join(' or ')})`);
    } else {
      console.log(`  ❌ ${key} not found (expected: ${expectedValues.join(' or ')})`);
    }
  }
  
  // Test certificate directory configuration
  totalTests++;
  if (allConfig.MTLS_CERT_DIR) {
    console.log(`  ✅ MTLS_CERT_DIR=${allConfig.MTLS_CERT_DIR}`);
    passedTests++;
  } else {
    console.log(`  ❌ MTLS_CERT_DIR not configured`);
  }
  
  console.log(`  📊 ${passedTests}/${totalTests} configuration tests passed\n`);
  
  return { passed: passedTests, total: totalTests };
}

function testCertificateAccess() {
  console.log('🔐 Testing certificate file access...\n');
  
  let totalTests = 0;
  let passedTests = 0;
  
  for (const env of environments) {
    console.log(`📁 Testing ${env} certificate access:`);
    
    const certDir = `./private-keys/${env}/certs/mtls`;
    const requiredCerts = [
      'ca/ca.crt',
      'services/public-api/server.crt',
      'services/public-api/server.key',
      'services/public-api/client.crt',
      'services/public-api/client.key'
    ];
    
    for (const certPath of requiredCerts) {
      totalTests++;
      const fullPath = path.join(certDir, certPath);
      
      if (fs.existsSync(fullPath)) {
        // Check if file is readable
        try {
          fs.accessSync(fullPath, fs.constants.R_OK);
          console.log(`  ✅ ${certPath} (readable)`);
          passedTests++;
        } catch (error) {
          console.log(`  ❌ ${certPath} (not readable)`);
        }
      } else {
        console.log(`  ❌ ${certPath} (missing)`);
      }
    }
    
    console.log('');
  }
  
  console.log(`📊 Certificate Access: ${passedTests}/${totalTests} tests passed\n`);
  
  return { passed: passedTests, total: totalTests };
}

function testDockerConfiguration() {
  console.log('🐳 Testing Docker configuration files...\n');
  
  const dockerFiles = [
    'docker/staging-mtls.yml',
    'docker/mtls-staging.yml',
    'docker/mtls-production.yml'
  ];
  
  let totalTests = 0;
  let passedTests = 0;
  
  for (const dockerFile of dockerFiles) {
    totalTests++;
    
    if (fs.existsSync(dockerFile)) {
      const content = fs.readFileSync(dockerFile, 'utf8');
      
      // Check for mTLS environment variables
      const hasMTLSEnv = content.includes('ENABLE_MTLS=true');
      const hasCertDir = content.includes('MTLS_CERT_DIR');
      const hasCertMounts = content.includes('/etc/ssl/ca/ca.crt') || content.includes('/etc/ssl/certs/');
      
      if (hasMTLSEnv && hasCertDir && hasCertMounts) {
        console.log(`  ✅ ${dockerFile} (properly configured)`);
        passedTests++;
      } else {
        console.log(`  ⚠️  ${dockerFile} (missing some mTLS configuration)`);
        if (!hasMTLSEnv) console.log(`    - Missing ENABLE_MTLS=true`);
        if (!hasCertDir) console.log(`    - Missing MTLS_CERT_DIR`);
        if (!hasCertMounts) console.log(`    - Missing certificate volume mounts`);
      }
    } else {
      console.log(`  ❌ ${dockerFile} (file not found)`);
    }
  }
  
  console.log(`📊 Docker Configuration: ${passedTests}/${totalTests} tests passed\n`);
  
  return { passed: passedTests, total: totalTests };
}

// Run all tests
console.log('🧪 mTLS Configuration Test Suite\n');
console.log('=' .repeat(50) + '\n');

let totalPassed = 0;
let totalTests = 0;

// Test environment configurations
for (const env of environments) {
  const result = testEnvironmentConfig(env);
  totalPassed += result.passed;
  totalTests += result.total;
}

// Test certificate access
const certResult = testCertificateAccess();
totalPassed += certResult.passed;
totalTests += certResult.total;

// Test Docker configuration
const dockerResult = testDockerConfiguration();
totalPassed += dockerResult.passed;
totalTests += dockerResult.total;

// Final results
console.log('=' .repeat(50));
console.log(`📊 Final Results: ${totalPassed}/${totalTests} tests passed`);

if (totalPassed === totalTests) {
  console.log('🎉 All mTLS configuration tests passed!');
  console.log('✅ mTLS is properly enabled and configured.');
  process.exit(0);
} else {
  console.log('⚠️  Some mTLS configuration tests failed.');
  console.log('❌ Please review the configuration and fix any issues.');
  process.exit(1);
}
