#!/usr/bin/env node

/**
 * Monitor mTLS logs for handshake success/failures
 * This script monitors various log sources for mTLS-related events
 */

const fs = require("fs");
const path = require("path");
const { spawn } = require("child_process");

// Colors for console output
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
  magenta: "\x1b[35m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
  dim: "\x1b[2m",
};

function log(message, color = "reset") {
  const timestamp = new Date().toISOString();
  console.log(
    `${colors.dim}[${timestamp}]${colors.reset} ${colors[color]}${message}${colors.reset}`
  );
}

// mTLS log patterns to monitor
const MTLS_PATTERNS = {
  success: [
    /✅.*mTLS/i,
    /🔐.*enabled/i,
    /mTLS.*successful/i,
    /certificate.*valid/i,
    /handshake.*complete/i,
    /TLS.*established/i,
  ],
  failure: [
    /❌.*mTLS/i,
    /⚠️.*mTLS/i,
    /mTLS.*failed/i,
    /certificate.*invalid/i,
    /certificate.*expired/i,
    /handshake.*failed/i,
    /TLS.*error/i,
    /ECONNREFUSED/i,
    /CERT_UNTRUSTED/i,
    /UNABLE_TO_GET_ISSUER_CERT/i,
  ],
  info: [
    /🔐.*mTLS/i,
    /🔑.*certificate/i,
    /🏛️.*CA/i,
    /📜.*certificate/i,
    /mTLS.*configuration/i,
    /certificate.*loaded/i,
  ],
  debug: [
    /DEBUG.*mTLS/i,
    /mTLS.*debug/i,
    /certificate.*path/i,
    /agent.*created/i,
  ],
};

// Statistics tracking
const stats = {
  total: 0,
  success: 0,
  failure: 0,
  info: 0,
  debug: 0,
  startTime: Date.now(),
};

// Log sources to monitor
const LOG_SOURCES = [
  {
    name: "Docker Compose Logs",
    command: "docker",
    args: ["compose", "-f", "docker/local.yml", "logs", "-f", "--tail=50"],
    enabled: true,
  },
  {
    name: "Local mTLS Test Server",
    command: "node",
    args: ["test-mtls-integration.js"],
    enabled: false, // Only enable if test server is needed
  },
];

// Process log line
function processLogLine(line, source) {
  stats.total++;

  let category = "other";
  let color = "reset";

  // Check for mTLS patterns
  for (const [cat, patterns] of Object.entries(MTLS_PATTERNS)) {
    if (patterns.some((pattern) => pattern.test(line))) {
      category = cat;
      stats[cat]++;

      switch (cat) {
        case "success":
          color = "green";
          break;
        case "failure":
          color = "red";
          break;
        case "info":
          color = "cyan";
          break;
        case "debug":
          color = "dim";
          break;
      }
      break;
    }
  }

  // Log the line with appropriate formatting
  if (category !== "other") {
    const icon =
      {
        success: "✅",
        failure: "❌",
        info: "🔐",
        debug: "🔍",
      }[category] || "📝";

    log(`${icon} [${source}] ${line.trim()}`, color);
  }
}

// Monitor a log source
function monitorLogSource(source) {
  if (!source.enabled) {
    return null;
  }

  log(`🚀 Starting monitor: ${source.name}`, "blue");

  const process = spawn(source.command, source.args, {
    stdio: ["ignore", "pipe", "pipe"],
    cwd: "/workspaces/server",
  });

  process.stdout.on("data", (data) => {
    const lines = data.toString().split("\n");
    lines.forEach((line) => {
      if (line.trim()) {
        processLogLine(line, source.name);
      }
    });
  });

  process.stderr.on("data", (data) => {
    const lines = data.toString().split("\n");
    lines.forEach((line) => {
      if (line.trim()) {
        processLogLine(line, `${source.name} (stderr)`);
      }
    });
  });

  process.on("close", (code) => {
    log(`📊 Monitor stopped: ${source.name} (exit code: ${code})`, "yellow");
  });

  process.on("error", (error) => {
    log(`❌ Monitor error: ${source.name} - ${error.message}`, "red");
  });

  return process;
}

// Display statistics
function displayStats() {
  const runtime = Math.round((Date.now() - stats.startTime) / 1000);

  console.log("\n" + "=".repeat(60));
  log("📊 mTLS Monitoring Statistics", "bold");
  console.log("=".repeat(60));

  log(`⏱️  Runtime: ${runtime} seconds`, "cyan");
  log(`📝 Total log lines processed: ${stats.total}`, "cyan");
  log(`✅ Success events: ${stats.success}`, "green");
  log(`❌ Failure events: ${stats.failure}`, "red");
  log(`🔐 Info events: ${stats.info}`, "cyan");
  log(`🔍 Debug events: ${stats.debug}`, "dim");

  const successRate =
    stats.success + stats.failure > 0
      ? Math.round((stats.success / (stats.success + stats.failure)) * 100)
      : 0;

  log(
    `📈 Success rate: ${successRate}%`,
    successRate > 80 ? "green" : successRate > 50 ? "yellow" : "red"
  );

  console.log("=".repeat(60));
}

// Main monitoring function
async function startMonitoring(duration = 60) {
  log("🚀 Starting mTLS Log Monitoring", "bold");
  log(`⏱️  Monitoring duration: ${duration} seconds`, "cyan");
  log("🔍 Watching for mTLS handshake events...", "cyan");
  console.log("=".repeat(60));

  // Start monitoring processes
  const processes = [];

  for (const source of LOG_SOURCES) {
    const proc = monitorLogSource(source);
    if (proc) {
      processes.push(proc);
    }
  }

  // If no processes started, run a quick test
  if (processes.length === 0 || true) {
    // Always run tests for demonstration
    log("⚠️  No log sources available, running quick mTLS test...", "yellow");

    // Run our test scripts to generate some mTLS logs
    const testCommands = [
      {
        name: "Internal mTLS Test",
        cmd: "node",
        args: ["test-internal-mtls.js"],
      },
      { name: "Cloud mTLS Test", cmd: "node", args: ["test-cloud-mtls.js"] },
    ];

    for (const test of testCommands) {
      log(`🧪 Running: ${test.name}`, "blue");

      const testProcess = spawn(test.cmd, test.args, {
        stdio: ["ignore", "pipe", "pipe"],
        cwd: "/workspaces/server",
      });

      testProcess.stdout.on("data", (data) => {
        const lines = data.toString().split("\n");
        lines.forEach((line) => {
          if (line.trim()) {
            processLogLine(line, test.name);
          }
        });
      });

      testProcess.stderr.on("data", (data) => {
        const lines = data.toString().split("\n");
        lines.forEach((line) => {
          if (line.trim()) {
            processLogLine(line, `${test.name} (stderr)`);
          }
        });
      });

      // Wait for test to complete
      await new Promise((resolve) => {
        testProcess.on("close", resolve);
      });
    }
  }

  // Monitor for specified duration
  if (processes.length > 0) {
    await new Promise((resolve) => setTimeout(resolve, duration * 1000));

    // Stop all processes
    processes.forEach((proc) => {
      if (proc && !proc.killed) {
        proc.kill("SIGTERM");
      }
    });
  }

  // Display final statistics
  displayStats();

  // Provide recommendations
  console.log("\n" + "=".repeat(60));
  log("🎯 Recommendations", "bold");
  console.log("=".repeat(60));

  if (stats.success > 0) {
    log("✅ mTLS is working correctly - handshakes successful", "green");
  }

  if (stats.failure > 0) {
    log(
      "⚠️  Some mTLS failures detected - check certificate configuration",
      "yellow"
    );
  }

  if (stats.success === 0 && stats.failure === 0) {
    log(
      "🔧 No mTLS activity detected - services may not be configured for mTLS",
      "yellow"
    );
    log("   This is expected if services are using regular HTTPS", "cyan");
  }

  log("📝 For detailed logs, check individual service logs", "cyan");
  log(
    "🔍 Use DEBUG_MTLS=true environment variable for more detailed logging",
    "cyan"
  );

  log("\n🏁 mTLS monitoring complete!", "bold");
}

// Handle command line arguments
const args = process.argv.slice(2);
const duration = args[0] ? parseInt(args[0]) : 30;

// Handle graceful shutdown
process.on("SIGINT", () => {
  log("\n🛑 Monitoring interrupted by user", "yellow");
  displayStats();
  process.exit(0);
});

// Run monitoring
if (require.main === module) {
  startMonitoring(duration).catch(console.error);
}

module.exports = { startMonitoring, processLogLine, displayStats };
