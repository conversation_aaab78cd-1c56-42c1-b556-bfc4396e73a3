# Divinci mTLS Package

This package provides utilities for implementing server-to-server mutual TLS (mTLS) authentication between Divinci internal services. This protects internal APIs from external access while allowing secure service-to-service communication.

**Note**: This package is for server-to-server mTLS only. Client-side/user-facing mTLS has been removed to focus on internal service protection.

## Installation

```bash
npm install @divinci-ai/mtls
```

## Usage

### Server-side mTLS

To create an HTTP or HTTPS server with mTLS support:

```typescript
import { createMTLSServer } from "@divinci-ai/mtls";
import express from "express";

// Create Express app
const app = express();

// Create mTLS server
const { server, mtlsEnabled, clientVerificationEnabled } = createMTLSServer({
  enableMTLS: true,
  verifyClient: true,
  environment: "production",
  debug: true,
});

// Use Express app with server
server.on("request", (req, res) => {
  app(req, res);
});

// Start server
const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
  console.log(`Server listening on port ${PORT}`);
  console.log(`mTLS enabled: ${mtlsEnabled}`);
  console.log(`Client verification enabled: ${clientVerificationEnabled}`);
});
```

To verify client certificates in Express middleware:

```typescript
import { verifyClientCertMiddleware } from "@divinci-ai/mtls";
import express from "express";

const app = express();

// Add middleware to verify client certificates
app.use(verifyClientCertMiddleware);

// Add routes
app.get("/", (req, res) => {
  res.send("Hello World!");
});
```

### Service-to-Service mTLS

To make requests between internal services:

```typescript
import { serviceFetch } from "@divinci-ai/mtls";

async function makeInternalRequest() {
  // serviceFetch automatically detects internal vs external services
  // Internal services use mTLS, external services use regular HTTPS
  const response = await serviceFetch(
    "https://internal-api.divinci.local/data",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ hello: "world" }),
    }
  );

  return response.json();
}
```

For external services (automatically uses regular HTTPS):

```typescript
async function makeExternalRequest() {
  // External services automatically use regular HTTPS
  const response = await serviceFetch("https://api.external.com/data", {
    method: "GET",
    headers: {
      Authorization: "Bearer token",
    },
  });

  return response.json();
}
```

## Certificate Management

The package provides utilities for managing certificates:

```typescript
import {
  getStandardPath,
  getAllPossiblePaths,
  resolveCertificatePath,
  getCAPath,
  loadCertificate,
  loadCACertificate,
  validateCertificate,
} from "@divinci-ai/mtls";

// Get standard path for server certificate
const serverCertPath = getStandardPath("server", "cert");
// => '/etc/ssl/certs/server.crt'

// Get all possible paths for client key
const clientKeyPaths = getAllPossiblePaths("client", "key");
// => ['/etc/ssl/private/client.key', ...]

// Resolve actual path for server certificate
const resolvedServerCertPath = resolveCertificatePath("server", "cert");
// => '/etc/ssl/certs/server.crt' (if it exists)
// => null (if not found)

// Get CA certificate path
const caCertPath = getCAPath();
// => '/etc/ssl/certs/ca.crt' (if it exists)
// => null (if not found)

// Load server certificate
const serverCert = loadCertificate("server", "cert");
// => Certificate content (if found)
// => null (if not found)

// Load CA certificate
const caCert = loadCACertificate();
// => CA certificate content (if found)
// => null (if not found)

// Validate certificate
const isValid = validateCertificate(serverCert, "cert");
// => true (if valid)
// => false (if invalid)
```

## Environment Variables

The package uses the following environment variables:

- `ENABLE_MTLS`: Enable mTLS (true/1 or false/0)
- `MTLS_VERIFY_CLIENT`: Verify client certificates (true/1 or false/0)
- `MTLS_VERIFY_SERVER`: Verify server certificates (true/1 or false/0)
- `MTLS_CERT_DIR`: Custom certificate directory
- `MTLS_SERVER_CERT_PATH`: Custom server certificate path
- `MTLS_SERVER_KEY_PATH`: Custom server key path
- `MTLS_CLIENT_CERT_PATH`: Custom client certificate path
- `MTLS_CLIENT_KEY_PATH`: Custom client key path
- `MTLS_CA_CERT_PATH`: Custom CA certificate path
- `DEBUG_MTLS`: Enable debug logging (true/1 or false/0)
- `ENVIRONMENT`: Environment (local, develop, staging, production)

## Certificate Paths

The package looks for certificates in the following locations (in order):

### Server Certificate

1. `/etc/ssl/certs/server.crt`
2. `$MTLS_SERVER_CERT_PATH`
3. `$MTLS_CERT_DIR/server.crt`
4. `/private-keys/local/certs/mtls/server.crt`
5. `/private-keys/$ENVIRONMENT/certs/mtls/server.crt`

### Server Key

1. `/etc/ssl/private/server.key`
2. `$MTLS_SERVER_KEY_PATH`
3. `$MTLS_CERT_DIR/server.key`
4. `/private-keys/local/certs/mtls/server.key`
5. `/private-keys/$ENVIRONMENT/certs/mtls/server.key`

### Client Certificate

1. `/etc/ssl/client/client.crt`
2. `$MTLS_CLIENT_CERT_PATH`
3. `$MTLS_CERT_DIR/client.crt`
4. `/private-keys/local/certs/mtls/client.crt`
5. `/private-keys/$ENVIRONMENT/certs/mtls/client.crt`

### Client Key

1. `/etc/ssl/private/client.key`
2. `$MTLS_CLIENT_KEY_PATH`
3. `$MTLS_CERT_DIR/client.key`
4. `/private-keys/local/certs/mtls/client.key`
5. `/private-keys/$ENVIRONMENT/certs/mtls/client.key`

### CA Certificate

1. `/etc/ssl/certs/ca.crt`
2. `$MTLS_CA_CERT_PATH`
3. `$MTLS_CERT_DIR/ca.crt`
4. `/private-keys/local/certs/mtls/ca.crt`
5. `/private-keys/$ENVIRONMENT/certs/mtls/ca.crt`
