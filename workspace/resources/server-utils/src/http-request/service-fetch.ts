/**
 * Service fetch utilities with automatic mTLS detection for internal services
 */

// Note: Using dynamic import for mTLS to avoid build-time dependency issues
// import { fetchWithMTLS } from "@divinci-ai/mtls";

/**
 * List of internal service hostnames/patterns that should use mTLS
 */
const INTERNAL_SERVICE_PATTERNS = [
  "audio-speak-dia-pyannote",
  "audio-splitter-ffmpeg",
  "open-parse",
  "localhost",
  ".local",
  ".internal",
];

/**
 * List of specific internal service hostnames (exact matches)
 */
const INTERNAL_SERVICE_EXACT_MATCHES = ["pyannote", "ffmpeg"];

/**
 * Determines if a URL points to an internal service that should use mTLS
 * @param url The URL to check
 * @returns true if the URL is for an internal service
 */
export function isInternalService(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Check for exact matches first (to avoid false positives)
    if (INTERNAL_SERVICE_EXACT_MATCHES.includes(hostname)) {
      return true;
    }

    // Check if hostname contains any internal service patterns
    return INTERNAL_SERVICE_PATTERNS.some((pattern) =>
      hostname.includes(pattern.toLowerCase())
    );
  } catch {
    // If URL parsing fails, assume it's external
    return false;
  }
}

/**
 * Fetch function that automatically uses mTLS for internal services
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise<Response>
 */
export async function serviceFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const isInternal = isInternalService(url);

  if (isInternal) {
    // Use mTLS for internal services
    const debug =
      process.env.DEBUG_MTLS === "true" || process.env.DEBUG_MTLS === "1";
    if (debug) {
      console.log(`🔐 Using mTLS for internal service: ${url}`);
    }

    try {
      // Try to load mTLS module at runtime
      const mtlsModule = require("../../../mtls/dist/index.js");
      const { fetchWithMTLS } = mtlsModule;

      if (fetchWithMTLS) {
        return fetchWithMTLS(url, {
          ...options,
          mtlsOptions: {
            enableMTLS: true,
            verifyServer: true,
            debug,
          },
        });
      } else {
        throw new Error("fetchWithMTLS not found in mTLS module");
      }
    } catch (error) {
      if (debug) {
        console.warn(
          `⚠️ mTLS module not available, falling back to regular fetch: ${error}`
        );
      }
      return fetch(url, options);
    }
  } else {
    // Use regular fetch for external services
    const debug =
      process.env.DEBUG_MTLS === "true" || process.env.DEBUG_MTLS === "1";
    if (debug) {
      console.log(`🌐 Using regular HTTPS for external service: ${url}`);
    }
    return fetch(url, options);
  }
}

/**
 * Service fetch with JSON response parsing
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise<T> parsed JSON response
 */
export async function serviceFetchJSON<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await serviceFetch(url, options);

  if (!response.ok) {
    throw new Error(
      `Service fetch failed: ${response.status} ${response.statusText}`
    );
  }

  return response.json() as Promise<T>;
}

/**
 * Create a service fetch function with a base URL
 * @param baseUrl Base URL for all requests
 * @param defaultOptions Default options to apply to all requests
 * @returns Function that makes requests relative to the base URL
 */
export function createServiceFetch(
  baseUrl: string,
  defaultOptions: RequestInit = {}
): (path: string, options?: RequestInit) => Promise<Response> {
  return async (path: string, options: RequestInit = {}) => {
    const url = new URL(path, baseUrl);
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    return serviceFetch(url.toString(), mergedOptions);
  };
}

/**
 * Create a service fetch function with JSON parsing
 * @param baseUrl Base URL for all requests
 * @param defaultOptions Default options to apply to all requests
 * @returns Function that makes requests and parses JSON responses
 */
export function createServiceFetchJSON<T = any>(
  baseUrl: string,
  defaultOptions: RequestInit = {}
): (path: string, options?: RequestInit) => Promise<T> {
  const serviceFetch = createServiceFetch(baseUrl, defaultOptions);

  return async (path: string, options: RequestInit = {}) => {
    const response = await serviceFetch(path, options);

    if (!response.ok) {
      throw new Error(
        `Service fetch failed: ${response.status} ${response.statusText}`
      );
    }

    return response.json() as Promise<T>;
  };
}
