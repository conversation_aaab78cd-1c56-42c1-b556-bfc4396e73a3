import fs from "fs";
import https from "https";
import { Agent } from "https";

/**
 * Type definition for request options
 */
export type RequestOptions = RequestInit & {
  baseURL?: string;
  agent?: Agent;
};

/**
 * Creates an HTTPS agent configured for mutual TLS (mTLS)
 *
 * @deprecated Use createMTLSAgent from @divinci-ai/mtls instead
 * @param options Configuration options for the mTLS agent
 * @returns HTTPS agent configured for mTLS
 */
export function createMtlsAgent(options: {
  caCert: string;
  clientCert: string;
  clientKey: string;
}) {
  const { caCert, clientCert, clientKey } = options;

  // Read certificate files
  const ca = fs.readFileSync(caCert);
  const cert = fs.readFileSync(clientCert);
  const key = fs.readFileSync(clientKey);

  // Create HTTPS agent with mTLS configuration
  return new https.Agent({
    ca,
    cert,
    key,
    rejectUnauthorized: true, // Enforce certificate validation
  });
}

/**
 * Creates a fetch function configured for mTLS
 *
 * @param baseURL Base URL for the fetch function
 * @param options Configuration options for the mTLS agent
 * @returns Fetch function configured for mTLS
 */
export function createMtlsFetch(
  baseURL: string,
  options: {
    caCert: string;
    clientCert: string;
    clientKey: string;
  }
) {
  // Create mTLS HTTPS agent
  const httpsAgent = createMtlsAgent(options);

  // Return a fetch function with mTLS configuration
  return async (url: string, init: RequestInit = {}): Promise<Response> => {
    const fullUrl = new URL(url, baseURL).toString();

    // Create a new options object with our agent
    const fetchOptions = {
      ...init,
      // Node.js fetch accepts agent property but TypeScript doesn't know about it
      agent: httpsAgent,
    } as RequestInit;

    return fetch(fullUrl, fetchOptions);
  };
}

/**
 * Adds mTLS configuration to existing fetch options
 *
 * @param options Existing fetch options
 * @param mtlsOptions Configuration options for the mTLS agent
 * @returns Updated fetch options with mTLS
 */
export function addMtlsToFetchOptions(
  options: RequestOptions = {},
  mtlsOptions: {
    caCert: string;
    clientCert: string;
    clientKey: string;
  }
): RequestOptions {
  // Create mTLS HTTPS agent
  const httpsAgent = createMtlsAgent(mtlsOptions);

  // Add mTLS configuration to existing options
  return {
    ...options,
    agent: httpsAgent,
  };
}

/**
 * Determines if mTLS is enabled based on environment variables
 *
 * @returns Boolean indicating if mTLS is enabled
 */
export function isMtlsEnabled(): boolean {
  return process.env.MTLS_ENABLED === "true";
}

/**
 * Gets mTLS configuration from environment variables
 *
 * @returns mTLS configuration object or null if not enabled
 */
export function getMtlsConfig(): {
  caCert: string;
  clientCert: string;
  clientKey: string;
} | null {
  if (!isMtlsEnabled()) {
    return null;
  }

  const caCert = process.env.MTLS_CA_CERT;
  const clientCert = process.env.MTLS_CLIENT_CERT;
  const clientKey = process.env.MTLS_CLIENT_KEY;

  if (!caCert || !clientCert || !clientKey) {
    throw new Error(
      "mTLS is enabled but certificate paths are not properly configured"
    );
  }

  return {
    caCert,
    clientCert,
    clientKey,
  };
}

/**
 * Creates a fetch function configured for mTLS if enabled, or a regular fetch function if not
 *
 * @param baseURL Base URL for the fetch function
 * @returns Fetch function (with mTLS if enabled)
 */
export function createMTLSServiceFetch(baseURL: string) {
  const mtlsConfig = getMtlsConfig();

  if (mtlsConfig) {
    return createMtlsFetch(baseURL, mtlsConfig);
  }

  // Return a regular fetch function
  return async (url: string, init: RequestInit = {}): Promise<Response> => {
    const fullUrl = new URL(url, baseURL).toString();
    return fetch(fullUrl, init);
  };
}

/**
 * Adds mTLS configuration to existing fetch options if mTLS is enabled
 *
 * @param options Existing fetch options
 * @returns Updated fetch options (with mTLS if enabled)
 */
export function addServiceMtlsToFetchOptions(
  options: RequestOptions = {}
): RequestOptions {
  const mtlsConfig = getMtlsConfig();

  if (mtlsConfig) {
    return addMtlsToFetchOptions(options, mtlsConfig);
  }

  return options;
}

// For backward compatibility with code that expects axios-like interfaces
// These functions are deprecated and should be migrated to fetch-based alternatives

/**
 * @deprecated Use createMtlsFetch instead
 */
export function createMtlsAxiosInstance(
  baseURL: string,
  options: {
    caCert: string;
    clientCert: string;
    clientKey: string;
  }
) {
  console.warn(
    "createMtlsAxiosInstance is deprecated. Use createMtlsFetch instead."
  );
  const fetchFn = createMtlsFetch(baseURL, options);

  // Return an axios-like interface that uses fetch internally
  return {
    get: async (url: string, config: any = {}) => {
      const response = await fetchFn(url, { method: "GET", ...config });
      return {
        data: await response.json(),
        status: response.status,
        headers: response.headers,
      };
    },
    post: async (url: string, data: any, config: any = {}) => {
      const response = await fetchFn(url, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
          ...(config.headers || {}),
        },
        ...config,
      });
      return {
        data: await response.json(),
        status: response.status,
        headers: response.headers,
      };
    },
    // Add other methods as needed for compatibility
  };
}

/**
 * @deprecated Use addMtlsToFetchOptions instead
 */
export function addMtlsToAxiosConfig(
  config: any,
  options: {
    caCert: string;
    clientCert: string;
    clientKey: string;
  }
): any {
  console.warn(
    "addMtlsToAxiosConfig is deprecated. Use addMtlsToFetchOptions instead."
  );
  const fetchOptions = addMtlsToFetchOptions({}, options);
  return { ...config, agent: fetchOptions.agent };
}

/**
 * @deprecated Use createMTLSServiceFetch instead
 */
export function createServiceAxiosInstance(baseURL: string) {
  console.warn(
    "createServiceAxiosInstance is deprecated. Use createMTLSServiceFetch instead."
  );
  const fetchFn = createMTLSServiceFetch(baseURL);

  // Return an axios-like interface that uses fetch internally
  return {
    get: async (url: string, config: any = {}) => {
      const response = await fetchFn(url, { method: "GET", ...config });
      return {
        data: await response.json(),
        status: response.status,
        headers: response.headers,
      };
    },
    post: async (url: string, data: any, config: any = {}) => {
      const response = await fetchFn(url, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
          ...(config.headers || {}),
        },
        ...config,
      });
      return {
        data: await response.json(),
        status: response.status,
        headers: response.headers,
      };
    },
    // Add other methods as needed for compatibility
  };
}

/**
 * @deprecated Use addServiceMtlsToFetchOptions instead
 */
export function addServiceMtlsToAxiosConfig(config: any): any {
  console.warn(
    "addServiceMtlsToAxiosConfig is deprecated. Use addServiceMtlsToFetchOptions instead."
  );
  const fetchOptions = addServiceMtlsToFetchOptions({});
  return { ...config, agent: fetchOptions.agent };
}
