# mTLS Configuration for Staging Environment
# This file contains all mTLS-related environment variables for staging

# Enable mTLS globally
ENABLE_MTLS=true

# Enable debug logging for mTLS
DEBUG_MTLS=true

# Certificate directory (standard Linux SSL path for production/staging)
MTLS_CERT_DIR=/etc/ssl

# Server certificate verification (verify server certificates when making outbound requests)
MTLS_VERIFY_SERVER=true

# Client certificate verification (require client certificates for inbound requests)
# Set to false for server-only TLS mode (recommended for initial deployment)
MTLS_VERIFY_CLIENT=false

# Certificate paths (these will be auto-resolved based on MTLS_CERT_DIR)
# Uncomment and customize if you need specific paths
# MTLS_CA_CERT_PATH=/etc/ssl/ca/ca.crt
# MTLS_SERVER_CERT_PATH=/etc/ssl/certs/server.crt
# MTLS_SERVER_KEY_PATH=/etc/ssl/private/server.key
# MTLS_CLIENT_CERT_PATH=/etc/ssl/certs/client.crt
# MTLS_CLIENT_KEY_PATH=/etc/ssl/private/client.key

# Debug key for mTLS debugging
DEBUG_MTLS_KEY=abcdefg

# TLS options
REQUIRE_TLS=false
ENABLE_SERVER_ONLY_TLS=true

# OPTIONS request handling (bypass mTLS for CORS preflight)
MTLS_BYPASS_OPTIONS=true
OPTIONS_PORT=8082
