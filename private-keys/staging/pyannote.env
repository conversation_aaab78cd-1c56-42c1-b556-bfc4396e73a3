# Configuration for the pyannote speaker diarization service
DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT=8085
PYANNOTE_APIKEY=sk_2f83e0deacd444a288ff6043f6430ebd

# Hugging Face token for accessing pyannote models
HUGGING_FACE_ACCESS_TOKEN=*************************************

# Service URL for other services to access this one
DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL=https://pyannote.stage.divinci.app

# Logging configuration
LOG_LEVEL=INFO

# mTLS configuration (enabled for staging)
MTLS_ENABLED=true
ENABLE_MTLS=true
MTLS_CERT_DIR=/etc/ssl

# Cache directories
HOME=/home/<USER>
MPLCONFIGDIR=/home/<USER>/.cache/matplotlib
HF_HOME=/home/<USER>/.cache/huggingface
XDG_CACHE_HOME=/home/<USER>/.cache

# For API, not local worker
# https://www.pyannote.ai/
PYANNOTE_APIKEY=sk_2f83e0deacd444a288ff6043f6430ebd
