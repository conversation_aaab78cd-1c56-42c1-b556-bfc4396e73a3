"""Generated message classes for clouddeploy version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'clouddeploy'


class AbandonReleaseRequest(_messages.Message):
  r"""The request object used by `AbandonRelease`."""


class AbandonReleaseResponse(_messages.Message):
  r"""The response object for `AbandonRelease`."""


class AdvanceChildRolloutJob(_messages.Message):
  r"""An advanceChildRollout Job."""


class AdvanceChildRolloutJobRun(_messages.Message):
  r"""AdvanceChildRolloutJobRun contains information specific to a
  advanceChildRollout `JobRun`.

  Fields:
    rollout: Output only. Name of the `ChildRollout`. Format is `projects/{pro
      ject}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases
      /{release}/rollouts/{rollout}`.
    rolloutPhaseId: Output only. the ID of the ChildRollout's Phase.
  """

  rollout = _messages.StringField(1)
  rolloutPhaseId = _messages.StringField(2)


class AdvanceRolloutOperation(_messages.Message):
  r"""Contains the information of an automated advance-rollout operation.

  Fields:
    destinationPhase: Output only. The phase the rollout will be advanced to.
    rollout: Output only. The name of the rollout that initiates the
      `AutomationRun`.
    sourcePhase: Output only. The phase of a deployment that initiated the
      operation.
    wait: Output only. How long the operation will be paused.
  """

  destinationPhase = _messages.StringField(1)
  rollout = _messages.StringField(2)
  sourcePhase = _messages.StringField(3)
  wait = _messages.StringField(4)


class AdvanceRolloutRequest(_messages.Message):
  r"""The request object used by `AdvanceRollout`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
    phaseId: Required. The phase ID to advance the `Rollout` to.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  phaseId = _messages.StringField(2)


class AdvanceRolloutResponse(_messages.Message):
  r"""The response object from `AdvanceRollout`."""


class AdvanceRolloutRule(_messages.Message):
  r"""The `AdvanceRollout` automation rule will automatically advance a
  successful Rollout to the next phase.

  Fields:
    condition: Output only. Information around the state of the Automation
      rule.
    id: Required. ID of the rule. This id must be unique in the `Automation`
      resource to which this rule belongs. The format is
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
    sourcePhases: Optional. Proceeds only after phase name matched any one in
      the list. This value must consist of lower-case letters, numbers, and
      hyphens, start with a letter and end with a letter or a number, and have
      a max length of 63 characters. In other words, it must match the
      following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    wait: Optional. How long to wait after a rollout is finished.
  """

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  id = _messages.StringField(2)
  sourcePhases = _messages.StringField(3, repeated=True)
  wait = _messages.StringField(4)


class AlertPolicyCheck(_messages.Message):
  r"""AlertPolicyCheck configures a set of Cloud Monitoring alerting policies
  that will be periodically polled for alerts. If any of the listed policies
  have an active alert, the analysis check will fail.

  Messages:
    LabelsValue: Optional. A set of labels to filter active alerts. If set,
      only alerts having all of the specified labels will be considered.

  Fields:
    alertPolicies: Required. The Cloud Monitoring Alert Policies to check for
      active alerts. Format is
      `projects/{project}/alertPolicies/{alert_policy}`.
    id: Required. The ID of the analysis check.
    labels: Optional. A set of labels to filter active alerts. If set, only
      alerts having all of the specified labels will be considered.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of labels to filter active alerts. If set, only alerts
    having all of the specified labels will be considered.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alertPolicies = _messages.StringField(1, repeated=True)
  id = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)


class AlertPolicyCheckStatus(_messages.Message):
  r"""AlertPolicyCheckStatus contains information specific to a single run of
  an alert policy check.

  Messages:
    LabelsValue: Output only. The resolved labels used to filter for specific
      incidents.

  Fields:
    alertPolicies: Output only. The alert policies that this analysis
      monitors. Format is
      `projects/{project}/locations/{location}/alertPolicies/{alertPolicy}`.
    failedAlertPolicies: Output only. The alert policies that were found to be
      firing during this check. This will be empty if no incidents were found.
    id: Output only. The ID of this analysis.
    labels: Output only. The resolved labels used to filter for specific
      incidents.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. The resolved labels used to filter for specific
    incidents.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alertPolicies = _messages.StringField(1, repeated=True)
  failedAlertPolicies = _messages.MessageField('FailedAlertPolicy', 2, repeated=True)
  id = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)


class Analysis(_messages.Message):
  r"""Analysis contains the configuration for the set of analyses to be
  performed on the target.

  Fields:
    customChecks: Optional. Custom analysis checks from 3P metric providers.
    duration: Required. The amount of time in minutes the analysis on the
      target will last. If all analysis checks have successfully completed
      before the specified duration, the analysis is successful. If a check is
      still running while the specified duration passes, it will wait for that
      check to complete to determine if the analysis is successful. The
      maximum duration is 48 hours.
    googleCloud: Optional. Google Cloud - based analysis checks.
  """

  customChecks = _messages.MessageField('CustomCheck', 1, repeated=True)
  duration = _messages.StringField(2)
  googleCloud = _messages.MessageField('GoogleCloudAnalysis', 3)


class AnalysisJob(_messages.Message):
  r"""An analysis Job.

  Fields:
    customChecks: Optional. Custom analysis checks from 3P metric providers
      that are run as part of the analysis Job.
    duration: Required. The amount of time in minutes the analysis Job will
      last. If any check in this Job is still running when the duration ends,
      the Job keeps running until that check completes. The maximum duration
      is 48 hours.
    googleCloud: Optional. Google Cloud - based analysis checks that are run
      as part of the analysis Job.
  """

  customChecks = _messages.MessageField('CustomCheck', 1, repeated=True)
  duration = _messages.StringField(2)
  googleCloud = _messages.MessageField('GoogleCloudAnalysis', 3)


class AnalysisJobRun(_messages.Message):
  r"""AnalysisJobRun contains information specific to an analysis `JobRun`.

  Fields:
    alertPolicyAnalyses: Output only. The status of the running alert policy
      checks configured for this analysis.
    customCheckAnalyses: Output only. The status of the running custom checks
      configured for this analysis.
    failedCheckId: Output only. The ID of the configured check that failed.
      This will always be blank while the analysis is in progress or if it
      succeeded.
  """

  alertPolicyAnalyses = _messages.MessageField('AlertPolicyCheckStatus', 1, repeated=True)
  customCheckAnalyses = _messages.MessageField('CustomCheckStatus', 2, repeated=True)
  failedCheckId = _messages.StringField(3)


class AnthosCluster(_messages.Message):
  r"""Information specifying an Anthos Cluster.

  Fields:
    membership: Optional. Membership of the GKE Hub-registered cluster to
      which to apply the Skaffold configuration. Format is
      `projects/{project}/locations/{location}/memberships/{membership_name}`.
  """

  membership = _messages.StringField(1)


class AnthosRenderMetadata(_messages.Message):
  r"""AnthosRenderMetadata contains Anthos information associated with a
  `Release` render.

  Fields:
    canaryDeployment: Output only. Name of the canary version of the
      Kubernetes Deployment that will be applied to the Anthos cluster. Only
      set if a canary deployment strategy was configured.
    deployment: Output only. Name of the Kubernetes Deployment that will be
      applied to the Anthos cluster. Only set if a single Deployment was
      provided in the rendered manifest.
    kubernetesNamespace: Output only. Namespace the Kubernetes resources will
      be applied to in the Anthos cluster. Only set if applying resources to a
      single namespace.
  """

  canaryDeployment = _messages.StringField(1)
  deployment = _messages.StringField(2)
  kubernetesNamespace = _messages.StringField(3)


class ApproveRolloutRequest(_messages.Message):
  r"""The request object used by `ApproveRollout`.

  Fields:
    approved: Required. True = approve; false = reject
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  """

  approved = _messages.BooleanField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)


class ApproveRolloutResponse(_messages.Message):
  r"""The response object from `ApproveRollout`."""


class AssociatedEntities(_messages.Message):
  r"""Information about entities associated with a `Target`.

  Fields:
    anthosClusters: Optional. Information specifying Anthos clusters as
      associated entities.
    gkeClusters: Optional. Information specifying GKE clusters as associated
      entities.
  """

  anthosClusters = _messages.MessageField('AnthosCluster', 1, repeated=True)
  gkeClusters = _messages.MessageField('GkeCluster', 2, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Automation(_messages.Message):
  r"""An `Automation` resource in the Cloud Deploy API. An `Automation`
  enables the automation of manually driven actions for a Delivery Pipeline,
  which includes Release promotion among Targets, Rollout repair and Rollout
  deployment strategy advancement. The intention of Automation is to reduce
  manual intervention in the continuous delivery process.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. Annotations must meet
      the following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (`/`). * The name segment is required and must be
      63 characters or less, beginning and ending with an alphanumeric
      character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots
      (`.`), and alphanumerics between. * The prefix is optional. If
      specified, the prefix must be a DNS subdomain: a series of DNS labels
      separated by dots(`.`), not longer than 253 characters in total,
      followed by a slash (`/`). See
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 63 characters.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. Annotations must meet the
      following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (`/`). * The name segment is required and must be
      63 characters or less, beginning and ending with an alphanumeric
      character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots
      (`.`), and alphanumerics between. * The prefix is optional. If
      specified, the prefix must be a DNS subdomain: a series of DNS labels
      separated by dots(`.`), not longer than 253 characters in total,
      followed by a slash (`/`). See
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    createTime: Output only. Time at which the automation was created.
    description: Optional. Description of the `Automation`. Max length is 255
      characters.
    etag: Optional. The weak etag of the `Automation` resource. This checksum
      is computed by the server based on the value of other fields, and may be
      sent on update and delete requests to ensure the client has an up-to-
      date value before proceeding.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 63 characters.
    name: Output only. Name of the `Automation`. Format is `projects/{project}
      /locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/
      {automation}`.
    rules: Required. List of Automation rules associated with the Automation
      resource. Must have at least one rule and limited to 250 rules per
      Delivery Pipeline. Note: the order of the rules here is not the same as
      the order of execution.
    selector: Required. Selected resources to which the automation will be
      applied.
    serviceAccount: Required. Email address of the user-managed IAM service
      account that creates Cloud Deploy release and rollout resources.
    suspended: Optional. When Suspended, automation is deactivated from
      execution.
    uid: Output only. Unique identifier of the `Automation`.
    updateTime: Output only. Time at which the automation was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. Annotations must meet the following
    constraints: * Annotations are key/value pairs. * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash
    (`/`). * The name segment is required and must be 63 characters or less,
    beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with
    dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between. *
    The prefix is optional. If specified, the prefix must be a DNS subdomain:
    a series of DNS labels separated by dots(`.`), not longer than 253
    characters in total, followed by a slash (`/`). See
    https://kubernetes.io/docs/concepts/overview/working-with-
    objects/annotations/#syntax-and-character-set for more details.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    63 characters.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  rules = _messages.MessageField('AutomationRule', 7, repeated=True)
  selector = _messages.MessageField('AutomationResourceSelector', 8)
  serviceAccount = _messages.StringField(9)
  suspended = _messages.BooleanField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class AutomationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/automation" Platform Log
  event that describes the Automation related events.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    automation: The name of the `AutomationRun`.
    message: Debug message for when there is an update on the AutomationRun.
      Provides further details about the resource creation or state change.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  automation = _messages.StringField(1)
  message = _messages.StringField(2)
  pipelineUid = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class AutomationResourceSelector(_messages.Message):
  r"""AutomationResourceSelector contains the information to select the
  resources to which an Automation is going to be applied.

  Fields:
    targets: Optional. Contains attributes about a target.
  """

  targets = _messages.MessageField('TargetAttribute', 1, repeated=True)


class AutomationRolloutMetadata(_messages.Message):
  r"""AutomationRolloutMetadata contains Automation-related actions that were
  performed on a rollout.

  Fields:
    advanceAutomationRuns: Output only. The names of the AutomationRuns
      initiated by an advance rollout rule.
    promoteAutomationRun: Output only. The name of the AutomationRun initiated
      by a promote release rule.
    repairAutomationRuns: Output only. The names of the AutomationRuns
      initiated by a repair rollout rule.
  """

  advanceAutomationRuns = _messages.StringField(1, repeated=True)
  promoteAutomationRun = _messages.StringField(2)
  repairAutomationRuns = _messages.StringField(3, repeated=True)


class AutomationRule(_messages.Message):
  r"""`AutomationRule` defines the automation activities.

  Fields:
    advanceRolloutRule: Optional. The `AdvanceRolloutRule` will automatically
      advance a successful Rollout.
    promoteReleaseRule: Optional. `PromoteReleaseRule` will automatically
      promote a release from the current target to a specified target.
    repairRolloutRule: Optional. The `RepairRolloutRule` will automatically
      repair a failed rollout.
    timedPromoteReleaseRule: Optional. The `TimedPromoteReleaseRule` will
      automatically promote a release from the current target(s) to the
      specified target(s) on a configured schedule.
  """

  advanceRolloutRule = _messages.MessageField('AdvanceRolloutRule', 1)
  promoteReleaseRule = _messages.MessageField('PromoteReleaseRule', 2)
  repairRolloutRule = _messages.MessageField('RepairRolloutRule', 3)
  timedPromoteReleaseRule = _messages.MessageField('TimedPromoteReleaseRule', 4)


class AutomationRuleCondition(_messages.Message):
  r"""`AutomationRuleCondition` contains conditions relevant to an
  `Automation` rule.

  Fields:
    targetsPresentCondition: Optional. Details around targets enumerated in
      the rule.
    timedPromoteReleaseCondition: Optional. TimedPromoteReleaseCondition
      contains rule conditions specific to a an Automation with a timed
      promote release rule defined.
  """

  targetsPresentCondition = _messages.MessageField('TargetsPresentCondition', 1)
  timedPromoteReleaseCondition = _messages.MessageField('TimedPromoteReleaseCondition', 2)


class AutomationRun(_messages.Message):
  r"""An `AutomationRun` resource in the Cloud Deploy API. An `AutomationRun`
  represents an execution instance of an automation rule.

  Enums:
    StateValueValuesEnum: Output only. Current state of the `AutomationRun`.

  Fields:
    advanceRolloutOperation: Output only. Advances a rollout to the next
      phase.
    automationId: Output only. The ID of the automation that initiated the
      operation.
    automationSnapshot: Output only. Snapshot of the Automation taken at
      AutomationRun creation time.
    createTime: Output only. Time at which the `AutomationRun` was created.
    etag: Output only. The weak etag of the `AutomationRun` resource. This
      checksum is computed by the server based on the value of other fields,
      and may be sent on update and delete requests to ensure the client has
      an up-to-date value before proceeding.
    expireTime: Output only. Time the `AutomationRun` expires. An
      `AutomationRun` expires after 14 days from its creation date.
    name: Output only. Name of the `AutomationRun`. Format is `projects/{proje
      ct}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automatio
      nRuns/{automation_run}`.
    policyViolation: Output only. Contains information about what policies
      prevented the `AutomationRun` from proceeding.
    promoteReleaseOperation: Output only. Promotes a release to a specified
      'Target'.
    repairRolloutOperation: Output only. Repairs a failed 'Rollout'.
    ruleId: Output only. The ID of the automation rule that initiated the
      operation.
    serviceAccount: Output only. Email address of the user-managed IAM service
      account that performs the operations against Cloud Deploy resources.
    state: Output only. Current state of the `AutomationRun`.
    stateDescription: Output only. Explains the current state of the
      `AutomationRun`. Present only when an explanation is needed.
    targetId: Output only. The ID of the source target that initiates the
      `AutomationRun`. The value of this field is the last segment of a target
      name.
    timedPromoteReleaseOperation: Output only. Promotes a release to a
      specified 'Target' as defined in a Timed Promote Release rule.
    updateTime: Output only. Time at which the automationRun was updated.
    waitUntilTime: Output only. Earliest time the `AutomationRun` will attempt
      to resume. Wait-time is configured by `wait` in automation rule.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the `AutomationRun`.

    Values:
      STATE_UNSPECIFIED: The `AutomationRun` has an unspecified state.
      SUCCEEDED: The `AutomationRun` has succeeded.
      CANCELLED: The `AutomationRun` was cancelled.
      FAILED: The `AutomationRun` has failed.
      IN_PROGRESS: The `AutomationRun` is in progress.
      PENDING: The `AutomationRun` is pending.
      ABORTED: The `AutomationRun` was aborted.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    CANCELLED = 2
    FAILED = 3
    IN_PROGRESS = 4
    PENDING = 5
    ABORTED = 6

  advanceRolloutOperation = _messages.MessageField('AdvanceRolloutOperation', 1)
  automationId = _messages.StringField(2)
  automationSnapshot = _messages.MessageField('Automation', 3)
  createTime = _messages.StringField(4)
  etag = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  name = _messages.StringField(7)
  policyViolation = _messages.MessageField('PolicyViolation', 8)
  promoteReleaseOperation = _messages.MessageField('PromoteReleaseOperation', 9)
  repairRolloutOperation = _messages.MessageField('RepairRolloutOperation', 10)
  ruleId = _messages.StringField(11)
  serviceAccount = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  stateDescription = _messages.StringField(14)
  targetId = _messages.StringField(15)
  timedPromoteReleaseOperation = _messages.MessageField('TimedPromoteReleaseOperation', 16)
  updateTime = _messages.StringField(17)
  waitUntilTime = _messages.StringField(18)


class AutomationRunEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/automation_run" Platform
  Log event that describes the AutomationRun related events.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    automationId: Identifier of the `Automation`.
    automationRun: The name of the `AutomationRun`.
    destinationTargetId: ID of the `Target` to which the `AutomationRun` is
      created.
    message: Debug message for when there is an update on the AutomationRun.
      Provides further details about the resource creation or state change.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    ruleId: Identifier of the `Automation` rule.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  automationId = _messages.StringField(1)
  automationRun = _messages.StringField(2)
  destinationTargetId = _messages.StringField(3)
  message = _messages.StringField(4)
  pipelineUid = _messages.StringField(5)
  ruleId = _messages.StringField(6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BuildArtifact(_messages.Message):
  r"""Description of an a image to use during Skaffold rendering.

  Fields:
    image: Optional. Image name in Skaffold configuration.
    tag: Optional. Image tag to use. This will generally be the full path to
      an image, such as "gcr.io/my-project/busybox:1.2.3" or "gcr.io/my-
      project/busybox@sha256:abc123".
  """

  image = _messages.StringField(1)
  tag = _messages.StringField(2)


class Canary(_messages.Message):
  r"""Canary represents the canary deployment strategy.

  Fields:
    canaryDeployment: Optional. Configures the progressive based deployment
      for a Target.
    customCanaryDeployment: Optional. Configures the progressive based
      deployment for a Target, but allows customizing at the phase level where
      a phase represents each of the percentage deployments.
    runtimeConfig: Optional. Runtime specific configurations for the
      deployment strategy. The runtime configuration is used to determine how
      Cloud Deploy will split traffic to enable a progressive deployment.
  """

  canaryDeployment = _messages.MessageField('CanaryDeployment', 1)
  customCanaryDeployment = _messages.MessageField('CustomCanaryDeployment', 2)
  runtimeConfig = _messages.MessageField('RuntimeConfig', 3)


class CanaryDeployment(_messages.Message):
  r"""CanaryDeployment represents the canary deployment configuration

  Fields:
    analysis: Optional. Configuration for the analysis job. If configured, the
      analysis will run after each percentage deployment.
    percentages: Required. The percentage based deployments that will occur as
      a part of a `Rollout`. List is expected in ascending order and each
      integer n is 0 <= n < 100. If the GatewayServiceMesh is configured for
      Kubernetes, then the range for n is 0 <= n <= 100.
    postdeploy: Optional. Configuration for the postdeploy job of the last
      phase. If this is not configured, there will be no postdeploy job for
      this phase.
    predeploy: Optional. Configuration for the predeploy job of the first
      phase. If this is not configured, there will be no predeploy job for
      this phase.
    verify: Optional. Whether to run verify tests after each percentage
      deployment via `skaffold verify`.
    verifyConfig: Optional. Configuration for the verify job. Cannot be set if
      `verify` is set to true.
  """

  analysis = _messages.MessageField('Analysis', 1)
  percentages = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.INT32)
  postdeploy = _messages.MessageField('Postdeploy', 3)
  predeploy = _messages.MessageField('Predeploy', 4)
  verify = _messages.BooleanField(5)
  verifyConfig = _messages.MessageField('Verify', 6)


class CancelAutomationRunRequest(_messages.Message):
  r"""The request object used by `CancelAutomationRun`."""


class CancelAutomationRunResponse(_messages.Message):
  r"""The response object from `CancelAutomationRun`."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CancelRolloutRequest(_messages.Message):
  r"""The request object used by `CancelRollout`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)


class CancelRolloutResponse(_messages.Message):
  r"""The response object from `CancelRollout`."""


class ChildRolloutJobs(_messages.Message):
  r"""ChildRollouts job composition

  Fields:
    advanceRolloutJobs: Output only. List of AdvanceChildRolloutJobs
    createRolloutJobs: Output only. List of CreateChildRolloutJobs
  """

  advanceRolloutJobs = _messages.MessageField('Job', 1, repeated=True)
  createRolloutJobs = _messages.MessageField('Job', 2, repeated=True)


class CloudRunConfig(_messages.Message):
  r"""CloudRunConfig contains the Cloud Run runtime configuration.

  Fields:
    automaticTrafficControl: Optional. Whether Cloud Deploy should update the
      traffic stanza in a Cloud Run Service on the user's behalf to facilitate
      traffic splitting. This is required to be true for CanaryDeployments,
      but optional for CustomCanaryDeployments.
    canaryRevisionTags: Optional. A list of tags that are added to the canary
      revision while the canary phase is in progress.
    priorRevisionTags: Optional. A list of tags that are added to the prior
      revision while the canary phase is in progress.
    stableRevisionTags: Optional. A list of tags that are added to the final
      stable revision when the stable phase is applied.
  """

  automaticTrafficControl = _messages.BooleanField(1)
  canaryRevisionTags = _messages.StringField(2, repeated=True)
  priorRevisionTags = _messages.StringField(3, repeated=True)
  stableRevisionTags = _messages.StringField(4, repeated=True)


class CloudRunLocation(_messages.Message):
  r"""Information specifying where to deploy a Cloud Run Service.

  Fields:
    location: Required. The location for the Cloud Run Service. Format must be
      `projects/{project}/locations/{location}`.
  """

  location = _messages.StringField(1)


class CloudRunMetadata(_messages.Message):
  r"""CloudRunMetadata contains information from a Cloud Run deployment.

  Fields:
    job: Output only. The name of the Cloud Run job that is associated with a
      `Rollout`. Format is
      `projects/{project}/locations/{location}/jobs/{job_name}`.
    previousRevision: Output only. The previous Cloud Run Revision name
      associated with a `Rollout`. Only set when a canary deployment strategy
      is configured. Format is projects/{project}/locations/{location}/service
      s/{service}/revisions/{revision}.
    revision: Output only. The Cloud Run Revision id associated with a
      `Rollout`.
    service: Output only. The name of the Cloud Run Service that is associated
      with a `Rollout`. Format is
      `projects/{project}/locations/{location}/services/{service}`.
    serviceUrls: Output only. The Cloud Run Service urls that are associated
      with a `Rollout`.
  """

  job = _messages.StringField(1)
  previousRevision = _messages.StringField(2)
  revision = _messages.StringField(3)
  service = _messages.StringField(4)
  serviceUrls = _messages.StringField(5, repeated=True)


class CloudRunRenderMetadata(_messages.Message):
  r"""CloudRunRenderMetadata contains Cloud Run information associated with a
  `Release` render.

  Fields:
    job: Output only. The name of the Cloud Run Job in the rendered manifest.
      Format is `projects/{project}/locations/{location}/jobs/{job}`.
    revision: Output only. The name of the Cloud Run Revision in the rendered
      manifest. Format is `projects/{project}/locations/{location}/services/{s
      ervice}/revisions/{revision}`.
    service: Output only. The name of the Cloud Run Service in the rendered
      manifest. Format is
      `projects/{project}/locations/{location}/services/{service}`.
  """

  job = _messages.StringField(1)
  revision = _messages.StringField(2)
  service = _messages.StringField(3)


class CloudServiceMesh(_messages.Message):
  r"""Information about the Cloud Service Mesh configuration.

  Fields:
    deployment: Required. Name of the Kubernetes Deployment whose traffic is
      managed by the specified Service.
    grpcRoute: Required. Name of the GrpcRoute resource that defines how gRPC
      traffic routed by a Mesh or Gateway resource is routed. Format is
      `projects/{project}/locations/global/httpRoutes/{grpc_route}`
    httpRoute: Required. Name of the HttpRoute resource that defines how HTTP
      traffic should be routed by a Mesh or Gateway resource. Format is
      `projects/{project}/locations/global/httpRoutes/{http_route}`.
    podSelectorLabel: Optional. The label to use when selecting Pods for the
      Deployment and Service resources. This label must already be present in
      both resources.
    routeUpdateWaitTime: Optional. The time to wait for route updates to
      propagate. The maximum configurable time is 3 hours, in seconds format.
      If unspecified, there is no wait time.
    service: Required. Name of the Kubernetes Service.
  """

  deployment = _messages.StringField(1)
  grpcRoute = _messages.StringField(2)
  httpRoute = _messages.StringField(3)
  podSelectorLabel = _messages.StringField(4)
  routeUpdateWaitTime = _messages.StringField(5)
  service = _messages.StringField(6)


class ClouddeployProjectsLocationsCustomTargetTypesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesCreateRequest object.

  Fields:
    customTargetType: A CustomTargetType resource to be passed as the request
      body.
    customTargetTypeId: Required. ID of the `CustomTargetType`.
    parent: Required. The parent collection in which the `CustomTargetType`
      must be created. The format is
      `projects/{project_id}/locations/{location_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  customTargetType = _messages.MessageField('CustomTargetType', 1)
  customTargetTypeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `CustomTargetType` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `CustomTargetType` to delete. Format must
      be `projects/{project_id}/locations/{location_name}/customTargetTypes/{c
      ustom_target_type}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated but no
      actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsCustomTargetTypesGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsCustomTargetTypesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesGetRequest object.

  Fields:
    name: Required. Name of the `CustomTargetType`. Format must be `projects/{
      project_id}/locations/{location_name}/customTargetTypes/{custom_target_t
      ype}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsCustomTargetTypesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesListRequest object.

  Fields:
    filter: Optional. Filter custom target types to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `CustomTargetType` objects to
      return. The service may return fewer than this value. If unspecified, at
      most 50 `CustomTargetType` objects will be returned. The maximum value
      is 1000; values above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListCustomTargetTypes` call. Provide this to retrieve the subsequent
      page. When paginating, all other provided parameters match the call that
      provided the page token.
    parent: Required. The parent that owns this collection of custom target
      types. Format must be `projects/{project_id}/locations/{location_name}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsCustomTargetTypesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `CustomTargetType` that
      does not exist will result in the creation of a new `CustomTargetType`.
    customTargetType: A CustomTargetType resource to be passed as the request
      body.
    name: Identifier. Name of the `CustomTargetType`. Format is `projects/{pro
      ject}/locations/{location}/customTargetTypes/{customTargetType}`. The
      `customTargetType` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the `CustomTargetType` resource. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it's in the mask. If the user
      doesn't provide a mask then all fields are overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  customTargetType = _messages.MessageField('CustomTargetType', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsCustomTargetTypesSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest
  object.

  Fields:
    cancelAutomationRunRequest: A CancelAutomationRunRequest resource to be
      passed as the request body.
    name: Required. Name of the `AutomationRun`. Format is `projects/{project}
      /locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRu
      ns/{automation_run}`.
  """

  cancelAutomationRunRequest = _messages.MessageField('CancelAutomationRunRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest
  object.

  Fields:
    name: Required. Name of the `AutomationRun`. Format must be `projects/{pro
      ject}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automat
      ionRuns/{automation_run}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest
  object.

  Fields:
    filter: Filter automationRuns to be returned. All fields can be used in
      the filter.
    orderBy: Field to sort by.
    pageSize: The maximum number of automationRuns to return. The service may
      return fewer than this value. If unspecified, at most 50 automationRuns
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListAutomationRuns`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent `Delivery Pipeline`, which owns this
      collection of automationRuns. Format must be `projects/{project}/locatio
      ns/{location}/deliveryPipelines/{delivery_pipeline}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest
  object.

  Fields:
    automation: A Automation resource to be passed as the request body.
    automationId: Required. ID of the `Automation`.
    parent: Required. The parent collection in which the `Automation` must be
      created. The format is `projects/{project_id}/locations/{location_name}/
      deliveryPipelines/{pipeline_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  automation = _messages.MessageField('Automation', 1)
  automationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest
  object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `Automation` will succeed.
    etag: Optional. The weak etag of the request. This checksum is computed by
      the server based on the value of other fields, and may be sent on update
      and delete requests to ensure the client has an up-to-date value before
      proceeding.
    name: Required. The name of the `Automation` to delete. The format is `pro
      jects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline
      _name}/automations/{automation_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and verify whether
      the resource exists, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest
  object.

  Fields:
    name: Required. Name of the `Automation`. Format must be `projects/{projec
      t_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automa
      tions/{automation_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest
  object.

  Fields:
    filter: Filter automations to be returned. All fields can be used in the
      filter.
    orderBy: Field to sort by.
    pageSize: The maximum number of automations to return. The service may
      return fewer than this value. If unspecified, at most 50 automations
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListAutomations` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      provided parameters match the call that provided the page token.
    parent: Required. The parent `Delivery Pipeline`, which owns this
      collection of automations. Format must be `projects/{project_id}/locatio
      ns/{location_name}/deliveryPipelines/{pipeline_name}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest
  object.

  Fields:
    allowMissing: Optional. If set to true, updating a `Automation` that does
      not exist will result in the creation of a new `Automation`.
    automation: A Automation resource to be passed as the request body.
    name: Output only. Name of the `Automation`. Format is `projects/{project}
      /locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/
      {automation}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the `Automation` resource. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it's in the mask. If the user
      doesn't provide a mask then all fields are overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  automation = _messages.MessageField('Automation', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest object.

  Fields:
    deliveryPipeline: A DeliveryPipeline resource to be passed as the request
      body.
    deliveryPipelineId: Required. ID of the `DeliveryPipeline`.
    parent: Required. The parent collection in which the `DeliveryPipeline`
      must be created. The format is
      `projects/{project_id}/locations/{location_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  deliveryPipeline = _messages.MessageField('DeliveryPipeline', 1)
  deliveryPipelineId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `DeliveryPipeline` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    force: Optional. If set to true, all child resources under this pipeline
      will also be deleted. Otherwise, the request will only work if the
      pipeline has no child resources.
    name: Required. The name of the `DeliveryPipeline` to delete. The format
      is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{p
      ipeline_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesGetRequest object.

  Fields:
    name: Required. Name of the `DeliveryPipeline`. Format must be `projects/{
      project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`
      .
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesListRequest object.

  Fields:
    filter: Filter pipelines to be returned. See https://google.aip.dev/160
      for more details.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: The maximum number of pipelines to return. The service may
      return fewer than this value. If unspecified, at most 50 pipelines will
      be returned. The maximum value is 1000; values above 1000 will be set to
      1000.
    pageToken: A page token, received from a previous `ListDeliveryPipelines`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of pipelines.
      Format must be `projects/{project_id}/locations/{location_name}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `DeliveryPipeline` that
      does not exist will result in the creation of a new `DeliveryPipeline`.
    deliveryPipeline: A DeliveryPipeline resource to be passed as the request
      body.
    name: Identifier. Name of the `DeliveryPipeline`. Format is `projects/{pro
      ject}/locations/{location}/deliveryPipelines/{deliveryPipeline}`. The
      `deliveryPipeline` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the `DeliveryPipeline` resource. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it's in the mask. If the user
      doesn't provide a mask then all fields are overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  deliveryPipeline = _messages.MessageField('DeliveryPipeline', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest
  object.

  Fields:
    abandonReleaseRequest: A AbandonReleaseRequest resource to be passed as
      the request body.
    name: Required. Name of the Release. Format is `projects/{project}/locatio
      ns/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}`.
  """

  abandonReleaseRequest = _messages.MessageField('AbandonReleaseRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest
  object.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
    parent: Required. The parent collection in which the `Release` is created.
      The format is `projects/{project_id}/locations/{location_name}/deliveryP
      ipelines/{pipeline_name}`.
    release: A Release resource to be passed as the request body.
    releaseId: Required. ID of the `Release`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  parent = _messages.StringField(2, required=True)
  release = _messages.MessageField('Release', 3)
  releaseId = _messages.StringField(4)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest
  object.

  Fields:
    name: Required. Name of the `Release`. Format must be `projects/{project_i
      d}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/
      {release_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest
  object.

  Fields:
    filter: Optional. Filter releases to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Release` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Release` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListReleases`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `DeliveryPipeline` which owns this collection of
      `Release` objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest
  object.

  Fields:
    advanceRolloutRequest: A AdvanceRolloutRequest resource to be passed as
      the request body.
    name: Required. Name of the Rollout. Format is `projects/{project}/locatio
      ns/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/ro
      llouts/{rollout}`.
  """

  advanceRolloutRequest = _messages.MessageField('AdvanceRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest
  object.

  Fields:
    approveRolloutRequest: A ApproveRolloutRequest resource to be passed as
      the request body.
    name: Required. Name of the Rollout. Format is `projects/{project}/locatio
      ns/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/ro
      llouts/{rollout}`.
  """

  approveRolloutRequest = _messages.MessageField('ApproveRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest
  object.

  Fields:
    cancelRolloutRequest: A CancelRolloutRequest resource to be passed as the
      request body.
    name: Required. Name of the Rollout. Format is `projects/{project}/locatio
      ns/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/ro
      llouts/{rollout}`.
  """

  cancelRolloutRequest = _messages.MessageField('CancelRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest
  object.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
    parent: Required. The parent collection in which the `Rollout` must be
      created. The format is `projects/{project_id}/locations/{location_name}/
      deliveryPipelines/{pipeline_name}/releases/{release_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rollout: A Rollout resource to be passed as the request body.
    rolloutId: Required. ID of the `Rollout`.
    startingPhaseId: Optional. The starting phase ID for the `Rollout`. If
      empty the `Rollout` will start at the first phase.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  rollout = _messages.MessageField('Rollout', 4)
  rolloutId = _messages.StringField(5)
  startingPhaseId = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest
  object.

  Fields:
    name: Required. Name of the `Rollout`. Format must be `projects/{project_i
      d}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/
      {release_name}/rollouts/{rollout_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJobRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJob
  Request object.

  Fields:
    ignoreJobRequest: A IgnoreJobRequest resource to be passed as the request
      body.
    rollout: Required. Name of the Rollout. Format is `projects/{project}/loca
      tions/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}
      /rollouts/{rollout}`.
  """

  ignoreJobRequest = _messages.MessageField('IgnoreJobRequest', 1)
  rollout = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGe
  tRequest object.

  Fields:
    name: Required. Name of the `JobRun`. Format must be `projects/{project_id
      }/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{
      release_name}/rollouts/{rollout_name}/jobRuns/{job_run_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsLi
  stRequest object.

  Fields:
    filter: Optional. Filter results to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `JobRun` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `JobRun` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListJobRuns`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `Rollout` which owns this collection of `JobRun`
      objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTerminateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTe
  rminateRequest object.

  Fields:
    name: Required. Name of the `JobRun`. Format must be `projects/{project}/l
      ocations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{relea
      se}/rollouts/{rollout}/jobRuns/{jobRun}`.
    terminateJobRunRequest: A TerminateJobRunRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  terminateJobRunRequest = _messages.MessageField('TerminateJobRunRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest
  object.

  Fields:
    filter: Optional. Filter rollouts to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Rollout` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Rollout` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListRollouts`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `Release` which owns this collection of `Rollout`
      objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest
  object.

  Fields:
    retryJobRequest: A RetryJobRequest resource to be passed as the request
      body.
    rollout: Required. Name of the Rollout. Format is `projects/{project}/loca
      tions/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}
      /rollouts/{rollout}`.
  """

  retryJobRequest = _messages.MessageField('RetryJobRequest', 1)
  rollout = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest
  object.

  Fields:
    name: Required. The `DeliveryPipeline` for which the rollback `Rollout`
      must be created. The format is `projects/{project_id}/locations/{locatio
      n_name}/deliveryPipelines/{pipeline_name}`.
    rollbackTargetRequest: A RollbackTargetRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackTargetRequest = _messages.MessageField('RollbackTargetRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ClouddeployProjectsLocationsDeployPoliciesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesCreateRequest object.

  Fields:
    deployPolicy: A DeployPolicy resource to be passed as the request body.
    deployPolicyId: Required. ID of the `DeployPolicy`.
    parent: Required. The parent collection in which the `DeployPolicy` must
      be created. The format is
      `projects/{project_id}/locations/{location_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  deployPolicy = _messages.MessageField('DeployPolicy', 1)
  deployPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeployPoliciesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `DeployPolicy` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `DeployPolicy` to delete. The format is `p
      rojects/{project_id}/locations/{location_name}/deployPolicies/{deploy_po
      licy_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeployPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeployPoliciesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesGetRequest object.

  Fields:
    name: Required. Name of the `DeployPolicy`. Format must be `projects/{proj
      ect_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeployPoliciesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesListRequest object.

  Fields:
    filter: Filter deploy policies to be returned. See
      https://google.aip.dev/160 for more details. All fields can be used in
      the filter.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: The maximum number of deploy policies to return. The service may
      return fewer than this value. If unspecified, at most 50 deploy policies
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListDeployPolicies`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of deploy
      policies. Format must be
      `projects/{project_id}/locations/{location_name}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeployPoliciesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `DeployPolicy` that
      does not exist will result in the creation of a new `DeployPolicy`.
    deployPolicy: A DeployPolicy resource to be passed as the request body.
    name: Output only. Name of the `DeployPolicy`. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
      The `deployPolicy` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the `DeployPolicy` resource. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it's in the mask. If the user
      doesn't provide a mask then all fields are overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  deployPolicy = _messages.MessageField('DeployPolicy', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeployPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsGetConfigRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsGetConfigRequest object.

  Fields:
    name: Required. Name of requested configuration.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ClouddeployProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ClouddeployProjectsLocationsTargetsCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsCreateRequest object.

  Fields:
    parent: Required. The parent collection in which the `Target` must be
      created. The format is
      `projects/{project_id}/locations/{location_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    target: A Target resource to be passed as the request body.
    targetId: Required. ID of the `Target`.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  target = _messages.MessageField('Target', 3)
  targetId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsTargetsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `Target` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `Target` to delete. The format is
      `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsTargetsGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsTargetsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsGetRequest object.

  Fields:
    name: Required. Name of the `Target`. Format must be
      `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsTargetsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsListRequest object.

  Fields:
    filter: Optional. Filter targets to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Target` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Target` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListTargets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of targets.
      Format must be `projects/{project_id}/locations/{location_name}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsTargetsPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `Target` that does not
      exist will result in the creation of a new `Target`.
    name: Identifier. Name of the `Target`. Format is
      `projects/{project}/locations/{location}/targets/{target}`. The `target`
      component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    target: A Target resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the `Target` resource. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it's in the mask. If the user doesn't
      provide a mask then all fields are overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  target = _messages.MessageField('Target', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsTargetsSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class Config(_messages.Message):
  r"""Service-wide configuration.

  Fields:
    defaultSkaffoldVersion: Default Skaffold version that is assigned when a
      Release is created without specifying a Skaffold version.
    name: Name of the configuration.
    supportedVersions: All supported versions of Skaffold.
  """

  defaultSkaffoldVersion = _messages.StringField(1)
  name = _messages.StringField(2)
  supportedVersions = _messages.MessageField('SkaffoldVersion', 3, repeated=True)


class ConfigTask(_messages.Message):
  r"""ConfigTask represents either a task in the Deploy Config or a custom
  action in the Skaffold Config.

  Messages:
    EnvValue: Optional. Environment variables that are passed into the
      containers defined for either the task in the Deploy Config or the
      custom action in the Skaffold Config.

  Fields:
    env: Optional. Environment variables that are passed into the containers
      defined for either the task in the Deploy Config or the custom action in
      the Skaffold Config.
    id: Required. The name of the task in the Deploy Config or the name of the
      custom action in the Skaffold Config.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvValue(_messages.Message):
    r"""Optional. Environment variables that are passed into the containers
    defined for either the task in the Deploy Config or the custom action in
    the Skaffold Config.

    Messages:
      AdditionalProperty: An additional property for a EnvValue object.

    Fields:
      additionalProperties: Additional properties of type EnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  env = _messages.MessageField('EnvValue', 1)
  id = _messages.StringField(2)


class Container(_messages.Message):
  r"""Container definition for the containers task.

  Messages:
    EnvValue: Optional. Environment variables that are set in the container.

  Fields:
    args: Optional. Args is the container arguments to use. This overrides the
      default arguments defined in the container image.
    command: Optional. Command is the container entrypoint to use. This
      overrides the default entrypoint defined in thhe container image.
    env: Optional. Environment variables that are set in the container.
    image: Required. Image is the container image to use.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvValue(_messages.Message):
    r"""Optional. Environment variables that are set in the container.

    Messages:
      AdditionalProperty: An additional property for a EnvValue object.

    Fields:
      additionalProperties: Additional properties of type EnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('EnvValue', 3)
  image = _messages.StringField(4)


class ContainersTask(_messages.Message):
  r"""This task is represented by a set of containers that are executed in
  parallel in the Cloud Build execution environment.

  Fields:
    containers: Required. Set of containers that are executed in parallel.
  """

  containers = _messages.MessageField('Container', 1, repeated=True)


class CreateChildRolloutJob(_messages.Message):
  r"""A createChildRollout Job."""


class CreateChildRolloutJobRun(_messages.Message):
  r"""CreateChildRolloutJobRun contains information specific to a
  createChildRollout `JobRun`.

  Fields:
    rollout: Output only. Name of the `ChildRollout`. Format is `projects/{pro
      ject}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases
      /{release}/rollouts/{rollout}`.
    rolloutPhaseId: Output only. The ID of the childRollout Phase initiated by
      this JobRun.
  """

  rollout = _messages.StringField(1)
  rolloutPhaseId = _messages.StringField(2)


class CustomCanaryDeployment(_messages.Message):
  r"""CustomCanaryDeployment represents the custom canary deployment
  configuration.

  Fields:
    phaseConfigs: Required. Configuration for each phase in the canary
      deployment in the order executed.
  """

  phaseConfigs = _messages.MessageField('PhaseConfig', 1, repeated=True)


class CustomCheck(_messages.Message):
  r"""CustomCheck configures a third-party metric provider to run the
  analysis, via a Task that runs at a specified frequency.

  Fields:
    frequency: Optional. The frequency at which the custom check will be run,
      with a minimum and default of 5 minutes.
    id: Required. The ID of the custom Analysis check.
    task: Required. The Task to be run for this custom check.
  """

  frequency = _messages.StringField(1)
  id = _messages.StringField(2)
  task = _messages.MessageField('Task', 3)


class CustomCheckStatus(_messages.Message):
  r"""CustomCheckStatus contains information specific to a single iteration of
  a custom analysis job.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the analysis failed.
      This will always be unspecified while the analysis is in progress or if
      it succeeded.

  Fields:
    failureCause: Output only. The reason the analysis failed. This will
      always be unspecified while the analysis is in progress or if it
      succeeded.
    failureMessage: Output only. Additional information about the analysis
      failure, if available.
    frequency: Output only. The frequency in minutes at which the custom check
      is run.
    id: Output only. The ID of the custom check.
    latestBuild: Output only. The resource name of the Cloud Build `Build`
      object that was used to execute the latest run of this custom action
      check. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    metadata: Output only. Custom metadata provided by the user-defined custom
      check operation. result.
    task: Output only. The task that ran for this custom check.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the analysis failed. This will always be
    unspecified while the analysis is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The analysis operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The analysis job run did not complete within the
        alloted time defined in the target's execution environment
        configuration.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    CLOUD_BUILD_REQUEST_FAILED = 4

  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 1)
  failureMessage = _messages.StringField(2)
  frequency = _messages.StringField(3)
  id = _messages.StringField(4)
  latestBuild = _messages.StringField(5)
  metadata = _messages.MessageField('CustomMetadata', 6)
  task = _messages.MessageField('Task', 7)


class CustomMetadata(_messages.Message):
  r"""CustomMetadata contains information from a user-defined operation.

  Messages:
    ValuesValue: Output only. Key-value pairs provided by the user-defined
      operation.

  Fields:
    values: Output only. Key-value pairs provided by the user-defined
      operation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Output only. Key-value pairs provided by the user-defined operation.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  values = _messages.MessageField('ValuesValue', 1)


class CustomTarget(_messages.Message):
  r"""Information specifying a Custom Target.

  Fields:
    customTargetType: Required. The name of the CustomTargetType. Format must
      be `projects/{project}/locations/{location}/customTargetTypes/{custom_ta
      rget_type}`.
  """

  customTargetType = _messages.StringField(1)


class CustomTargetDeployMetadata(_messages.Message):
  r"""CustomTargetDeployMetadata contains information from a Custom Target
  deploy operation.

  Fields:
    skipMessage: Output only. Skip message provided in the results of a custom
      deploy operation.
  """

  skipMessage = _messages.StringField(1)


class CustomTargetSkaffoldActions(_messages.Message):
  r"""CustomTargetSkaffoldActions represents the `CustomTargetType`
  configuration using Skaffold custom actions.

  Fields:
    deployAction: Required. The Skaffold custom action responsible for deploy
      operations.
    includeSkaffoldModules: Optional. List of Skaffold modules Cloud Deploy
      will include in the Skaffold Config as required before performing
      diagnose.
    renderAction: Optional. The Skaffold custom action responsible for render
      operations. If not provided then Cloud Deploy will perform the render
      operations via `skaffold render`.
  """

  deployAction = _messages.StringField(1)
  includeSkaffoldModules = _messages.MessageField('SkaffoldModules', 2, repeated=True)
  renderAction = _messages.StringField(3)


class CustomTargetType(_messages.Message):
  r"""A `CustomTargetType` resource in the Cloud Deploy API. A
  `CustomTargetType` defines a type of custom target that can be referenced in
  a `Target` in order to facilitate deploying to other systems besides the
  supported runtimes.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    createTime: Output only. Time at which the `CustomTargetType` was created.
    customActions: Optional. Configures render and deploy for the
      `CustomTargetType` using Skaffold custom actions.
    customTargetTypeId: Output only. Resource id of the `CustomTargetType`.
    description: Optional. Description of the `CustomTargetType`. Max length
      is 255 characters.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.
    name: Identifier. Name of the `CustomTargetType`. Format is `projects/{pro
      ject}/locations/{location}/customTargetTypes/{customTargetType}`. The
      `customTargetType` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    uid: Output only. Unique identifier of the `CustomTargetType`.
    updateTime: Output only. Most recent time at which the `CustomTargetType`
      was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  customActions = _messages.MessageField('CustomTargetSkaffoldActions', 3)
  customTargetTypeId = _messages.StringField(4)
  description = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class CustomTargetTypeNotificationEvent(_messages.Message):
  r"""Payload proto for
  "clouddeploy.googleapis.com/customtargettype_notification" Platform Log
  event that describes the failure to send a custom target type status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    customTargetType: The name of the `CustomTargetType`.
    customTargetTypeUid: Unique identifier of the `CustomTargetType`.
    message: Debug message for when a notification fails to send.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  customTargetType = _messages.StringField(1)
  customTargetTypeUid = _messages.StringField(2)
  message = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DefaultPool(_messages.Message):
  r"""Execution using the default Cloud Build pool.

  Fields:
    artifactStorage: Optional. Cloud Storage location where execution outputs
      should be stored. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) will be used.
  """

  artifactStorage = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class DeliveryPipeline(_messages.Message):
  r"""A `DeliveryPipeline` resource in the Cloud Deploy API. A
  `DeliveryPipeline` defines a pipeline through which a Skaffold configuration
  can progress.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy.
    condition: Output only. Information around the state of the Delivery
      Pipeline.
    createTime: Output only. Time at which the pipeline was created.
    description: Optional. Description of the `DeliveryPipeline`. Max length
      is 255 characters.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Identifier. Name of the `DeliveryPipeline`. Format is `projects/{pro
      ject}/locations/{location}/deliveryPipelines/{deliveryPipeline}`. The
      `deliveryPipeline` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    serialPipeline: Optional. SerialPipeline defines a sequential set of
      stages for a `DeliveryPipeline`.
    suspended: Optional. When suspended, no new releases or rollouts can be
      created, but in-progress ones will complete.
    uid: Output only. Unique identifier of the `DeliveryPipeline`.
    updateTime: Output only. Most recent time at which the pipeline was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('PipelineCondition', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  serialPipeline = _messages.MessageField('SerialPipeline', 8)
  suspended = _messages.BooleanField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class DeliveryPipelineAttribute(_messages.Message):
  r"""Contains criteria for selecting DeliveryPipelines.

  Messages:
    LabelsValue: DeliveryPipeline labels.

  Fields:
    id: Optional. ID of the `DeliveryPipeline`. The value of this field could
      be one of the following: * The last segment of a pipeline name * "*",
      all delivery pipelines in a location
    labels: DeliveryPipeline labels.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""DeliveryPipeline labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)


class DeliveryPipelineNotificationEvent(_messages.Message):
  r"""Payload proto for
  "clouddeploy.googleapis.com/deliverypipeline_notification" Platform Log
  event that describes the failure to send delivery pipeline status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    deliveryPipeline: The name of the `Delivery Pipeline`.
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  deliveryPipeline = _messages.StringField(1)
  message = _messages.StringField(2)
  pipelineUid = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class DeployArtifact(_messages.Message):
  r"""The artifacts produced by a deploy operation.

  Fields:
    artifactUri: Output only. URI of a directory containing the artifacts. All
      paths are relative to this location.
    manifestPaths: Output only. File paths of the manifests applied during the
      deploy operation relative to the URI.
  """

  artifactUri = _messages.StringField(1)
  manifestPaths = _messages.StringField(2, repeated=True)


class DeployJob(_messages.Message):
  r"""A deploy Job."""


class DeployJobRun(_messages.Message):
  r"""DeployJobRun contains information specific to a deploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the deploy failed.
      This will always be unspecified while the deploy is in progress or if it
      succeeded.

  Fields:
    artifact: Output only. The artifact of a deploy job run, if available.
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to deploy. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    failureCause: Output only. The reason the deploy failed. This will always
      be unspecified while the deploy is in progress or if it succeeded.
    failureMessage: Output only. Additional information about the deploy
      failure, if available.
    metadata: Output only. Metadata containing information about the deploy
      job run.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the deploy failed. This will always be
    unspecified while the deploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [Required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The deploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The deploy job run did not complete within the
        allotted time.
      MISSING_RESOURCES_FOR_CANARY: There were missing resources in the
        runtime environment required for a canary deployment. Check the Cloud
        Build logs for more information.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
      DEPLOY_FEATURE_NOT_SUPPORTED: The deploy operation had a feature
        configured that is not supported.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    MISSING_RESOURCES_FOR_CANARY = 4
    CLOUD_BUILD_REQUEST_FAILED = 5
    DEPLOY_FEATURE_NOT_SUPPORTED = 6

  artifact = _messages.MessageField('DeployArtifact', 1)
  build = _messages.StringField(2)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 3)
  failureMessage = _messages.StringField(4)
  metadata = _messages.MessageField('DeployJobRunMetadata', 5)


class DeployJobRunMetadata(_messages.Message):
  r"""DeployJobRunMetadata surfaces information associated with a
  `DeployJobRun` to the user.

  Fields:
    cloudRun: Output only. The name of the Cloud Run Service that is
      associated with a `DeployJobRun`.
    custom: Output only. Custom metadata provided by user-defined deploy
      operation.
    customTarget: Output only. Custom Target metadata associated with a
      `DeployJobRun`.
  """

  cloudRun = _messages.MessageField('CloudRunMetadata', 1)
  custom = _messages.MessageField('CustomMetadata', 2)
  customTarget = _messages.MessageField('CustomTargetDeployMetadata', 3)


class DeployParameters(_messages.Message):
  r"""DeployParameters contains deploy parameters information.

  Messages:
    MatchTargetLabelsValue: Optional. Deploy parameters are applied to targets
      with match labels. If unspecified, deploy parameters are applied to all
      targets (including child targets of a multi-target).
    ValuesValue: Required. Values are deploy parameters in key-value pairs.

  Fields:
    matchTargetLabels: Optional. Deploy parameters are applied to targets with
      match labels. If unspecified, deploy parameters are applied to all
      targets (including child targets of a multi-target).
    values: Required. Values are deploy parameters in key-value pairs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MatchTargetLabelsValue(_messages.Message):
    r"""Optional. Deploy parameters are applied to targets with match labels.
    If unspecified, deploy parameters are applied to all targets (including
    child targets of a multi-target).

    Messages:
      AdditionalProperty: An additional property for a MatchTargetLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MatchTargetLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MatchTargetLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Required. Values are deploy parameters in key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  matchTargetLabels = _messages.MessageField('MatchTargetLabelsValue', 1)
  values = _messages.MessageField('ValuesValue', 2)


class DeployPolicy(_messages.Message):
  r"""A `DeployPolicy` resource in the Cloud Deploy API. A `DeployPolicy`
  inhibits manual or automation-driven actions within a Delivery Pipeline or
  Target.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. Annotations must meet
      the following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (`/`). * The name segment is required and must be
      63 characters or less, beginning and ending with an alphanumeric
      character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots
      (`.`), and alphanumerics between. * The prefix is optional. If
      specified, the prefix must be a DNS subdomain: a series of DNS labels
      separated by dots(`.`), not longer than 253 characters in total,
      followed by a slash (`/`). See
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. Annotations must meet the
      following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (`/`). * The name segment is required and must be
      63 characters or less, beginning and ending with an alphanumeric
      character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots
      (`.`), and alphanumerics between. * The prefix is optional. If
      specified, the prefix must be a DNS subdomain: a series of DNS labels
      separated by dots(`.`), not longer than 253 characters in total,
      followed by a slash (`/`). See
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    createTime: Output only. Time at which the deploy policy was created.
    description: Optional. Description of the `DeployPolicy`. Max length is
      255 characters.
    etag: The weak etag of the `DeployPolicy` resource. This checksum is
      computed by the server based on the value of other fields, and may be
      sent on update and delete requests to ensure the client has an up-to-
      date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Output only. Name of the `DeployPolicy`. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
      The `deployPolicy` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    rules: Required. Rules to apply. At least one rule must be present.
    selectors: Required. Selected resources to which the policy will be
      applied. At least one selector is required. If one selector matches the
      resource the policy applies. For example, if there are two selectors and
      the action being attempted matches one of them, the policy will apply to
      that action.
    suspended: Optional. When suspended, the policy will not prevent actions
      from occurring, even if the action violates the policy.
    uid: Output only. Unique identifier of the `DeployPolicy`.
    updateTime: Output only. Most recent time at which the deploy policy was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. Annotations must meet the following
    constraints: * Annotations are key/value pairs. * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash
    (`/`). * The name segment is required and must be 63 characters or less,
    beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with
    dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between. *
    The prefix is optional. If specified, the prefix must be a DNS subdomain:
    a series of DNS labels separated by dots(`.`), not longer than 253
    characters in total, followed by a slash (`/`). See
    https://kubernetes.io/docs/concepts/overview/working-with-
    objects/annotations/#syntax-and-character-set for more details.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  rules = _messages.MessageField('PolicyRule', 7, repeated=True)
  selectors = _messages.MessageField('DeployPolicyResourceSelector', 8, repeated=True)
  suspended = _messages.BooleanField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class DeployPolicyEvaluationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/deploypolicy_evaluation"
  Platform Log event that describes the deploy policy evaluation event.

  Enums:
    InvokerValueValuesEnum: What invoked the action (e.g. a user or
      automation).
    OverridesValueListEntryValuesEnum:
    VerdictValueValuesEnum: The policy verdict of the request.

  Fields:
    allowed: Whether the request is allowed. Allowed is set as true if: (1)
      the request complies with the policy; or (2) the request doesn't comply
      with the policy but the policy was overridden; or (3) the request
      doesn't comply with the policy but the policy was suspended
    deliveryPipeline: The name of the `Delivery Pipeline`.
    deployPolicy: The name of the `DeployPolicy`.
    deployPolicyUid: Unique identifier of the `DeployPolicy`.
    invoker: What invoked the action (e.g. a user or automation).
    message: Debug message for when a deploy policy event occurs.
    overrides: Things that could have overridden the policy verdict. Overrides
      together with verdict decide whether the request is allowed.
    pipelineUid: Unique identifier of the `Delivery Pipeline`.
    rule: Rule id.
    ruleType: Rule type (e.g. Restrict Rollouts).
    target: The name of the `Target`. This is an optional field, as a `Target`
      may not always be applicable to a policy.
    targetUid: Unique identifier of the `Target`. This is an optional field,
      as a `Target` may not always be applicable to a policy.
    verdict: The policy verdict of the request.
  """

  class InvokerValueValuesEnum(_messages.Enum):
    r"""What invoked the action (e.g. a user or automation).

    Values:
      INVOKER_UNSPECIFIED: Unspecified.
      USER: The action is user-driven. For example, creating a rollout
        manually via a gcloud create command.
      DEPLOY_AUTOMATION: Automated action by Cloud Deploy.
    """
    INVOKER_UNSPECIFIED = 0
    USER = 1
    DEPLOY_AUTOMATION = 2

  class OverridesValueListEntryValuesEnum(_messages.Enum):
    r"""OverridesValueListEntryValuesEnum enum type.

    Values:
      POLICY_VERDICT_OVERRIDE_UNSPECIFIED: This should never happen.
      POLICY_OVERRIDDEN: The policy was overridden.
      POLICY_SUSPENDED: The policy was suspended.
    """
    POLICY_VERDICT_OVERRIDE_UNSPECIFIED = 0
    POLICY_OVERRIDDEN = 1
    POLICY_SUSPENDED = 2

  class VerdictValueValuesEnum(_messages.Enum):
    r"""The policy verdict of the request.

    Values:
      POLICY_VERDICT_UNSPECIFIED: This should never happen.
      ALLOWED_BY_POLICY: Allowed by policy. This enum value is not currently
        used but may be used in the future. Currently logs are only generated
        when a request is denied by policy.
      DENIED_BY_POLICY: Denied by policy.
    """
    POLICY_VERDICT_UNSPECIFIED = 0
    ALLOWED_BY_POLICY = 1
    DENIED_BY_POLICY = 2

  allowed = _messages.BooleanField(1)
  deliveryPipeline = _messages.StringField(2)
  deployPolicy = _messages.StringField(3)
  deployPolicyUid = _messages.StringField(4)
  invoker = _messages.EnumField('InvokerValueValuesEnum', 5)
  message = _messages.StringField(6)
  overrides = _messages.EnumField('OverridesValueListEntryValuesEnum', 7, repeated=True)
  pipelineUid = _messages.StringField(8)
  rule = _messages.StringField(9)
  ruleType = _messages.StringField(10)
  target = _messages.StringField(11)
  targetUid = _messages.StringField(12)
  verdict = _messages.EnumField('VerdictValueValuesEnum', 13)


class DeployPolicyNotificationEvent(_messages.Message):
  r"""Payload proto for
  "clouddeploy.googleapis.com/deploypolicy_notification". Platform Log event
  that describes the failure to send a pub/sub notification when there is a
  DeployPolicy status change.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    deployPolicy: The name of the `DeployPolicy`.
    deployPolicyUid: Unique identifier of the deploy policy.
    message: Debug message for when a deploy policy fails to send a pub/sub
      notification.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  deployPolicy = _messages.StringField(1)
  deployPolicyUid = _messages.StringField(2)
  message = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class DeployPolicyResourceSelector(_messages.Message):
  r"""Contains information on the resources to select for a deploy policy.
  Attributes provided must all match the resource in order for policy
  restrictions to apply. For example, if delivery pipelines attributes given
  are an id "prod" and labels "foo: bar", a delivery pipeline resource must
  match both that id and have that label in order to be subject to the policy.

  Fields:
    deliveryPipeline: Optional. Contains attributes about a delivery pipeline.
    target: Optional. Contains attributes about a target.
  """

  deliveryPipeline = _messages.MessageField('DeliveryPipelineAttribute', 1)
  target = _messages.MessageField('TargetAttribute', 2)


class DeploymentJobs(_messages.Message):
  r"""Deployment job composition.

  Fields:
    analysisJob: Output only. The analysis Job. Runs after a verify if there
      is a verify job and the verify job succeeds.
    deployJob: Output only. The deploy Job. This is the deploy job in the
      phase.
    postdeployJob: Output only. The postdeploy Job, which is the last job on
      the phase.
    predeployJob: Output only. The predeploy Job, which is the first job on
      the phase.
    verifyJob: Output only. The verify Job. Runs after a deploy if the deploy
      succeeds.
  """

  analysisJob = _messages.MessageField('Job', 1)
  deployJob = _messages.MessageField('Job', 2)
  postdeployJob = _messages.MessageField('Job', 3)
  predeployJob = _messages.MessageField('Job', 4)
  verifyJob = _messages.MessageField('Job', 5)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecutionConfig(_messages.Message):
  r"""Configuration of the environment to use when calling Skaffold.

  Enums:
    UsagesValueListEntryValuesEnum:

  Fields:
    artifactStorage: Optional. Cloud Storage location in which to store
      execution outputs. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    defaultPool: Optional. Use default Cloud Build pool.
    executionTimeout: Optional. Execution timeout for a Cloud Build Execution.
      This must be between 10m and 24h in seconds format. If unspecified, a
      default timeout of 1h is used.
    privatePool: Optional. Use private Cloud Build pool.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) is used.
    usages: Required. Usages when this configuration should be applied.
    verbose: Optional. If true, additional logging will be enabled when
      running builds in this execution environment.
    workerPool: Optional. The resource name of the `WorkerPool`, with the
      format
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`. If
      this optional field is unspecified, the default Cloud Build pool will be
      used.
  """

  class UsagesValueListEntryValuesEnum(_messages.Enum):
    r"""UsagesValueListEntryValuesEnum enum type.

    Values:
      EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED: Default value. This value is
        unused.
      RENDER: Use for rendering.
      DEPLOY: Use for deploying and deployment hooks.
      VERIFY: Use for deployment verification.
      PREDEPLOY: Use for predeploy job execution.
      POSTDEPLOY: Use for postdeploy job execution.
      ANALYSIS: Use for analysis job execution.
    """
    EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED = 0
    RENDER = 1
    DEPLOY = 2
    VERIFY = 3
    PREDEPLOY = 4
    POSTDEPLOY = 5
    ANALYSIS = 6

  artifactStorage = _messages.StringField(1)
  defaultPool = _messages.MessageField('DefaultPool', 2)
  executionTimeout = _messages.StringField(3)
  privatePool = _messages.MessageField('PrivatePool', 4)
  serviceAccount = _messages.StringField(5)
  usages = _messages.EnumField('UsagesValueListEntryValuesEnum', 6, repeated=True)
  verbose = _messages.BooleanField(7)
  workerPool = _messages.StringField(8)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FailedAlertPolicy(_messages.Message):
  r"""FailedAlertPolicy contains information about an alert policy that was
  found to be firing during an alert policy check.

  Fields:
    alertPolicy: Output only. The name of the alert policy that was found to
      be firing. Format is
      `projects/{project}/locations/{location}/alertPolicies/{alertPolicy}`.
    alerts: Output only. Open alerts for the alerting policies that matched
      the alert policy check configuration.
  """

  alertPolicy = _messages.StringField(1)
  alerts = _messages.StringField(2, repeated=True)


class GatewayServiceMesh(_messages.Message):
  r"""Information about the Kubernetes Gateway API service mesh configuration.

  Fields:
    deployment: Required. Name of the Kubernetes Deployment whose traffic is
      managed by the specified HTTPRoute and Service.
    httpRoute: Required. Name of the Gateway API HTTPRoute.
    podSelectorLabel: Optional. The label to use when selecting Pods for the
      Deployment and Service resources. This label must already be present in
      both resources.
    routeDestinations: Optional. Route destinations allow configuring the
      Gateway API HTTPRoute to be deployed to additional clusters. This option
      is available for multi-cluster service mesh set ups that require the
      route to exist in the clusters that call the service. If unspecified,
      the HTTPRoute will only be deployed to the Target cluster.
    routeUpdateWaitTime: Optional. The time to wait for route updates to
      propagate. The maximum configurable time is 3 hours, in seconds format.
      If unspecified, there is no wait time.
    service: Required. Name of the Kubernetes Service.
    stableCutbackDuration: Optional. The amount of time to migrate traffic
      back from the canary Service to the original Service during the stable
      phase deployment. If specified, must be between 15s and 3600s. If
      unspecified, there is no cutback time.
  """

  deployment = _messages.StringField(1)
  httpRoute = _messages.StringField(2)
  podSelectorLabel = _messages.StringField(3)
  routeDestinations = _messages.MessageField('RouteDestinations', 4)
  routeUpdateWaitTime = _messages.StringField(5)
  service = _messages.StringField(6)
  stableCutbackDuration = _messages.StringField(7)


class GkeCluster(_messages.Message):
  r"""Information specifying a GKE Cluster.

  Fields:
    cluster: Optional. Information specifying a GKE Cluster. Format is
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`.
    dnsEndpoint: Optional. If set, the cluster will be accessed using the DNS
      endpoint. Note that both `dns_endpoint` and `internal_ip` cannot be set
      to true.
    internalIp: Optional. If true, `cluster` is accessed using the private IP
      address of the control plane endpoint. Otherwise, the default IP address
      of the control plane endpoint is used. The default IP address is the
      private IP address for clusters with private control-plane endpoints and
      the public IP address otherwise. Only specify this option when `cluster`
      is a [private GKE cluster](https://cloud.google.com/kubernetes-
      engine/docs/concepts/private-cluster-concept). Note that `internal_ip`
      and `dns_endpoint` cannot both be set to true.
    proxyUrl: Optional. If set, used to configure a
      [proxy](https://kubernetes.io/docs/concepts/configuration/organize-
      cluster-access-kubeconfig/#proxy) to the Kubernetes server.
  """

  cluster = _messages.StringField(1)
  dnsEndpoint = _messages.BooleanField(2)
  internalIp = _messages.BooleanField(3)
  proxyUrl = _messages.StringField(4)


class GkeRenderMetadata(_messages.Message):
  r"""GkeRenderMetadata contains GKE information associated with a `Release`
  render.

  Fields:
    canaryDeployment: Output only. Name of the canary version of the
      Kubernetes Deployment that will be applied to the GKE cluster. Only set
      if a canary deployment strategy was configured.
    deployment: Output only. Name of the Kubernetes Deployment that will be
      applied to the GKE cluster. Only set if a single Deployment was provided
      in the rendered manifest.
    kubernetesNamespace: Output only. Namespace the Kubernetes resources will
      be applied to in the GKE cluster. Only set if applying resources to a
      single namespace.
  """

  canaryDeployment = _messages.StringField(1)
  deployment = _messages.StringField(2)
  kubernetesNamespace = _messages.StringField(3)


class GoogleCloudAnalysis(_messages.Message):
  r"""GoogleCloudAnalysis is a set of Google Cloud-based checks to perform on
  the deployment.

  Fields:
    alertPolicyChecks: Optional. A list of Cloud Monitoring Alert Policy
      checks to perform as part of the analysis.
  """

  alertPolicyChecks = _messages.MessageField('AlertPolicyCheck', 1, repeated=True)


class IgnoreJobRequest(_messages.Message):
  r"""The request object used by `IgnoreJob`.

  Fields:
    jobId: Required. The job ID for the Job to ignore.
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
    phaseId: Required. The phase ID the Job to ignore belongs to.
  """

  jobId = _messages.StringField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)
  phaseId = _messages.StringField(3)


class IgnoreJobResponse(_messages.Message):
  r"""The response object from `IgnoreJob`."""


class Job(_messages.Message):
  r"""Job represents an operation for a `Rollout`.

  Enums:
    StateValueValuesEnum: Output only. The current state of the Job.

  Fields:
    advanceChildRolloutJob: Output only. An advanceChildRollout Job.
    analysisJob: Output only. An analysis Job.
    createChildRolloutJob: Output only. A createChildRollout Job.
    deployJob: Output only. A deploy Job.
    id: Output only. The ID of the Job.
    jobRun: Output only. The name of the `JobRun` responsible for the most
      recent invocation of this Job.
    postdeployJob: Output only. A postdeploy Job.
    predeployJob: Output only. A predeploy Job.
    skipMessage: Output only. Additional information on why the Job was
      skipped, if available.
    state: Output only. The current state of the Job.
    verifyJob: Output only. A verify Job.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Job.

    Values:
      STATE_UNSPECIFIED: The Job has an unspecified state.
      PENDING: The Job is waiting for an earlier Phase(s) or Job(s) to
        complete.
      DISABLED: The Job is disabled.
      IN_PROGRESS: The Job is in progress.
      SUCCEEDED: The Job succeeded.
      FAILED: The Job failed.
      ABORTED: The Job was aborted.
      SKIPPED: The Job was skipped.
      IGNORED: The Job was ignored.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    DISABLED = 2
    IN_PROGRESS = 3
    SUCCEEDED = 4
    FAILED = 5
    ABORTED = 6
    SKIPPED = 7
    IGNORED = 8

  advanceChildRolloutJob = _messages.MessageField('AdvanceChildRolloutJob', 1)
  analysisJob = _messages.MessageField('AnalysisJob', 2)
  createChildRolloutJob = _messages.MessageField('CreateChildRolloutJob', 3)
  deployJob = _messages.MessageField('DeployJob', 4)
  id = _messages.StringField(5)
  jobRun = _messages.StringField(6)
  postdeployJob = _messages.MessageField('PostdeployJob', 7)
  predeployJob = _messages.MessageField('PredeployJob', 8)
  skipMessage = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  verifyJob = _messages.MessageField('VerifyJob', 11)


class JobRun(_messages.Message):
  r"""A `JobRun` resource in the Cloud Deploy API. A `JobRun` contains
  information of a single `Rollout` job evaluation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the `JobRun`.

  Fields:
    advanceChildRolloutJobRun: Output only. Information specific to an
      advanceChildRollout `JobRun`
    analysisJobRun: Output only. Information specific to an analysis `JobRun`.
    createChildRolloutJobRun: Output only. Information specific to a
      createChildRollout `JobRun`.
    createTime: Output only. Time at which the `JobRun` was created.
    deployJobRun: Output only. Information specific to a deploy `JobRun`.
    endTime: Output only. Time at which the `JobRun` ended.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    jobId: Output only. ID of the `Rollout` job this `JobRun` corresponds to.
    name: Output only. Name of the `JobRun`. Format is `projects/{project}/loc
      ations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release
      s}/rollouts/{rollouts}/jobRuns/{uuid}`.
    phaseId: Output only. ID of the `Rollout` phase this `JobRun` belongs in.
    postdeployJobRun: Output only. Information specific to a postdeploy
      `JobRun`.
    predeployJobRun: Output only. Information specific to a predeploy
      `JobRun`.
    startTime: Output only. Time at which the `JobRun` was started.
    state: Output only. The current state of the `JobRun`.
    uid: Output only. Unique identifier of the `JobRun`.
    verifyJobRun: Output only. Information specific to a verify `JobRun`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the `JobRun`.

    Values:
      STATE_UNSPECIFIED: The `JobRun` has an unspecified state.
      IN_PROGRESS: The `JobRun` is in progress.
      SUCCEEDED: The `JobRun` has succeeded.
      FAILED: The `JobRun` has failed.
      TERMINATING: The `JobRun` is terminating.
      TERMINATED: The `JobRun` was terminated.
    """
    STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    SUCCEEDED = 2
    FAILED = 3
    TERMINATING = 4
    TERMINATED = 5

  advanceChildRolloutJobRun = _messages.MessageField('AdvanceChildRolloutJobRun', 1)
  analysisJobRun = _messages.MessageField('AnalysisJobRun', 2)
  createChildRolloutJobRun = _messages.MessageField('CreateChildRolloutJobRun', 3)
  createTime = _messages.StringField(4)
  deployJobRun = _messages.MessageField('DeployJobRun', 5)
  endTime = _messages.StringField(6)
  etag = _messages.StringField(7)
  jobId = _messages.StringField(8)
  name = _messages.StringField(9)
  phaseId = _messages.StringField(10)
  postdeployJobRun = _messages.MessageField('PostdeployJobRun', 11)
  predeployJobRun = _messages.MessageField('PredeployJobRun', 12)
  startTime = _messages.StringField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  uid = _messages.StringField(15)
  verifyJobRun = _messages.MessageField('VerifyJobRun', 16)


class JobRunNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/jobrun_notification"
  Platform Log event that describes the failure to send JobRun resource update
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    jobRun: The name of the `JobRun`.
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    release: The name of the `Release`.
    releaseUid: Unique identifier of the `Release`.
    rollout: The name of the `Rollout`.
    rolloutUid: Unique identifier of the `Rollout`.
    targetId: ID of the `Target`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  jobRun = _messages.StringField(1)
  message = _messages.StringField(2)
  pipelineUid = _messages.StringField(3)
  release = _messages.StringField(4)
  releaseUid = _messages.StringField(5)
  rollout = _messages.StringField(6)
  rolloutUid = _messages.StringField(7)
  targetId = _messages.StringField(8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)


class KubernetesConfig(_messages.Message):
  r"""KubernetesConfig contains the Kubernetes runtime configuration.

  Fields:
    cloudServiceMesh: Optional. Cloud Service Mesh configuration.
    gatewayServiceMesh: Optional. Kubernetes Gateway API service mesh
      configuration.
    serviceNetworking: Optional. Kubernetes Service networking configuration.
  """

  cloudServiceMesh = _messages.MessageField('CloudServiceMesh', 1)
  gatewayServiceMesh = _messages.MessageField('GatewayServiceMesh', 2)
  serviceNetworking = _messages.MessageField('ServiceNetworking', 3)


class ListAutomationRunsResponse(_messages.Message):
  r"""The response object from `ListAutomationRuns`.

  Fields:
    automationRuns: The `AutomationRuns` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  automationRuns = _messages.MessageField('AutomationRun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListAutomationsResponse(_messages.Message):
  r"""The response object from `ListAutomations`.

  Fields:
    automations: The `Automation` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  automations = _messages.MessageField('Automation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCustomTargetTypesResponse(_messages.Message):
  r"""The response object from `ListCustomTargetTypes.`

  Fields:
    customTargetTypes: The `CustomTargetType` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  customTargetTypes = _messages.MessageField('CustomTargetType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDeliveryPipelinesResponse(_messages.Message):
  r"""The response object from `ListDeliveryPipelines`.

  Fields:
    deliveryPipelines: The `DeliveryPipeline` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  deliveryPipelines = _messages.MessageField('DeliveryPipeline', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDeployPoliciesResponse(_messages.Message):
  r"""The response object from `ListDeployPolicies`.

  Fields:
    deployPolicies: The `DeployPolicy` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  deployPolicies = _messages.MessageField('DeployPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListJobRunsResponse(_messages.Message):
  r"""ListJobRunsResponse is the response object returned by `ListJobRuns`.

  Fields:
    jobRuns: The `JobRun` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached
  """

  jobRuns = _messages.MessageField('JobRun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReleasesResponse(_messages.Message):
  r"""The response object from `ListReleases`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    releases: The `Release` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  releases = _messages.MessageField('Release', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRolloutsResponse(_messages.Message):
  r"""ListRolloutsResponse is the response object returned by `ListRollouts`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    rollouts: The `Rollout` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  rollouts = _messages.MessageField('Rollout', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTargetsResponse(_messages.Message):
  r"""The response object from `ListTargets`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    targets: The `Target` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  targets = _messages.MessageField('Target', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Metadata(_messages.Message):
  r"""Metadata includes information associated with a `Rollout`.

  Fields:
    automation: Output only. AutomationRolloutMetadata contains the
      information about the interactions between Automation service and this
      rollout.
    cloudRun: Output only. The name of the Cloud Run Service that is
      associated with a `Rollout`.
    custom: Output only. Custom metadata provided by user-defined `Rollout`
      operations.
  """

  automation = _messages.MessageField('AutomationRolloutMetadata', 1)
  cloudRun = _messages.MessageField('CloudRunMetadata', 2)
  custom = _messages.MessageField('CustomMetadata', 3)


class MultiTarget(_messages.Message):
  r"""Information specifying a multiTarget.

  Fields:
    targetIds: Required. The target_ids of this multiTarget.
  """

  targetIds = _messages.StringField(1, repeated=True)


class OneTimeWindow(_messages.Message):
  r"""One-time window within which actions are restricted. For example,
  blocking actions over New Year's Eve from December 31st at 5pm to January
  1st at 9am.

  Fields:
    endDate: Required. End date.
    endTime: Required. End time (exclusive). You may use 24:00 for the end of
      the day.
    startDate: Required. Start date.
    startTime: Required. Start time (inclusive). Use 00:00 for the beginning
      of the day.
  """

  endDate = _messages.MessageField('Date', 1)
  endTime = _messages.MessageField('TimeOfDay', 2)
  startDate = _messages.MessageField('Date', 3)
  startTime = _messages.MessageField('TimeOfDay', 4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Phase(_messages.Message):
  r"""Phase represents a collection of jobs that are logically grouped
  together for a `Rollout`.

  Enums:
    StateValueValuesEnum: Output only. Current state of the Phase.

  Fields:
    childRolloutJobs: Output only. ChildRollout job composition.
    deploymentJobs: Output only. Deployment job composition.
    id: Output only. The ID of the Phase.
    skipMessage: Output only. Additional information on why the Phase was
      skipped, if available.
    state: Output only. Current state of the Phase.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the Phase.

    Values:
      STATE_UNSPECIFIED: The Phase has an unspecified state.
      PENDING: The Phase is waiting for an earlier Phase(s) to complete.
      IN_PROGRESS: The Phase is in progress.
      SUCCEEDED: The Phase has succeeded.
      FAILED: The Phase has failed.
      ABORTED: The Phase was aborted.
      SKIPPED: The Phase was skipped.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    IN_PROGRESS = 2
    SUCCEEDED = 3
    FAILED = 4
    ABORTED = 5
    SKIPPED = 6

  childRolloutJobs = _messages.MessageField('ChildRolloutJobs', 1)
  deploymentJobs = _messages.MessageField('DeploymentJobs', 2)
  id = _messages.StringField(3)
  skipMessage = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class PhaseArtifact(_messages.Message):
  r"""Contains the paths to the artifacts, relative to the URI, for a phase.

  Fields:
    backendServicePath: Output only. File path of the rendered backend service
      configuration relative to the URI.
    deployConfigPath: Output only. File path of the resolved Deploy Config
      relative to the URI. Only one of deploy_config_path or
      skaffold_config_path will be set.
    jobManifestsPath: Output only. File path of the directory of rendered job
      manifests relative to the URI. This is only set if it is applicable.
    manifestPath: Output only. File path of the rendered manifest relative to
      the URI.
    skaffoldConfigPath: Output only. File path of the resolved Skaffold
      configuration relative to the URI.
  """

  backendServicePath = _messages.StringField(1)
  deployConfigPath = _messages.StringField(2)
  jobManifestsPath = _messages.StringField(3)
  manifestPath = _messages.StringField(4)
  skaffoldConfigPath = _messages.StringField(5)


class PhaseConfig(_messages.Message):
  r"""PhaseConfig represents the configuration for a phase in the custom
  canary deployment.

  Fields:
    analysis: Optional. Configuration for the analysis job of this phase. If
      this is not configured, there will be no analysis job for this phase.
    percentage: Required. Percentage deployment for the phase.
    phaseId: Required. The ID to assign to the `Rollout` phase. This value
      must consist of lower-case letters, numbers, and hyphens, start with a
      letter and end with a letter or a number, and have a max length of 63
      characters. In other words, it must match the following regex:
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    postdeploy: Optional. Configuration for the postdeploy job of this phase.
      If this is not configured, there will be no postdeploy job for this
      phase.
    predeploy: Optional. Configuration for the predeploy job of this phase. If
      this is not configured, there will be no predeploy job for this phase.
    profiles: Optional. Skaffold profiles to use when rendering the manifest
      for this phase. These are in addition to the profiles list specified in
      the `DeliveryPipeline` stage.
    verify: Optional. Whether to run verify tests after the deployment via
      `skaffold verify`.
    verifyConfig: Optional. Configuration for the verify job. Cannot be set if
      `verify` is set to true.
  """

  analysis = _messages.MessageField('Analysis', 1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  phaseId = _messages.StringField(3)
  postdeploy = _messages.MessageField('Postdeploy', 4)
  predeploy = _messages.MessageField('Predeploy', 5)
  profiles = _messages.StringField(6, repeated=True)
  verify = _messages.BooleanField(7)
  verifyConfig = _messages.MessageField('Verify', 8)


class PipelineCondition(_messages.Message):
  r"""PipelineCondition contains all conditions relevant to a Delivery
  Pipeline.

  Fields:
    pipelineReadyCondition: Details around the Pipeline's overall status.
    targetsPresentCondition: Details around targets enumerated in the
      pipeline.
    targetsTypeCondition: Details on the whether the targets enumerated in the
      pipeline are of the same type.
  """

  pipelineReadyCondition = _messages.MessageField('PipelineReadyCondition', 1)
  targetsPresentCondition = _messages.MessageField('TargetsPresentCondition', 2)
  targetsTypeCondition = _messages.MessageField('TargetsTypeCondition', 3)


class PipelineReadyCondition(_messages.Message):
  r"""PipelineReadyCondition contains information around the status of the
  Pipeline.

  Fields:
    status: True if the Pipeline is in a valid state. Otherwise at least one
      condition in `PipelineCondition` is in an invalid state. Iterate over
      those conditions and see which condition(s) has status = false to find
      out what is wrong with the Pipeline.
    updateTime: Last time the condition was updated.
  """

  status = _messages.BooleanField(1)
  updateTime = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PolicyRule(_messages.Message):
  r"""Deploy Policy rule.

  Fields:
    rolloutRestriction: Optional. Rollout restrictions.
  """

  rolloutRestriction = _messages.MessageField('RolloutRestriction', 1)


class PolicyViolation(_messages.Message):
  r"""Returned from an action if one or more policies were violated, and
  therefore the action was prevented. Contains information about what policies
  were violated and why.

  Fields:
    policyViolationDetails: Policy violation details.
  """

  policyViolationDetails = _messages.MessageField('PolicyViolationDetails', 1, repeated=True)


class PolicyViolationDetails(_messages.Message):
  r"""Policy violation details.

  Fields:
    failureMessage: User readable message about why the request violated a
      policy. This is not intended for machine parsing.
    policy: Name of the policy that was violated. Policy resource will be in
      the format of
      `projects/{project}/locations/{location}/policies/{policy}`.
    ruleId: Id of the rule that triggered the policy violation.
  """

  failureMessage = _messages.StringField(1)
  policy = _messages.StringField(2)
  ruleId = _messages.StringField(3)


class Postdeploy(_messages.Message):
  r"""Postdeploy contains the postdeploy job configuration information.

  Fields:
    actions: Optional. A sequence of Skaffold custom actions to invoke during
      execution of the postdeploy job.
    tasks: Optional. The tasks that will run as a part of the postdeploy job.
      The tasks are executed sequentially in the order specified. Only one of
      `actions` or `tasks` can be specified.
  """

  actions = _messages.StringField(1, repeated=True)
  tasks = _messages.MessageField('Task', 2, repeated=True)


class PostdeployJob(_messages.Message):
  r"""A postdeploy Job.

  Fields:
    actions: Output only. The custom actions that the postdeploy Job executes.
    tasks: Output only. The tasks that are executed as part of the postdeploy
      Job.
  """

  actions = _messages.StringField(1, repeated=True)
  tasks = _messages.MessageField('Task', 2, repeated=True)


class PostdeployJobRun(_messages.Message):
  r"""PostdeployJobRun contains information specific to a postdeploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the postdeploy
      failed. This will always be unspecified while the postdeploy is in
      progress or if it succeeded.

  Fields:
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to execute the custom actions associated with the
      postdeploy Job. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    failureCause: Output only. The reason the postdeploy failed. This will
      always be unspecified while the postdeploy is in progress or if it
      succeeded.
    failureMessage: Output only. Additional information about the postdeploy
      failure, if available.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the postdeploy failed. This will always be
    unspecified while the postdeploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The postdeploy operation did not complete
        successfully; check Cloud Build logs.
      DEADLINE_EXCEEDED: The postdeploy job run did not complete within the
        allotted time.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    CLOUD_BUILD_REQUEST_FAILED = 4

  build = _messages.StringField(1)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 2)
  failureMessage = _messages.StringField(3)


class Predeploy(_messages.Message):
  r"""Predeploy contains the predeploy job configuration information.

  Fields:
    actions: Optional. A sequence of Skaffold custom actions to invoke during
      execution of the predeploy job.
    tasks: Optional. The tasks that will run as a part of the predeploy job.
      The tasks are executed sequentially in the order specified. Only one of
      `actions` or `tasks` can be specified.
  """

  actions = _messages.StringField(1, repeated=True)
  tasks = _messages.MessageField('Task', 2, repeated=True)


class PredeployJob(_messages.Message):
  r"""A predeploy Job.

  Fields:
    actions: Output only. The custom actions that the predeploy Job executes.
    tasks: Output only. The tasks that are executed as part of the predeploy
      Job.
  """

  actions = _messages.StringField(1, repeated=True)
  tasks = _messages.MessageField('Task', 2, repeated=True)


class PredeployJobRun(_messages.Message):
  r"""PredeployJobRun contains information specific to a predeploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the predeploy failed.
      This will always be unspecified while the predeploy is in progress or if
      it succeeded.

  Fields:
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to execute the custom actions associated with the predeploy
      Job. Format is `projects/{project}/locations/{location}/builds/{build}`.
    failureCause: Output only. The reason the predeploy failed. This will
      always be unspecified while the predeploy is in progress or if it
      succeeded.
    failureMessage: Output only. Additional information about the predeploy
      failure, if available.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the predeploy failed. This will always be
    unspecified while the predeploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The predeploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The predeploy job run did not complete within the
        allotted time.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    CLOUD_BUILD_REQUEST_FAILED = 4

  build = _messages.StringField(1)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 2)
  failureMessage = _messages.StringField(3)


class PrivatePool(_messages.Message):
  r"""Execution using a private Cloud Build pool.

  Fields:
    artifactStorage: Optional. Cloud Storage location where execution outputs
      should be stored. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) will be used.
    workerPool: Required. Resource name of the Cloud Build worker pool to use.
      The format is
      `projects/{project}/locations/{location}/workerPools/{pool}`.
  """

  artifactStorage = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class PromoteReleaseOperation(_messages.Message):
  r"""Contains the information of an automated promote-release operation.

  Fields:
    phase: Output only. The starting phase of the rollout created by this
      operation.
    rollout: Output only. The name of the rollout that initiates the
      `AutomationRun`.
    targetId: Output only. The ID of the target that represents the promotion
      stage to which the release will be promoted. The value of this field is
      the last segment of a target name.
    wait: Output only. How long the operation will be paused.
  """

  phase = _messages.StringField(1)
  rollout = _messages.StringField(2)
  targetId = _messages.StringField(3)
  wait = _messages.StringField(4)


class PromoteReleaseRule(_messages.Message):
  r"""The `PromoteRelease` rule will automatically promote a release from the
  current target to a specified target.

  Fields:
    condition: Output only. Information around the state of the Automation
      rule.
    destinationPhase: Optional. The starting phase of the rollout created by
      this operation. Default to the first phase.
    destinationTargetId: Optional. The ID of the stage in the pipeline to
      which this `Release` is deploying. If unspecified, default it to the
      next stage in the promotion flow. The value of this field could be one
      of the following: * The last segment of a target name * "@next", the
      next target in the promotion sequence
    id: Required. ID of the rule. This id must be unique in the `Automation`
      resource to which this rule belongs. The format is
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
    wait: Optional. How long the release need to be paused until being
      promoted to the next target.
  """

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  destinationPhase = _messages.StringField(2)
  destinationTargetId = _messages.StringField(3)
  id = _messages.StringField(4)
  wait = _messages.StringField(5)


class Release(_messages.Message):
  r"""A `Release` resource in the Cloud Deploy API. A `Release` defines a
  specific Skaffold configuration instance that can be deployed.

  Enums:
    RenderStateValueValuesEnum: Output only. Current state of the render
      operation.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    DeployParametersValue: Optional. The deploy parameters to use for all
      targets in this release.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    TargetArtifactsValue: Output only. Map from target ID to the target
      artifacts created during the render operation.
    TargetRendersValue: Output only. Map from target ID to details of the
      render operation for that target.

  Fields:
    abandoned: Output only. Indicates whether this is an abandoned release.
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    buildArtifacts: Optional. List of artifacts to pass through to Skaffold
      command.
    condition: Output only. Information around the state of the Release.
    createTime: Output only. Time at which the `Release` was created.
    customTargetTypeSnapshots: Output only. Snapshot of the custom target
      types referenced by the targets taken at release creation time.
    deliveryPipelineSnapshot: Output only. Snapshot of the parent pipeline
      taken at release creation time.
    deployConfigPath: Optional. Filepath of the Deploy Config file inside of
      the config URI. Only one of skaffold_config_path or deploy_config_path
      can be set.
    deployConfigUri: Optional. Cloud Storage URI of tar.gz archive containing
      the release configuration with a Deploy Config file. Only one of
      skaffold_config_uri or deploy_config_uri can be set.
    deployParameters: Optional. The deploy parameters to use for all targets
      in this release.
    description: Optional. Description of the `Release`. Max length is 255
      characters.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Identifier. Name of the `Release`. Format is `projects/{project}/loc
      ations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release
      }`. The `release` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    renderEndTime: Output only. Time at which the render completed.
    renderStartTime: Output only. Time at which the render began.
    renderState: Output only. Current state of the render operation.
    skaffoldConfigPath: Optional. Filepath of the Skaffold config inside of
      the config URI.
    skaffoldConfigUri: Optional. Cloud Storage URI of tar.gz archive
      containing Skaffold configuration.
    skaffoldVersion: Optional. The Skaffold version to use when operating on
      this release, such as "1.20.0". Not all versions are valid; Cloud Deploy
      supports a specific set of versions. If unset, the most recent supported
      Skaffold version will be used.
    targetArtifacts: Output only. Map from target ID to the target artifacts
      created during the render operation.
    targetRenders: Output only. Map from target ID to details of the render
      operation for that target.
    targetSnapshots: Output only. Snapshot of the targets taken at release
      creation time.
    uid: Output only. Unique identifier of the `Release`.
  """

  class RenderStateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the render operation.

    Values:
      RENDER_STATE_UNSPECIFIED: The render state is unspecified.
      SUCCEEDED: All rendering operations have completed successfully.
      FAILED: All rendering operations have completed, and one or more have
        failed.
      IN_PROGRESS: Rendering has started and is not complete.
    """
    RENDER_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeployParametersValue(_messages.Message):
    r"""Optional. The deploy parameters to use for all targets in this
    release.

    Messages:
      AdditionalProperty: An additional property for a DeployParametersValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeployParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeployParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TargetArtifactsValue(_messages.Message):
    r"""Output only. Map from target ID to the target artifacts created during
    the render operation.

    Messages:
      AdditionalProperty: An additional property for a TargetArtifactsValue
        object.

    Fields:
      additionalProperties: Additional properties of type TargetArtifactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TargetArtifactsValue object.

      Fields:
        key: Name of the additional property.
        value: A TargetArtifact attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TargetArtifact', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TargetRendersValue(_messages.Message):
    r"""Output only. Map from target ID to details of the render operation for
    that target.

    Messages:
      AdditionalProperty: An additional property for a TargetRendersValue
        object.

    Fields:
      additionalProperties: Additional properties of type TargetRendersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TargetRendersValue object.

      Fields:
        key: Name of the additional property.
        value: A TargetRender attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TargetRender', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  abandoned = _messages.BooleanField(1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  buildArtifacts = _messages.MessageField('BuildArtifact', 3, repeated=True)
  condition = _messages.MessageField('ReleaseCondition', 4)
  createTime = _messages.StringField(5)
  customTargetTypeSnapshots = _messages.MessageField('CustomTargetType', 6, repeated=True)
  deliveryPipelineSnapshot = _messages.MessageField('DeliveryPipeline', 7)
  deployConfigPath = _messages.StringField(8)
  deployConfigUri = _messages.StringField(9)
  deployParameters = _messages.MessageField('DeployParametersValue', 10)
  description = _messages.StringField(11)
  etag = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  name = _messages.StringField(14)
  renderEndTime = _messages.StringField(15)
  renderStartTime = _messages.StringField(16)
  renderState = _messages.EnumField('RenderStateValueValuesEnum', 17)
  skaffoldConfigPath = _messages.StringField(18)
  skaffoldConfigUri = _messages.StringField(19)
  skaffoldVersion = _messages.StringField(20)
  targetArtifacts = _messages.MessageField('TargetArtifactsValue', 21)
  targetRenders = _messages.MessageField('TargetRendersValue', 22)
  targetSnapshots = _messages.MessageField('Target', 23, repeated=True)
  uid = _messages.StringField(24)


class ReleaseCondition(_messages.Message):
  r"""ReleaseCondition contains all conditions relevant to a Release.

  Fields:
    releaseReadyCondition: Details around the Releases's overall status.
    skaffoldSupportedCondition: Details around the support state of the
      release's Skaffold version.
  """

  releaseReadyCondition = _messages.MessageField('ReleaseReadyCondition', 1)
  skaffoldSupportedCondition = _messages.MessageField('SkaffoldSupportedCondition', 2)


class ReleaseNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/release_notification"
  Platform Log event that describes the failure to send release status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    release: The name of the `Release`.
    releaseUid: Unique identifier of the `Release`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  message = _messages.StringField(1)
  pipelineUid = _messages.StringField(2)
  release = _messages.StringField(3)
  releaseUid = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class ReleaseReadyCondition(_messages.Message):
  r"""ReleaseReadyCondition contains information around the status of the
  Release. If a release is not ready, you cannot create a rollout with the
  release.

  Fields:
    status: True if the Release is in a valid state. Otherwise at least one
      condition in `ReleaseCondition` is in an invalid state. Iterate over
      those conditions and see which condition(s) has status = false to find
      out what is wrong with the Release.
  """

  status = _messages.BooleanField(1)


class ReleaseRenderEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/release_render" Platform
  Log event that describes the render status change.

  Enums:
    ReleaseRenderStateValueValuesEnum: The state of the release render.
    TypeValueValuesEnum: Type of this notification, e.g. for a release render
      state change event.

  Fields:
    message: Debug message for when a render transition occurs. Provides
      further details as rendering progresses through render states.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    release: The name of the release. release_uid is not in this log message
      because we write some of these log messages at release creation time,
      before we've generated the uid.
    releaseRenderState: The state of the release render.
    type: Type of this notification, e.g. for a release render state change
      event.
  """

  class ReleaseRenderStateValueValuesEnum(_messages.Enum):
    r"""The state of the release render.

    Values:
      RENDER_STATE_UNSPECIFIED: The render state is unspecified.
      SUCCEEDED: All rendering operations have completed successfully.
      FAILED: All rendering operations have completed, and one or more have
        failed.
      IN_PROGRESS: Rendering has started and is not complete.
    """
    RENDER_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a release render state change
    event.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  message = _messages.StringField(1)
  pipelineUid = _messages.StringField(2)
  release = _messages.StringField(3)
  releaseRenderState = _messages.EnumField('ReleaseRenderStateValueValuesEnum', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class RenderMetadata(_messages.Message):
  r"""RenderMetadata includes information associated with a `Release` render.

  Fields:
    anthos: Output only. Metadata associated with rendering for an Anthos
      Target.
    cloudRun: Output only. Metadata associated with rendering for Cloud Run.
    custom: Output only. Custom metadata provided by user-defined render
      operation.
    gke: Output only. Metadata associated with rendering for a GKE Target.
  """

  anthos = _messages.MessageField('AnthosRenderMetadata', 1)
  cloudRun = _messages.MessageField('CloudRunRenderMetadata', 2)
  custom = _messages.MessageField('CustomMetadata', 3)
  gke = _messages.MessageField('GkeRenderMetadata', 4)


class RepairPhase(_messages.Message):
  r"""RepairPhase tracks the repair attempts that have been made for each
  `RepairPhaseConfig` specified in the `Automation` resource.

  Fields:
    retry: Output only. Records of the retry attempts for retry repair mode.
    rollback: Output only. Rollback attempt for rollback repair mode .
  """

  retry = _messages.MessageField('RetryPhase', 1)
  rollback = _messages.MessageField('RollbackAttempt', 2)


class RepairPhaseConfig(_messages.Message):
  r"""Configuration of the repair phase.

  Fields:
    retry: Optional. Retries a failed job.
    rollback: Optional. Rolls back a `Rollout`.
  """

  retry = _messages.MessageField('Retry', 1)
  rollback = _messages.MessageField('Rollback', 2)


class RepairRolloutOperation(_messages.Message):
  r"""Contains the information for an automated `repair rollout` operation.

  Fields:
    currentRepairPhaseIndex: Output only. The index of the current repair
      action in the repair sequence.
    jobId: Output only. The job ID for the Job to repair.
    phaseId: Output only. The phase ID of the phase that includes the job
      being repaired.
    repairPhases: Output only. Records of the repair attempts. Each repair
      phase may have multiple retry attempts or single rollback attempt.
    rollout: Output only. The name of the rollout that initiates the
      `AutomationRun`.
  """

  currentRepairPhaseIndex = _messages.IntegerField(1)
  jobId = _messages.StringField(2)
  phaseId = _messages.StringField(3)
  repairPhases = _messages.MessageField('RepairPhase', 4, repeated=True)
  rollout = _messages.StringField(5)


class RepairRolloutRule(_messages.Message):
  r"""The `RepairRolloutRule` automation rule will automatically repair a
  failed `Rollout`.

  Fields:
    condition: Output only. Information around the state of the 'Automation'
      rule.
    id: Required. ID of the rule. This id must be unique in the `Automation`
      resource to which this rule belongs. The format is
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
    jobs: Optional. Jobs to repair. Proceeds only after job name matched any
      one in the list, or for all jobs if unspecified or empty. The phase that
      includes the job must match the phase ID specified in `source_phase`.
      This value must consist of lower-case letters, numbers, and hyphens,
      start with a letter and end with a letter or a number, and have a max
      length of 63 characters. In other words, it must match the following
      regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    phases: Optional. Phases within which jobs are subject to automatic repair
      actions on failure. Proceeds only after phase name matched any one in
      the list, or for all phases if unspecified. This value must consist of
      lower-case letters, numbers, and hyphens, start with a letter and end
      with a letter or a number, and have a max length of 63 characters. In
      other words, it must match the following regex:
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    repairPhases: Required. Defines the types of automatic repair phases for
      failed jobs.
  """

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  id = _messages.StringField(2)
  jobs = _messages.StringField(3, repeated=True)
  phases = _messages.StringField(4, repeated=True)
  repairPhases = _messages.MessageField('RepairPhaseConfig', 5, repeated=True)


class Retry(_messages.Message):
  r"""Retries the failed job.

  Enums:
    BackoffModeValueValuesEnum: Optional. The pattern of how wait time will be
      increased. Default is linear. Backoff mode will be ignored if `wait` is
      0.

  Fields:
    attempts: Required. Total number of retries. Retry is skipped if set to 0;
      The minimum value is 1, and the maximum value is 10.
    backoffMode: Optional. The pattern of how wait time will be increased.
      Default is linear. Backoff mode will be ignored if `wait` is 0.
    wait: Optional. How long to wait for the first retry. Default is 0, and
      the maximum value is 14d.
  """

  class BackoffModeValueValuesEnum(_messages.Enum):
    r"""Optional. The pattern of how wait time will be increased. Default is
    linear. Backoff mode will be ignored if `wait` is 0.

    Values:
      BACKOFF_MODE_UNSPECIFIED: No WaitMode is specified.
      BACKOFF_MODE_LINEAR: Increases the wait time linearly.
      BACKOFF_MODE_EXPONENTIAL: Increases the wait time exponentially.
    """
    BACKOFF_MODE_UNSPECIFIED = 0
    BACKOFF_MODE_LINEAR = 1
    BACKOFF_MODE_EXPONENTIAL = 2

  attempts = _messages.IntegerField(1)
  backoffMode = _messages.EnumField('BackoffModeValueValuesEnum', 2)
  wait = _messages.StringField(3)


class RetryAttempt(_messages.Message):
  r"""RetryAttempt represents an action of retrying the failed Cloud Deploy
  job.

  Enums:
    StateValueValuesEnum: Output only. Valid state of this retry action.

  Fields:
    attempt: Output only. The index of this retry attempt.
    state: Output only. Valid state of this retry action.
    stateDesc: Output only. Description of the state of the Retry.
    wait: Output only. How long the operation will be paused.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Valid state of this retry action.

    Values:
      REPAIR_STATE_UNSPECIFIED: The `repair` has an unspecified state.
      REPAIR_STATE_SUCCEEDED: The `repair` action has succeeded.
      REPAIR_STATE_CANCELLED: The `repair` action was cancelled.
      REPAIR_STATE_FAILED: The `repair` action has failed.
      REPAIR_STATE_IN_PROGRESS: The `repair` action is in progress.
      REPAIR_STATE_PENDING: The `repair` action is pending.
      REPAIR_STATE_ABORTED: The `repair` action was aborted.
    """
    REPAIR_STATE_UNSPECIFIED = 0
    REPAIR_STATE_SUCCEEDED = 1
    REPAIR_STATE_CANCELLED = 2
    REPAIR_STATE_FAILED = 3
    REPAIR_STATE_IN_PROGRESS = 4
    REPAIR_STATE_PENDING = 5
    REPAIR_STATE_ABORTED = 6

  attempt = _messages.IntegerField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  stateDesc = _messages.StringField(3)
  wait = _messages.StringField(4)


class RetryJobRequest(_messages.Message):
  r"""RetryJobRequest is the request object used by `RetryJob`.

  Fields:
    jobId: Required. The job ID for the Job to retry.
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
    phaseId: Required. The phase ID the Job to retry belongs to.
  """

  jobId = _messages.StringField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)
  phaseId = _messages.StringField(3)


class RetryJobResponse(_messages.Message):
  r"""The response object from 'RetryJob'."""


class RetryPhase(_messages.Message):
  r"""RetryPhase contains the retry attempts and the metadata for initiating a
  new attempt.

  Enums:
    BackoffModeValueValuesEnum: Output only. The pattern of how the wait time
      of the retry attempt is calculated.

  Fields:
    attempts: Output only. Detail of a retry action.
    backoffMode: Output only. The pattern of how the wait time of the retry
      attempt is calculated.
    totalAttempts: Output only. The number of attempts that have been made.
  """

  class BackoffModeValueValuesEnum(_messages.Enum):
    r"""Output only. The pattern of how the wait time of the retry attempt is
    calculated.

    Values:
      BACKOFF_MODE_UNSPECIFIED: No WaitMode is specified.
      BACKOFF_MODE_LINEAR: Increases the wait time linearly.
      BACKOFF_MODE_EXPONENTIAL: Increases the wait time exponentially.
    """
    BACKOFF_MODE_UNSPECIFIED = 0
    BACKOFF_MODE_LINEAR = 1
    BACKOFF_MODE_EXPONENTIAL = 2

  attempts = _messages.MessageField('RetryAttempt', 1, repeated=True)
  backoffMode = _messages.EnumField('BackoffModeValueValuesEnum', 2)
  totalAttempts = _messages.IntegerField(3)


class Rollback(_messages.Message):
  r"""Rolls back a `Rollout`.

  Fields:
    destinationPhase: Optional. The starting phase ID for the `Rollout`. If
      unspecified, the `Rollout` will start in the stable phase.
    disableRollbackIfRolloutPending: Optional. If pending rollout exists on
      the target, the rollback operation will be aborted.
  """

  destinationPhase = _messages.StringField(1)
  disableRollbackIfRolloutPending = _messages.BooleanField(2)


class RollbackAttempt(_messages.Message):
  r"""RollbackAttempt represents an action of rolling back a Cloud Deploy
  'Target'.

  Enums:
    StateValueValuesEnum: Output only. Valid state of this rollback action.

  Fields:
    destinationPhase: Output only. The phase to which the rollout will be
      rolled back to.
    disableRollbackIfRolloutPending: Output only. If active rollout exists on
      the target, abort this rollback.
    rolloutId: Output only. ID of the rollback `Rollout` to create.
    state: Output only. Valid state of this rollback action.
    stateDesc: Output only. Description of the state of the Rollback.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Valid state of this rollback action.

    Values:
      REPAIR_STATE_UNSPECIFIED: The `repair` has an unspecified state.
      REPAIR_STATE_SUCCEEDED: The `repair` action has succeeded.
      REPAIR_STATE_CANCELLED: The `repair` action was cancelled.
      REPAIR_STATE_FAILED: The `repair` action has failed.
      REPAIR_STATE_IN_PROGRESS: The `repair` action is in progress.
      REPAIR_STATE_PENDING: The `repair` action is pending.
      REPAIR_STATE_ABORTED: The `repair` action was aborted.
    """
    REPAIR_STATE_UNSPECIFIED = 0
    REPAIR_STATE_SUCCEEDED = 1
    REPAIR_STATE_CANCELLED = 2
    REPAIR_STATE_FAILED = 3
    REPAIR_STATE_IN_PROGRESS = 4
    REPAIR_STATE_PENDING = 5
    REPAIR_STATE_ABORTED = 6

  destinationPhase = _messages.StringField(1)
  disableRollbackIfRolloutPending = _messages.BooleanField(2)
  rolloutId = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  stateDesc = _messages.StringField(5)


class RollbackTargetConfig(_messages.Message):
  r"""Configs for the Rollback rollout.

  Fields:
    rollout: Optional. The rollback `Rollout` to create.
    startingPhaseId: Optional. The starting phase ID for the `Rollout`. If
      unspecified, the `Rollout` will start in the stable phase.
  """

  rollout = _messages.MessageField('Rollout', 1)
  startingPhaseId = _messages.StringField(2)


class RollbackTargetRequest(_messages.Message):
  r"""The request object for `RollbackTarget`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deploy_policy}`
      .
    releaseId: Optional. ID of the `Release` to roll back to. If this isn't
      specified, the previous successful `Rollout` to the specified target
      will be used to determine the `Release`.
    rollbackConfig: Optional. Configs for the rollback `Rollout`.
    rolloutId: Required. ID of the rollback `Rollout` to create.
    rolloutToRollBack: Optional. If provided, this must be the latest
      `Rollout` that is on the `Target`.
    targetId: Required. ID of the `Target` that is being rolled back.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with a `RollbackTargetResponse`.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  releaseId = _messages.StringField(2)
  rollbackConfig = _messages.MessageField('RollbackTargetConfig', 3)
  rolloutId = _messages.StringField(4)
  rolloutToRollBack = _messages.StringField(5)
  targetId = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class RollbackTargetResponse(_messages.Message):
  r"""The response object from `RollbackTarget`.

  Fields:
    rollbackConfig: The config of the rollback `Rollout` created or will be
      created.
  """

  rollbackConfig = _messages.MessageField('RollbackTargetConfig', 1)


class Rollout(_messages.Message):
  r"""A `Rollout` resource in the Cloud Deploy API. A `Rollout` contains
  information around a specific deployment to a `Target`.

  Enums:
    ApprovalStateValueValuesEnum: Output only. Approval state of the
      `Rollout`.
    DeployFailureCauseValueValuesEnum: Output only. The reason this rollout
      failed. This will always be unspecified while the rollout is in
      progress.
    StateValueValuesEnum: Output only. Current state of the `Rollout`.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    activeRepairAutomationRun: Output only. The AutomationRun actively
      repairing the rollout.
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    approvalState: Output only. Approval state of the `Rollout`.
    approveTime: Output only. Time at which the `Rollout` was approved.
    controllerRollout: Output only. Name of the `ControllerRollout`. Format is
      `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipe
      line}/releases/{release}/rollouts/{rollout}`.
    createTime: Output only. Time at which the `Rollout` was created.
    deployEndTime: Output only. Time at which the `Rollout` finished
      deploying.
    deployFailureCause: Output only. The reason this rollout failed. This will
      always be unspecified while the rollout is in progress.
    deployStartTime: Output only. Time at which the `Rollout` started
      deploying.
    deployingBuild: Output only. The resource name of the Cloud Build `Build`
      object that is used to deploy the Rollout. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    description: Optional. Description of the `Rollout` for user purposes. Max
      length is 255 characters.
    enqueueTime: Output only. Time at which the `Rollout` was enqueued.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    failureReason: Output only. Additional information about the rollout
      failure, if available.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    metadata: Output only. Metadata contains information about the rollout.
    name: Identifier. Name of the `Rollout`. Format is `projects/{project}/loc
      ations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release
      }/rollouts/{rollout}`. The `rollout` component must match
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    phases: Output only. The phases that represent the workflows of this
      `Rollout`.
    rollbackOfRollout: Output only. Name of the `Rollout` that is rolled back
      by this `Rollout`. Empty if this `Rollout` wasn't created as a rollback.
    rolledBackByRollouts: Output only. Names of `Rollouts` that rolled back
      this `Rollout`.
    state: Output only. Current state of the `Rollout`.
    targetId: Required. The ID of Target to which this `Rollout` is deploying.
    uid: Output only. Unique identifier of the `Rollout`.
  """

  class ApprovalStateValueValuesEnum(_messages.Enum):
    r"""Output only. Approval state of the `Rollout`.

    Values:
      APPROVAL_STATE_UNSPECIFIED: The `Rollout` has an unspecified approval
        state.
      NEEDS_APPROVAL: The `Rollout` requires approval.
      DOES_NOT_NEED_APPROVAL: The `Rollout` does not require approval.
      APPROVED: The `Rollout` has been approved.
      REJECTED: The `Rollout` has been rejected.
    """
    APPROVAL_STATE_UNSPECIFIED = 0
    NEEDS_APPROVAL = 1
    DOES_NOT_NEED_APPROVAL = 2
    APPROVED = 3
    REJECTED = 4

  class DeployFailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason this rollout failed. This will always be
    unspecified while the rollout is in progress.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The deploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: Deployment did not complete within the allotted time.
      RELEASE_FAILED: Release is in a failed state.
      RELEASE_ABANDONED: Release is abandoned.
      VERIFICATION_CONFIG_NOT_FOUND: No Skaffold verify configuration was
        found.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
      OPERATION_FEATURE_NOT_SUPPORTED: A Rollout operation had a feature
        configured that is not supported.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    RELEASE_FAILED = 4
    RELEASE_ABANDONED = 5
    VERIFICATION_CONFIG_NOT_FOUND = 6
    CLOUD_BUILD_REQUEST_FAILED = 7
    OPERATION_FEATURE_NOT_SUPPORTED = 8

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the `Rollout`.

    Values:
      STATE_UNSPECIFIED: The `Rollout` has an unspecified state.
      SUCCEEDED: The `Rollout` has completed successfully.
      FAILED: The `Rollout` has failed.
      IN_PROGRESS: The `Rollout` is being deployed.
      PENDING_APPROVAL: The `Rollout` needs approval.
      APPROVAL_REJECTED: An approver rejected the `Rollout`.
      PENDING: The `Rollout` is waiting for an earlier Rollout(s) to complete
        on this `Target`.
      PENDING_RELEASE: The `Rollout` is waiting for the `Release` to be fully
        rendered.
      CANCELLING: The `Rollout` is in the process of being cancelled.
      CANCELLED: The `Rollout` has been cancelled.
      HALTED: The `Rollout` is halted.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3
    PENDING_APPROVAL = 4
    APPROVAL_REJECTED = 5
    PENDING = 6
    PENDING_RELEASE = 7
    CANCELLING = 8
    CANCELLED = 9
    HALTED = 10

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activeRepairAutomationRun = _messages.StringField(1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  approvalState = _messages.EnumField('ApprovalStateValueValuesEnum', 3)
  approveTime = _messages.StringField(4)
  controllerRollout = _messages.StringField(5)
  createTime = _messages.StringField(6)
  deployEndTime = _messages.StringField(7)
  deployFailureCause = _messages.EnumField('DeployFailureCauseValueValuesEnum', 8)
  deployStartTime = _messages.StringField(9)
  deployingBuild = _messages.StringField(10)
  description = _messages.StringField(11)
  enqueueTime = _messages.StringField(12)
  etag = _messages.StringField(13)
  failureReason = _messages.StringField(14)
  labels = _messages.MessageField('LabelsValue', 15)
  metadata = _messages.MessageField('Metadata', 16)
  name = _messages.StringField(17)
  phases = _messages.MessageField('Phase', 18, repeated=True)
  rollbackOfRollout = _messages.StringField(19)
  rolledBackByRollouts = _messages.StringField(20, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  targetId = _messages.StringField(22)
  uid = _messages.StringField(23)


class RolloutNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/rollout_notification"
  Platform Log event that describes the failure to send rollout status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    release: The name of the `Release`.
    releaseUid: Unique identifier of the `Release`.
    rollout: The name of the `Rollout`.
    rolloutUid: Unique identifier of the `Rollout`.
    targetId: ID of the `Target` that the rollout is deployed to.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  message = _messages.StringField(1)
  pipelineUid = _messages.StringField(2)
  release = _messages.StringField(3)
  releaseUid = _messages.StringField(4)
  rollout = _messages.StringField(5)
  rolloutUid = _messages.StringField(6)
  targetId = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)


class RolloutRestriction(_messages.Message):
  r"""Rollout restrictions.

  Enums:
    ActionsValueListEntryValuesEnum:
    InvokersValueListEntryValuesEnum:

  Fields:
    actions: Optional. Rollout actions to be restricted as part of the policy.
      If left empty, all actions will be restricted.
    id: Required. Restriction rule ID. Required and must be unique within a
      DeployPolicy. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
    invokers: Optional. What invoked the action. If left empty, all invoker
      types will be restricted.
    timeWindows: Required. Time window within which actions are restricted.
  """

  class ActionsValueListEntryValuesEnum(_messages.Enum):
    r"""ActionsValueListEntryValuesEnum enum type.

    Values:
      ROLLOUT_ACTIONS_UNSPECIFIED: Unspecified.
      ADVANCE: Advance the rollout to the next phase.
      APPROVE: Approve the rollout.
      CANCEL: Cancel the rollout.
      CREATE: Create a rollout.
      IGNORE_JOB: Ignore a job result on the rollout.
      RETRY_JOB: Retry a job for a rollout.
      ROLLBACK: Rollback a rollout.
      TERMINATE_JOBRUN: Terminate a jobrun.
    """
    ROLLOUT_ACTIONS_UNSPECIFIED = 0
    ADVANCE = 1
    APPROVE = 2
    CANCEL = 3
    CREATE = 4
    IGNORE_JOB = 5
    RETRY_JOB = 6
    ROLLBACK = 7
    TERMINATE_JOBRUN = 8

  class InvokersValueListEntryValuesEnum(_messages.Enum):
    r"""InvokersValueListEntryValuesEnum enum type.

    Values:
      INVOKER_UNSPECIFIED: Unspecified.
      USER: The action is user-driven. For example, creating a rollout
        manually via a gcloud create command.
      DEPLOY_AUTOMATION: Automated action by Cloud Deploy.
    """
    INVOKER_UNSPECIFIED = 0
    USER = 1
    DEPLOY_AUTOMATION = 2

  actions = _messages.EnumField('ActionsValueListEntryValuesEnum', 1, repeated=True)
  id = _messages.StringField(2)
  invokers = _messages.EnumField('InvokersValueListEntryValuesEnum', 3, repeated=True)
  timeWindows = _messages.MessageField('TimeWindows', 4)


class RolloutUpdateEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/rollout_update" Platform
  Log event that describes the rollout update event.

  Enums:
    RolloutUpdateTypeValueValuesEnum: The type of the rollout update.
    TypeValueValuesEnum: Type of this notification, e.g. for a rollout update
      event.

  Fields:
    message: Debug message for when a rollout update event occurs.
    pipelineUid: Unique identifier of the pipeline.
    release: The name of the `Release`.
    releaseUid: Unique identifier of the release.
    rollout: The name of the rollout. rollout_uid is not in this log message
      because we write some of these log messages at rollout creation time,
      before we've generated the uid.
    rolloutUpdateType: The type of the rollout update.
    targetId: ID of the target.
    type: Type of this notification, e.g. for a rollout update event.
  """

  class RolloutUpdateTypeValueValuesEnum(_messages.Enum):
    r"""The type of the rollout update.

    Values:
      ROLLOUT_UPDATE_TYPE_UNSPECIFIED: Rollout update type unspecified.
      PENDING: rollout state updated to pending.
      PENDING_RELEASE: Rollout state updated to pending release.
      IN_PROGRESS: Rollout state updated to in progress.
      CANCELLING: Rollout state updated to cancelling.
      CANCELLED: Rollout state updated to cancelled.
      HALTED: Rollout state updated to halted.
      SUCCEEDED: Rollout state updated to succeeded.
      FAILED: Rollout state updated to failed.
      APPROVAL_REQUIRED: Rollout requires approval.
      APPROVED: Rollout has been approved.
      REJECTED: Rollout has been rejected.
      ADVANCE_REQUIRED: Rollout requires advance to the next phase.
      ADVANCED: Rollout has been advanced.
    """
    ROLLOUT_UPDATE_TYPE_UNSPECIFIED = 0
    PENDING = 1
    PENDING_RELEASE = 2
    IN_PROGRESS = 3
    CANCELLING = 4
    CANCELLED = 5
    HALTED = 6
    SUCCEEDED = 7
    FAILED = 8
    APPROVAL_REQUIRED = 9
    APPROVED = 10
    REJECTED = 11
    ADVANCE_REQUIRED = 12
    ADVANCED = 13

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a rollout update event.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  message = _messages.StringField(1)
  pipelineUid = _messages.StringField(2)
  release = _messages.StringField(3)
  releaseUid = _messages.StringField(4)
  rollout = _messages.StringField(5)
  rolloutUpdateType = _messages.EnumField('RolloutUpdateTypeValueValuesEnum', 6)
  targetId = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)


class RouteDestinations(_messages.Message):
  r"""Information about route destinations for the Gateway API service mesh.

  Fields:
    destinationIds: Required. The clusters where the Gateway API HTTPRoute
      resource will be deployed to. Valid entries include the associated
      entities IDs configured in the Target resource and "@self" to include
      the Target cluster.
    propagateService: Optional. Whether to propagate the Kubernetes Service to
      the route destination clusters. The Service will always be deployed to
      the Target cluster even if the HTTPRoute is not. This option may be used
      to facilitate successful DNS lookup in the route destination clusters.
      Can only be set to true if destinations are specified.
  """

  destinationIds = _messages.StringField(1, repeated=True)
  propagateService = _messages.BooleanField(2)


class RuntimeConfig(_messages.Message):
  r"""RuntimeConfig contains the runtime specific configurations for a
  deployment strategy.

  Fields:
    cloudRun: Optional. Cloud Run runtime configuration.
    kubernetes: Optional. Kubernetes runtime configuration.
  """

  cloudRun = _messages.MessageField('CloudRunConfig', 1)
  kubernetes = _messages.MessageField('KubernetesConfig', 2)


class SerialPipeline(_messages.Message):
  r"""SerialPipeline defines a sequential set of stages for a
  `DeliveryPipeline`.

  Fields:
    stages: Optional. Each stage specifies configuration for a `Target`. The
      ordering of this list defines the promotion flow.
  """

  stages = _messages.MessageField('Stage', 1, repeated=True)


class ServiceNetworking(_messages.Message):
  r"""Information about the Kubernetes Service networking configuration.

  Fields:
    deployment: Required. Name of the Kubernetes Deployment whose traffic is
      managed by the specified Service.
    disablePodOverprovisioning: Optional. Whether to disable Pod
      overprovisioning. If Pod overprovisioning is disabled then Cloud Deploy
      will limit the number of total Pods used for the deployment strategy to
      the number of Pods the Deployment has on the cluster.
    podSelectorLabel: Optional. The label to use when selecting Pods for the
      Deployment resource. This label must already be present in the
      Deployment.
    service: Required. Name of the Kubernetes Service.
  """

  deployment = _messages.StringField(1)
  disablePodOverprovisioning = _messages.BooleanField(2)
  podSelectorLabel = _messages.StringField(3)
  service = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SkaffoldGCBRepoSource(_messages.Message):
  r"""Cloud Build V2 Repository containing Skaffold Configs.

  Fields:
    path: Optional. Relative path from the repository root to the Skaffold
      Config file.
    ref: Optional. Branch or tag to use when cloning the repository.
    repository: Required. Name of the Cloud Build V2 Repository. Format is pro
      jects/{project}/locations/{location}/connections/{connection}/repositori
      es/{repository}.
  """

  path = _messages.StringField(1)
  ref = _messages.StringField(2)
  repository = _messages.StringField(3)


class SkaffoldGCSSource(_messages.Message):
  r"""Cloud Storage bucket containing Skaffold Config modules.

  Fields:
    path: Optional. Relative path from the source to the Skaffold file.
    source: Required. Cloud Storage source paths to copy recursively. For
      example, providing "gs://my-bucket/dir/configs/*" will result in
      Skaffold copying all files within the "dir/configs" directory in the
      bucket "my-bucket".
  """

  path = _messages.StringField(1)
  source = _messages.StringField(2)


class SkaffoldGitSource(_messages.Message):
  r"""Git repository containing Skaffold Config modules.

  Fields:
    path: Optional. Relative path from the repository root to the Skaffold
      file.
    ref: Optional. Git branch or tag to use when cloning the repository.
    repo: Required. Git repository the package should be cloned from.
  """

  path = _messages.StringField(1)
  ref = _messages.StringField(2)
  repo = _messages.StringField(3)


class SkaffoldModules(_messages.Message):
  r"""Skaffold Config modules and their remote source.

  Fields:
    configs: Optional. The Skaffold Config modules to use from the specified
      source.
    git: Optional. Remote git repository containing the Skaffold Config
      modules.
    googleCloudBuildRepo: Optional. Cloud Build V2 repository containing the
      Skaffold Config modules.
    googleCloudStorage: Optional. Cloud Storage bucket containing the Skaffold
      Config modules.
  """

  configs = _messages.StringField(1, repeated=True)
  git = _messages.MessageField('SkaffoldGitSource', 2)
  googleCloudBuildRepo = _messages.MessageField('SkaffoldGCBRepoSource', 3)
  googleCloudStorage = _messages.MessageField('SkaffoldGCSSource', 4)


class SkaffoldSupportedCondition(_messages.Message):
  r"""SkaffoldSupportedCondition contains information about when support for
  the release's version of Skaffold ends.

  Enums:
    SkaffoldSupportStateValueValuesEnum: The Skaffold support state for this
      release's version of Skaffold.

  Fields:
    maintenanceModeTime: The time at which this release's version of Skaffold
      will enter maintenance mode.
    skaffoldSupportState: The Skaffold support state for this release's
      version of Skaffold.
    status: True if the version of Skaffold used by this release is supported.
    supportExpirationTime: The time at which this release's version of
      Skaffold will no longer be supported.
  """

  class SkaffoldSupportStateValueValuesEnum(_messages.Enum):
    r"""The Skaffold support state for this release's version of Skaffold.

    Values:
      SKAFFOLD_SUPPORT_STATE_UNSPECIFIED: Default value. This value is unused.
      SKAFFOLD_SUPPORT_STATE_SUPPORTED: This Skaffold version is currently
        supported.
      SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE: This Skaffold version is in
        maintenance mode.
      SKAFFOLD_SUPPORT_STATE_UNSUPPORTED: This Skaffold version is no longer
        supported.
    """
    SKAFFOLD_SUPPORT_STATE_UNSPECIFIED = 0
    SKAFFOLD_SUPPORT_STATE_SUPPORTED = 1
    SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE = 2
    SKAFFOLD_SUPPORT_STATE_UNSUPPORTED = 3

  maintenanceModeTime = _messages.StringField(1)
  skaffoldSupportState = _messages.EnumField('SkaffoldSupportStateValueValuesEnum', 2)
  status = _messages.BooleanField(3)
  supportExpirationTime = _messages.StringField(4)


class SkaffoldVersion(_messages.Message):
  r"""Details of a supported Skaffold version.

  Fields:
    maintenanceModeTime: The time at which this version of Skaffold will enter
      maintenance mode.
    supportEndDate: Date when this version is expected to no longer be
      supported.
    supportExpirationTime: The time at which this version of Skaffold will no
      longer be supported.
    version: Release version number. For example, "1.20.3".
  """

  maintenanceModeTime = _messages.StringField(1)
  supportEndDate = _messages.MessageField('Date', 2)
  supportExpirationTime = _messages.StringField(3)
  version = _messages.StringField(4)


class Stage(_messages.Message):
  r"""Stage specifies a location to which to deploy.

  Fields:
    deployParameters: Optional. The deploy parameters to use for the target in
      this stage.
    profiles: Optional. Skaffold profiles to use when rendering the manifest
      for this stage's `Target`.
    strategy: Optional. The strategy to use for a `Rollout` to this stage.
    targetId: Optional. The target_id to which this stage points. This field
      refers exclusively to the last segment of a target name. For example,
      this field would just be `my-target` (rather than
      `projects/project/locations/location/targets/my-target`). The location
      of the `Target` is inferred to be the same as the location of the
      `DeliveryPipeline` that contains this `Stage`.
  """

  deployParameters = _messages.MessageField('DeployParameters', 1, repeated=True)
  profiles = _messages.StringField(2, repeated=True)
  strategy = _messages.MessageField('Strategy', 3)
  targetId = _messages.StringField(4)


class Standard(_messages.Message):
  r"""Standard represents the standard deployment strategy.

  Fields:
    analysis: Optional. Configuration for the analysis job. If this is not
      configured, the analysis job will not be present.
    postdeploy: Optional. Configuration for the postdeploy job. If this is not
      configured, the postdeploy job will not be present.
    predeploy: Optional. Configuration for the predeploy job. If this is not
      configured, the predeploy job will not be present.
    verify: Optional. Whether to verify a deployment via `skaffold verify`.
    verifyConfig: Optional. Configuration for the verify job. Cannot be set if
      `verify` is set to true.
  """

  analysis = _messages.MessageField('Analysis', 1)
  postdeploy = _messages.MessageField('Postdeploy', 2)
  predeploy = _messages.MessageField('Predeploy', 3)
  verify = _messages.BooleanField(4)
  verifyConfig = _messages.MessageField('Verify', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Strategy(_messages.Message):
  r"""Strategy contains deployment strategy information.

  Fields:
    canary: Optional. Canary deployment strategy provides progressive
      percentage based deployments to a Target.
    standard: Optional. Standard deployment strategy executes a single deploy
      and allows verifying the deployment.
  """

  canary = _messages.MessageField('Canary', 1)
  standard = _messages.MessageField('Standard', 2)


class Target(_messages.Message):
  r"""A `Target` resource in the Cloud Deploy API. A `Target` defines a
  location to which a Skaffold configuration can be deployed.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    AssociatedEntitiesValue: Optional. Map of entity IDs to their associated
      entities. Associated entities allows specifying places other than the
      deployment target for specific features. For example, the Gateway API
      canary can be configured to deploy the HTTPRoute to a different
      cluster(s) than the deployment cluster using associated entities. An
      entity ID must consist of lower-case letters, numbers, and hyphens,
      start with a letter and end with a letter or a number, and have a max
      length of 63 characters. In other words, it must match the following
      regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    DeployParametersValue: Optional. The deploy parameters to use for this
      target.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    anthosCluster: Optional. Information specifying an Anthos Cluster.
    associatedEntities: Optional. Map of entity IDs to their associated
      entities. Associated entities allows specifying places other than the
      deployment target for specific features. For example, the Gateway API
      canary can be configured to deploy the HTTPRoute to a different
      cluster(s) than the deployment cluster using associated entities. An
      entity ID must consist of lower-case letters, numbers, and hyphens,
      start with a letter and end with a letter or a number, and have a max
      length of 63 characters. In other words, it must match the following
      regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    createTime: Output only. Time at which the `Target` was created.
    customTarget: Optional. Information specifying a Custom Target.
    deployParameters: Optional. The deploy parameters to use for this target.
    description: Optional. Description of the `Target`. Max length is 255
      characters.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    executionConfigs: Optional. Configurations for all execution that relates
      to this `Target`. Each `ExecutionEnvironmentUsage` value may only be
      used in a single configuration; using the same value multiple times is
      an error. When one or more configurations are specified, they must
      include the `RENDER` and `DEPLOY` `ExecutionEnvironmentUsage` values.
      When no configurations are specified, execution will use the default
      specified in `DefaultPool`.
    gke: Optional. Information specifying a GKE Cluster.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.
    multiTarget: Optional. Information specifying a multiTarget.
    name: Identifier. Name of the `Target`. Format is
      `projects/{project}/locations/{location}/targets/{target}`. The `target`
      component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
    requireApproval: Optional. Whether or not the `Target` requires approval.
    run: Optional. Information specifying a Cloud Run deployment target.
    targetId: Output only. Resource id of the `Target`.
    uid: Output only. Unique identifier of the `Target`.
    updateTime: Output only. Most recent time at which the `Target` was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AssociatedEntitiesValue(_messages.Message):
    r"""Optional. Map of entity IDs to their associated entities. Associated
    entities allows specifying places other than the deployment target for
    specific features. For example, the Gateway API canary can be configured
    to deploy the HTTPRoute to a different cluster(s) than the deployment
    cluster using associated entities. An entity ID must consist of lower-case
    letters, numbers, and hyphens, start with a letter and end with a letter
    or a number, and have a max length of 63 characters. In other words, it
    must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.

    Messages:
      AdditionalProperty: An additional property for a AssociatedEntitiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AssociatedEntitiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AssociatedEntitiesValue object.

      Fields:
        key: Name of the additional property.
        value: A AssociatedEntities attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AssociatedEntities', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeployParametersValue(_messages.Message):
    r"""Optional. The deploy parameters to use for this target.

    Messages:
      AdditionalProperty: An additional property for a DeployParametersValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeployParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeployParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  anthosCluster = _messages.MessageField('AnthosCluster', 2)
  associatedEntities = _messages.MessageField('AssociatedEntitiesValue', 3)
  createTime = _messages.StringField(4)
  customTarget = _messages.MessageField('CustomTarget', 5)
  deployParameters = _messages.MessageField('DeployParametersValue', 6)
  description = _messages.StringField(7)
  etag = _messages.StringField(8)
  executionConfigs = _messages.MessageField('ExecutionConfig', 9, repeated=True)
  gke = _messages.MessageField('GkeCluster', 10)
  labels = _messages.MessageField('LabelsValue', 11)
  multiTarget = _messages.MessageField('MultiTarget', 12)
  name = _messages.StringField(13)
  requireApproval = _messages.BooleanField(14)
  run = _messages.MessageField('CloudRunLocation', 15)
  targetId = _messages.StringField(16)
  uid = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class TargetArtifact(_messages.Message):
  r"""The artifacts produced by a target render operation.

  Messages:
    PhaseArtifactsValue: Output only. Map from the phase ID to the phase
      artifacts for the `Target`.

  Fields:
    artifactUri: Output only. URI of a directory containing the artifacts.
      This contains deployment configuration used by Skaffold during a
      rollout, and all paths are relative to this location.
    deployConfigPath: Output only. File path of the resolved Deploy Config for
      the stable phase, relative to the URI. Only one of deploy_config_path or
      skaffold_config_path will be set.
    manifestPath: Output only. File path of the rendered manifest relative to
      the URI for the stable phase.
    phaseArtifacts: Output only. Map from the phase ID to the phase artifacts
      for the `Target`.
    skaffoldConfigPath: Output only. File path of the resolved Skaffold
      configuration for the stable phase, relative to the URI.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PhaseArtifactsValue(_messages.Message):
    r"""Output only. Map from the phase ID to the phase artifacts for the
    `Target`.

    Messages:
      AdditionalProperty: An additional property for a PhaseArtifactsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PhaseArtifactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PhaseArtifactsValue object.

      Fields:
        key: Name of the additional property.
        value: A PhaseArtifact attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PhaseArtifact', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  artifactUri = _messages.StringField(1)
  deployConfigPath = _messages.StringField(2)
  manifestPath = _messages.StringField(3)
  phaseArtifacts = _messages.MessageField('PhaseArtifactsValue', 4)
  skaffoldConfigPath = _messages.StringField(5)


class TargetAttribute(_messages.Message):
  r"""Contains criteria for selecting Targets. This could be used to select
  targets for a Deploy Policy or for an Automation.

  Messages:
    LabelsValue: Target labels.

  Fields:
    id: Optional. ID of the `Target`. The value of this field could be one of
      the following: * The last segment of a target name * "*", all targets in
      a location
    labels: Target labels.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Target labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)


class TargetNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/target_notification"
  Platform Log event that describes the failure to send target status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    target: The name of the `Target`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_ROLLOUT_UPDATE: Rollout updated.
      TYPE_DEPLOY_POLICY_EVALUATION: Deploy Policy evaluation.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_ROLLOUT_UPDATE = 6
    TYPE_DEPLOY_POLICY_EVALUATION = 7
    TYPE_RENDER_STATUES_CHANGE = 8

  message = _messages.StringField(1)
  target = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class TargetRender(_messages.Message):
  r"""Details of rendering for a single target.

  Enums:
    FailureCauseValueValuesEnum: Output only. Reason this render failed. This
      will always be unspecified while the render in progress.
    RenderingStateValueValuesEnum: Output only. Current state of the render
      operation for this Target.

  Fields:
    failureCause: Output only. Reason this render failed. This will always be
      unspecified while the render in progress.
    failureMessage: Output only. Additional information about the render
      failure, if available.
    metadata: Output only. Metadata related to the `Release` render for this
      Target.
    renderingBuild: Output only. The resource name of the Cloud Build `Build`
      object that is used to render the manifest for this target. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    renderingState: Output only. Current state of the render operation for
      this Target.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. Reason this render failed. This will always be
    unspecified while the render in progress.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The render operation did not complete successfully;
        check Cloud Build logs.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
      VERIFICATION_CONFIG_NOT_FOUND: The render operation did not complete
        successfully because the verification stanza required for verify was
        not found on the Skaffold configuration.
      CUSTOM_ACTION_NOT_FOUND: The render operation did not complete
        successfully because the custom action(s) required for Rollout jobs
        were not found in the Skaffold configuration. See failure_message for
        additional details.
      DEPLOYMENT_STRATEGY_NOT_SUPPORTED: Release failed during rendering
        because the release configuration is not supported with the specified
        deployment strategy.
      RENDER_FEATURE_NOT_SUPPORTED: The render operation had a feature
        configured that is not supported.
      TASK_NOT_FOUND: The render operation did not complete successfully
        because the task(s) required for Rollout jobs were not found in the
        configuration file. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    CLOUD_BUILD_REQUEST_FAILED = 3
    VERIFICATION_CONFIG_NOT_FOUND = 4
    CUSTOM_ACTION_NOT_FOUND = 5
    DEPLOYMENT_STRATEGY_NOT_SUPPORTED = 6
    RENDER_FEATURE_NOT_SUPPORTED = 7
    TASK_NOT_FOUND = 8

  class RenderingStateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the render operation for this Target.

    Values:
      TARGET_RENDER_STATE_UNSPECIFIED: The render operation state is
        unspecified.
      SUCCEEDED: The render operation has completed successfully.
      FAILED: The render operation has failed.
      IN_PROGRESS: The render operation is in progress.
    """
    TARGET_RENDER_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3

  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 1)
  failureMessage = _messages.StringField(2)
  metadata = _messages.MessageField('RenderMetadata', 3)
  renderingBuild = _messages.StringField(4)
  renderingState = _messages.EnumField('RenderingStateValueValuesEnum', 5)


class Targets(_messages.Message):
  r"""The targets involved in a single timed promotion.

  Fields:
    destinationTargetId: Optional. The destination target ID.
    sourceTargetId: Optional. The source target ID.
  """

  destinationTargetId = _messages.StringField(1)
  sourceTargetId = _messages.StringField(2)


class TargetsPresentCondition(_messages.Message):
  r"""`TargetsPresentCondition` contains information on any Targets referenced
  in the Delivery Pipeline that do not actually exist.

  Fields:
    missingTargets: The list of Target names that do not exist. For example,
      `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
    status: True if there aren't any missing Targets.
    updateTime: Last time the condition was updated.
  """

  missingTargets = _messages.StringField(1, repeated=True)
  status = _messages.BooleanField(2)
  updateTime = _messages.StringField(3)


class TargetsTypeCondition(_messages.Message):
  r"""TargetsTypeCondition contains information on whether the Targets defined
  in the Delivery Pipeline are of the same type.

  Fields:
    errorDetails: Human readable error message.
    status: True if the targets are all a comparable type. For example this is
      true if all targets are GKE clusters. This is false if some targets are
      Cloud Run targets and others are GKE clusters.
  """

  errorDetails = _messages.StringField(1)
  status = _messages.BooleanField(2)


class Task(_messages.Message):
  r"""A Task represents a unit of work that is executed as part of a Job.

  Fields:
    config: Optional. This task is represented by either a task in the Deploy
      Config or a custom action in the Skaffold Config.
    containersTask: Optional. This task is represented by a set of containers
      that are executed in parallel in the Cloud Build execution environment.
  """

  config = _messages.MessageField('ConfigTask', 1)
  containersTask = _messages.MessageField('ContainersTask', 2)


class TerminateJobRunRequest(_messages.Message):
  r"""The request object used by `TerminateJobRun`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)


class TerminateJobRunResponse(_messages.Message):
  r"""The response object from `TerminateJobRun`."""


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeWindows(_messages.Message):
  r"""Time windows within which actions are restricted. See the
  [documentation](https://cloud.google.com/deploy/docs/deploy-
  policy#dates_times) for more information on how to configure dates/times.

  Fields:
    oneTimeWindows: Optional. One-time windows within which actions are
      restricted.
    timeZone: Required. The time zone in IANA format [IANA Time Zone
      Database](https://www.iana.org/time-zones) (e.g. America/New_York).
    weeklyWindows: Optional. Recurring weekly windows within which actions are
      restricted.
  """

  oneTimeWindows = _messages.MessageField('OneTimeWindow', 1, repeated=True)
  timeZone = _messages.StringField(2)
  weeklyWindows = _messages.MessageField('WeeklyWindow', 3, repeated=True)


class TimedPromoteReleaseCondition(_messages.Message):
  r"""`TimedPromoteReleaseCondition` contains conditions specific to an
  Automation with a Timed Promote Release rule defined.

  Fields:
    nextPromotionTime: Output only. When the next scheduled promotion(s) will
      occur.
    targetsList: Output only. A list of targets involved in the upcoming timed
      promotion(s).
  """

  nextPromotionTime = _messages.StringField(1)
  targetsList = _messages.MessageField('Targets', 2, repeated=True)


class TimedPromoteReleaseOperation(_messages.Message):
  r"""Contains the information of an automated timed promote-release
  operation.

  Fields:
    phase: Output only. The starting phase of the rollout created by this
      operation.
    release: Output only. The name of the release to be promoted.
    targetId: Output only. The ID of the target that represents the promotion
      stage to which the release will be promoted. The value of this field is
      the last segment of a target name.
  """

  phase = _messages.StringField(1)
  release = _messages.StringField(2)
  targetId = _messages.StringField(3)


class TimedPromoteReleaseRule(_messages.Message):
  r"""The `TimedPromoteReleaseRule` will automatically promote a release from
  the current target(s) to the specified target(s) on a configured schedule.

  Fields:
    condition: Output only. Information around the state of the Automation
      rule.
    destinationPhase: Optional. The starting phase of the rollout created by
      this rule. Default to the first phase.
    destinationTargetId: Optional. The ID of the stage in the pipeline to
      which this `Release` is deploying. If unspecified, default it to the
      next stage in the promotion flow. The value of this field could be one
      of the following: * The last segment of a target name * "@next", the
      next target in the promotion sequence
    id: Required. ID of the rule. This ID must be unique in the `Automation`
      resource to which this rule belongs. The format is
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
    schedule: Required. Schedule in crontab format. e.g. "0 9 * * 1" for every
      Monday at 9am.
    timeZone: Required. The time zone in IANA format [IANA Time Zone
      Database](https://www.iana.org/time-zones) (e.g. America/New_York).
  """

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  destinationPhase = _messages.StringField(2)
  destinationTargetId = _messages.StringField(3)
  id = _messages.StringField(4)
  schedule = _messages.StringField(5)
  timeZone = _messages.StringField(6)


class Verify(_messages.Message):
  r"""Verify contains the verify job configuration information.

  Fields:
    tasks: Optional. The tasks that will run as a part of the verify job. The
      tasks are executed sequentially in the order specified.
  """

  tasks = _messages.MessageField('Task', 1, repeated=True)


class VerifyJob(_messages.Message):
  r"""A verify Job.

  Fields:
    tasks: Output only. The tasks that are executed as part of the verify Job.
  """

  tasks = _messages.MessageField('Task', 1, repeated=True)


class VerifyJobRun(_messages.Message):
  r"""VerifyJobRun contains information specific to a verify `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the verify failed.
      This will always be unspecified while the verify is in progress or if it
      succeeded.

  Fields:
    artifactUri: Output only. URI of a directory containing the verify
      artifacts. This contains the Skaffold event log.
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to verify. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    eventLogPath: Output only. File path of the Skaffold event log relative to
      the artifact URI.
    failureCause: Output only. The reason the verify failed. This will always
      be unspecified while the verify is in progress or if it succeeded.
    failureMessage: Output only. Additional information about the verify
      failure, if available.
    metadata: Output only. Metadata containing information about the verify
      `JobRun`.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the verify failed. This will always be
    unspecified while the verify is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The verify operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The verify job run did not complete within the
        allotted time.
      VERIFICATION_CONFIG_NOT_FOUND: No Skaffold verify configuration was
        found.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    VERIFICATION_CONFIG_NOT_FOUND = 4
    CLOUD_BUILD_REQUEST_FAILED = 5

  artifactUri = _messages.StringField(1)
  build = _messages.StringField(2)
  eventLogPath = _messages.StringField(3)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 4)
  failureMessage = _messages.StringField(5)
  metadata = _messages.MessageField('VerifyJobRunMetadata', 6)


class VerifyJobRunMetadata(_messages.Message):
  r"""VerifyJobRunMetadata contains metadata about the verify `JobRun`.

  Fields:
    custom: Output only. Custom metadata provided by user-defined verify
      operation.
  """

  custom = _messages.MessageField('CustomMetadata', 1)


class WeeklyWindow(_messages.Message):
  r"""Weekly windows. For example, blocking actions every Saturday and Sunday.
  Another example would be blocking actions every weekday from 5pm to
  midnight.

  Enums:
    DaysOfWeekValueListEntryValuesEnum:

  Fields:
    daysOfWeek: Optional. Days of week. If left empty, all days of the week
      will be included.
    endTime: Optional. End time (exclusive). Use 24:00 to indicate midnight.
      If you specify end_time you must also specify start_time. If left empty,
      this will block for the entire day for the days specified in
      days_of_week.
    startTime: Optional. Start time (inclusive). Use 00:00 for the beginning
      of the day. If you specify start_time you must also specify end_time. If
      left empty, this will block for the entire day for the days specified in
      days_of_week.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 1, repeated=True)
  endTime = _messages.MessageField('TimeOfDay', 2)
  startTime = _messages.MessageField('TimeOfDay', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ClouddeployProjectsLocationsCustomTargetTypesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ClouddeployProjectsLocationsDeployPoliciesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ClouddeployProjectsLocationsTargetsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
