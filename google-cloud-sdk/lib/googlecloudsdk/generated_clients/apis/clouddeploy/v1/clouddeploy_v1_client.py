"""Generated client library for clouddeploy version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.clouddeploy.v1 import clouddeploy_v1_messages as messages


class ClouddeployV1(base_api.BaseApiClient):
  """Generated client library for service clouddeploy version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://clouddeploy.googleapis.com/'
  MTLS_BASE_URL = 'https://clouddeploy.mtls.googleapis.com/'

  _PACKAGE = 'clouddeploy'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ClouddeployV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new clouddeploy handle."""
    url = url or self.BASE_URL
    super(ClouddeployV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_customTargetTypes = self.ProjectsLocationsCustomTargetTypesService(self)
    self.projects_locations_deliveryPipelines_automationRuns = self.ProjectsLocationsDeliveryPipelinesAutomationRunsService(self)
    self.projects_locations_deliveryPipelines_automations = self.ProjectsLocationsDeliveryPipelinesAutomationsService(self)
    self.projects_locations_deliveryPipelines_releases_rollouts_jobRuns = self.ProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsService(self)
    self.projects_locations_deliveryPipelines_releases_rollouts = self.ProjectsLocationsDeliveryPipelinesReleasesRolloutsService(self)
    self.projects_locations_deliveryPipelines_releases = self.ProjectsLocationsDeliveryPipelinesReleasesService(self)
    self.projects_locations_deliveryPipelines = self.ProjectsLocationsDeliveryPipelinesService(self)
    self.projects_locations_deployPolicies = self.ProjectsLocationsDeployPoliciesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_targets = self.ProjectsLocationsTargetsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsCustomTargetTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_customTargetTypes resource."""

    _NAME = 'projects_locations_customTargetTypes'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsCustomTargetTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new CustomTargetType in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes',
        http_method='POST',
        method_id='clouddeploy.projects.locations.customTargetTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customTargetTypeId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/customTargetTypes',
        request_field='customTargetType',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single CustomTargetType.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.customTargetTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single CustomTargetType.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CustomTargetType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.customTargetTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesGetRequest',
        response_type_name='CustomTargetType',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}:getIamPolicy',
        http_method='GET',
        method_id='clouddeploy.projects.locations.customTargetTypes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CustomTargetTypes in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCustomTargetTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes',
        http_method='GET',
        method_id='clouddeploy.projects.locations.customTargetTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customTargetTypes',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesListRequest',
        response_type_name='ListCustomTargetTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single CustomTargetType.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}',
        http_method='PATCH',
        method_id='clouddeploy.projects.locations.customTargetTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='customTargetType',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ClouddeployProjectsLocationsCustomTargetTypesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}:setIamPolicy',
        http_method='POST',
        method_id='clouddeploy.projects.locations.customTargetTypes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ClouddeployProjectsLocationsCustomTargetTypesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesAutomationRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines_automationRuns resource."""

    _NAME = 'projects_locations_deliveryPipelines_automationRuns'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesAutomationRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels an AutomationRun. The `state` of the `AutomationRun` after cancelling is `CANCELLED`. `CancelAutomationRun` can be called on AutomationRun in the state `IN_PROGRESS` and `PENDING`; AutomationRun in a different state returns an `FAILED_PRECONDITION` error.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CancelAutomationRunResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns/{automationRunsId}:cancel',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automationRuns.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelAutomationRunRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest',
        response_type_name='CancelAutomationRunResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AutomationRun.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutomationRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns/{automationRunsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automationRuns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest',
        response_type_name='AutomationRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AutomationRuns in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutomationRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automationRuns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/automationRuns',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest',
        response_type_name='ListAutomationRunsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesAutomationsService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines_automations resource."""

    _NAME = 'projects_locations_deliveryPipelines_automations'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesAutomationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Automation in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['automationId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/automations',
        request_field='automation',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Automation resource.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Automation.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Automation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest',
        response_type_name='Automation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Automations in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutomationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/automations',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest',
        response_type_name='ListAutomationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Automation resource.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}',
        http_method='PATCH',
        method_id='clouddeploy.projects.locations.deliveryPipelines.automations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='automation',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines_releases_rollouts_jobRuns resource."""

    _NAME = 'projects_locations_deliveryPipelines_releases_rollouts_jobRuns'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single JobRun.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (JobRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns/{jobRunsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGetRequest',
        response_type_name='JobRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists JobRuns in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListJobRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/jobRuns',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsListRequest',
        response_type_name='ListJobRunsResponse',
        supports_download=False,
    )

    def Terminate(self, request, global_params=None):
      r"""Terminates a Job Run in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTerminateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TerminateJobRunResponse) The response message.
      """
      config = self.GetMethodConfig('Terminate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Terminate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns/{jobRunsId}:terminate',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.terminate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:terminate',
        request_field='terminateJobRunRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTerminateRequest',
        response_type_name='TerminateJobRunResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesReleasesRolloutsService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines_releases_rollouts resource."""

    _NAME = 'projects_locations_deliveryPipelines_releases_rollouts'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesReleasesRolloutsService, self).__init__(client)
      self._upload_configs = {
          }

    def Advance(self, request, global_params=None):
      r"""Advances a Rollout in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AdvanceRolloutResponse) The response message.
      """
      config = self.GetMethodConfig('Advance')
      return self._RunMethod(
          config, request, global_params=global_params)

    Advance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:advance',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.advance',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:advance',
        request_field='advanceRolloutRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest',
        response_type_name='AdvanceRolloutResponse',
        supports_download=False,
    )

    def Approve(self, request, global_params=None):
      r"""Approves a Rollout.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRolloutResponse) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:approve',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:approve',
        request_field='approveRolloutRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest',
        response_type_name='ApproveRolloutResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Cancels a Rollout in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CancelRolloutResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:cancel',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelRolloutRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest',
        response_type_name='CancelRolloutResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Rollout in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['overrideDeployPolicy', 'requestId', 'rolloutId', 'startingPhaseId', 'validateOnly'],
        relative_path='v1/{+parent}/rollouts',
        request_field='rollout',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Rollout.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Rollout) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest',
        response_type_name='Rollout',
        supports_download=False,
    )

    def IgnoreJob(self, request, global_params=None):
      r"""Ignores the specified Job in a Rollout.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJobRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IgnoreJobResponse) The response message.
      """
      config = self.GetMethodConfig('IgnoreJob')
      return self._RunMethod(
          config, request, global_params=global_params)

    IgnoreJob.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:ignoreJob',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.ignoreJob',
        ordered_params=['rollout'],
        path_params=['rollout'],
        query_params=[],
        relative_path='v1/{+rollout}:ignoreJob',
        request_field='ignoreJobRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJobRequest',
        response_type_name='IgnoreJobResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Rollouts in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolloutsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/rollouts',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest',
        response_type_name='ListRolloutsResponse',
        supports_download=False,
    )

    def RetryJob(self, request, global_params=None):
      r"""Retries the specified Job in a Rollout.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RetryJobResponse) The response message.
      """
      config = self.GetMethodConfig('RetryJob')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetryJob.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:retryJob',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.retryJob',
        ordered_params=['rollout'],
        path_params=['rollout'],
        query_params=[],
        relative_path='v1/{+rollout}:retryJob',
        request_field='retryJobRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest',
        response_type_name='RetryJobResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesReleasesService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines_releases resource."""

    _NAME = 'projects_locations_deliveryPipelines_releases'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesReleasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Abandon(self, request, global_params=None):
      r"""Abandons a Release in the Delivery Pipeline.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AbandonReleaseResponse) The response message.
      """
      config = self.GetMethodConfig('Abandon')
      return self._RunMethod(
          config, request, global_params=global_params)

    Abandon.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}:abandon',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.abandon',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:abandon',
        request_field='abandonReleaseRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest',
        response_type_name='AbandonReleaseResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Release in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['overrideDeployPolicy', 'releaseId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/releases',
        request_field='release',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Release.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Release) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest',
        response_type_name='Release',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Releases in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReleasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.releases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/releases',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest',
        response_type_name='ListReleasesResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeliveryPipelinesService(base_api.BaseApiService):
    """Service class for the projects_locations_deliveryPipelines resource."""

    _NAME = 'projects_locations_deliveryPipelines'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeliveryPipelinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DeliveryPipeline in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deliveryPipelineId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/deliveryPipelines',
        request_field='deliveryPipeline',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single DeliveryPipeline.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.deliveryPipelines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'requestId', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single DeliveryPipeline.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DeliveryPipeline) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesGetRequest',
        response_type_name='DeliveryPipeline',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:getIamPolicy',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeliveryPipelines in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDeliveryPipelinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deliveryPipelines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/deliveryPipelines',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesListRequest',
        response_type_name='ListDeliveryPipelinesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single DeliveryPipeline.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}',
        http_method='PATCH',
        method_id='clouddeploy.projects.locations.deliveryPipelines.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='deliveryPipeline',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RollbackTarget(self, request, global_params=None):
      r"""Creates a `Rollout` to roll back the specified target.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RollbackTargetResponse) The response message.
      """
      config = self.GetMethodConfig('RollbackTarget')
      return self._RunMethod(
          config, request, global_params=global_params)

    RollbackTarget.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:rollbackTarget',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.rollbackTarget',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rollbackTarget',
        request_field='rollbackTargetRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest',
        response_type_name='RollbackTargetResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:setIamPolicy',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:testIamPermissions',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deliveryPipelines.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeployPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_deployPolicies resource."""

    _NAME = 'projects_locations_deployPolicies'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsDeployPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DeployPolicy in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deployPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deployPolicyId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/deployPolicies',
        request_field='deployPolicy',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single DeployPolicy.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.deployPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single DeployPolicy.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DeployPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deployPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesGetRequest',
        response_type_name='DeployPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deployPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeployPolicies in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDeployPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies',
        http_method='GET',
        method_id='clouddeploy.projects.locations.deployPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/deployPolicies',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesListRequest',
        response_type_name='ListDeployPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single DeployPolicy.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}',
        http_method='PATCH',
        method_id='clouddeploy.projects.locations.deployPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='deployPolicy',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ClouddeployProjectsLocationsDeployPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='clouddeploy.projects.locations.deployPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ClouddeployProjectsLocationsDeployPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ClouddeployProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='clouddeploy.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='ClouddeployProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ClouddeployProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ClouddeployProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ClouddeployProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='clouddeploy.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTargetsService(base_api.BaseApiService):
    """Service class for the projects_locations_targets resource."""

    _NAME = 'projects_locations_targets'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsTargetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Target in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsTargetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets',
        http_method='POST',
        method_id='clouddeploy.projects.locations.targets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'targetId', 'validateOnly'],
        relative_path='v1/{+parent}/targets',
        request_field='target',
        request_type_name='ClouddeployProjectsLocationsTargetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Target.

      Args:
        request: (ClouddeployProjectsLocationsTargetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}',
        http_method='DELETE',
        method_id='clouddeploy.projects.locations.targets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsTargetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Target.

      Args:
        request: (ClouddeployProjectsLocationsTargetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Target) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.targets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsTargetsGetRequest',
        response_type_name='Target',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ClouddeployProjectsLocationsTargetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:getIamPolicy',
        http_method='GET',
        method_id='clouddeploy.projects.locations.targets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsTargetsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Targets in a given project and location.

      Args:
        request: (ClouddeployProjectsLocationsTargetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTargetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets',
        http_method='GET',
        method_id='clouddeploy.projects.locations.targets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/targets',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsTargetsListRequest',
        response_type_name='ListTargetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Target.

      Args:
        request: (ClouddeployProjectsLocationsTargetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}',
        http_method='PATCH',
        method_id='clouddeploy.projects.locations.targets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='target',
        request_type_name='ClouddeployProjectsLocationsTargetsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ClouddeployProjectsLocationsTargetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:setIamPolicy',
        http_method='POST',
        method_id='clouddeploy.projects.locations.targets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ClouddeployProjectsLocationsTargetsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:testIamPermissions',
        http_method='POST',
        method_id='clouddeploy.projects.locations.targets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ClouddeployProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='clouddeploy.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetConfig(self, request, global_params=None):
      r"""Gets the configuration for a location.

      Args:
        request: (ClouddeployProjectsLocationsGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Config) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/config',
        http_method='GET',
        method_id='clouddeploy.projects.locations.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsGetConfigRequest',
        response_type_name='Config',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ClouddeployProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='clouddeploy.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='ClouddeployProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ClouddeployV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
