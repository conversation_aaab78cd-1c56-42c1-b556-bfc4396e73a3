"""Generated client library for auditmanager version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.auditmanager.v1 import auditmanager_v1_messages as messages


class AuditmanagerV1(base_api.BaseApiClient):
  """Generated client library for service auditmanager version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://auditmanager.googleapis.com/'
  MTLS_BASE_URL = 'https://auditmanager.mtls.googleapis.com/'

  _PACKAGE = 'auditmanager'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AuditmanagerV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new auditmanager handle."""
    url = url or self.BASE_URL
    super(AuditmanagerV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_auditReports = self.FoldersLocationsAuditReportsService(self)
    self.folders_locations_auditScopeReports = self.FoldersLocationsAuditScopeReportsService(self)
    self.folders_locations_operationDetails = self.FoldersLocationsOperationDetailsService(self)
    self.folders_locations_operationIds = self.FoldersLocationsOperationIdsService(self)
    self.folders_locations_resourceEnrollmentStatuses = self.FoldersLocationsResourceEnrollmentStatusesService(self)
    self.folders_locations_standards_controls = self.FoldersLocationsStandardsControlsService(self)
    self.folders_locations_standards = self.FoldersLocationsStandardsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_auditReports = self.OrganizationsLocationsAuditReportsService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_resourceEnrollmentStatuses = self.OrganizationsLocationsResourceEnrollmentStatusesService(self)
    self.organizations_locations_standards_controls = self.OrganizationsLocationsStandardsControlsService(self)
    self.organizations_locations_standards = self.OrganizationsLocationsStandardsService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_auditReports = self.ProjectsLocationsAuditReportsService(self)
    self.projects_locations_auditScopeReports = self.ProjectsLocationsAuditScopeReportsService(self)
    self.projects_locations_operationDetails = self.ProjectsLocationsOperationDetailsService(self)
    self.projects_locations_operationIds = self.ProjectsLocationsOperationIdsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_resourceEnrollmentStatuses = self.ProjectsLocationsResourceEnrollmentStatusesService(self)
    self.projects_locations_standards_controls = self.ProjectsLocationsStandardsControlsService(self)
    self.projects_locations_standards = self.ProjectsLocationsStandardsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsAuditReportsService(base_api.BaseApiService):
    """Service class for the folders_locations_auditReports resource."""

    _NAME = 'folders_locations_auditReports'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsAuditReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Generate(self, request, global_params=None):
      r"""Register the Audit Report generation requests and returns the OperationId using which the customer can track the report generation progress.

      Args:
        request: (AuditmanagerFoldersLocationsAuditReportsGenerateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Generate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Generate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/auditReports:generate',
        http_method='POST',
        method_id='auditmanager.folders.locations.auditReports.generate',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}/auditReports:generate',
        request_field='generateAuditReportRequest',
        request_type_name='AuditmanagerFoldersLocationsAuditReportsGenerateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the overall audit report.

      Args:
        request: (AuditmanagerFoldersLocationsAuditReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuditReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/auditReports/{auditReportsId}',
        http_method='GET',
        method_id='auditmanager.folders.locations.auditReports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsAuditReportsGetRequest',
        response_type_name='AuditReport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists audit reports in the selected parent scope.

      Args:
        request: (AuditmanagerFoldersLocationsAuditReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuditReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/auditReports',
        http_method='GET',
        method_id='auditmanager.folders.locations.auditReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/auditReports',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsAuditReportsListRequest',
        response_type_name='ListAuditReportsResponse',
        supports_download=False,
    )

  class FoldersLocationsAuditScopeReportsService(base_api.BaseApiService):
    """Service class for the folders_locations_auditScopeReports resource."""

    _NAME = 'folders_locations_auditScopeReports'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsAuditScopeReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Generate(self, request, global_params=None):
      r"""Generates a demo report highlighting different responsibilities (Google/Customer/ shared) required to be fulfilled for the customer's workload to be compliant with the given standard.

      Args:
        request: (AuditmanagerFoldersLocationsAuditScopeReportsGenerateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuditScopeReport) The response message.
      """
      config = self.GetMethodConfig('Generate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Generate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/auditScopeReports:generate',
        http_method='POST',
        method_id='auditmanager.folders.locations.auditScopeReports.generate',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}/auditScopeReports:generate',
        request_field='generateAuditScopeReportRequest',
        request_type_name='AuditmanagerFoldersLocationsAuditScopeReportsGenerateRequest',
        response_type_name='AuditScopeReport',
        supports_download=False,
    )

  class FoldersLocationsOperationDetailsService(base_api.BaseApiService):
    """Service class for the folders_locations_operationDetails resource."""

    _NAME = 'folders_locations_operationDetails'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsOperationDetailsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about generate audit report operation.

      Args:
        request: (AuditmanagerFoldersLocationsOperationDetailsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/operationDetails/{operationDetailsId}',
        http_method='GET',
        method_id='auditmanager.folders.locations.operationDetails.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsOperationDetailsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsOperationIdsService(base_api.BaseApiService):
    """Service class for the folders_locations_operationIds resource."""

    _NAME = 'folders_locations_operationIds'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsOperationIdsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about generate audit report operation.

      Args:
        request: (AuditmanagerFoldersLocationsOperationIdsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/operationIds/{operationIdsId}',
        http_method='GET',
        method_id='auditmanager.folders.locations.operationIds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsOperationIdsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsResourceEnrollmentStatusesService(base_api.BaseApiService):
    """Service class for the folders_locations_resourceEnrollmentStatuses resource."""

    _NAME = 'folders_locations_resourceEnrollmentStatuses'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsResourceEnrollmentStatusesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a resource along with its enrollment status.

      Args:
        request: (AuditmanagerFoldersLocationsResourceEnrollmentStatusesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceEnrollmentStatus) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/resourceEnrollmentStatuses/{resourceEnrollmentStatusesId}',
        http_method='GET',
        method_id='auditmanager.folders.locations.resourceEnrollmentStatuses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsResourceEnrollmentStatusesGetRequest',
        response_type_name='ResourceEnrollmentStatus',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Fetches all resources under the parent along with their enrollment.

      Args:
        request: (AuditmanagerFoldersLocationsResourceEnrollmentStatusesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResourceEnrollmentStatusesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/resourceEnrollmentStatuses',
        http_method='GET',
        method_id='auditmanager.folders.locations.resourceEnrollmentStatuses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/resourceEnrollmentStatuses',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsResourceEnrollmentStatusesListRequest',
        response_type_name='ListResourceEnrollmentStatusesResponse',
        supports_download=False,
    )

  class FoldersLocationsStandardsControlsService(base_api.BaseApiService):
    """Service class for the folders_locations_standards_controls resource."""

    _NAME = 'folders_locations_standards_controls'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsStandardsControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Gets controls needed to be implemented to be compliant to a standard.

      Args:
        request: (AuditmanagerFoldersLocationsStandardsControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListControlsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/standards/{standardsId}/controls',
        http_method='GET',
        method_id='auditmanager.folders.locations.standards.controls.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/controls',
        request_field='',
        request_type_name='AuditmanagerFoldersLocationsStandardsControlsListRequest',
        response_type_name='ListControlsResponse',
        supports_download=False,
    )

  class FoldersLocationsStandardsService(base_api.BaseApiService):
    """Service class for the folders_locations_standards resource."""

    _NAME = 'folders_locations_standards'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsStandardsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def EnrollResource(self, request, global_params=None):
      r"""Enrolls the customer resource(folder/project/organization) to the audit manager service by creating the audit managers P4SA in customers workload and granting required permissions to the P4SA. Please note that if enrollment request is made on the already enrolled workload then enrollment is executed overriding the existing set of destinations. As per https://google.aip.dev/127 recommendation, we are having multiple URI binding for Enroll API.

      Args:
        request: (AuditmanagerFoldersLocationsEnrollResourceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Enrollment) The response message.
      """
      config = self.GetMethodConfig('EnrollResource')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnrollResource.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}:enrollResource',
        http_method='POST',
        method_id='auditmanager.folders.locations.enrollResource',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}:enrollResource',
        request_field='enrollResourceRequest',
        request_type_name='AuditmanagerFoldersLocationsEnrollResourceRequest',
        response_type_name='Enrollment',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(AuditmanagerV1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsAuditReportsService(base_api.BaseApiService):
    """Service class for the organizations_locations_auditReports resource."""

    _NAME = 'organizations_locations_auditReports'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsAuditReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists audit reports in the selected parent scope.

      Args:
        request: (AuditmanagerOrganizationsLocationsAuditReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuditReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/auditReports',
        http_method='GET',
        method_id='auditmanager.organizations.locations.auditReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/auditReports',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsAuditReportsListRequest',
        response_type_name='ListAuditReportsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (AuditmanagerOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='auditmanager.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='AuditmanagerOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AuditmanagerOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='auditmanager.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AuditmanagerOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='auditmanager.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AuditmanagerOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='auditmanager.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsResourceEnrollmentStatusesService(base_api.BaseApiService):
    """Service class for the organizations_locations_resourceEnrollmentStatuses resource."""

    _NAME = 'organizations_locations_resourceEnrollmentStatuses'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsResourceEnrollmentStatusesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a resource along with its enrollment status.

      Args:
        request: (AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceEnrollmentStatus) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/resourceEnrollmentStatuses/{resourceEnrollmentStatusesId}',
        http_method='GET',
        method_id='auditmanager.organizations.locations.resourceEnrollmentStatuses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesGetRequest',
        response_type_name='ResourceEnrollmentStatus',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Fetches all resources under the parent along with their enrollment.

      Args:
        request: (AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResourceEnrollmentStatusesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/resourceEnrollmentStatuses',
        http_method='GET',
        method_id='auditmanager.organizations.locations.resourceEnrollmentStatuses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/resourceEnrollmentStatuses',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesListRequest',
        response_type_name='ListResourceEnrollmentStatusesResponse',
        supports_download=False,
    )

  class OrganizationsLocationsStandardsControlsService(base_api.BaseApiService):
    """Service class for the organizations_locations_standards_controls resource."""

    _NAME = 'organizations_locations_standards_controls'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsStandardsControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Gets controls needed to be implemented to be compliant to a standard.

      Args:
        request: (AuditmanagerOrganizationsLocationsStandardsControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListControlsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/standards/{standardsId}/controls',
        http_method='GET',
        method_id='auditmanager.organizations.locations.standards.controls.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/controls',
        request_field='',
        request_type_name='AuditmanagerOrganizationsLocationsStandardsControlsListRequest',
        response_type_name='ListControlsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsStandardsService(base_api.BaseApiService):
    """Service class for the organizations_locations_standards resource."""

    _NAME = 'organizations_locations_standards'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsStandardsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def EnrollResource(self, request, global_params=None):
      r"""Enrolls the customer resource(folder/project/organization) to the audit manager service by creating the audit managers P4SA in customers workload and granting required permissions to the P4SA. Please note that if enrollment request is made on the already enrolled workload then enrollment is executed overriding the existing set of destinations. As per https://google.aip.dev/127 recommendation, we are having multiple URI binding for Enroll API.

      Args:
        request: (AuditmanagerOrganizationsLocationsEnrollResourceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Enrollment) The response message.
      """
      config = self.GetMethodConfig('EnrollResource')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnrollResource.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}:enrollResource',
        http_method='POST',
        method_id='auditmanager.organizations.locations.enrollResource',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}:enrollResource',
        request_field='enrollResourceRequest',
        request_type_name='AuditmanagerOrganizationsLocationsEnrollResourceRequest',
        response_type_name='Enrollment',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(AuditmanagerV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAuditReportsService(base_api.BaseApiService):
    """Service class for the projects_locations_auditReports resource."""

    _NAME = 'projects_locations_auditReports'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsAuditReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Generate(self, request, global_params=None):
      r"""Register the Audit Report generation requests and returns the OperationId using which the customer can track the report generation progress.

      Args:
        request: (AuditmanagerProjectsLocationsAuditReportsGenerateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Generate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Generate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/auditReports:generate',
        http_method='POST',
        method_id='auditmanager.projects.locations.auditReports.generate',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}/auditReports:generate',
        request_field='generateAuditReportRequest',
        request_type_name='AuditmanagerProjectsLocationsAuditReportsGenerateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the overall audit report.

      Args:
        request: (AuditmanagerProjectsLocationsAuditReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuditReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/auditReports/{auditReportsId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.auditReports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsAuditReportsGetRequest',
        response_type_name='AuditReport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists audit reports in the selected parent scope.

      Args:
        request: (AuditmanagerProjectsLocationsAuditReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuditReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/auditReports',
        http_method='GET',
        method_id='auditmanager.projects.locations.auditReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/auditReports',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsAuditReportsListRequest',
        response_type_name='ListAuditReportsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAuditScopeReportsService(base_api.BaseApiService):
    """Service class for the projects_locations_auditScopeReports resource."""

    _NAME = 'projects_locations_auditScopeReports'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsAuditScopeReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Generate(self, request, global_params=None):
      r"""Generates a demo report highlighting different responsibilities (Google/Customer/ shared) required to be fulfilled for the customer's workload to be compliant with the given standard.

      Args:
        request: (AuditmanagerProjectsLocationsAuditScopeReportsGenerateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuditScopeReport) The response message.
      """
      config = self.GetMethodConfig('Generate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Generate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/auditScopeReports:generate',
        http_method='POST',
        method_id='auditmanager.projects.locations.auditScopeReports.generate',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}/auditScopeReports:generate',
        request_field='generateAuditScopeReportRequest',
        request_type_name='AuditmanagerProjectsLocationsAuditScopeReportsGenerateRequest',
        response_type_name='AuditScopeReport',
        supports_download=False,
    )

  class ProjectsLocationsOperationDetailsService(base_api.BaseApiService):
    """Service class for the projects_locations_operationDetails resource."""

    _NAME = 'projects_locations_operationDetails'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsOperationDetailsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about generate audit report operation.

      Args:
        request: (AuditmanagerProjectsLocationsOperationDetailsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operationDetails/{operationDetailsId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.operationDetails.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsOperationDetailsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationIdsService(base_api.BaseApiService):
    """Service class for the projects_locations_operationIds resource."""

    _NAME = 'projects_locations_operationIds'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsOperationIdsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about generate audit report operation.

      Args:
        request: (AuditmanagerProjectsLocationsOperationIdsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operationIds/{operationIdsId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.operationIds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsOperationIdsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (AuditmanagerProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='auditmanager.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='AuditmanagerProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AuditmanagerProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='auditmanager.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AuditmanagerProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AuditmanagerProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='auditmanager.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsResourceEnrollmentStatusesService(base_api.BaseApiService):
    """Service class for the projects_locations_resourceEnrollmentStatuses resource."""

    _NAME = 'projects_locations_resourceEnrollmentStatuses'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsResourceEnrollmentStatusesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a resource along with its enrollment status.

      Args:
        request: (AuditmanagerProjectsLocationsResourceEnrollmentStatusesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceEnrollmentStatus) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/resourceEnrollmentStatuses/{resourceEnrollmentStatusesId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.resourceEnrollmentStatuses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsResourceEnrollmentStatusesGetRequest',
        response_type_name='ResourceEnrollmentStatus',
        supports_download=False,
    )

  class ProjectsLocationsStandardsControlsService(base_api.BaseApiService):
    """Service class for the projects_locations_standards_controls resource."""

    _NAME = 'projects_locations_standards_controls'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsStandardsControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Gets controls needed to be implemented to be compliant to a standard.

      Args:
        request: (AuditmanagerProjectsLocationsStandardsControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListControlsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/standards/{standardsId}/controls',
        http_method='GET',
        method_id='auditmanager.projects.locations.standards.controls.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/controls',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsStandardsControlsListRequest',
        response_type_name='ListControlsResponse',
        supports_download=False,
    )

  class ProjectsLocationsStandardsService(base_api.BaseApiService):
    """Service class for the projects_locations_standards resource."""

    _NAME = 'projects_locations_standards'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsStandardsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def EnrollResource(self, request, global_params=None):
      r"""Enrolls the customer resource(folder/project/organization) to the audit manager service by creating the audit managers P4SA in customers workload and granting required permissions to the P4SA. Please note that if enrollment request is made on the already enrolled workload then enrollment is executed overriding the existing set of destinations. As per https://google.aip.dev/127 recommendation, we are having multiple URI binding for Enroll API.

      Args:
        request: (AuditmanagerProjectsLocationsEnrollResourceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Enrollment) The response message.
      """
      config = self.GetMethodConfig('EnrollResource')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnrollResource.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:enrollResource',
        http_method='POST',
        method_id='auditmanager.projects.locations.enrollResource',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=[],
        relative_path='v1/{+scope}:enrollResource',
        request_field='enrollResourceRequest',
        request_type_name='AuditmanagerProjectsLocationsEnrollResourceRequest',
        response_type_name='Enrollment',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (AuditmanagerProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='auditmanager.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (AuditmanagerProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='auditmanager.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='AuditmanagerProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AuditmanagerV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
