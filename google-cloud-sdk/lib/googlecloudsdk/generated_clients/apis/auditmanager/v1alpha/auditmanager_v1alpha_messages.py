"""Generated message classes for auditmanager version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'auditmanager'


class AuditReport(_messages.Message):
  r"""Represents an audit report.

  Enums:
    ReportGenerationStateValueValuesEnum: Output only. The state of Audit
      Report Generation.

  Fields:
    complianceFramework: Output only. Compliance Framework of Audit Report
    complianceStandard: Output only. Compliance Standard.
    controlDetails: Output only. The overall status of controls
    createTime: Output only. Creation time of the audit report.
    destinationDetails: Output only. The location where the generated report
      will be uploaded.
    name: Identifier. The name of this Audit Report, in the format of scope
      given in request.
    operationId: Output only. ClientOperationId
    reportGenerationState: Output only. The state of Audit Report Generation.
    reportSummary: Output only. Report summary with compliance, violation
      counts etc.
    scope: Output only. The parent scope on which the report was generated.
    scopeId: Output only. The ID/ Number for the scope on which the audit
      report was generated.
  """

  class ReportGenerationStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of Audit Report Generation.

    Values:
      REPORT_GENERATION_STATE_UNSPECIFIED: Unspecified. Invalid state.
      IN_PROGRESS: Audit report generation process is in progress, ie.
        operation state is neither OPERATION_STATE_DONE nor
        OPERATION_STATE_FAILED.
      COMPLETED: Audit report generation process is completed. Operation state
        is OPERATION_STATE_DONE.
      FAILED: Audit report generation process is failed. Operation state is
        OPERATION_STATE_FAILED.
      SUMMARY_UNKNOWN: Audit report generation process has completed. But
        report summary is unknown. This is valid for older reports.
    """
    REPORT_GENERATION_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    COMPLETED = 2
    FAILED = 3
    SUMMARY_UNKNOWN = 4

  complianceFramework = _messages.StringField(1)
  complianceStandard = _messages.StringField(2)
  controlDetails = _messages.MessageField('ControlDetails', 3, repeated=True)
  createTime = _messages.StringField(4)
  destinationDetails = _messages.MessageField('DestinationDetails', 5)
  name = _messages.StringField(6)
  operationId = _messages.StringField(7)
  reportGenerationState = _messages.EnumField('ReportGenerationStateValueValuesEnum', 8)
  reportSummary = _messages.MessageField('ReportSummary', 9)
  scope = _messages.StringField(10)
  scopeId = _messages.StringField(11)


class AuditScopeReport(_messages.Message):
  r"""Response message containing the Audit Scope Report.

  Fields:
    name: Identifier. The name of this Audit Report, in the format of scope
      given in request.
    scopeReportContents: Audit Scope report content in byte format.
  """

  name = _messages.StringField(1)
  scopeReportContents = _messages.BytesField(2)


class AuditmanagerFoldersLocationsAuditReportsGenerateRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsAuditReportsGenerateRequest object.

  Fields:
    generateAuditReportRequest: A GenerateAuditReportRequest resource to be
      passed as the request body.
    scope: Required. Scope for which the AuditScopeReport is required. Must be
      of format resource_type/resource_identifier Eg: projects/{project-
      id}/locations/{location}, folders/{folder-id}/locations/{location}
  """

  generateAuditReportRequest = _messages.MessageField('GenerateAuditReportRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerFoldersLocationsAuditReportsGetRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsAuditReportsGetRequest object.

  Fields:
    name: Required. Format projects/{project-
      id}/locations/{location}/auditReports/{auditReportName},
      folders/{folder-id}/locations/{location}/auditReports/{auditReportName}
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerFoldersLocationsAuditReportsListRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsAuditReportsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent scope for which to list the reports.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerFoldersLocationsAuditScopeReportsGenerateRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsAuditScopeReportsGenerateRequest object.

  Fields:
    generateAuditScopeReportRequest: A GenerateAuditScopeReportRequest
      resource to be passed as the request body.
    scope: Required. Scope for which the AuditScopeReport is required. Must be
      of format resource_type/resource_identifier Eg: projects/{project-
      id}/locations/{location}, folders/{folder-id}/locations/{location}
  """

  generateAuditScopeReportRequest = _messages.MessageField('GenerateAuditScopeReportRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerFoldersLocationsEnrollResourceRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsEnrollResourceRequest object.

  Fields:
    enrollResourceRequest: A EnrollResourceRequest resource to be passed as
      the request body.
    scope: Required. The resource to be enrolled to the audit manager. Scope
      format should be resource_type/resource_identifier Eg:
      projects/{project-id}/locations/{location}, folders/{folder-
      id}/locations/{location} organizations/{organization-
      id}/locations/{location}
  """

  enrollResourceRequest = _messages.MessageField('EnrollResourceRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerFoldersLocationsOperationDetailsGetRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsOperationDetailsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerFoldersLocationsOperationIdsGetRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsOperationIdsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerFoldersLocationsResourceEnrollmentStatusesGetRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsResourceEnrollmentStatusesGetRequest
  object.

  Fields:
    name: Required. Format folders/{folder}/locations/{location}/resourceEnrol
      lmentStatuses/{resource_enrollment_status}, projects/{project}/locations
      /{location}/resourceEnrollmentStatuses/{resource_enrollment_status}, org
      anizations/{organization}/locations/{location}/resourceEnrollmentStatuse
      s/{resource_enrollment_status}
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerFoldersLocationsResourceEnrollmentStatusesListRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsResourceEnrollmentStatusesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent scope for which the list of resources with
      enrollments are required.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerFoldersLocationsStandardsControlsListRequest(_messages.Message):
  r"""A AuditmanagerFoldersLocationsStandardsControlsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. Format projects/{project-
      id}/locations/{location}/standards/{compliance-standard},
      folders/{folder-id}/locations/{location}/standards/{compliance-standard}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerOrganizationsLocationsAuditReportsListRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsAuditReportsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent scope for which to list the reports.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerOrganizationsLocationsEnrollResourceRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsEnrollResourceRequest object.

  Fields:
    enrollResourceRequest: A EnrollResourceRequest resource to be passed as
      the request body.
    scope: Required. The resource to be enrolled to the audit manager. Scope
      format should be resource_type/resource_identifier Eg:
      projects/{project-id}/locations/{location}, folders/{folder-
      id}/locations/{location} organizations/{organization-
      id}/locations/{location}
  """

  enrollResourceRequest = _messages.MessageField('EnrollResourceRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class AuditmanagerOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesGetRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesGetRequest
  object.

  Fields:
    name: Required. Format folders/{folder}/locations/{location}/resourceEnrol
      lmentStatuses/{resource_enrollment_status}, projects/{project}/locations
      /{location}/resourceEnrollmentStatuses/{resource_enrollment_status}, org
      anizations/{organization}/locations/{location}/resourceEnrollmentStatuse
      s/{resource_enrollment_status}
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesListRequest(_messages.Message):
  r"""A
  AuditmanagerOrganizationsLocationsResourceEnrollmentStatusesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent scope for which the list of resources with
      enrollments are required.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerOrganizationsLocationsStandardsControlsListRequest(_messages.Message):
  r"""A AuditmanagerOrganizationsLocationsStandardsControlsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. Format projects/{project-
      id}/locations/{location}/standards/{compliance-standard},
      folders/{folder-id}/locations/{location}/standards/{compliance-standard}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerProjectsLocationsAuditReportsGenerateRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsAuditReportsGenerateRequest object.

  Fields:
    generateAuditReportRequest: A GenerateAuditReportRequest resource to be
      passed as the request body.
    scope: Required. Scope for which the AuditScopeReport is required. Must be
      of format resource_type/resource_identifier Eg: projects/{project-
      id}/locations/{location}, folders/{folder-id}/locations/{location}
  """

  generateAuditReportRequest = _messages.MessageField('GenerateAuditReportRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerProjectsLocationsAuditReportsGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsAuditReportsGetRequest object.

  Fields:
    name: Required. Format projects/{project-
      id}/locations/{location}/auditReports/{auditReportName},
      folders/{folder-id}/locations/{location}/auditReports/{auditReportName}
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsAuditReportsListRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsAuditReportsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent scope for which to list the reports.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AuditmanagerProjectsLocationsAuditScopeReportsGenerateRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsAuditScopeReportsGenerateRequest object.

  Fields:
    generateAuditScopeReportRequest: A GenerateAuditScopeReportRequest
      resource to be passed as the request body.
    scope: Required. Scope for which the AuditScopeReport is required. Must be
      of format resource_type/resource_identifier Eg: projects/{project-
      id}/locations/{location}, folders/{folder-id}/locations/{location}
  """

  generateAuditScopeReportRequest = _messages.MessageField('GenerateAuditScopeReportRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerProjectsLocationsEnrollResourceRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsEnrollResourceRequest object.

  Fields:
    enrollResourceRequest: A EnrollResourceRequest resource to be passed as
      the request body.
    scope: Required. The resource to be enrolled to the audit manager. Scope
      format should be resource_type/resource_identifier Eg:
      projects/{project-id}/locations/{location}, folders/{folder-
      id}/locations/{location} organizations/{organization-
      id}/locations/{location}
  """

  enrollResourceRequest = _messages.MessageField('EnrollResourceRequest', 1)
  scope = _messages.StringField(2, required=True)


class AuditmanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsListRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class AuditmanagerProjectsLocationsOperationDetailsGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationDetailsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsOperationIdsGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationIdsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class AuditmanagerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AuditmanagerProjectsLocationsResourceEnrollmentStatusesGetRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsResourceEnrollmentStatusesGetRequest
  object.

  Fields:
    name: Required. Format folders/{folder}/locations/{location}/resourceEnrol
      lmentStatuses/{resource_enrollment_status}, projects/{project}/locations
      /{location}/resourceEnrollmentStatuses/{resource_enrollment_status}, org
      anizations/{organization}/locations/{location}/resourceEnrollmentStatuse
      s/{resource_enrollment_status}
  """

  name = _messages.StringField(1, required=True)


class AuditmanagerProjectsLocationsStandardsControlsListRequest(_messages.Message):
  r"""A AuditmanagerProjectsLocationsStandardsControlsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of resources to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. Format projects/{project-
      id}/locations/{location}/standards/{compliance-standard},
      folders/{folder-id}/locations/{location}/standards/{compliance-standard}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Control(_messages.Message):
  r"""Represents a control.

  Enums:
    FamilyValueValuesEnum: Output only. Group where the control belongs. E.g.
      Access Control.

  Fields:
    controlFamily: Output only. Regulatory Family of the control E.g. Access
      Control
    customerResponsibilityDescription: Output only. Description of the
      customer responsibility for implementing this control.
    customerResponsibilityImplementation: Output only. Implementation of the
      customer responsibility for implementing this control.
    description: Output only. Regulatory control ask of the control
    displayName: Output only. Display name of the control.
    family: Output only. Group where the control belongs. E.g. Access Control.
    googleResponsibilityDescription: Output only. Description of the google
      responsibility for implementing this control.
    googleResponsibilityImplementation: Output only. Implementation of the
      google responsibility for implementing this control.
    id: Output only. The control identifier used to fetch the findings. This
      is same as the control report name.
    responsibilityType: Output only. The type of responsibility for
      implementing this control. It can be google, customer or shared.
  """

  class FamilyValueValuesEnum(_messages.Enum):
    r"""Output only. Group where the control belongs. E.g. Access Control.

    Values:
      FAMILY_UNSPECIFIED: Unspecified. Invalid state.
      AC: Access Control
      AT: Awareness and Training
      AU: Audit and Accountability
      CA: Certification, Accreditation and Security Assessments
      CM: Configuration Management
      CP: Contingency Planning
      IA: Identification and Authentication
      IR: Incident Response
      MA: Maintenance
      MP: Media Protection
      PE: Physical and Environmental Protection
      PL: Security Planning
      PS: Personnel Security
      RA: Risk Assessment
      SA: System Services and Acquisition
      SC: System and Communications Protection
      SI: System and Information Integrity
      SR: Supply Chain Risk Management
    """
    FAMILY_UNSPECIFIED = 0
    AC = 1
    AT = 2
    AU = 3
    CA = 4
    CM = 5
    CP = 6
    IA = 7
    IR = 8
    MA = 9
    MP = 10
    PE = 11
    PL = 12
    PS = 13
    RA = 14
    SA = 15
    SC = 16
    SI = 17
    SR = 18

  controlFamily = _messages.MessageField('ControlFamily', 1)
  customerResponsibilityDescription = _messages.StringField(2)
  customerResponsibilityImplementation = _messages.StringField(3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  family = _messages.EnumField('FamilyValueValuesEnum', 6)
  googleResponsibilityDescription = _messages.StringField(7)
  googleResponsibilityImplementation = _messages.StringField(8)
  id = _messages.StringField(9)
  responsibilityType = _messages.StringField(10)


class ControlDetails(_messages.Message):
  r"""Evaluation details for a control

  Enums:
    ComplianceStateValueValuesEnum: Output only. Overall status of the
      findings for the control.

  Fields:
    complianceState: Output only. Overall status of the findings for the
      control.
    control: The control for which the findings are being reported.
    controlReportSummary: Report summary with compliance, violation counts
      etc.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""Output only. Overall status of the findings for the control.

    Values:
      COMPLIANCE_STATE_UNSPECIFIED: Unspecified. Invalid state.
      COMPLIANT: Compliant.
      VIOLATION: Violation.
      MANUAL_REVIEW_NEEDED: MANUAL_REVIEW_NEEDED, requires manual review
      ERROR: Error while computing status.
      AUDIT_NOT_SUPPORTED: Cannot be audited
    """
    COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    VIOLATION = 2
    MANUAL_REVIEW_NEEDED = 3
    ERROR = 4
    AUDIT_NOT_SUPPORTED = 5

  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 1)
  control = _messages.MessageField('Control', 2)
  controlReportSummary = _messages.MessageField('ReportSummary', 3)


class ControlFamily(_messages.Message):
  r"""Regulatory Family of the control

  Fields:
    displayName: Display name of the regulatory control family.
    familyId: ID of the regulatory control family.
  """

  displayName = _messages.StringField(1)
  familyId = _messages.StringField(2)


class DestinationDetails(_messages.Message):
  r"""Represents the locations where the generated reports is saved.

  Fields:
    gcsBucketUri: The Cloud Storage bucket where the audit report is/will be
      uploaded.
  """

  gcsBucketUri = _messages.StringField(1)


class EligibleDestination(_messages.Message):
  r"""Message containing the destination details where audit report should be
  uploaded.

  Fields:
    eligibleGcsBucket: Cloud storage bucket location where audit report and
      evidences can be uploaded if specified during the GenerateAuditReport
      API call.
  """

  eligibleGcsBucket = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnrollResourceRequest(_messages.Message):
  r"""Request message to subscribe the Audit Manager service for given
  resource.

  Fields:
    destinations: Required. List of destination among which customer can
      choose to upload their reports during the audit process. While enrolling
      at a organization/folder level, customer can choose Cloud storage bucket
      in any project. If the audit is triggered at project level using the
      service agent at organization/folder level, all the destination options
      associated with respective organization/folder level service agent will
      be available to auditing projects.
  """

  destinations = _messages.MessageField('EligibleDestination', 1, repeated=True)


class Enrollment(_messages.Message):
  r"""In case of success client will be notified with HTTP 200 response code
  but for failure scenario relevant exception message is thrown with the
  corresponding response code

  Fields:
    destinationDetails: Output only. The locations where the generated reports
      can be uploaded.
    name: Identifier. The name of this Enrollment, in the format of scope
      given in request.
  """

  destinationDetails = _messages.MessageField('DestinationDetails', 1, repeated=True)
  name = _messages.StringField(2)


class GenerateAuditReportRequest(_messages.Message):
  r"""Message for requesting the Audit Report.

  Enums:
    ReportFormatValueValuesEnum: Required. The format in which the audit
      report should be created.

  Fields:
    complianceFramework: Required. Compliance framework against which the
      Report must be generated.
    complianceStandard: Required. Compliance Standard against which the Scope
      Report must be generated. Eg: FEDRAMP_MODERATE
    gcsUri: Destination Cloud storage bucket where report and evidence must be
      uploaded. The Cloud storage bucket provided here must be selected among
      the buckets entered during the enrollment process.
    reportFormat: Required. The format in which the audit report should be
      created.
  """

  class ReportFormatValueValuesEnum(_messages.Enum):
    r"""Required. The format in which the audit report should be created.

    Values:
      AUDIT_REPORT_FORMAT_UNSPECIFIED: Unspecified. Invalid state.
      AUDIT_REPORT_FORMAT_ODF: Audit Report creation format is Open Document.
    """
    AUDIT_REPORT_FORMAT_UNSPECIFIED = 0
    AUDIT_REPORT_FORMAT_ODF = 1

  complianceFramework = _messages.StringField(1)
  complianceStandard = _messages.StringField(2)
  gcsUri = _messages.StringField(3)
  reportFormat = _messages.EnumField('ReportFormatValueValuesEnum', 4)


class GenerateAuditScopeReportRequest(_messages.Message):
  r"""Message for requesting audit scope report.

  Enums:
    ReportFormatValueValuesEnum: Required. The format in which the Scope
      report bytes should be returned.

  Fields:
    complianceFramework: Required. Compliance framework against which the
      Scope Report must be generated.
    complianceStandard: Required. Compliance Standard against which the Scope
      Report must be generated. Eg: FEDRAMP_MODERATE
    reportFormat: Required. The format in which the Scope report bytes should
      be returned.
  """

  class ReportFormatValueValuesEnum(_messages.Enum):
    r"""Required. The format in which the Scope report bytes should be
    returned.

    Values:
      AUDIT_SCOPE_REPORT_FORMAT_UNSPECIFIED: Unspecified. Invalid format.
      AUDIT_SCOPE_REPORT_FORMAT_ODF: Audit Scope Report creation format is
        Open Document.
    """
    AUDIT_SCOPE_REPORT_FORMAT_UNSPECIFIED = 0
    AUDIT_SCOPE_REPORT_FORMAT_ODF = 1

  complianceFramework = _messages.StringField(1)
  complianceStandard = _messages.StringField(2)
  reportFormat = _messages.EnumField('ReportFormatValueValuesEnum', 3)


class ListAuditReportsResponse(_messages.Message):
  r"""Response message with all the audit reports.

  Fields:
    auditReports: Output only. The audit reports.
    nextPageToken: Output only. The token to retrieve the next page of
      results.
  """

  auditReports = _messages.MessageField('AuditReport', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListControlsResponse(_messages.Message):
  r"""Response message with all the controls for a compliance standard.

  Fields:
    controls: Output only. The controls for the compliance standard.
    nextPageToken: Output only. The token to retrieve the next page of
      results.
  """

  controls = _messages.MessageField('Control', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListResourceEnrollmentStatusesResponse(_messages.Message):
  r"""Response message with all the descendent resources with enrollment.

  Fields:
    nextPageToken: Output only. The token to retrieve the next page of
      results.
    resourceEnrollmentStatuses: The resources with their enrollment status.
  """

  nextPageToken = _messages.StringField(1)
  resourceEnrollmentStatuses = _messages.MessageField('ResourceEnrollmentStatus', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ReportGenerationProgress(_messages.Message):
  r"""The ReportGenerationProgress is part of {google.longrunning.Operation}
  returned to client for every GET Operation request.

  Enums:
    StateValueValuesEnum: Output only. Highlights the current state of
      executation for report generation.

  Fields:
    destinationGcsBucket: Output only. The Cloud Storage bucket where the
      audit report will be uploaded once the evaluation process is completed.
    evaluationPercentComplete: Shows the progress of the CESS service
      evaluation process. The progress is defined in terms of percentage
      complete and is being fetched from the CESS service.
    failureReason: Output only. States the reason of failure during the audit
      report generation process. This field is set only if the state attribute
      is OPERATION_STATE_FAILED.
    reportGenerationPercentComplete: Shows the report generation progress of
      the CESS Result Processor Service. The // progress is defined in terms
      of percentage complete and is being fetched from the CESS service. If
      report_generation_in_progress is non zero then
      evaluation_percent_complete will be 100%.
    reportUploadingPercentComplete: Shows the report uploading progress of the
      CESS Result Processor Service. The progress is defined in terms of
      percentage complete and is being fetched from the CESS service. If
      report_uploading_in_progress is non zero then
      evaluation_percent_complete and report_generation_percent_complete will
      be 100%.
    state: Output only. Highlights the current state of executation for report
      generation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Highlights the current state of executation for report
    generation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified. Invalid state.
      OPERATION_STATE_NOT_STARTED: Audit report generation process has not
        stated.
      OPERATION_STATE_EVALUATION_IN_PROGRESS: Audit Manager is currently
        evaluating the workloads against specific standard.
      OPERATION_STATE_EVALUATION_DONE: Audit Manager has completed Evaluation
        for the workload.
      OPERATION_STATE_EVIDENCE_REPORT_GENERATION_IN_PROGRESS: Audit Manager is
        creating audit report from the evaluated data.
      OPERATION_STATE_EVIDENCE_REPORT_GENERATION_DONE: Audit Manager has
        completed generation of the audit report.
      OPERATION_STATE_EVIDENCE_UPLOAD_IN_PROGRESS: Audit Manager is uploading
        the audit report and evidences to the customer provided destination.
      OPERATION_STATE_DONE: Audit report generation process is completed.
      OPERATION_STATE_FAILED: Audit report generation process has failed.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_NOT_STARTED = 1
    OPERATION_STATE_EVALUATION_IN_PROGRESS = 2
    OPERATION_STATE_EVALUATION_DONE = 3
    OPERATION_STATE_EVIDENCE_REPORT_GENERATION_IN_PROGRESS = 4
    OPERATION_STATE_EVIDENCE_REPORT_GENERATION_DONE = 5
    OPERATION_STATE_EVIDENCE_UPLOAD_IN_PROGRESS = 6
    OPERATION_STATE_DONE = 7
    OPERATION_STATE_FAILED = 8

  destinationGcsBucket = _messages.StringField(1)
  evaluationPercentComplete = _messages.FloatField(2)
  failureReason = _messages.StringField(3)
  reportGenerationPercentComplete = _messages.FloatField(4)
  reportUploadingPercentComplete = _messages.FloatField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class ReportSummary(_messages.Message):
  r"""Represents additional information for an audit operation.

  Fields:
    compliantCount: Number of compliant checks.
    errorCount: Number of checks that could not be performed due to errors.
    manualReviewNeededCount: Number of checks with "manual review needed"
      status.
    totalCount: Total number of checks.
    violationCount: Number of checks with violations.
  """

  compliantCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errorCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  manualReviewNeededCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  totalCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  violationCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class ResourceEnrollmentStatus(_messages.Message):
  r"""Represents a resource (project or folder or organization) with its
  enrollment status.

  Enums:
    EnrollmentStateValueValuesEnum: Output only. Enrollment state of the
      resource.

  Fields:
    displayName: Output only. Display name of the project/folder/organization.
    enrolled: Output only. Is resource enrolled.
    enrollment: Output only. Enrollment which contains enrolled destination
      details for a resource
    enrollmentState: Output only. Enrollment state of the resource.
    name: Identifier. The name of this resource.
  """

  class EnrollmentStateValueValuesEnum(_messages.Enum):
    r"""Output only. Enrollment state of the resource.

    Values:
      RESOURCE_ENROLLMENT_STATE_UNSPECIFIED: Unspecified. Invalid state.
      NOT_ENROLLED: Not enrolled.
      INHERITED: Resource is not enrolled but the parent is enrolled.
      ENROLLED: Enrolled.
    """
    RESOURCE_ENROLLMENT_STATE_UNSPECIFIED = 0
    NOT_ENROLLED = 1
    INHERITED = 2
    ENROLLED = 3

  displayName = _messages.StringField(1)
  enrolled = _messages.BooleanField(2)
  enrollment = _messages.MessageField('Enrollment', 3)
  enrollmentState = _messages.EnumField('EnrollmentStateValueValuesEnum', 4)
  name = _messages.StringField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
