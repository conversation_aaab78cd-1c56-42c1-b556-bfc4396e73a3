"""Generated message classes for baremetalsolution version v2.

Provides ways to manage Bare Metal Solution hardware installed in a regional
extension located near a Google Cloud data center.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'baremetalsolution'


class AllocateLunsRequest(_messages.Message):
  r"""Message for creating Luns for Volume.

  Fields:
    lunRanges: Required. LUN ranges to be allocated.
  """

  lunRanges = _messages.MessageField('AllocateLunsRequestLunRange', 1, repeated=True)


class AllocateLunsRequestLunRange(_messages.Message):
  r"""A LUN(Logical Unit Number) range.

  Fields:
    quantity: Number of LUNs to create.
    sizeGb: The requested size of each LUN, in GB.
  """

  quantity = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class AllowedClient(_messages.Message):
  r"""Represents an 'access point' for the share.

  Enums:
    MountPermissionsValueValuesEnum: Mount permissions.

  Fields:
    allowDev: Allow dev flag. Which controls whether to allow creation of
      devices.
    allowSuid: Allow the setuid flag.
    allowedClientsCidr: The subnet of IP addresses permitted to access the
      share.
    mountPermissions: Mount permissions.
    network: The network the access point sits on.
    nfsPath: Output only. The path to access NFS, in format
      shareIP:/InstanceID InstanceID is the generated ID instead of customer
      provided name. example like "10.0.0.0:/g123456789-nfs001"
    noRootSquash: Disable root squashing, which is a feature of NFS. Root
      squash is a special mapping of the remote superuser (root) identity when
      using identity authentication.
    shareIp: Output only. The IP address of the share on this network.
      Assigned automatically during provisioning based on the network's
      services_cidr.
  """

  class MountPermissionsValueValuesEnum(_messages.Enum):
    r"""Mount permissions.

    Values:
      MOUNT_PERMISSIONS_UNSPECIFIED: Permissions were not specified.
      READ: NFS share can be mount with read-only permissions.
      READ_WRITE: NFS share can be mount with read-write permissions.
    """
    MOUNT_PERMISSIONS_UNSPECIFIED = 0
    READ = 1
    READ_WRITE = 2

  allowDev = _messages.BooleanField(1)
  allowSuid = _messages.BooleanField(2)
  allowedClientsCidr = _messages.StringField(3)
  mountPermissions = _messages.EnumField('MountPermissionsValueValuesEnum', 4)
  network = _messages.StringField(5)
  nfsPath = _messages.StringField(6)
  noRootSquash = _messages.BooleanField(7)
  shareIp = _messages.StringField(8)


class AttachVolumeRequest(_messages.Message):
  r"""Message for attaching Volume to an instance. All Luns of the Volume will
  be attached.

  Fields:
    volume: Name of the Volume to attach.
    volumes: Names of the multiple Volumes to attach. The volumes attaching
      will be an additive operation and will have no effect on existing
      attached volumes.
  """

  volume = _messages.StringField(1)
  volumes = _messages.StringField(2, repeated=True)


class BaremetalsolutionProjectsLocationsGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsInstanceQuotasListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstanceQuotasListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
      Notice that page_size field is not supported and won't be respected in
      the API request for now, will be updated when pagination is supported.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListProvisioningQuotasRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsInstancesAttachVolumeRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesAttachVolumeRequest object.

  Fields:
    attachVolumeRequest: A AttachVolumeRequest resource to be passed as the
      request body.
    instance: Required. Name of the instance.
  """

  attachVolumeRequest = _messages.MessageField('AttachVolumeRequest', 1)
  instance = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesDetachLunRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesDetachLunRequest object.

  Fields:
    detachLunRequest: A DetachLunRequest resource to be passed as the request
      body.
    instance: Required. Name of the instance.
  """

  detachLunRequest = _messages.MessageField('DetachLunRequest', 1)
  instance = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesDetachVolumeRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesDetachVolumeRequest object.

  Fields:
    detachVolumeRequest: A DetachVolumeRequest resource to be passed as the
      request body.
    instance: Required. Name of the instance.
  """

  detachVolumeRequest = _messages.MessageField('DetachVolumeRequest', 1)
  instance = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesDisableHyperthreadingRequest(_messages.Message):
  r"""A
  BaremetalsolutionProjectsLocationsInstancesDisableHyperthreadingRequest
  object.

  Fields:
    disableHyperthreadingRequest: A DisableHyperthreadingRequest resource to
      be passed as the request body.
    name: Required. The `name` field is used to identify the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
  """

  disableHyperthreadingRequest = _messages.MessageField('DisableHyperthreadingRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesDisableInteractiveSerialConsoleRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesDisableInteractiveSerialCon
  soleRequest object.

  Fields:
    disableInteractiveSerialConsoleRequest: A
      DisableInteractiveSerialConsoleRequest resource to be passed as the
      request body.
    name: Required. Name of the resource.
  """

  disableInteractiveSerialConsoleRequest = _messages.MessageField('DisableInteractiveSerialConsoleRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesEnableHyperthreadingRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesEnableHyperthreadingRequest
  object.

  Fields:
    enableHyperthreadingRequest: A EnableHyperthreadingRequest resource to be
      passed as the request body.
    name: Required. The `name` field is used to identify the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
  """

  enableHyperthreadingRequest = _messages.MessageField('EnableHyperthreadingRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesEnableInteractiveSerialConsoleRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesEnableInteractiveSerialCons
  oleRequest object.

  Fields:
    enableInteractiveSerialConsoleRequest: A
      EnableInteractiveSerialConsoleRequest resource to be passed as the
      request body.
    name: Required. Name of the resource.
  """

  enableInteractiveSerialConsoleRequest = _messages.MessageField('EnableInteractiveSerialConsoleRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesListRequest object.

  Fields:
    filter: List filter.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, the server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListInstancesRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BaremetalsolutionProjectsLocationsInstancesLoadAuthInfoRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesLoadAuthInfoRequest object.

  Fields:
    name: Required. Name of the server.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Immutable. The resource name of this `Instance`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/instances/{instance}`
    updateMask: The list of fields to update. The currently supported fields
      are: `labels` `hyperthreading_enabled` `os_image` `ssh_keys`
      `kms_key_version`
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsInstancesReimageRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesReimageRequest object.

  Fields:
    name: Required. The `name` field is used to identify the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
    reimageInstanceRequest: A ReimageInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  reimageInstanceRequest = _messages.MessageField('ReimageInstanceRequest', 2)


class BaremetalsolutionProjectsLocationsInstancesRenameRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesRenameRequest object.

  Fields:
    name: Required. The `name` field is used to identify the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
    renameInstanceRequest: A RenameInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  renameInstanceRequest = _messages.MessageField('RenameInstanceRequest', 2)


class BaremetalsolutionProjectsLocationsInstancesResetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesResetRequest object.

  Fields:
    name: Required. Name of the resource.
    resetInstanceRequest: A ResetInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetInstanceRequest = _messages.MessageField('ResetInstanceRequest', 2)


class BaremetalsolutionProjectsLocationsInstancesStartRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesStartRequest object.

  Fields:
    name: Required. Name of the resource.
    startInstanceRequest: A StartInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startInstanceRequest = _messages.MessageField('StartInstanceRequest', 2)


class BaremetalsolutionProjectsLocationsInstancesStopRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsInstancesStopRequest object.

  Fields:
    name: Required. Name of the resource.
    stopInstanceRequest: A StopInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopInstanceRequest = _messages.MessageField('StopInstanceRequest', 2)


class BaremetalsolutionProjectsLocationsListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class BaremetalsolutionProjectsLocationsNetworkQuotasListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworkQuotasListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
      Notice that page_size field is not supported and won't be respected in
      the API request for now, will be updated when pagination is supported.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListProvisioningQuotasRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsNetworksGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworksGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsNetworksListNetworkUsageRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworksListNetworkUsageRequest
  object.

  Fields:
    location: Required. Parent value (project and location).
  """

  location = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsNetworksListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworksListRequest object.

  Fields:
    filter: List filter.
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListNetworksRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BaremetalsolutionProjectsLocationsNetworksPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworksPatchRequest object.

  Fields:
    name: Output only. The resource name of this `Network`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/networks/{network}`
    network: A Network resource to be passed as the request body.
    updateMask: The list of fields to update. The only currently supported
      fields are: `labels`, `reservations`, `vrf.vlan_attachments`
  """

  name = _messages.StringField(1, required=True)
  network = _messages.MessageField('Network', 2)
  updateMask = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsNetworksRenameRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNetworksRenameRequest object.

  Fields:
    name: Required. The `name` field is used to identify the network. Format:
      projects/{project}/locations/{location}/networks/{network}
    renameNetworkRequest: A RenameNetworkRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  renameNetworkRequest = _messages.MessageField('RenameNetworkRequest', 2)


class BaremetalsolutionProjectsLocationsNfsSharesCreateRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesCreateRequest object.

  Fields:
    nfsShare: A NfsShare resource to be passed as the request body.
    parent: Required. The parent project and location.
  """

  nfsShare = _messages.MessageField('NfsShare', 1)
  parent = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsNfsSharesDeleteRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesDeleteRequest object.

  Fields:
    name: Required. The name of the NFS share to delete.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsNfsSharesGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsNfsSharesListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesListRequest object.

  Fields:
    filter: List filter.
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListNfsSharesRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BaremetalsolutionProjectsLocationsNfsSharesPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesPatchRequest object.

  Fields:
    name: Immutable. The name of the NFS share.
    nfsShare: A NfsShare resource to be passed as the request body.
    updateMask: The list of fields to update. The only currently supported
      fields are: `labels` `allowed_clients`
  """

  name = _messages.StringField(1, required=True)
  nfsShare = _messages.MessageField('NfsShare', 2)
  updateMask = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsNfsSharesRenameRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsNfsSharesRenameRequest object.

  Fields:
    name: Required. The `name` field is used to identify the nfsshare. Format:
      projects/{project}/locations/{location}/nfsshares/{nfsshare}
    renameNfsShareRequest: A RenameNfsShareRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  renameNfsShareRequest = _messages.MessageField('RenameNfsShareRequest', 2)


class BaremetalsolutionProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsOsImagesGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsOsImagesGetRequest object.

  Fields:
    name: Required. Name of the OS image.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsOsImagesListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsOsImagesListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
      Notice that page_size field is not supported and won't be respected in
      the API request for now, will be updated when pagination is supported.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListOSImagesRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsProvisioningConfigsCreateRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsProvisioningConfigsCreateRequest
  object.

  Fields:
    email: Optional. Email provided to send a confirmation with provisioning
      config to.
    parent: Required. The parent project and location containing the
      ProvisioningConfig.
    provisioningConfig: A ProvisioningConfig resource to be passed as the
      request body.
  """

  email = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  provisioningConfig = _messages.MessageField('ProvisioningConfig', 3)


class BaremetalsolutionProjectsLocationsProvisioningConfigsGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsProvisioningConfigsGetRequest
  object.

  Fields:
    name: Required. Name of the ProvisioningConfig.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsProvisioningConfigsPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsProvisioningConfigsPatchRequest
  object.

  Fields:
    email: Optional. Email provided to send a confirmation with provisioning
      config to.
    name: Output only. The system-generated name of the provisioning config.
      This follows the UUID format.
    provisioningConfig: A ProvisioningConfig resource to be passed as the
      request body.
    updateMask: Required. The list of fields to update.
  """

  email = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  provisioningConfig = _messages.MessageField('ProvisioningConfig', 3)
  updateMask = _messages.StringField(4)


class BaremetalsolutionProjectsLocationsProvisioningConfigsSubmitRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsProvisioningConfigsSubmitRequest
  object.

  Fields:
    parent: Required. The parent project and location containing the
      ProvisioningConfig.
    submitProvisioningConfigRequest: A SubmitProvisioningConfigRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  submitProvisioningConfigRequest = _messages.MessageField('SubmitProvisioningConfigRequest', 2)


class BaremetalsolutionProjectsLocationsProvisioningQuotasListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsProvisioningQuotasListRequest
  object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
      Notice that page_size field is not supported and won't be respected in
      the API request for now, will be updated when pagination is supported.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListProvisioningQuotasRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesCreateRequest(_messages.Message):
  r"""A
  BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesCreateRequest
  object.

  Fields:
    parent: Required. The parent project and location containing the
      SnapshotSchedulePolicy.
    snapshotSchedulePolicy: A SnapshotSchedulePolicy resource to be passed as
      the request body.
    snapshotSchedulePolicyId: Required. Snapshot policy ID
  """

  parent = _messages.StringField(1, required=True)
  snapshotSchedulePolicy = _messages.MessageField('SnapshotSchedulePolicy', 2)
  snapshotSchedulePolicyId = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesDeleteRequest(_messages.Message):
  r"""A
  BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesDeleteRequest
  object.

  Fields:
    name: Required. The name of the snapshot schedule policy to delete.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesListRequest
  object.

  Fields:
    filter: List filter.
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent project containing the Snapshot Schedule
      Policies.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesPatchRequest
  object.

  Fields:
    name: Output only. The name of the snapshot schedule policy.
    snapshotSchedulePolicy: A SnapshotSchedulePolicy resource to be passed as
      the request body.
    updateMask: Required. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  snapshotSchedulePolicy = _messages.MessageField('SnapshotSchedulePolicy', 2)
  updateMask = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsSshKeysCreateRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSshKeysCreateRequest object.

  Fields:
    parent: Required. The parent containing the SSH keys.
    sSHKey: A SSHKey resource to be passed as the request body.
    sshKeyId: Required. The ID to use for the key, which will become the final
      component of the key's resource name. This value must match the regex:
      [a-zA-Z0-9@.\-_]{1,64}
  """

  parent = _messages.StringField(1, required=True)
  sSHKey = _messages.MessageField('SSHKey', 2)
  sshKeyId = _messages.StringField(3)


class BaremetalsolutionProjectsLocationsSshKeysDeleteRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSshKeysDeleteRequest object.

  Fields:
    name: Required. The name of the SSH key to delete. Currently, the only
      valid value for the location is "global".
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsSshKeysListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsSshKeysListRequest object.

  Fields:
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent containing the SSH keys. Currently, the only
      valid value for the location is "global".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsStorageQuotasListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsStorageQuotasListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
      Notice that page_size field is not supported and won't be respected in
      the API request for now, will be updated when pagination is supported.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListProvisioningQuotasRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsVolumesAllocateLunsRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesAllocateLunsRequest object.

  Fields:
    allocateLunsRequest: A AllocateLunsRequest resource to be passed as the
      request body.
    parent: Required. Parent volume.
  """

  allocateLunsRequest = _messages.MessageField('AllocateLunsRequest', 1)
  parent = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesCreateAndAttachRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesCreateAndAttachRequest
  object.

  Fields:
    createAndAttachVolumeRequest: A CreateAndAttachVolumeRequest resource to
      be passed as the request body.
    parent: Required. The parent project and location.
  """

  createAndAttachVolumeRequest = _messages.MessageField('CreateAndAttachVolumeRequest', 1)
  parent = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesCreateRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesCreateRequest object.

  Fields:
    parent: Required. The parent project and location.
    volume: A Volume resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  volume = _messages.MessageField('Volume', 2)


class BaremetalsolutionProjectsLocationsVolumesDeleteRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesDeleteRequest object.

  Fields:
    force: If true, will put into cooloff all volume's luns as well. Luns must
      not be attached to any Instances. If false operation will fail if a
      volume has active (not in cooloff) luns.
    name: Required. The name of the Volume to delete.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesEvictRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesEvictRequest object.

  Fields:
    evictVolumeRequest: A EvictVolumeRequest resource to be passed as the
      request body.
    name: Required. The name of the Volume.
  """

  evictVolumeRequest = _messages.MessageField('EvictVolumeRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsVolumesListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesListRequest object.

  Fields:
    filter: List filter.
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListVolumesRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BaremetalsolutionProjectsLocationsVolumesLunsDeleteRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesLunsDeleteRequest object.

  Fields:
    name: Required. The name of the Lun to delete.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsVolumesLunsEvictRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesLunsEvictRequest object.

  Fields:
    evictLunRequest: A EvictLunRequest resource to be passed as the request
      body.
    name: Required. The name of the lun.
  """

  evictLunRequest = _messages.MessageField('EvictLunRequest', 1)
  name = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesLunsGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesLunsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsVolumesLunsListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesLunsListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListLunsRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsVolumesPatchRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesPatchRequest object.

  Fields:
    name: Output only. The resource name of this `Volume`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/volumes/{volume}`
    updateMask: The list of fields to update. The only currently supported
      fields are: 'labels'
    volume: A Volume resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  volume = _messages.MessageField('Volume', 3)


class BaremetalsolutionProjectsLocationsVolumesRenameRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesRenameRequest object.

  Fields:
    name: Required. The `name` field is used to identify the volume. Format:
      projects/{project}/locations/{location}/volumes/{volume}
    renameVolumeRequest: A RenameVolumeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  renameVolumeRequest = _messages.MessageField('RenameVolumeRequest', 2)


class BaremetalsolutionProjectsLocationsVolumesResizeRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesResizeRequest object.

  Fields:
    resizeVolumeRequest: A ResizeVolumeRequest resource to be passed as the
      request body.
    volume: Required. Volume to resize.
  """

  resizeVolumeRequest = _messages.MessageField('ResizeVolumeRequest', 1)
  volume = _messages.StringField(2, required=True)


class BaremetalsolutionProjectsLocationsVolumesSnapshotsCreateRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesSnapshotsCreateRequest
  object.

  Fields:
    parent: Required. The volume to snapshot.
    volumeSnapshot: A VolumeSnapshot resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  volumeSnapshot = _messages.MessageField('VolumeSnapshot', 2)


class BaremetalsolutionProjectsLocationsVolumesSnapshotsDeleteRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesSnapshotsDeleteRequest
  object.

  Fields:
    name: Required. The name of the snapshot to delete.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsVolumesSnapshotsGetRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesSnapshotsGetRequest object.

  Fields:
    name: Required. The name of the snapshot.
  """

  name = _messages.StringField(1, required=True)


class BaremetalsolutionProjectsLocationsVolumesSnapshotsListRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesSnapshotsListRequest object.

  Fields:
    pageSize: Requested page size. The server might return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results from the server.
    parent: Required. Parent value for ListVolumesRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BaremetalsolutionProjectsLocationsVolumesSnapshotsRestoreVolumeSnapshotRequest(_messages.Message):
  r"""A BaremetalsolutionProjectsLocationsVolumesSnapshotsRestoreVolumeSnapsho
  tRequest object.

  Fields:
    restoreVolumeSnapshotRequest: A RestoreVolumeSnapshotRequest resource to
      be passed as the request body.
    volumeSnapshot: Required. Name of the snapshot which will be used to
      restore its parent volume.
  """

  restoreVolumeSnapshotRequest = _messages.MessageField('RestoreVolumeSnapshotRequest', 1)
  volumeSnapshot = _messages.StringField(2, required=True)


class CreateAndAttachVolumeRequest(_messages.Message):
  r"""Message for creating a volume with immediate Luns allocation and their
  attachment to instances.

  Fields:
    instances: List of instance to attach this volume to. If defined, will
      attach all LUNs of this Volume to specified instances. Makes sense only
      when lun_ranges are defined.
    lunRanges: LUN ranges to be allocated. If defined, will immediately
      allocate LUNs.
    volume: Required. The volume to create.
  """

  instances = _messages.StringField(1, repeated=True)
  lunRanges = _messages.MessageField('VolumeLunRange', 2, repeated=True)
  volume = _messages.MessageField('Volume', 3)


class DetachLunRequest(_messages.Message):
  r"""Message for detach specific LUN from an Instance.

  Fields:
    lun: Required. Name of the Lun to detach.
    skipReboot: If true, performs lun unmapping without instance reboot.
  """

  lun = _messages.StringField(1)
  skipReboot = _messages.BooleanField(2)


class DetachVolumeRequest(_messages.Message):
  r"""Message for detaching Volume from an instance. All Luns of the Volume
  will be detached.

  Fields:
    skipReboot: If true, performs Volume unmapping without instance reboot.
    volume: Name of the Volume to detach.
    volumes: Names of the multiple Volumes to detach. The detaching of volumes
      will have no effect on other existing attached volumes.
  """

  skipReboot = _messages.BooleanField(1)
  volume = _messages.StringField(2)
  volumes = _messages.StringField(3, repeated=True)


class DisableHyperthreadingRequest(_messages.Message):
  r"""Message requesting to perform disable hyperthreading operation on a
  server.
  """



class DisableInteractiveSerialConsoleRequest(_messages.Message):
  r"""Message for disabling the interactive serial console on an instance."""


class DisableInteractiveSerialConsoleResponse(_messages.Message):
  r"""Message for response of DisableInteractiveSerialConsole."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableHyperthreadingRequest(_messages.Message):
  r"""Message requesting to perform enable hyperthreading operation on a
  server.
  """



class EnableInteractiveSerialConsoleRequest(_messages.Message):
  r"""Message for enabling the interactive serial console on an instance."""


class EnableInteractiveSerialConsoleResponse(_messages.Message):
  r"""Message for response of EnableInteractiveSerialConsole."""


class EvictLunRequest(_messages.Message):
  r"""Request for skip lun cooloff and delete it."""


class EvictVolumeRequest(_messages.Message):
  r"""Request for skip volume cooloff and delete it."""


class GoogleCloudBaremetalsolutionV2LogicalInterface(_messages.Message):
  r"""Each logical interface represents a logical abstraction of the
  underlying physical interface (for eg. bond, nic) of the instance. Each
  logical interface can effectively map to multiple network-IP pairs and still
  be mapped to one underlying physical interface.

  Fields:
    interfaceIndex: The index of the logical interface mapping to the index of
      the hardware bond or nic on the chosen network template. This field is
      deprecated.
    logicalNetworkInterfaces: List of logical network interfaces within a
      logical interface.
    name: Interface name. This is of syntax or and forms part of the network
      template name.
  """

  interfaceIndex = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  logicalNetworkInterfaces = _messages.MessageField('LogicalNetworkInterface', 2, repeated=True)
  name = _messages.StringField(3)


class GoogleCloudBaremetalsolutionV2ServerNetworkTemplateLogicalInterface(_messages.Message):
  r"""Logical interface.

  Enums:
    TypeValueValuesEnum: Interface type.

  Fields:
    name: Interface name. This is not a globally unique identifier. Name is
      unique only inside the ServerNetworkTemplate. This is of syntax or and
      forms part of the network template name.
    required: If true, interface must have network connected.
    type: Interface type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Interface type.

    Values:
      INTERFACE_TYPE_UNSPECIFIED: Unspecified value.
      BOND: Bond interface type.
      NIC: NIC interface type.
    """
    INTERFACE_TYPE_UNSPECIFIED = 0
    BOND = 1
    NIC = 2

  name = _messages.StringField(1)
  required = _messages.BooleanField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Instance(_messages.Message):
  r"""A server.

  Enums:
    StateValueValuesEnum: Output only. The state of the server.
    WorkloadProfileValueValuesEnum: The workload profile for the instance.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    createTime: Output only. Create a time stamp.
    firmwareVersion: Output only. The firmware version for the instance.
    hyperthreadingEnabled: True if you enable hyperthreading for the server,
      otherwise false. The default value is false.
    id: Output only. An identifier for the `Instance`, generated by the
      backend.
    interactiveSerialConsoleEnabled: Output only. True if the interactive
      serial console feature is enabled for the instance, false otherwise. The
      default value is false.
    kmsKeyVersion: Optional. Name of the KMS crypto key version used to
      encrypt the initial passwords. The key has to have ASYMMETRIC_DECRYPT
      purpose. Format is `projects/{project}/locations/{location}/keyRings/{ke
      yring}/cryptoKeys/{key}/cryptoKeyVersions/{version}`.
    labels: Labels as key value pairs.
    logicalInterfaces: List of logical interfaces for the instance. The number
      of logical interfaces will be the same as number of hardware bond/nic on
      the chosen network template. For the non-multivlan configurations (for
      eg, existing servers) that use existing default network template
      (bondaa-bondaa), both the Instance.networks field and the
      Instance.logical_interfaces fields will be filled to ensure backward
      compatibility. For the others, only Instance.logical_interfaces will be
      filled.
    loginInfo: Output only. Text field about info for logging in.
    luns: Immutable. List of LUNs associated with this server.
    machineType: Immutable. The server type. [Available server
      types](https://cloud.google.com/bare-metal/docs/bms-
      planning#server_configurations)
    name: Immutable. The resource name of this `Instance`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/instances/{instance}`
    networkTemplate: Instance network template name. For eg, bondaa-bondaa,
      bondab-nic, etc. Generally, the template name follows the syntax of
      "bond" or "nic".
    networks: Output only. List of networks associated with this server.
    osImage: The OS image currently installed on the server.
    pod: Immutable. Pod name. Pod is an independent part of infrastructure.
      Instance can only be connected to the assets (networks, volumes)
      allocated in the same pod.
    sshKeys: Optional. List of SSH Keys used during instance provisioning.
    state: Output only. The state of the server.
    updateTime: Output only. Update a time stamp.
    volumes: Input only. List of Volumes to attach to this Instance on
      creation. This field won't be populated in Get/List responses.
    workloadProfile: The workload profile for the instance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the server.

    Values:
      STATE_UNSPECIFIED: The server is in an unknown state.
      PROVISIONING: The server is being provisioned.
      RUNNING: The server is running.
      DELETED: The server has been deleted.
      UPDATING: The server is being updated.
      STARTING: The server is starting.
      STOPPING: The server is stopping.
      SHUTDOWN: The server is shutdown.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    DELETED = 3
    UPDATING = 4
    STARTING = 5
    STOPPING = 6
    SHUTDOWN = 7

  class WorkloadProfileValueValuesEnum(_messages.Enum):
    r"""The workload profile for the instance.

    Values:
      WORKLOAD_PROFILE_UNSPECIFIED: The workload profile is in an unknown
        state.
      WORKLOAD_PROFILE_GENERIC: The workload profile is generic.
      WORKLOAD_PROFILE_HANA: The workload profile is hana.
    """
    WORKLOAD_PROFILE_UNSPECIFIED = 0
    WORKLOAD_PROFILE_GENERIC = 1
    WORKLOAD_PROFILE_HANA = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  firmwareVersion = _messages.StringField(2)
  hyperthreadingEnabled = _messages.BooleanField(3)
  id = _messages.StringField(4)
  interactiveSerialConsoleEnabled = _messages.BooleanField(5)
  kmsKeyVersion = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  logicalInterfaces = _messages.MessageField('GoogleCloudBaremetalsolutionV2LogicalInterface', 8, repeated=True)
  loginInfo = _messages.StringField(9)
  luns = _messages.MessageField('Lun', 10, repeated=True)
  machineType = _messages.StringField(11)
  name = _messages.StringField(12)
  networkTemplate = _messages.StringField(13)
  networks = _messages.MessageField('Network', 14, repeated=True)
  osImage = _messages.StringField(15)
  pod = _messages.StringField(16)
  sshKeys = _messages.StringField(17, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  updateTime = _messages.StringField(19)
  volumes = _messages.MessageField('Volume', 20, repeated=True)
  workloadProfile = _messages.EnumField('WorkloadProfileValueValuesEnum', 21)


class InstanceConfig(_messages.Message):
  r"""Configuration parameters for a new instance.

  Enums:
    NetworkConfigValueValuesEnum: The type of network configuration on the
      instance.

  Fields:
    accountNetworksEnabled: If true networks can be from different projects of
      the same vendor account.
    clientNetwork: Client network address. Filled if
      InstanceConfig.multivlan_config is false.
    hyperthreading: Whether the instance should be provisioned with
      Hyperthreading enabled.
    id: A transient unique identifier to idenfity an instance within an
      ProvisioningConfig request.
    instanceType: Instance type. [Available
      types](https://cloud.google.com/bare-metal/docs/bms-
      planning#server_configurations)
    kmsKeyVersion: Name of the KMS crypto key version used to encrypt the
      initial passwords. The key has to have ASYMMETRIC_DECRYPT purpose.
    logicalInterfaces: List of logical interfaces for the instance. The number
      of logical interfaces will be the same as number of hardware bond/nic on
      the chosen network template. Filled if InstanceConfig.multivlan_config
      is true.
    name: The name of the instance config.
    networkConfig: The type of network configuration on the instance.
    networkTemplate: Server network template name. Filled if
      InstanceConfig.multivlan_config is true.
    osImage: OS image to initialize the instance. [Available
      images](https://cloud.google.com/bare-metal/docs/bms-
      planning#server_configurations)
    privateNetwork: Private network address, if any. Filled if
      InstanceConfig.multivlan_config is false.
    sshKeyNames: Optional. List of names of ssh keys used to provision the
      instance.
    userNote: User note field, it can be used by customers to add additional
      information for the BMS Ops team .
  """

  class NetworkConfigValueValuesEnum(_messages.Enum):
    r"""The type of network configuration on the instance.

    Values:
      NETWORKCONFIG_UNSPECIFIED: The unspecified network configuration.
      SINGLE_VLAN: Instance part of single client network and single private
        network.
      MULTI_VLAN: Instance part of multiple (or single) client networks and
        private networks.
    """
    NETWORKCONFIG_UNSPECIFIED = 0
    SINGLE_VLAN = 1
    MULTI_VLAN = 2

  accountNetworksEnabled = _messages.BooleanField(1)
  clientNetwork = _messages.MessageField('NetworkAddress', 2)
  hyperthreading = _messages.BooleanField(3)
  id = _messages.StringField(4)
  instanceType = _messages.StringField(5)
  kmsKeyVersion = _messages.StringField(6)
  logicalInterfaces = _messages.MessageField('GoogleCloudBaremetalsolutionV2LogicalInterface', 7, repeated=True)
  name = _messages.StringField(8)
  networkConfig = _messages.EnumField('NetworkConfigValueValuesEnum', 9)
  networkTemplate = _messages.StringField(10)
  osImage = _messages.StringField(11)
  privateNetwork = _messages.MessageField('NetworkAddress', 12)
  sshKeyNames = _messages.StringField(13, repeated=True)
  userNote = _messages.StringField(14)


class InstanceQuota(_messages.Message):
  r"""A resource budget.

  Fields:
    availableMachineCount: Number of machines than can be created for the
      given location and instance_type.
    gcpService: The gcp service of the provisioning quota.
    instanceType: Instance type. Deprecated: use gcp_service.
    location: Location where the quota applies.
    name: Output only. The name of the instance quota.
  """

  availableMachineCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcpService = _messages.StringField(2)
  instanceType = _messages.StringField(3)
  location = _messages.StringField(4)
  name = _messages.StringField(5)


class IntakeVlanAttachment(_messages.Message):
  r"""A GCP vlan attachment.

  Fields:
    id: Identifier of the VLAN attachment.
    pairingKey: Attachment pairing key.
  """

  id = _messages.StringField(1)
  pairingKey = _messages.StringField(2)


class ListInstanceQuotasResponse(_messages.Message):
  r"""Response message for the list of Instance provisioning quotas.

  Fields:
    instanceQuotas: The provisioning quotas registered in this project.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  instanceQuotas = _messages.MessageField('InstanceQuota', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstancesResponse(_messages.Message):
  r"""Response message for the list of servers.

  Fields:
    instances: The list of servers.
    nextPageToken: A token identifying a page of results from the server.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLunsResponse(_messages.Message):
  r"""Response message containing the list of storage volume luns.

  Fields:
    luns: The list of luns.
    nextPageToken: A token identifying a page of results from the server.
    unreachable: Locations that could not be reached.
  """

  luns = _messages.MessageField('Lun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNetworkQuotasResponse(_messages.Message):
  r"""Response message for the list of Network provisioning quotas.

  Fields:
    networkQuotas: The provisioning quotas registered in this project.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  networkQuotas = _messages.MessageField('NetworkQuota', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNetworkUsageResponse(_messages.Message):
  r"""Response with Networks with IPs

  Fields:
    networks: Networks with IPs.
  """

  networks = _messages.MessageField('NetworkUsage', 1, repeated=True)


class ListNetworksResponse(_messages.Message):
  r"""Response message containing the list of networks.

  Fields:
    networks: The list of networks.
    nextPageToken: A token identifying a page of results from the server.
    unreachable: Locations that could not be reached.
  """

  networks = _messages.MessageField('Network', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNfsSharesResponse(_messages.Message):
  r"""Response message containing the list of NFS shares.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    nfsShares: The list of NFS shares.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  nfsShares = _messages.MessageField('NfsShare', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOSImagesResponse(_messages.Message):
  r"""Request for getting all available OS images.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    osImages: The OS images available.
  """

  nextPageToken = _messages.StringField(1)
  osImages = _messages.MessageField('OSImage', 2, repeated=True)


class ListProvisioningQuotasResponse(_messages.Message):
  r"""Response message for the list of provisioning quotas.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    provisioningQuotas: The provisioning quotas registered in this project.
  """

  nextPageToken = _messages.StringField(1)
  provisioningQuotas = _messages.MessageField('ProvisioningQuota', 2, repeated=True)


class ListSSHKeysResponse(_messages.Message):
  r"""Message for response of ListSSHKeys.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    sshKeys: The SSH keys registered in the project.
  """

  nextPageToken = _messages.StringField(1)
  sshKeys = _messages.MessageField('SSHKey', 2, repeated=True)


class ListSnapshotSchedulePoliciesResponse(_messages.Message):
  r"""Response message containing the list of snapshot schedule policies.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    snapshotSchedulePolicies: The snapshot schedule policies registered in
      this project.
  """

  nextPageToken = _messages.StringField(1)
  snapshotSchedulePolicies = _messages.MessageField('SnapshotSchedulePolicy', 2, repeated=True)


class ListStorageQuotasResponse(_messages.Message):
  r"""Response message for the list of Storage provisioning quotas.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    storageQuotas: The provisioning quotas registered in this project.
  """

  nextPageToken = _messages.StringField(1)
  storageQuotas = _messages.MessageField('StorageQuota', 2, repeated=True)


class ListVolumeSnapshotsResponse(_messages.Message):
  r"""Response message containing the list of volume snapshots.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    unreachable: Locations that could not be reached.
    volumeSnapshots: The list of snapshots.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  volumeSnapshots = _messages.MessageField('VolumeSnapshot', 3, repeated=True)


class ListVolumesResponse(_messages.Message):
  r"""Response message containing the list of storage volumes.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    unreachable: Locations that could not be reached.
    volumes: The list of storage volumes.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  volumes = _messages.MessageField('Volume', 3, repeated=True)


class LoadInstanceAuthInfoResponse(_messages.Message):
  r"""Response for LoadInstanceAuthInfo.

  Messages:
    UserAccountsValue: Map of username to the user account info.

  Fields:
    sshKeys: List of ssh keys.
    userAccounts: Map of username to the user account info.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserAccountsValue(_messages.Message):
    r"""Map of username to the user account info.

    Messages:
      AdditionalProperty: An additional property for a UserAccountsValue
        object.

    Fields:
      additionalProperties: Additional properties of type UserAccountsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserAccountsValue object.

      Fields:
        key: Name of the additional property.
        value: A UserAccount attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('UserAccount', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  sshKeys = _messages.MessageField('SSHKey', 1, repeated=True)
  userAccounts = _messages.MessageField('UserAccountsValue', 2)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogicalNetworkInterface(_messages.Message):
  r"""Each logical network interface is effectively a network and IP pair.

  Enums:
    NetworkTypeValueValuesEnum: Type of network.

  Fields:
    defaultGateway: Whether this interface is the default gateway for the
      instance. Only one interface can be the default gateway for the
      instance.
    id: An identifier for the `Network`, generated by the backend.
    ipAddress: IP address in the network
    network: Name of the network
    networkType: Type of network.
  """

  class NetworkTypeValueValuesEnum(_messages.Enum):
    r"""Type of network.

    Values:
      TYPE_UNSPECIFIED: Unspecified value.
      CLIENT: Client network, a network peered to a Google Cloud VPC.
      PRIVATE: Private network, a network local to the Bare Metal Solution
        environment.
    """
    TYPE_UNSPECIFIED = 0
    CLIENT = 1
    PRIVATE = 2

  defaultGateway = _messages.BooleanField(1)
  id = _messages.StringField(2)
  ipAddress = _messages.StringField(3)
  network = _messages.StringField(4)
  networkType = _messages.EnumField('NetworkTypeValueValuesEnum', 5)


class Lun(_messages.Message):
  r"""A storage volume logical unit number (LUN).

  Enums:
    MultiprotocolTypeValueValuesEnum: The LUN multiprotocol type ensures the
      characteristics of the LUN are optimized for each operating system.
    StateValueValuesEnum: The state of this storage volume.
    StorageTypeValueValuesEnum: The storage type for this LUN.

  Fields:
    bootLun: Display if this LUN is a boot LUN.
    expireTime: Output only. Time after which LUN will be fully deleted. It is
      filled only for LUNs in COOL_OFF state.
    id: An identifier for the LUN, generated by the backend.
    instances: Output only. Instances this Lun is attached to.
    multiprotocolType: The LUN multiprotocol type ensures the characteristics
      of the LUN are optimized for each operating system.
    name: Output only. The name of the LUN.
    shareable: Display if this LUN can be shared between multiple physical
      servers.
    sizeGb: The size of this LUN, in GiB.
    state: The state of this storage volume.
    storageType: The storage type for this LUN.
    storageVolume: Display the storage volume for this LUN.
    wwid: The WWID for this LUN.
  """

  class MultiprotocolTypeValueValuesEnum(_messages.Enum):
    r"""The LUN multiprotocol type ensures the characteristics of the LUN are
    optimized for each operating system.

    Values:
      MULTIPROTOCOL_TYPE_UNSPECIFIED: Server has no OS specified.
      LINUX: Server with Linux OS.
    """
    MULTIPROTOCOL_TYPE_UNSPECIFIED = 0
    LINUX = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of this storage volume.

    Values:
      STATE_UNSPECIFIED: The LUN is in an unknown state.
      CREATING: The LUN is being created.
      UPDATING: The LUN is being updated.
      READY: The LUN is ready for use.
      DELETING: The LUN has been requested to be deleted.
      COOL_OFF: The LUN is in cool off state. It will be deleted after
        `expire_time`.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    UPDATING = 2
    READY = 3
    DELETING = 4
    COOL_OFF = 5

  class StorageTypeValueValuesEnum(_messages.Enum):
    r"""The storage type for this LUN.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The storage type for this LUN is unknown.
      SSD: This storage type for this LUN is SSD.
      HDD: This storage type for this LUN is HDD.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  bootLun = _messages.BooleanField(1)
  expireTime = _messages.StringField(2)
  id = _messages.StringField(3)
  instances = _messages.StringField(4, repeated=True)
  multiprotocolType = _messages.EnumField('MultiprotocolTypeValueValuesEnum', 5)
  name = _messages.StringField(6)
  shareable = _messages.BooleanField(7)
  sizeGb = _messages.IntegerField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  storageType = _messages.EnumField('StorageTypeValueValuesEnum', 10)
  storageVolume = _messages.StringField(11)
  wwid = _messages.StringField(12)


class LunRange(_messages.Message):
  r"""A LUN(Logical Unit Number) range.

  Fields:
    quantity: Number of LUNs to create.
    sizeGb: The requested size of each LUN, in GB.
  """

  quantity = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Network(_messages.Message):
  r"""A Network.

  Enums:
    StateValueValuesEnum: The Network state.
    TypeValueValuesEnum: The type of this network.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    cidr: The cidr of the Network.
    gatewayIp: Output only. Gateway ip address.
    id: An identifier for the `Network`, generated by the backend.
    ipAddress: IP address configured.
    jumboFramesEnabled: Whether network uses standard frames or jumbo ones.
    labels: Labels as key value pairs.
    macAddress: List of physical interfaces.
    mountPoints: Input only. List of mount points to attach the network to.
    name: Output only. The resource name of this `Network`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/networks/{network}`
    pod: Immutable. Pod name. Pod is an independent part of infrastructure.
      Network can only be connected to the assets (instances, nfsshares)
      allocated in the same pod.
    reservations: List of IP address reservations in this network. When
      updating this field, an error will be generated if a reservation
      conflicts with an IP address already allocated to a physical server.
    servicesCidr: IP range for reserved for services (e.g. NFS).
    state: The Network state.
    type: The type of this network.
    vlanId: The vlan id of the Network.
    vrf: The Vrf for the Network. Use this only if a new Vrf needs to be
      created.
    vrfAttachment: Optional. The name of a pre-existing Vrf that the network
      should be attached to. Format is `vrfs/{vrf}`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The Network state.

    Values:
      STATE_UNSPECIFIED: The Network is in an unknown state.
      PROVISIONING: The Network is provisioning.
      PROVISIONED: The Network has been provisioned.
      DEPROVISIONING: The Network is being deprovisioned.
      UPDATING: The Network is being updated.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    PROVISIONED = 2
    DEPROVISIONING = 3
    UPDATING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this network.

    Values:
      TYPE_UNSPECIFIED: Unspecified value.
      CLIENT: Client network, a network peered to a Google Cloud VPC.
      PRIVATE: Private network, a network local to the Bare Metal Solution
        environment.
    """
    TYPE_UNSPECIFIED = 0
    CLIENT = 1
    PRIVATE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cidr = _messages.StringField(1)
  gatewayIp = _messages.StringField(2)
  id = _messages.StringField(3)
  ipAddress = _messages.StringField(4)
  jumboFramesEnabled = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  macAddress = _messages.StringField(7, repeated=True)
  mountPoints = _messages.MessageField('NetworkMountPoint', 8, repeated=True)
  name = _messages.StringField(9)
  pod = _messages.StringField(10)
  reservations = _messages.MessageField('NetworkAddressReservation', 11, repeated=True)
  servicesCidr = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  type = _messages.EnumField('TypeValueValuesEnum', 14)
  vlanId = _messages.StringField(15)
  vrf = _messages.MessageField('VRF', 16)
  vrfAttachment = _messages.StringField(17)


class NetworkAddress(_messages.Message):
  r"""A network.

  Fields:
    address: IPv4 address to be assigned to the server.
    existingNetworkId: Name of the existing network to use.
    networkId: Id of the network to use, within the same ProvisioningConfig
      request.
  """

  address = _messages.StringField(1)
  existingNetworkId = _messages.StringField(2)
  networkId = _messages.StringField(3)


class NetworkAddressReservation(_messages.Message):
  r"""A reservation of one or more addresses in a network.

  Fields:
    endAddress: The last address of this reservation block, inclusive. I.e.,
      for cases when reservations are only single addresses, end_address and
      start_address will be the same. Must be specified as a single IPv4
      address, e.g. ********.
    note: A note about this reservation, intended for human consumption.
    startAddress: The first address of this reservation block. Must be
      specified as a single IPv4 address, e.g. ********.
  """

  endAddress = _messages.StringField(1)
  note = _messages.StringField(2)
  startAddress = _messages.StringField(3)


class NetworkConfig(_messages.Message):
  r"""Configuration parameters for a new network.

  Enums:
    BandwidthValueValuesEnum: Interconnect bandwidth. Set only when type is
      CLIENT.
    ServiceCidrValueValuesEnum: Service CIDR, if any.
    TypeValueValuesEnum: The type of this network, either Client or Private.

  Fields:
    bandwidth: Interconnect bandwidth. Set only when type is CLIENT.
    cidr: CIDR range of the network.
    gcpService: The GCP service of the network. Available gcp_service are in
      https://cloud.google.com/bare-metal/docs/bms-planning.
    id: A transient unique identifier to identify a volume within an
      ProvisioningConfig request.
    jumboFramesEnabled: The JumboFramesEnabled option for customer to set.
    name: Output only. The name of the network config.
    serviceCidr: Service CIDR, if any.
    type: The type of this network, either Client or Private.
    userNote: User note field, it can be used by customers to add additional
      information for the BMS Ops team .
    vlanAttachments: List of VLAN attachments. As of now there are always 2
      attachments, but it is going to change in the future (multi vlan). Use
      only one of vlan_attachments or vrf
    vlanSameProject: Whether the VLAN attachment pair is located in the same
      project.
    vrf: Optional. The name of a pre-existing Vrf that the network should be
      attached to. Format is `vrfs/{vrf}`. If vrf is specified,
      vlan_attachments must be empty.
  """

  class BandwidthValueValuesEnum(_messages.Enum):
    r"""Interconnect bandwidth. Set only when type is CLIENT.

    Values:
      BANDWIDTH_UNSPECIFIED: Unspecified value.
      BW_1_GBPS: 1 Gbps.
      BW_2_GBPS: 2 Gbps.
      BW_5_GBPS: 5 Gbps.
      BW_10_GBPS: 10 Gbps.
    """
    BANDWIDTH_UNSPECIFIED = 0
    BW_1_GBPS = 1
    BW_2_GBPS = 2
    BW_5_GBPS = 3
    BW_10_GBPS = 4

  class ServiceCidrValueValuesEnum(_messages.Enum):
    r"""Service CIDR, if any.

    Values:
      SERVICE_CIDR_UNSPECIFIED: Unspecified value.
      DISABLED: Services are disabled for the given network.
      HIGH_26: Use the highest /26 block of the network to host services.
      HIGH_27: Use the highest /27 block of the network to host services.
      HIGH_28: Use the highest /28 block of the network to host services.
    """
    SERVICE_CIDR_UNSPECIFIED = 0
    DISABLED = 1
    HIGH_26 = 2
    HIGH_27 = 3
    HIGH_28 = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this network, either Client or Private.

    Values:
      TYPE_UNSPECIFIED: Unspecified value.
      CLIENT: Client network, that is a network peered to a GCP VPC.
      PRIVATE: Private network, that is a network local to the BMS POD.
    """
    TYPE_UNSPECIFIED = 0
    CLIENT = 1
    PRIVATE = 2

  bandwidth = _messages.EnumField('BandwidthValueValuesEnum', 1)
  cidr = _messages.StringField(2)
  gcpService = _messages.StringField(3)
  id = _messages.StringField(4)
  jumboFramesEnabled = _messages.BooleanField(5)
  name = _messages.StringField(6)
  serviceCidr = _messages.EnumField('ServiceCidrValueValuesEnum', 7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)
  userNote = _messages.StringField(9)
  vlanAttachments = _messages.MessageField('IntakeVlanAttachment', 10, repeated=True)
  vlanSameProject = _messages.BooleanField(11)
  vrf = _messages.StringField(12)


class NetworkMountPoint(_messages.Message):
  r"""Mount point for a network.

  Fields:
    defaultGateway: Network should be a default gateway.
    instance: Instance to attach network to.
    ipAddress: Ip address of the server.
    logicalInterface: Logical interface to detach from.
  """

  defaultGateway = _messages.BooleanField(1)
  instance = _messages.StringField(2)
  ipAddress = _messages.StringField(3)
  logicalInterface = _messages.StringField(4)


class NetworkQuota(_messages.Message):
  r"""A network provisioning quota.

  Fields:
    availableBandwidthGbps: Network bandwidth, Gbps
    gcpService: The gcp service of the provisioning quota.
    name: Output only. The name of the provisioning quota.
  """

  availableBandwidthGbps = _messages.IntegerField(1)
  gcpService = _messages.StringField(2)
  name = _messages.StringField(3)


class NetworkUsage(_messages.Message):
  r"""Network with all used IP addresses.

  Fields:
    network: Network.
    usedIps: All used IP addresses in this network.
  """

  network = _messages.MessageField('Network', 1)
  usedIps = _messages.StringField(2, repeated=True)


class NfsExport(_messages.Message):
  r"""A NFS export entry.

  Enums:
    PermissionsValueValuesEnum: Export permissions.

  Fields:
    allowDev: Allow dev flag in NfsShare AllowedClientsRequest.
    allowSuid: Allow the setuid flag.
    cidr: A CIDR range.
    machineId: Either a single machine, identified by an ID, or a comma-
      separated list of machine IDs.
    networkId: Network to use to publish the export.
    noRootSquash: Disable root squashing, which is a feature of NFS. Root
      squash is a special mapping of the remote superuser (root) identity when
      using identity authentication.
    permissions: Export permissions.
  """

  class PermissionsValueValuesEnum(_messages.Enum):
    r"""Export permissions.

    Values:
      PERMISSIONS_UNSPECIFIED: Unspecified value.
      READ_ONLY: Read-only permission.
      READ_WRITE: Read-write permission.
    """
    PERMISSIONS_UNSPECIFIED = 0
    READ_ONLY = 1
    READ_WRITE = 2

  allowDev = _messages.BooleanField(1)
  allowSuid = _messages.BooleanField(2)
  cidr = _messages.StringField(3)
  machineId = _messages.StringField(4)
  networkId = _messages.StringField(5)
  noRootSquash = _messages.BooleanField(6)
  permissions = _messages.EnumField('PermissionsValueValuesEnum', 7)


class NfsShare(_messages.Message):
  r"""An NFS share.

  Enums:
    StateValueValuesEnum: Output only. The state of the NFS share.
    StorageTypeValueValuesEnum: Immutable. The storage type of the underlying
      volume.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    allowedClients: List of allowed access points.
    id: Output only. An identifier for the NFS share, generated by the
      backend. This is the same value as nfs_share_id and will replace it in
      the future.
    labels: Labels as key value pairs.
    name: Immutable. The name of the NFS share.
    nfsShareId: Output only. An identifier for the NFS share, generated by the
      backend. This field will be deprecated in the future, use `id` instead.
    pod: Immutable. Pod name. Pod is an independent part of infrastructure.
      NFSShare can only be connected to the assets (networks, instances)
      allocated in the same pod.
    requestedSizeGib: The requested size, in GiB.
    state: Output only. The state of the NFS share.
    storageType: Immutable. The storage type of the underlying volume.
    volume: Output only. The underlying volume of the share. Created
      automatically during provisioning.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the NFS share.

    Values:
      STATE_UNSPECIFIED: The share is in an unknown state.
      PROVISIONED: The share has been provisioned.
      CREATING: The NFS Share is being created.
      UPDATING: The NFS Share is being updated.
      DELETING: The NFS Share has been requested to be deleted.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONED = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4

  class StorageTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The storage type of the underlying volume.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The storage type for this volume is unknown.
      SSD: The storage type for this volume is SSD.
      HDD: This storage type for this volume is HDD.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowedClients = _messages.MessageField('AllowedClient', 1, repeated=True)
  id = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  nfsShareId = _messages.StringField(5)
  pod = _messages.StringField(6)
  requestedSizeGib = _messages.IntegerField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  storageType = _messages.EnumField('StorageTypeValueValuesEnum', 9)
  volume = _messages.StringField(10)


class OSImage(_messages.Message):
  r"""Operation System image.

  Fields:
    applicableInstanceTypes: Instance types this image is applicable to.
      [Available types](https://cloud.google.com/bare-metal/docs/bms-
      planning#server_configurations)
    code: OS Image code.
    description: OS Image description.
    name: Output only. OS Image's unique name.
    supportedNetworkTemplates: Network templates that can be used with this OS
      Image.
  """

  applicableInstanceTypes = _messages.StringField(1, repeated=True)
  code = _messages.StringField(2)
  description = _messages.StringField(3)
  name = _messages.StringField(4)
  supportedNetworkTemplates = _messages.StringField(5, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class ProvisioningConfig(_messages.Message):
  r"""A provisioning configuration.

  Enums:
    StateValueValuesEnum: Output only. State of ProvisioningConfig.

  Fields:
    cloudConsoleUri: Output only. URI to Cloud Console UI view of this
      provisioning config.
    customId: Optional. The user-defined identifier of the provisioning
      config.
    email: Email provided to send a confirmation with provisioning config to.
      Deprecated in favour of email field in request messages.
    handoverServiceAccount: A service account to enable customers to access
      instance credentials upon handover.
    instances: Instances to be created.
    location: Optional. Location name of this ProvisioningConfig. It is
      optional only for Intake UI transition period.
    name: Output only. The system-generated name of the provisioning config.
      This follows the UUID format.
    networks: Networks to be created.
    pod: Optional. Pod name. Pod is an independent part of infrastructure.
      Instance can be connected to the assets (networks, volumes, nfsshares)
      allocated in the same pod only.
    state: Output only. State of ProvisioningConfig.
    statusMessage: Optional status messages associated with the FAILED state.
    ticketId: A generated ticket id to track provisioning request.
    updateTime: Output only. Last update timestamp.
    volumes: Volumes to be created.
    vpcScEnabled: If true, VPC SC is enabled for the cluster.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of ProvisioningConfig.

    Values:
      STATE_UNSPECIFIED: State wasn't specified.
      DRAFT: ProvisioningConfig is a draft and can be freely modified.
      SUBMITTED: ProvisioningConfig was already submitted and cannot be
        modified.
      PROVISIONING: ProvisioningConfig was in the provisioning state.
        Initially this state comes from the work order table in big query when
        SNOW is used. Later this field can be set by the work order API.
      PROVISIONED: ProvisioningConfig was provisioned, meaning the resources
        exist.
      VALIDATED: ProvisioningConfig was validated. A validation tool will be
        run to set this state.
      CANCELLED: ProvisioningConfig was canceled.
      FAILED: The request is submitted for provisioning, with error return.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    SUBMITTED = 2
    PROVISIONING = 3
    PROVISIONED = 4
    VALIDATED = 5
    CANCELLED = 6
    FAILED = 7

  cloudConsoleUri = _messages.StringField(1)
  customId = _messages.StringField(2)
  email = _messages.StringField(3)
  handoverServiceAccount = _messages.StringField(4)
  instances = _messages.MessageField('InstanceConfig', 5, repeated=True)
  location = _messages.StringField(6)
  name = _messages.StringField(7)
  networks = _messages.MessageField('NetworkConfig', 8, repeated=True)
  pod = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  statusMessage = _messages.StringField(11)
  ticketId = _messages.StringField(12)
  updateTime = _messages.StringField(13)
  volumes = _messages.MessageField('VolumeConfig', 14, repeated=True)
  vpcScEnabled = _messages.BooleanField(15)


class ProvisioningQuota(_messages.Message):
  r"""A provisioning quota for a given project.

  Enums:
    AssetTypeValueValuesEnum: The asset type of this provisioning quota.

  Fields:
    assetType: The asset type of this provisioning quota.
    availableCount: The available count of the provisioning quota.
    gcpService: The gcp service of the provisioning quota.
    instanceQuota: Instance quota.
    location: The specific location of the provisioining quota.
    name: Output only. The name of the provisioning quota.
    networkBandwidth: Network bandwidth, Gbps
    serverCount: Server count.
    storageGib: Storage size (GB).
  """

  class AssetTypeValueValuesEnum(_messages.Enum):
    r"""The asset type of this provisioning quota.

    Values:
      ASSET_TYPE_UNSPECIFIED: The unspecified type.
      ASSET_TYPE_SERVER: The server asset type.
      ASSET_TYPE_STORAGE: The storage asset type.
      ASSET_TYPE_NETWORK: The network asset type.
    """
    ASSET_TYPE_UNSPECIFIED = 0
    ASSET_TYPE_SERVER = 1
    ASSET_TYPE_STORAGE = 2
    ASSET_TYPE_NETWORK = 3

  assetType = _messages.EnumField('AssetTypeValueValuesEnum', 1)
  availableCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  gcpService = _messages.StringField(3)
  instanceQuota = _messages.MessageField('InstanceQuota', 4)
  location = _messages.StringField(5)
  name = _messages.StringField(6)
  networkBandwidth = _messages.IntegerField(7)
  serverCount = _messages.IntegerField(8)
  storageGib = _messages.IntegerField(9)


class QosPolicy(_messages.Message):
  r"""QOS policy parameters.

  Fields:
    bandwidthGbps: The bandwidth permitted by the QOS policy, in gbps.
  """

  bandwidthGbps = _messages.FloatField(1)


class ReimageInstanceRequest(_messages.Message):
  r"""Message requesting to perform reimage operation on a server.

  Fields:
    kmsKeyVersion: Optional. Name of the KMS crypto key version used to
      encrypt the initial passwords. The key has to have ASYMMETRIC_DECRYPT
      purpose. Format is `projects/{project}/locations/{location}/keyRings/{ke
      yring}/cryptoKeys/{key}/cryptoKeyVersions/{version}`.
    osImage: Required. The OS image code of the image which will be used in
      the reimage operation.
    sshKeys: Optional. List of SSH Keys used during reimaging an instance.
  """

  kmsKeyVersion = _messages.StringField(1)
  osImage = _messages.StringField(2)
  sshKeys = _messages.StringField(3, repeated=True)


class RenameInstanceRequest(_messages.Message):
  r"""Message requesting rename of a server.

  Fields:
    newInstanceId: Required. The new `id` of the instance.
  """

  newInstanceId = _messages.StringField(1)


class RenameNetworkRequest(_messages.Message):
  r"""Message requesting rename of a server.

  Fields:
    newNetworkId: Required. The new `id` of the network.
  """

  newNetworkId = _messages.StringField(1)


class RenameNfsShareRequest(_messages.Message):
  r"""Message requesting rename of a server.

  Fields:
    newNfsshareId: Required. The new `id` of the nfsshare.
  """

  newNfsshareId = _messages.StringField(1)


class RenameVolumeRequest(_messages.Message):
  r"""Message requesting rename of a server.

  Fields:
    newVolumeId: Required. The new `id` of the volume.
  """

  newVolumeId = _messages.StringField(1)


class ResetInstanceRequest(_messages.Message):
  r"""Message requesting to reset a server."""


class ResetInstanceResponse(_messages.Message):
  r"""Response message from resetting a server."""


class ResizeVolumeRequest(_messages.Message):
  r"""Request for emergency resize Volume.

  Fields:
    sizeGib: New Volume size, in GiB.
  """

  sizeGib = _messages.IntegerField(1)


class RestoreVolumeSnapshotRequest(_messages.Message):
  r"""Message for restoring a volume snapshot."""


class SSHKey(_messages.Message):
  r"""An SSH key, used for authorizing with the interactive serial console
  feature.

  Fields:
    name: Output only. The name of this SSH key. Currently, the only valid
      value for the location is "global".
    publicKey: The public SSH key. This must be in OpenSSH .authorized_keys
      format.
  """

  name = _messages.StringField(1)
  publicKey = _messages.StringField(2)


class Schedule(_messages.Message):
  r"""A snapshot schedule.

  Fields:
    crontabSpec: A crontab-like specification that the schedule uses to take
      snapshots.
    prefix: A list of snapshot names created in this schedule.
    retentionCount: The maximum number of snapshots to retain in this
      schedule.
  """

  crontabSpec = _messages.StringField(1)
  prefix = _messages.StringField(2)
  retentionCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ServerNetworkTemplate(_messages.Message):
  r"""Network template.

  Fields:
    applicableInstanceTypes: Instance types this template is applicable to.
    logicalInterfaces: Logical interfaces.
    name: Output only. Template's unique name. The full resource name follows
      the pattern: `projects/{project}/locations/{location}/serverNetworkTempl
      ate/{server_network_template}` Generally, the {server_network_template}
      follows the syntax of "bond" or "nic".
  """

  applicableInstanceTypes = _messages.StringField(1, repeated=True)
  logicalInterfaces = _messages.MessageField('GoogleCloudBaremetalsolutionV2ServerNetworkTemplateLogicalInterface', 2, repeated=True)
  name = _messages.StringField(3)


class SnapshotReservationDetail(_messages.Message):
  r"""Details about snapshot space reservation and usage on the storage
  volume.

  Fields:
    reservedSpaceGib: The space on this storage volume reserved for snapshots,
      shown in GiB.
    reservedSpacePercent: Percent of the total Volume size reserved for
      snapshot copies. Enabling snapshots requires reserving 20% or more of
      the storage volume space for snapshots. Maximum reserved space for
      snapshots is 40%. Setting this field will effectively set
      snapshot_enabled to true.
    reservedSpaceRemainingGib: The amount, in GiB, of available space in this
      storage volume's reserved snapshot space.
    reservedSpaceUsedPercent: The percent of snapshot space on this storage
      volume actually being used by the snapshot copies. This value might be
      higher than 100% if the snapshot copies have overflowed into the data
      portion of the storage volume.
  """

  reservedSpaceGib = _messages.IntegerField(1)
  reservedSpacePercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  reservedSpaceRemainingGib = _messages.IntegerField(3)
  reservedSpaceUsedPercent = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class SnapshotSchedulePolicy(_messages.Message):
  r"""A snapshot schedule policy.

  Enums:
    StateValueValuesEnum: The state of the snapshot schedule policy.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    description: The description of the snapshot schedule policy.
    id: An identifier for the snapshot schedule policy, generated by the
      backend.
    labels: Labels as key value pairs.
    name: Output only. The name of the snapshot schedule policy.
    pod: Immutable. Pod name. Pod is an independent part of infrastructure.
      SnapshotSchedulePolicies can only be connected to the volumes allocated
      in the same pod.
    schedules: The snapshot schedules contained in this policy. You can
      specify a maximum of 5 schedules.
    state: The state of the snapshot schedule policy.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the snapshot schedule policy.

    Values:
      STATE_UNSPECIFIED: The policy is in an unknown state.
      PROVISIONED: The policy is been provisioned.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONED = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  id = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  pod = _messages.StringField(5)
  schedules = _messages.MessageField('Schedule', 6, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartInstanceRequest(_messages.Message):
  r"""Message requesting to start a server."""


class StartInstanceResponse(_messages.Message):
  r"""Response message from starting a server."""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopInstanceRequest(_messages.Message):
  r"""Message requesting to stop a server."""


class StopInstanceResponse(_messages.Message):
  r"""Response message from stopping a server."""


class StorageQuota(_messages.Message):
  r"""A storage provisioning quota .

  Fields:
    availableGib: Storage size (GiB).
    gcpService: The gcp service of the provisioning quota.
    name: Output only. The name of the provisioning quota.
  """

  availableGib = _messages.IntegerField(1)
  gcpService = _messages.StringField(2)
  name = _messages.StringField(3)


class SubmitProvisioningConfigRequest(_messages.Message):
  r"""Request for SubmitProvisioningConfig.

  Fields:
    email: Optional. Email provided to send a confirmation with provisioning
      config to.
    provisioningConfig: Required. The ProvisioningConfig to create.
  """

  email = _messages.StringField(1)
  provisioningConfig = _messages.MessageField('ProvisioningConfig', 2)


class SubmitProvisioningConfigResponse(_messages.Message):
  r"""Response for SubmitProvisioningConfig.

  Fields:
    provisioningConfig: The submitted provisioning config.
  """

  provisioningConfig = _messages.MessageField('ProvisioningConfig', 1)


class UserAccount(_messages.Message):
  r"""User account provisioned for the customer.

  Fields:
    encryptedPassword: Encrypted initial password value.
    kmsKeyVersion: KMS CryptoKey Version used to encrypt the password.
  """

  encryptedPassword = _messages.StringField(1)
  kmsKeyVersion = _messages.StringField(2)


class VRF(_messages.Message):
  r"""A network VRF.

  Enums:
    StateValueValuesEnum: The possible state of VRF.

  Fields:
    name: The name of the VRF.
    qosPolicy: The QOS policy applied to this VRF. The value is only
      meaningful when all the vlan attachments have the same QoS. This field
      should not be used for new integrations, use vlan attachment level qos
      instead. The field is left for backward-compatibility.
    state: The possible state of VRF.
    vlanAttachments: The list of VLAN attachments for the VRF.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The possible state of VRF.

    Values:
      STATE_UNSPECIFIED: The unspecified state.
      PROVISIONING: The vrf is provisioning.
      PROVISIONED: The vrf is provisioned.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    PROVISIONED = 2

  name = _messages.StringField(1)
  qosPolicy = _messages.MessageField('QosPolicy', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  vlanAttachments = _messages.MessageField('VlanAttachment', 4, repeated=True)


class VlanAttachment(_messages.Message):
  r"""VLAN attachment details.

  Fields:
    id: Immutable. The identifier of the attachment within vrf.
    interconnectAttachment: Optional. The name of the vlan attachment within
      vrf. This is of the form projects/{project_number}/regions/{region}/inte
      rconnectAttachments/{interconnect_attachment}
    pairingKey: Input only. Pairing key.
    peerIp: The peer IP of the attachment.
    peerVlanId: The peer vlan ID of the attachment.
    qosPolicy: The QOS policy applied to this VLAN attachment. This value
      should be preferred to using qos at vrf level.
    routerIp: The router IP of the attachment.
  """

  id = _messages.StringField(1)
  interconnectAttachment = _messages.StringField(2)
  pairingKey = _messages.StringField(3)
  peerIp = _messages.StringField(4)
  peerVlanId = _messages.IntegerField(5)
  qosPolicy = _messages.MessageField('QosPolicy', 6)
  routerIp = _messages.StringField(7)


class Volume(_messages.Message):
  r"""A storage volume.

  Enums:
    PerformanceTierValueValuesEnum: Immutable. Performance tier of the Volume.
      Default is SHARED.
    ProtocolValueValuesEnum: Output only. Storage protocol for the Volume.
    SnapshotAutoDeleteBehaviorValueValuesEnum: The behavior to use when
      snapshot reserved space is full.
    StateValueValuesEnum: The state of this storage volume.
    StorageTypeValueValuesEnum: The storage type for this volume.
    WorkloadProfileValueValuesEnum: The workload profile for the volume.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    attached: Output only. Is the Volume attached at at least one instance.
      This field is a lightweight counterpart of `instances` field. It is
      filled in List responses as well.
    autoGrownSizeGib: The size, in GiB, that this storage volume has expanded
      as a result of an auto grow policy. In the absence of auto-grow, the
      value is 0.
    bootVolume: Output only. Whether this volume is a boot volume. A boot
      volume is one which contains a boot LUN.
    currentSizeGib: The current size of this storage volume, in GiB, including
      space reserved for snapshots. This size might be different than the
      requested size if the storage volume has been configured with auto grow
      or auto shrink.
    emergencySizeGib: Additional emergency size that was requested for this
      Volume, in GiB. current_size_gib includes this value.
    expireTime: Output only. Time after which volume will be fully deleted. It
      is filled only for volumes in COOLOFF state.
    id: An identifier for the `Volume`, generated by the backend.
    instances: Output only. Instances this Volume is attached to. This field
      is set only in Get requests.
    labels: Labels as key value pairs.
    maxSizeGib: Maximum size volume can be expanded to in case of evergency,
      in GiB.
    name: Output only. The resource name of this `Volume`. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/volumes/{volume}`
    notes: Input only. User-specified notes for new Volume. Used to provision
      Volumes that require manual intervention.
    originallyRequestedSizeGib: Originally requested size, in GiB.
    performanceTier: Immutable. Performance tier of the Volume. Default is
      SHARED.
    pod: Immutable. Pod name. Pod is an independent part of infrastructure.
      Volume can only be connected to the instances allocated in the same pod.
    protocol: Output only. Storage protocol for the Volume.
    remainingSpaceGib: The space remaining in the storage volume for new LUNs,
      in GiB, excluding space reserved for snapshots.
    requestedSizeGib: The requested size of this storage volume, in GiB.
    snapshotAutoDeleteBehavior: The behavior to use when snapshot reserved
      space is full.
    snapshotEnabled: Whether snapshots are enabled.
    snapshotReservationDetail: Details about snapshot space reservation and
      usage on the storage volume.
    snapshotSchedulePolicy: The name of the snapshot schedule policy in use
      for this volume, if any.
    state: The state of this storage volume.
    storageAggregatePool: Input only. Name of the storage aggregate pool to
      allocate the volume in. Can be used only for
      VOLUME_PERFORMANCE_TIER_ASSIGNED volumes.
    storageType: The storage type for this volume.
    workloadProfile: The workload profile for the volume.
  """

  class PerformanceTierValueValuesEnum(_messages.Enum):
    r"""Immutable. Performance tier of the Volume. Default is SHARED.

    Values:
      VOLUME_PERFORMANCE_TIER_UNSPECIFIED: Value is not specified.
      VOLUME_PERFORMANCE_TIER_SHARED: Regular volumes, shared aggregates.
      VOLUME_PERFORMANCE_TIER_ASSIGNED: Assigned aggregates.
      VOLUME_PERFORMANCE_TIER_HT: High throughput aggregates.
      VOLUME_PERFORMANCE_TIER_QOS2_PERFORMANCE: QoS 2.0 high performance
        storage.
    """
    VOLUME_PERFORMANCE_TIER_UNSPECIFIED = 0
    VOLUME_PERFORMANCE_TIER_SHARED = 1
    VOLUME_PERFORMANCE_TIER_ASSIGNED = 2
    VOLUME_PERFORMANCE_TIER_HT = 3
    VOLUME_PERFORMANCE_TIER_QOS2_PERFORMANCE = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Output only. Storage protocol for the Volume.

    Values:
      PROTOCOL_UNSPECIFIED: Value is not specified.
      FIBRE_CHANNEL: Fibre Channel protocol.
      NFS: NFS protocol means Volume is a NFS Share volume. Such volumes
        cannot be manipulated via Volumes API.
    """
    PROTOCOL_UNSPECIFIED = 0
    FIBRE_CHANNEL = 1
    NFS = 2

  class SnapshotAutoDeleteBehaviorValueValuesEnum(_messages.Enum):
    r"""The behavior to use when snapshot reserved space is full.

    Values:
      SNAPSHOT_AUTO_DELETE_BEHAVIOR_UNSPECIFIED: The unspecified behavior.
      DISABLED: Don't delete any snapshots. This disables new snapshot
        creation, as long as the snapshot reserved space is full.
      OLDEST_FIRST: Delete the oldest snapshots first.
      NEWEST_FIRST: Delete the newest snapshots first.
    """
    SNAPSHOT_AUTO_DELETE_BEHAVIOR_UNSPECIFIED = 0
    DISABLED = 1
    OLDEST_FIRST = 2
    NEWEST_FIRST = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of this storage volume.

    Values:
      STATE_UNSPECIFIED: The storage volume is in an unknown state.
      CREATING: The storage volume is being created.
      READY: The storage volume is ready for use.
      DELETING: The storage volume has been requested to be deleted.
      UPDATING: The storage volume is being updated.
      COOL_OFF: The storage volume is in cool off state. It will be deleted
        after `expire_time`.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4
    COOL_OFF = 5

  class StorageTypeValueValuesEnum(_messages.Enum):
    r"""The storage type for this volume.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The storage type for this volume is unknown.
      SSD: The storage type for this volume is SSD.
      HDD: This storage type for this volume is HDD.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  class WorkloadProfileValueValuesEnum(_messages.Enum):
    r"""The workload profile for the volume.

    Values:
      WORKLOAD_PROFILE_UNSPECIFIED: The workload profile is in an unknown
        state.
      GENERIC: The workload profile is generic.
      HANA: The workload profile is hana.
    """
    WORKLOAD_PROFILE_UNSPECIFIED = 0
    GENERIC = 1
    HANA = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attached = _messages.BooleanField(1)
  autoGrownSizeGib = _messages.IntegerField(2)
  bootVolume = _messages.BooleanField(3)
  currentSizeGib = _messages.IntegerField(4)
  emergencySizeGib = _messages.IntegerField(5)
  expireTime = _messages.StringField(6)
  id = _messages.StringField(7)
  instances = _messages.StringField(8, repeated=True)
  labels = _messages.MessageField('LabelsValue', 9)
  maxSizeGib = _messages.IntegerField(10)
  name = _messages.StringField(11)
  notes = _messages.StringField(12)
  originallyRequestedSizeGib = _messages.IntegerField(13)
  performanceTier = _messages.EnumField('PerformanceTierValueValuesEnum', 14)
  pod = _messages.StringField(15)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 16)
  remainingSpaceGib = _messages.IntegerField(17)
  requestedSizeGib = _messages.IntegerField(18)
  snapshotAutoDeleteBehavior = _messages.EnumField('SnapshotAutoDeleteBehaviorValueValuesEnum', 19)
  snapshotEnabled = _messages.BooleanField(20)
  snapshotReservationDetail = _messages.MessageField('SnapshotReservationDetail', 21)
  snapshotSchedulePolicy = _messages.StringField(22)
  state = _messages.EnumField('StateValueValuesEnum', 23)
  storageAggregatePool = _messages.StringField(24)
  storageType = _messages.EnumField('StorageTypeValueValuesEnum', 25)
  workloadProfile = _messages.EnumField('WorkloadProfileValueValuesEnum', 26)


class VolumeConfig(_messages.Message):
  r"""Configuration parameters for a new volume.

  Enums:
    PerformanceTierValueValuesEnum: Performance tier of the Volume. Default is
      SHARED.
    ProtocolValueValuesEnum: Volume protocol.
    TypeValueValuesEnum: The type of this Volume.

  Fields:
    gcpService: The GCP service of the storage volume. Available gcp_service
      are in https://cloud.google.com/bare-metal/docs/bms-planning.
    id: A transient unique identifier to identify a volume within an
      ProvisioningConfig request.
    lunRanges: LUN ranges to be configured. Set only when protocol is
      PROTOCOL_FC.
    machineIds: Machine ids connected to this volume. Set only when protocol
      is PROTOCOL_FC.
    name: Output only. The name of the volume config.
    nfsExports: NFS exports. Set only when protocol is PROTOCOL_NFS.
    performanceTier: Performance tier of the Volume. Default is SHARED.
    protocol: Volume protocol.
    sizeGb: The requested size of this volume, in GB.
    snapshotsEnabled: Whether snapshots should be enabled.
    storageAggregatePool: Input only. Name of the storage aggregate pool to
      allocate the volume in. Can be used only for
      VOLUME_PERFORMANCE_TIER_ASSIGNED volumes.
    type: The type of this Volume.
    userNote: User note field, it can be used by customers to add additional
      information for the BMS Ops team .
  """

  class PerformanceTierValueValuesEnum(_messages.Enum):
    r"""Performance tier of the Volume. Default is SHARED.

    Values:
      VOLUME_PERFORMANCE_TIER_UNSPECIFIED: Value is not specified.
      VOLUME_PERFORMANCE_TIER_SHARED: Regular volumes, shared aggregates.
      VOLUME_PERFORMANCE_TIER_ASSIGNED: Assigned aggregates.
      VOLUME_PERFORMANCE_TIER_HT: High throughput aggregates.
      VOLUME_PERFORMANCE_TIER_QOS2_PERFORMANCE: QoS 2.0 high performance
        storage.
    """
    VOLUME_PERFORMANCE_TIER_UNSPECIFIED = 0
    VOLUME_PERFORMANCE_TIER_SHARED = 1
    VOLUME_PERFORMANCE_TIER_ASSIGNED = 2
    VOLUME_PERFORMANCE_TIER_HT = 3
    VOLUME_PERFORMANCE_TIER_QOS2_PERFORMANCE = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Volume protocol.

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified value.
      PROTOCOL_FC: Fibre channel.
      PROTOCOL_NFS: Network file system.
    """
    PROTOCOL_UNSPECIFIED = 0
    PROTOCOL_FC = 1
    PROTOCOL_NFS = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this Volume.

    Values:
      TYPE_UNSPECIFIED: The unspecified type.
      FLASH: This Volume is on flash.
      DISK: This Volume is on disk.
    """
    TYPE_UNSPECIFIED = 0
    FLASH = 1
    DISK = 2

  gcpService = _messages.StringField(1)
  id = _messages.StringField(2)
  lunRanges = _messages.MessageField('LunRange', 3, repeated=True)
  machineIds = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5)
  nfsExports = _messages.MessageField('NfsExport', 6, repeated=True)
  performanceTier = _messages.EnumField('PerformanceTierValueValuesEnum', 7)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 8)
  sizeGb = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  snapshotsEnabled = _messages.BooleanField(10)
  storageAggregatePool = _messages.StringField(11)
  type = _messages.EnumField('TypeValueValuesEnum', 12)
  userNote = _messages.StringField(13)


class VolumeLunRange(_messages.Message):
  r"""A LUN(Logical Unit Number) range.

  Fields:
    quantity: Number of LUNs to create.
    sizeGb: The requested size of each LUN, in GB.
  """

  quantity = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class VolumeSnapshot(_messages.Message):
  r"""A snapshot of a volume. Only boot volumes can have snapshots.

  Enums:
    TypeValueValuesEnum: Output only. The type of the snapshot which indicates
      whether it was scheduled or manual/ad-hoc.

  Fields:
    createTime: Output only. The creation time of the snapshot.
    description: The description of the snapshot.
    id: Output only. An identifier for the snapshot, generated by the backend.
    name: The name of the snapshot.
    storageVolume: Output only. The name of the volume which this snapshot
      belongs to.
    type: Output only. The type of the snapshot which indicates whether it was
      scheduled or manual/ad-hoc.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the snapshot which indicates whether it was
    scheduled or manual/ad-hoc.

    Values:
      SNAPSHOT_TYPE_UNSPECIFIED: Type is not specified.
      AD_HOC: Snapshot was taken manually by user.
      SCHEDULED: Snapshot was taken automatically as a part of a snapshot
        schedule.
    """
    SNAPSHOT_TYPE_UNSPECIFIED = 0
    AD_HOC = 1
    SCHEDULED = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  name = _messages.StringField(4)
  storageVolume = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
