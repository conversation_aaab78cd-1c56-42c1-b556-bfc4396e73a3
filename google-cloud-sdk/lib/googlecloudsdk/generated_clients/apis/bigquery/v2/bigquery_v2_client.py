"""Generated client library for bigquery version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.bigquery.v2 import bigquery_v2_messages as messages


class BigqueryV2(base_api.BaseApiClient):
  """Generated client library for service bigquery version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://bigquery.googleapis.com/bigquery/v2/'
  MTLS_BASE_URL = 'https://bigquery.mtls.googleapis.com/bigquery/v2/'

  _PACKAGE = 'bigquery'
  _SCOPES = ['https://www.googleapis.com/auth/bigquery', 'https://www.googleapis.com/auth/bigquery.insertdata', 'https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/devstorage.read_only', 'https://www.googleapis.com/auth/devstorage.read_write']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'BigqueryV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new bigquery handle."""
    url = url or self.BASE_URL
    super(BigqueryV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.datasets = self.DatasetsService(self)
    self.jobs = self.JobsService(self)
    self.models = self.ModelsService(self)
    self.projects = self.ProjectsService(self)
    self.routines = self.RoutinesService(self)
    self.rowAccessPolicies = self.RowAccessPoliciesService(self)
    self.tabledata = self.TabledataService(self)
    self.tables = self.TablesService(self)

  class DatasetsService(base_api.BaseApiService):
    """Service class for the datasets resource."""

    _NAME = 'datasets'

    def __init__(self, client):
      super(BigqueryV2.DatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes the dataset specified by the datasetId value. Before you can delete a dataset, you must delete all its tables, either manually or by specifying deleteContents. Immediately after deletion, you can create another dataset with the same name.

      Args:
        request: (BigqueryDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryDatasetsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='bigquery.datasets.delete',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['deleteContents'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}',
        request_field='',
        request_type_name='BigqueryDatasetsDeleteRequest',
        response_type_name='BigqueryDatasetsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the dataset specified by datasetID.

      Args:
        request: (BigqueryDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='bigquery.datasets.get',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['accessPolicyVersion', 'datasetView'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}',
        request_field='',
        request_type_name='BigqueryDatasetsGetRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new empty dataset.

      Args:
        request: (BigqueryDatasetsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets',
        http_method='POST',
        method_id='bigquery.datasets.insert',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['accessPolicyVersion'],
        relative_path='projects/{+projectId}/datasets',
        request_field='dataset',
        request_type_name='BigqueryDatasetsInsertRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all datasets in the specified project to which the user has been granted the READER dataset role.

      Args:
        request: (BigqueryDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DatasetList) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets',
        http_method='GET',
        method_id='bigquery.datasets.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['all', 'filter', 'maxResults', 'pageToken'],
        relative_path='projects/{+projectId}/datasets',
        request_field='',
        request_type_name='BigqueryDatasetsListRequest',
        response_type_name='DatasetList',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource. This method supports RFC5789 patch semantics.

      Args:
        request: (BigqueryDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='bigquery.datasets.patch',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['accessPolicyVersion', 'updateMode'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}',
        request_field='dataset',
        request_type_name='BigqueryDatasetsPatchRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a dataset which is within time travel window based on datasetId. If a time is specified, the dataset version deleted at that time is undeleted, else the last live version is undeleted.

      Args:
        request: (BigqueryDatasetsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}:undelete',
        http_method='POST',
        method_id='bigquery.datasets.undelete',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}:undelete',
        request_field='undeleteDatasetRequest',
        request_type_name='BigqueryDatasetsUndeleteRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource.

      Args:
        request: (BigqueryDatasetsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}',
        http_method='PUT',
        method_id='bigquery.datasets.update',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['accessPolicyVersion', 'updateMode'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}',
        request_field='dataset',
        request_type_name='BigqueryDatasetsUpdateRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

  class JobsService(base_api.BaseApiService):
    """Service class for the jobs resource."""

    _NAME = 'jobs'

    def __init__(self, client):
      super(BigqueryV2.JobsService, self).__init__(client)
      self._upload_configs = {
          'Insert': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=True,
              resumable_path='/resumable/upload/bigquery/v2/projects/{+projectId}/jobs',
              simple_multipart=True,
              simple_path='/upload/bigquery/v2/projects/{+projectId}/jobs',
          ),
          }

    def Cancel(self, request, global_params=None):
      r"""Requests that a job be cancelled. This call will return immediately, and the client will need to poll for the job status to see if the cancel completed successfully. Cancelled jobs may still incur costs.

      Args:
        request: (BigqueryJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (JobCancelResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/jobs/{jobsId}/cancel',
        http_method='POST',
        method_id='bigquery.jobs.cancel',
        ordered_params=['projectId', 'jobId'],
        path_params=['jobId', 'projectId'],
        query_params=['location'],
        relative_path='projects/{+projectId}/jobs/{+jobId}/cancel',
        request_field='',
        request_type_name='BigqueryJobsCancelRequest',
        response_type_name='JobCancelResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Requests the deletion of the metadata of a job. This call returns when the job's metadata is deleted.

      Args:
        request: (BigqueryJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryJobsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/jobs/{jobsId}/delete',
        http_method='DELETE',
        method_id='bigquery.jobs.delete',
        ordered_params=['projectId', 'jobId'],
        path_params=['jobId', 'projectId'],
        query_params=['location'],
        relative_path='projects/{+projectId}/jobs/{+jobId}/delete',
        request_field='',
        request_type_name='BigqueryJobsDeleteRequest',
        response_type_name='BigqueryJobsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns information about a specific job. Job information is available for a six month period after creation. Requires that you're the person who ran the job, or have the Is Owner project role.

      Args:
        request: (BigqueryJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/jobs/{jobsId}',
        http_method='GET',
        method_id='bigquery.jobs.get',
        ordered_params=['projectId', 'jobId'],
        path_params=['jobId', 'projectId'],
        query_params=['location'],
        relative_path='projects/{+projectId}/jobs/{+jobId}',
        request_field='',
        request_type_name='BigqueryJobsGetRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def GetQueryResults(self, request, global_params=None):
      r"""RPC to get the results of a query job.

      Args:
        request: (BigqueryJobsGetQueryResultsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetQueryResultsResponse) The response message.
      """
      config = self.GetMethodConfig('GetQueryResults')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetQueryResults.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/queries/{queriesId}',
        http_method='GET',
        method_id='bigquery.jobs.getQueryResults',
        ordered_params=['projectId', 'jobId'],
        path_params=['jobId', 'projectId'],
        query_params=['formatOptions_useInt64Timestamp', 'location', 'maxResults', 'pageToken', 'startIndex', 'timeoutMs'],
        relative_path='projects/{+projectId}/queries/{+jobId}',
        request_field='',
        request_type_name='BigqueryJobsGetQueryResultsRequest',
        response_type_name='GetQueryResultsResponse',
        supports_download=False,
    )

    def Insert(self, request, global_params=None, upload=None):
      r"""Starts a new asynchronous job. This API has two different kinds of endpoint URIs, as this method supports a variety of use cases. * The *Metadata* URI is used for most interactions, as it accepts the job configuration directly. * The *Upload* URI is ONLY for the case when you're sending both a load job configuration and a data stream together. In this case, the Upload URI accepts the job configuration and the data as two distinct multipart MIME parts.

      Args:
        request: (BigqueryJobsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Insert')
      upload_config = self.GetUploadConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/jobs',
        http_method='POST',
        method_id='bigquery.jobs.insert',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/jobs',
        request_field='job',
        request_type_name='BigqueryJobsInsertRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all jobs that you started in the specified project. Job information is available for a six month period after creation. The job list is sorted in reverse chronological order, by job creation time. Requires the Can View project role, or the Is Owner project role if you set the allUsers property.

      Args:
        request: (BigqueryJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (JobList) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/jobs',
        http_method='GET',
        method_id='bigquery.jobs.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['allUsers', 'maxCreationTime', 'maxResults', 'minCreationTime', 'pageToken', 'parentJobId', 'projection', 'stateFilter'],
        relative_path='projects/{+projectId}/jobs',
        request_field='',
        request_type_name='BigqueryJobsListRequest',
        response_type_name='JobList',
        supports_download=False,
    )

    def Query(self, request, global_params=None):
      r"""Runs a BigQuery SQL query synchronously and returns query results if the query completes within a specified timeout.

      Args:
        request: (BigqueryJobsQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryResponse) The response message.
      """
      config = self.GetMethodConfig('Query')
      return self._RunMethod(
          config, request, global_params=global_params)

    Query.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/queries',
        http_method='POST',
        method_id='bigquery.jobs.query',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/queries',
        request_field='queryRequest',
        request_type_name='BigqueryJobsQueryRequest',
        response_type_name='QueryResponse',
        supports_download=False,
    )

  class ModelsService(base_api.BaseApiService):
    """Service class for the models resource."""

    _NAME = 'models'

    def __init__(self, client):
      super(BigqueryV2.ModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes the model specified by modelId from the dataset.

      Args:
        request: (BigqueryModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryModelsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}',
        http_method='DELETE',
        method_id='bigquery.models.delete',
        ordered_params=['projectId', 'datasetId', 'modelId'],
        path_params=['datasetId', 'modelId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}',
        request_field='',
        request_type_name='BigqueryModelsDeleteRequest',
        response_type_name='BigqueryModelsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified model resource by model ID.

      Args:
        request: (BigqueryModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Model) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}',
        http_method='GET',
        method_id='bigquery.models.get',
        ordered_params=['projectId', 'datasetId', 'modelId'],
        path_params=['datasetId', 'modelId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}',
        request_field='',
        request_type_name='BigqueryModelsGetRequest',
        response_type_name='Model',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all models in the specified dataset. Requires the READER dataset role. After retrieving the list of models, you can get information about a particular model by calling the models.get method.

      Args:
        request: (BigqueryModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/models',
        http_method='GET',
        method_id='bigquery.models.list',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['maxResults', 'pageToken'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/models',
        request_field='',
        request_type_name='BigqueryModelsListRequest',
        response_type_name='ListModelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patch specific fields in the specified model.

      Args:
        request: (BigqueryModelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Model) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}',
        http_method='PATCH',
        method_id='bigquery.models.patch',
        ordered_params=['projectId', 'datasetId', 'modelId'],
        path_params=['datasetId', 'modelId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}',
        request_field='model',
        request_type_name='BigqueryModelsPatchRequest',
        response_type_name='Model',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(BigqueryV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetServiceAccount(self, request, global_params=None):
      r"""RPC to get the service account for a project used for interactions with Google Cloud KMS.

      Args:
        request: (BigqueryProjectsGetServiceAccountRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetServiceAccountResponse) The response message.
      """
      config = self.GetMethodConfig('GetServiceAccount')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetServiceAccount.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/serviceAccount',
        http_method='GET',
        method_id='bigquery.projects.getServiceAccount',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/serviceAccount',
        request_field='',
        request_type_name='BigqueryProjectsGetServiceAccountRequest',
        response_type_name='GetServiceAccountResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""RPC to list projects to which the user has been granted any project role. Users of this method are encouraged to consider the [Resource Manager](https://cloud.google.com/resource-manager/docs/) API, which provides the underlying data for this method and has more capabilities.

      Args:
        request: (BigqueryProjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectList) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='bigquery.projects.list',
        ordered_params=[],
        path_params=[],
        query_params=['maxResults', 'pageToken'],
        relative_path='projects',
        request_field='',
        request_type_name='BigqueryProjectsListRequest',
        response_type_name='ProjectList',
        supports_download=False,
    )

  class RoutinesService(base_api.BaseApiService):
    """Service class for the routines resource."""

    _NAME = 'routines'

    def __init__(self, client):
      super(BigqueryV2.RoutinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes the routine specified by routineId from the dataset.

      Args:
        request: (BigqueryRoutinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryRoutinesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}',
        http_method='DELETE',
        method_id='bigquery.routines.delete',
        ordered_params=['projectId', 'datasetId', 'routineId'],
        path_params=['datasetId', 'projectId', 'routineId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}',
        request_field='',
        request_type_name='BigqueryRoutinesDeleteRequest',
        response_type_name='BigqueryRoutinesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified routine resource by routine ID.

      Args:
        request: (BigqueryRoutinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Routine) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}',
        http_method='GET',
        method_id='bigquery.routines.get',
        ordered_params=['projectId', 'datasetId', 'routineId'],
        path_params=['datasetId', 'projectId', 'routineId'],
        query_params=['readMask'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}',
        request_field='',
        request_type_name='BigqueryRoutinesGetRequest',
        response_type_name='Routine',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BigqueryRoutinesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}:getIamPolicy',
        http_method='POST',
        method_id='bigquery.routines.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigqueryRoutinesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new routine in the dataset.

      Args:
        request: (BigqueryRoutinesInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Routine) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines',
        http_method='POST',
        method_id='bigquery.routines.insert',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/routines',
        request_field='routine',
        request_type_name='BigqueryRoutinesInsertRequest',
        response_type_name='Routine',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all routines in the specified dataset. Requires the READER dataset role.

      Args:
        request: (BigqueryRoutinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines',
        http_method='GET',
        method_id='bigquery.routines.list',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['filter', 'maxResults', 'pageToken', 'readMask'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/routines',
        request_field='',
        request_type_name='BigqueryRoutinesListRequest',
        response_type_name='ListRoutinesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BigqueryRoutinesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}:setIamPolicy',
        http_method='POST',
        method_id='bigquery.routines.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigqueryRoutinesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates information in an existing routine. The update method replaces the entire Routine resource.

      Args:
        request: (BigqueryRoutinesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Routine) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}',
        http_method='PUT',
        method_id='bigquery.routines.update',
        ordered_params=['projectId', 'datasetId', 'routineId'],
        path_params=['datasetId', 'projectId', 'routineId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}',
        request_field='routine',
        request_type_name='BigqueryRoutinesUpdateRequest',
        response_type_name='Routine',
        supports_download=False,
    )

  class RowAccessPoliciesService(base_api.BaseApiService):
    """Service class for the rowAccessPolicies resource."""

    _NAME = 'rowAccessPolicies'

    def __init__(self, client):
      super(BigqueryV2.RowAccessPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes provided row access policies.

      Args:
        request: (BigqueryRowAccessPoliciesBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryRowAccessPoliciesBatchDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies:batchDelete',
        http_method='POST',
        method_id='bigquery.rowAccessPolicies.batchDelete',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies:batchDelete',
        request_field='batchDeleteRowAccessPoliciesRequest',
        request_type_name='BigqueryRowAccessPoliciesBatchDeleteRequest',
        response_type_name='BigqueryRowAccessPoliciesBatchDeleteResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a row access policy.

      Args:
        request: (BigqueryRowAccessPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryRowAccessPoliciesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}',
        http_method='DELETE',
        method_id='bigquery.rowAccessPolicies.delete',
        ordered_params=['projectId', 'datasetId', 'tableId', 'policyId'],
        path_params=['datasetId', 'policyId', 'projectId', 'tableId'],
        query_params=['force'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}',
        request_field='',
        request_type_name='BigqueryRowAccessPoliciesDeleteRequest',
        response_type_name='BigqueryRowAccessPoliciesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified row access policy by policy ID.

      Args:
        request: (BigqueryRowAccessPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RowAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}',
        http_method='GET',
        method_id='bigquery.rowAccessPolicies.get',
        ordered_params=['projectId', 'datasetId', 'tableId', 'policyId'],
        path_params=['datasetId', 'policyId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}',
        request_field='',
        request_type_name='BigqueryRowAccessPoliciesGetRequest',
        response_type_name='RowAccessPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BigqueryRowAccessPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:getIamPolicy',
        http_method='POST',
        method_id='bigquery.rowAccessPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigqueryRowAccessPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a row access policy.

      Args:
        request: (BigqueryRowAccessPoliciesInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RowAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies',
        http_method='POST',
        method_id='bigquery.rowAccessPolicies.insert',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies',
        request_field='rowAccessPolicy',
        request_type_name='BigqueryRowAccessPoliciesInsertRequest',
        response_type_name='RowAccessPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all row access policies on the specified table.

      Args:
        request: (BigqueryRowAccessPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRowAccessPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies',
        http_method='GET',
        method_id='bigquery.rowAccessPolicies.list',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies',
        request_field='',
        request_type_name='BigqueryRowAccessPoliciesListRequest',
        response_type_name='ListRowAccessPoliciesResponse',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BigqueryRowAccessPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='bigquery.rowAccessPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigqueryRowAccessPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a row access policy.

      Args:
        request: (BigqueryRowAccessPoliciesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RowAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}',
        http_method='PUT',
        method_id='bigquery.rowAccessPolicies.update',
        ordered_params=['projectId', 'datasetId', 'tableId', 'policyId'],
        path_params=['datasetId', 'policyId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}',
        request_field='rowAccessPolicy',
        request_type_name='BigqueryRowAccessPoliciesUpdateRequest',
        response_type_name='RowAccessPolicy',
        supports_download=False,
    )

  class TabledataService(base_api.BaseApiService):
    """Service class for the tabledata resource."""

    _NAME = 'tabledata'

    def __init__(self, client):
      super(BigqueryV2.TabledataService, self).__init__(client)
      self._upload_configs = {
          }

    def InsertAll(self, request, global_params=None):
      r"""Streams data into BigQuery one record at a time without needing to run a load job.

      Args:
        request: (BigqueryTabledataInsertAllRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TableDataInsertAllResponse) The response message.
      """
      config = self.GetMethodConfig('InsertAll')
      return self._RunMethod(
          config, request, global_params=global_params)

    InsertAll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/insertAll',
        http_method='POST',
        method_id='bigquery.tabledata.insertAll',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/insertAll',
        request_field='tableDataInsertAllRequest',
        request_type_name='BigqueryTabledataInsertAllRequest',
        response_type_name='TableDataInsertAllResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List the content of a table in rows.

      Args:
        request: (BigqueryTabledataListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TableDataList) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/data',
        http_method='GET',
        method_id='bigquery.tabledata.list',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=['formatOptions_useInt64Timestamp', 'maxResults', 'pageToken', 'selectedFields', 'startIndex'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/data',
        request_field='',
        request_type_name='BigqueryTabledataListRequest',
        response_type_name='TableDataList',
        supports_download=False,
    )

  class TablesService(base_api.BaseApiService):
    """Service class for the tables resource."""

    _NAME = 'tables'

    def __init__(self, client):
      super(BigqueryV2.TablesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes the table specified by tableId from the dataset. If the table contains data, all the data will be deleted.

      Args:
        request: (BigqueryTablesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BigqueryTablesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}',
        http_method='DELETE',
        method_id='bigquery.tables.delete',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}',
        request_field='',
        request_type_name='BigqueryTablesDeleteRequest',
        response_type_name='BigqueryTablesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified table resource by table ID. This method does not return the data in the table, it only returns the table resource, which describes the structure of this table.

      Args:
        request: (BigqueryTablesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}',
        http_method='GET',
        method_id='bigquery.tables.get',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=['selectedFields', 'view'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}',
        request_field='',
        request_type_name='BigqueryTablesGetRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BigqueryTablesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:getIamPolicy',
        http_method='POST',
        method_id='bigquery.tables.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigqueryTablesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new, empty table in the dataset.

      Args:
        request: (BigqueryTablesInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables',
        http_method='POST',
        method_id='bigquery.tables.insert',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=[],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables',
        request_field='table',
        request_type_name='BigqueryTablesInsertRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all tables in the specified dataset. Requires the READER dataset role.

      Args:
        request: (BigqueryTablesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TableList) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables',
        http_method='GET',
        method_id='bigquery.tables.list',
        ordered_params=['projectId', 'datasetId'],
        path_params=['datasetId', 'projectId'],
        query_params=['maxResults', 'pageToken'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables',
        request_field='',
        request_type_name='BigqueryTablesListRequest',
        response_type_name='TableList',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource. This method supports RFC5789 patch semantics.

      Args:
        request: (BigqueryTablesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}',
        http_method='PATCH',
        method_id='bigquery.tables.patch',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=['autodetect_schema'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}',
        request_field='table',
        request_type_name='BigqueryTablesPatchRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BigqueryTablesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:setIamPolicy',
        http_method='POST',
        method_id='bigquery.tables.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigqueryTablesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BigqueryTablesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:testIamPermissions',
        http_method='POST',
        method_id='bigquery.tables.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigqueryTablesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates information in an existing table. The update method replaces the entire Table resource, whereas the patch method only replaces fields that are provided in the submitted Table resource.

      Args:
        request: (BigqueryTablesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}',
        http_method='PUT',
        method_id='bigquery.tables.update',
        ordered_params=['projectId', 'datasetId', 'tableId'],
        path_params=['datasetId', 'projectId', 'tableId'],
        query_params=['autodetect_schema'],
        relative_path='projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}',
        request_field='table',
        request_type_name='BigqueryTablesUpdateRequest',
        response_type_name='Table',
        supports_download=False,
    )
