"""Generated message classes for bigquery version v2.

A data platform for customers to create, manage, share and query data.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import message_types as _message_types
from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'bigquery'


class AggregateClassificationMetrics(_messages.Message):
  r"""Aggregate metrics for classification/classifier models. For multi-class
  models, the metrics are either macro-averaged or micro-averaged. When macro-
  averaged, the metrics are calculated for each label and then an unweighted
  average is taken of those values. When micro-averaged, the metric is
  calculated globally by counting the total number of correctly predicted
  rows.

  Fields:
    accuracy: Accuracy is the fraction of predictions given the correct label.
      For multiclass this is a micro-averaged metric.
    f1Score: The F1 score is an average of recall and precision. For
      multiclass this is a macro-averaged metric.
    logLoss: Logarithmic Loss. For multiclass this is a macro-averaged metric.
    precision: Precision is the fraction of actual positive predictions that
      had positive actual labels. For multiclass this is a macro-averaged
      metric treating each class as a binary classifier.
    recall: Recall is the fraction of actual positive labels that were given a
      positive prediction. For multiclass this is a macro-averaged metric.
    rocAuc: Area Under a ROC Curve. For multiclass this is a macro-averaged
      metric.
    threshold: Threshold at which the metrics are computed. For binary
      classification models this is the positive class threshold. For multi-
      class classification models this is the confidence threshold.
  """

  accuracy = _messages.FloatField(1)
  f1Score = _messages.FloatField(2)
  logLoss = _messages.FloatField(3)
  precision = _messages.FloatField(4)
  recall = _messages.FloatField(5)
  rocAuc = _messages.FloatField(6)
  threshold = _messages.FloatField(7)


class AggregationThresholdPolicy(_messages.Message):
  r"""Represents privacy policy associated with "aggregation threshold"
  method.

  Fields:
    privacyUnitColumns: Optional. The privacy unit column(s) associated with
      this policy. For now, only one column per data source object (table,
      view) is allowed as a privacy unit column. Representing as a repeated
      field in metadata for extensibility to multiple columns in future.
      Duplicates and Repeated struct fields are not allowed. For nested
      fields, use dot notation ("outer.inner")
    threshold: Optional. The threshold for the "aggregation threshold" policy.
  """

  privacyUnitColumns = _messages.StringField(1, repeated=True)
  threshold = _messages.IntegerField(2)


class Argument(_messages.Message):
  r"""Input/output argument of a function or a stored procedure.

  Enums:
    ArgumentKindValueValuesEnum: Optional. Defaults to FIXED_TYPE.
    ModeValueValuesEnum: Optional. Specifies whether the argument is input or
      output. Can be set for procedures only.

  Fields:
    argumentKind: Optional. Defaults to FIXED_TYPE.
    dataType: Set if argument_kind == FIXED_TYPE.
    isAggregate: Optional. Whether the argument is an aggregate function
      parameter. Must be Unset for routine types other than
      AGGREGATE_FUNCTION. For AGGREGATE_FUNCTION, if set to false, it is
      equivalent to adding "NOT AGGREGATE" clause in DDL; Otherwise, it is
      equivalent to omitting "NOT AGGREGATE" clause in DDL.
    mode: Optional. Specifies whether the argument is input or output. Can be
      set for procedures only.
    name: Optional. The name of this argument. Can be absent for function
      return argument.
  """

  class ArgumentKindValueValuesEnum(_messages.Enum):
    r"""Optional. Defaults to FIXED_TYPE.

    Values:
      ARGUMENT_KIND_UNSPECIFIED: Default value.
      FIXED_TYPE: The argument is a variable with fully specified type, which
        can be a struct or an array, but not a table.
      ANY_TYPE: The argument is any type, including struct or array, but not a
        table.
    """
    ARGUMENT_KIND_UNSPECIFIED = 0
    FIXED_TYPE = 1
    ANY_TYPE = 2

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies whether the argument is input or output. Can be
    set for procedures only.

    Values:
      MODE_UNSPECIFIED: Default value.
      IN: The argument is input-only.
      OUT: The argument is output-only.
      INOUT: The argument is both an input and an output.
    """
    MODE_UNSPECIFIED = 0
    IN = 1
    OUT = 2
    INOUT = 3

  argumentKind = _messages.EnumField('ArgumentKindValueValuesEnum', 1)
  dataType = _messages.MessageField('StandardSqlDataType', 2)
  isAggregate = _messages.BooleanField(3)
  mode = _messages.EnumField('ModeValueValuesEnum', 4)
  name = _messages.StringField(5)


class ArimaCoefficients(_messages.Message):
  r"""Arima coefficients.

  Fields:
    autoRegressiveCoefficients: Auto-regressive coefficients, an array of
      double.
    interceptCoefficient: Intercept coefficient, just a double not an array.
    movingAverageCoefficients: Moving-average coefficients, an array of
      double.
  """

  autoRegressiveCoefficients = _messages.FloatField(1, repeated=True)
  interceptCoefficient = _messages.FloatField(2)
  movingAverageCoefficients = _messages.FloatField(3, repeated=True)


class ArimaFittingMetrics(_messages.Message):
  r"""ARIMA model fitting metrics.

  Fields:
    aic: AIC.
    logLikelihood: Log-likelihood.
    variance: Variance.
  """

  aic = _messages.FloatField(1)
  logLikelihood = _messages.FloatField(2)
  variance = _messages.FloatField(3)


class ArimaForecastingMetrics(_messages.Message):
  r"""Model evaluation metrics for ARIMA forecasting models.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaFittingMetrics: Arima model fitting metrics.
    arimaSingleModelForecastingMetrics: Repeated as there can be many metric
      sets (one for each model) in auto-arima and the large-scale case.
    hasDrift: Whether Arima model fitted with drift or not. It is always false
      when d is not 1.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: Id to differentiate different time series for the large-
      scale case.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 1, repeated=True)
  arimaSingleModelForecastingMetrics = _messages.MessageField('ArimaSingleModelForecastingMetrics', 2, repeated=True)
  hasDrift = _messages.BooleanField(3, repeated=True)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 4, repeated=True)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 5, repeated=True)
  timeSeriesId = _messages.StringField(6, repeated=True)


class ArimaModelInfo(_messages.Message):
  r"""Arima model information.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaCoefficients: Arima coefficients.
    arimaFittingMetrics: Arima fitting metrics.
    hasDrift: Whether Arima model fitted with drift or not. It is always false
      when d is not 1.
    hasHolidayEffect: If true, holiday_effect is a part of time series
      decomposition result.
    hasSpikesAndDips: If true, spikes_and_dips is a part of time series
      decomposition result.
    hasStepChanges: If true, step_changes is a part of time series
      decomposition result.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: The time_series_id value for this time series. It will be
      one of the unique values from the time_series_id_column specified during
      ARIMA model training. Only present when time_series_id_column training
      option was used.
    timeSeriesIds: The tuple of time_series_ids identifying this time series.
      It will be one of the unique tuples of values present in the
      time_series_id_columns specified during ARIMA model training. Only
      present when time_series_id_columns training option was used and the
      order of values here are same as the order of time_series_id_columns.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaCoefficients = _messages.MessageField('ArimaCoefficients', 1)
  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 2)
  hasDrift = _messages.BooleanField(3)
  hasHolidayEffect = _messages.BooleanField(4)
  hasSpikesAndDips = _messages.BooleanField(5)
  hasStepChanges = _messages.BooleanField(6)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 7)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 8, repeated=True)
  timeSeriesId = _messages.StringField(9)
  timeSeriesIds = _messages.StringField(10, repeated=True)


class ArimaOrder(_messages.Message):
  r"""Arima order, can be used for both non-seasonal and seasonal parts.

  Fields:
    d: Order of the differencing part.
    p: Order of the autoregressive part.
    q: Order of the moving-average part.
  """

  d = _messages.IntegerField(1)
  p = _messages.IntegerField(2)
  q = _messages.IntegerField(3)


class ArimaResult(_messages.Message):
  r"""(Auto-)arima fitting result. Wrap everything in ArimaResult for easier
  refactoring if we want to use model-specific iteration results.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaModelInfo: This message is repeated because there are multiple arima
      models fitted in auto-arima. For non-auto-arima model, its size is one.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaModelInfo = _messages.MessageField('ArimaModelInfo', 1, repeated=True)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 2, repeated=True)


class ArimaSingleModelForecastingMetrics(_messages.Message):
  r"""Model evaluation metrics for a single ARIMA forecasting model.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaFittingMetrics: Arima fitting metrics.
    hasDrift: Is arima model fitted with drift or not. It is always false when
      d is not 1.
    hasHolidayEffect: If true, holiday_effect is a part of time series
      decomposition result.
    hasSpikesAndDips: If true, spikes_and_dips is a part of time series
      decomposition result.
    hasStepChanges: If true, step_changes is a part of time series
      decomposition result.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: The time_series_id value for this time series. It will be
      one of the unique values from the time_series_id_column specified during
      ARIMA model training. Only present when time_series_id_column training
      option was used.
    timeSeriesIds: The tuple of time_series_ids identifying this time series.
      It will be one of the unique tuples of values present in the
      time_series_id_columns specified during ARIMA model training. Only
      present when time_series_id_columns training option was used and the
      order of values here are same as the order of time_series_id_columns.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 1)
  hasDrift = _messages.BooleanField(2)
  hasHolidayEffect = _messages.BooleanField(3)
  hasSpikesAndDips = _messages.BooleanField(4)
  hasStepChanges = _messages.BooleanField(5)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 6)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 7, repeated=True)
  timeSeriesId = _messages.StringField(8)
  timeSeriesIds = _messages.StringField(9, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AvroOptions(_messages.Message):
  r"""Options for external data sources.

  Fields:
    useAvroLogicalTypes: Optional. If sourceFormat is set to "AVRO", indicates
      whether to interpret logical types as the corresponding BigQuery data
      type (for example, TIMESTAMP), instead of using the raw type (for
      example, INTEGER).
  """

  useAvroLogicalTypes = _messages.BooleanField(1)


class BatchDeleteRowAccessPoliciesRequest(_messages.Message):
  r"""Request message for the BatchDeleteRowAccessPoliciesRequest method.

  Fields:
    force: If set to true, it deletes the row access policy even if it's the
      last row access policy on the table and the deletion will widen the
      access rather narrowing it.
    policyIds: Required. Policy IDs of the row access policies.
  """

  force = _messages.BooleanField(1)
  policyIds = _messages.StringField(2, repeated=True)


class BiEngineReason(_messages.Message):
  r"""Reason why BI Engine didn't accelerate the query (or sub-query).

  Enums:
    CodeValueValuesEnum: Output only. High-level BI Engine reason for partial
      or disabled acceleration

  Fields:
    code: Output only. High-level BI Engine reason for partial or disabled
      acceleration
    message: Output only. Free form human-readable reason for partial or
      disabled acceleration.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. High-level BI Engine reason for partial or disabled
    acceleration

    Values:
      CODE_UNSPECIFIED: BiEngineReason not specified.
      NO_RESERVATION: No reservation available for BI Engine acceleration.
      INSUFFICIENT_RESERVATION: Not enough memory available for BI Engine
        acceleration.
      UNSUPPORTED_SQL_TEXT: This particular SQL text is not supported for
        acceleration by BI Engine.
      INPUT_TOO_LARGE: Input too large for acceleration by BI Engine.
      OTHER_REASON: Catch-all code for all other cases for partial or disabled
        acceleration.
      TABLE_EXCLUDED: One or more tables were not eligible for BI Engine
        acceleration.
    """
    CODE_UNSPECIFIED = 0
    NO_RESERVATION = 1
    INSUFFICIENT_RESERVATION = 2
    UNSUPPORTED_SQL_TEXT = 3
    INPUT_TOO_LARGE = 4
    OTHER_REASON = 5
    TABLE_EXCLUDED = 6

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)


class BiEngineStatistics(_messages.Message):
  r"""Statistics for a BI Engine specific query. Populated as part of
  JobStatistics2

  Enums:
    AccelerationModeValueValuesEnum: Output only. Specifies which mode of BI
      Engine acceleration was performed (if any).
    BiEngineModeValueValuesEnum: Output only. Specifies which mode of BI
      Engine acceleration was performed (if any).

  Fields:
    accelerationMode: Output only. Specifies which mode of BI Engine
      acceleration was performed (if any).
    biEngineMode: Output only. Specifies which mode of BI Engine acceleration
      was performed (if any).
    biEngineReasons: In case of DISABLED or PARTIAL bi_engine_mode, these
      contain the explanatory reasons as to why BI Engine could not
      accelerate. In case the full query was accelerated, this field is not
      populated.
  """

  class AccelerationModeValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies which mode of BI Engine acceleration was
    performed (if any).

    Values:
      BI_ENGINE_ACCELERATION_MODE_UNSPECIFIED: BiEngineMode type not
        specified.
      BI_ENGINE_DISABLED: BI Engine acceleration was attempted but disabled.
        bi_engine_reasons specifies a more detailed reason.
      PARTIAL_INPUT: Some inputs were accelerated using BI Engine. See
        bi_engine_reasons for why parts of the query were not accelerated.
      FULL_INPUT: All of the query inputs were accelerated using BI Engine.
      FULL_QUERY: All of the query was accelerated using BI Engine.
    """
    BI_ENGINE_ACCELERATION_MODE_UNSPECIFIED = 0
    BI_ENGINE_DISABLED = 1
    PARTIAL_INPUT = 2
    FULL_INPUT = 3
    FULL_QUERY = 4

  class BiEngineModeValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies which mode of BI Engine acceleration was
    performed (if any).

    Values:
      ACCELERATION_MODE_UNSPECIFIED: BiEngineMode type not specified.
      DISABLED: BI Engine disabled the acceleration. bi_engine_reasons
        specifies a more detailed reason.
      PARTIAL: Part of the query was accelerated using BI Engine. See
        bi_engine_reasons for why parts of the query were not accelerated.
      FULL: All of the query was accelerated using BI Engine.
    """
    ACCELERATION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    PARTIAL = 2
    FULL = 3

  accelerationMode = _messages.EnumField('AccelerationModeValueValuesEnum', 1)
  biEngineMode = _messages.EnumField('BiEngineModeValueValuesEnum', 2)
  biEngineReasons = _messages.MessageField('BiEngineReason', 3, repeated=True)


class BigLakeConfiguration(_messages.Message):
  r"""Configuration for BigQuery tables for Apache Iceberg (formerly BigLake
  managed tables.)

  Enums:
    FileFormatValueValuesEnum: Optional. The file format the table data is
      stored in.
    TableFormatValueValuesEnum: Optional. The table format the metadata only
      snapshots are stored in.

  Fields:
    connectionId: Optional. The connection specifying the credentials to be
      used to read and write to external storage, such as Cloud Storage. The
      connection_id can have the form `{project}.{location}.{connection_id}`
      or
      `projects/{project}/locations/{location}/connections/{connection_id}".
    fileFormat: Optional. The file format the table data is stored in.
    storageUri: Optional. The fully qualified location prefix of the external
      folder where table data is stored. The '*' wildcard character is not
      allowed. The URI should be in the format `gs://bucket/path_to_table/`
    tableFormat: Optional. The table format the metadata only snapshots are
      stored in.
  """

  class FileFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The file format the table data is stored in.

    Values:
      FILE_FORMAT_UNSPECIFIED: Default Value.
      PARQUET: Apache Parquet format.
    """
    FILE_FORMAT_UNSPECIFIED = 0
    PARQUET = 1

  class TableFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The table format the metadata only snapshots are stored in.

    Values:
      TABLE_FORMAT_UNSPECIFIED: Default Value.
      ICEBERG: Apache Iceberg format.
    """
    TABLE_FORMAT_UNSPECIFIED = 0
    ICEBERG = 1

  connectionId = _messages.StringField(1)
  fileFormat = _messages.EnumField('FileFormatValueValuesEnum', 2)
  storageUri = _messages.StringField(3)
  tableFormat = _messages.EnumField('TableFormatValueValuesEnum', 4)


class BigQueryModelTraining(_messages.Message):
  r"""A BigQueryModelTraining object.

  Fields:
    currentIteration: Deprecated.
    expectedTotalIterations: Deprecated.
  """

  currentIteration = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  expectedTotalIterations = _messages.IntegerField(2)


class BigqueryDatasetsDeleteRequest(_messages.Message):
  r"""A BigqueryDatasetsDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of dataset being deleted
    deleteContents: If True, delete all the tables in the dataset. If False
      and the dataset contains tables, the request will fail. Default is False
    projectId: Required. Project ID of the dataset being deleted
  """

  datasetId = _messages.StringField(1, required=True)
  deleteContents = _messages.BooleanField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryDatasetsDeleteResponse(_messages.Message):
  r"""An empty BigqueryDatasetsDelete response."""


class BigqueryDatasetsGetRequest(_messages.Message):
  r"""A BigqueryDatasetsGetRequest object.

  Enums:
    DatasetViewValueValuesEnum: Optional. Specifies the view that determines
      which dataset information is returned. By default, metadata and ACL
      information are returned.

  Fields:
    accessPolicyVersion: Optional. The version of the access policy schema to
      fetch. Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected. Requests for conditional access policy binding
      in datasets must specify version 3. Dataset with no conditional role
      bindings in access policy may specify any valid value or leave the field
      unset. This field will be mapped to [IAM Policy version]
      (https://cloud.google.com/iam/docs/policies#versions) and will be used
      to fetch policy from IAM. If unset or if 0 or 1 value is used for
      dataset with conditional bindings, access entry with condition will have
      role string appended by 'withcond' string followed by a hash value. For
      example : { "access": [ { "role":
      "roles/bigquery.dataViewer_with_conditionalbinding_7a34awqsda",
      "userByEmail": "<EMAIL>", } ] } Please refer
      https://cloud.google.com/iam/docs/troubleshooting-withcond for more
      details.
    datasetId: Required. Dataset ID of the requested dataset
    datasetView: Optional. Specifies the view that determines which dataset
      information is returned. By default, metadata and ACL information are
      returned.
    projectId: Required. Project ID of the requested dataset
  """

  class DatasetViewValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the view that determines which dataset information
    is returned. By default, metadata and ACL information are returned.

    Values:
      DATASET_VIEW_UNSPECIFIED: The default value. Default to the FULL view.
      METADATA: Updates metadata information for the dataset, such as
        friendlyName, description, labels, etc.
      ACL: Updates ACL information for the dataset, which defines dataset
        access for one or more entities.
      FULL: Updates both dataset metadata and ACL information.
    """
    DATASET_VIEW_UNSPECIFIED = 0
    METADATA = 1
    ACL = 2
    FULL = 3

  accessPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  datasetId = _messages.StringField(2, required=True)
  datasetView = _messages.EnumField('DatasetViewValueValuesEnum', 3)
  projectId = _messages.StringField(4, required=True)


class BigqueryDatasetsInsertRequest(_messages.Message):
  r"""A BigqueryDatasetsInsertRequest object.

  Fields:
    accessPolicyVersion: Optional. The version of the provided access policy
      schema. Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected. This version refers to the schema version of the
      access policy and not the version of access policy. This field's value
      can be equal or more than the access policy schema provided in the
      request. For example, * Requests with conditional access policy binding
      in datasets must specify version 3. * But dataset with no conditional
      role bindings in access policy may specify any valid value or leave the
      field unset. If unset or if 0 or 1 value is used for dataset with
      conditional bindings, request will be rejected. This field will be
      mapped to IAM Policy version
      (https://cloud.google.com/iam/docs/policies#versions) and will be used
      to set policy in IAM.
    dataset: A Dataset resource to be passed as the request body.
    projectId: Required. Project ID of the new dataset
  """

  accessPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  dataset = _messages.MessageField('Dataset', 2)
  projectId = _messages.StringField(3, required=True)


class BigqueryDatasetsListRequest(_messages.Message):
  r"""A BigqueryDatasetsListRequest object.

  Fields:
    all: Whether to list all datasets, including hidden ones
    filter: An expression for filtering the results of the request by label.
      The syntax is `labels.[:]`. Multiple filters can be AND-ed together by
      connecting with a space. Example: `labels.department:receiving
      labels.active`. See [Filtering datasets using
      labels](https://cloud.google.com/bigquery/docs/filtering-
      labels#filtering_datasets_using_labels) for details.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Required. Project ID of the datasets to be listed
  """

  all = _messages.BooleanField(1)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)


class BigqueryDatasetsPatchRequest(_messages.Message):
  r"""A BigqueryDatasetsPatchRequest object.

  Enums:
    UpdateModeValueValuesEnum: Optional. Specifies the fields of dataset that
      update/patch operation is targeting By default, both metadata and ACL
      fields are updated.

  Fields:
    accessPolicyVersion: Optional. The version of the provided access policy
      schema. Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected. This version refers to the schema version of the
      access policy and not the version of access policy. This field's value
      can be equal or more than the access policy schema provided in the
      request. For example, * Operations updating conditional access policy
      binding in datasets must specify version 3. Some of the operations are :
      - Adding a new access policy entry with condition. - Removing an access
      policy entry with condition. - Updating an access policy entry with
      condition. * But dataset with no conditional role bindings in access
      policy may specify any valid value or leave the field unset. If unset or
      if 0 or 1 value is used for dataset with conditional bindings, request
      will be rejected. This field will be mapped to IAM Policy version
      (https://cloud.google.com/iam/docs/policies#versions) and will be used
      to set policy in IAM.
    dataset: A Dataset resource to be passed as the request body.
    datasetId: Required. Dataset ID of the dataset being updated
    projectId: Required. Project ID of the dataset being updated
    updateMode: Optional. Specifies the fields of dataset that update/patch
      operation is targeting By default, both metadata and ACL fields are
      updated.
  """

  class UpdateModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the fields of dataset that update/patch operation
    is targeting By default, both metadata and ACL fields are updated.

    Values:
      UPDATE_MODE_UNSPECIFIED: The default value. Default to the UPDATE_FULL.
      UPDATE_METADATA: Includes metadata information for the dataset, such as
        friendlyName, description, labels, etc.
      UPDATE_ACL: Includes ACL information for the dataset, which defines
        dataset access for one or more entities.
      UPDATE_FULL: Includes both dataset metadata and ACL information.
    """
    UPDATE_MODE_UNSPECIFIED = 0
    UPDATE_METADATA = 1
    UPDATE_ACL = 2
    UPDATE_FULL = 3

  accessPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  dataset = _messages.MessageField('Dataset', 2)
  datasetId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  updateMode = _messages.EnumField('UpdateModeValueValuesEnum', 5)


class BigqueryDatasetsUndeleteRequest(_messages.Message):
  r"""A BigqueryDatasetsUndeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of dataset being deleted
    projectId: Required. Project ID of the dataset to be undeleted
    undeleteDatasetRequest: A UndeleteDatasetRequest resource to be passed as
      the request body.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  undeleteDatasetRequest = _messages.MessageField('UndeleteDatasetRequest', 3)


class BigqueryDatasetsUpdateRequest(_messages.Message):
  r"""A BigqueryDatasetsUpdateRequest object.

  Enums:
    UpdateModeValueValuesEnum: Optional. Specifies the fields of dataset that
      update/patch operation is targeting By default, both metadata and ACL
      fields are updated.

  Fields:
    accessPolicyVersion: Optional. The version of the provided access policy
      schema. Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected. This version refers to the schema version of the
      access policy and not the version of access policy. This field's value
      can be equal or more than the access policy schema provided in the
      request. For example, * Operations updating conditional access policy
      binding in datasets must specify version 3. Some of the operations are :
      - Adding a new access policy entry with condition. - Removing an access
      policy entry with condition. - Updating an access policy entry with
      condition. * But dataset with no conditional role bindings in access
      policy may specify any valid value or leave the field unset. If unset or
      if 0 or 1 value is used for dataset with conditional bindings, request
      will be rejected. This field will be mapped to IAM Policy version
      (https://cloud.google.com/iam/docs/policies#versions) and will be used
      to set policy in IAM.
    dataset: A Dataset resource to be passed as the request body.
    datasetId: Required. Dataset ID of the dataset being updated
    projectId: Required. Project ID of the dataset being updated
    updateMode: Optional. Specifies the fields of dataset that update/patch
      operation is targeting By default, both metadata and ACL fields are
      updated.
  """

  class UpdateModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the fields of dataset that update/patch operation
    is targeting By default, both metadata and ACL fields are updated.

    Values:
      UPDATE_MODE_UNSPECIFIED: The default value. Default to the UPDATE_FULL.
      UPDATE_METADATA: Includes metadata information for the dataset, such as
        friendlyName, description, labels, etc.
      UPDATE_ACL: Includes ACL information for the dataset, which defines
        dataset access for one or more entities.
      UPDATE_FULL: Includes both dataset metadata and ACL information.
    """
    UPDATE_MODE_UNSPECIFIED = 0
    UPDATE_METADATA = 1
    UPDATE_ACL = 2
    UPDATE_FULL = 3

  accessPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  dataset = _messages.MessageField('Dataset', 2)
  datasetId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  updateMode = _messages.EnumField('UpdateModeValueValuesEnum', 5)


class BigqueryJobsCancelRequest(_messages.Message):
  r"""A BigqueryJobsCancelRequest object.

  Fields:
    jobId: Required. Job ID of the job to cancel
    location: The geographic location of the job. You must [specify the locati
      on](https://cloud.google.com/bigquery/docs/locations#specify_locations)
      to run the job for the following scenarios: * If the location to run a
      job is not in the `us` or the `eu` multi-regional location * If the
      job's location is in a single region (for example, `us-central1`)
    projectId: Required. Project ID of the job to cancel
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsDeleteRequest(_messages.Message):
  r"""A BigqueryJobsDeleteRequest object.

  Fields:
    jobId: Required. Job ID of the job for which metadata is to be deleted. If
      this is a parent job which has child jobs, the metadata from all child
      jobs will be deleted as well. Direct deletion of the metadata of child
      jobs is not allowed.
    location: The geographic location of the job. Required. For more
      information, see how to [specify locations](https://cloud.google.com/big
      query/docs/locations#specify_locations).
    projectId: Required. Project ID of the job for which metadata is to be
      deleted.
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsDeleteResponse(_messages.Message):
  r"""An empty BigqueryJobsDelete response."""


class BigqueryJobsGetQueryResultsRequest(_messages.Message):
  r"""A BigqueryJobsGetQueryResultsRequest object.

  Fields:
    formatOptions_useInt64Timestamp: Optional. Output timestamp as usec int64.
      Default is false.
    jobId: Required. Job ID of the query job.
    location: The geographic location of the job. You must specify the
      location to run the job for the following scenarios: * If the location
      to run a job is not in the `us` or the `eu` multi-regional location * If
      the job's location is in a single region (for example, `us-central1`)
      For more information, see how to [specify locations](https://cloud.googl
      e.com/bigquery/docs/locations#specify_locations).
    maxResults: Maximum number of results to read.
    pageToken: Page token, returned by a previous call, to request the next
      page of results.
    projectId: Required. Project ID of the query job.
    startIndex: Zero-based index of the starting row.
    timeoutMs: Optional: Specifies the maximum amount of time, in
      milliseconds, that the client is willing to wait for the query to
      complete. By default, this limit is 10 seconds (10,000 milliseconds). If
      the query is complete, the jobComplete field in the response is true. If
      the query has not yet completed, jobComplete is false. You can request a
      longer timeout period in the timeoutMs field. However, the call is not
      guaranteed to wait for the specified timeout; it typically returns after
      around 200 seconds (200,000 milliseconds), even if the query is not
      complete. If jobComplete is false, you can continue to wait for the
      query to complete by calling the getQueryResults method until the
      jobComplete field in the getQueryResults response is true.
  """

  formatOptions_useInt64Timestamp = _messages.BooleanField(1)
  jobId = _messages.StringField(2, required=True)
  location = _messages.StringField(3)
  maxResults = _messages.IntegerField(4, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(5)
  projectId = _messages.StringField(6, required=True)
  startIndex = _messages.IntegerField(7, variant=_messages.Variant.UINT64)
  timeoutMs = _messages.IntegerField(8, variant=_messages.Variant.UINT32)


class BigqueryJobsGetRequest(_messages.Message):
  r"""A BigqueryJobsGetRequest object.

  Fields:
    jobId: Required. Job ID of the requested job.
    location: The geographic location of the job. You must specify the
      location to run the job for the following scenarios: * If the location
      to run a job is not in the `us` or the `eu` multi-regional location * If
      the job's location is in a single region (for example, `us-central1`)
      For more information, see how to [specify locations](https://cloud.googl
      e.com/bigquery/docs/locations#specify_locations).
    projectId: Required. Project ID of the requested job.
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsInsertRequest(_messages.Message):
  r"""A BigqueryJobsInsertRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    projectId: Project ID of project that will be billed for the job.
  """

  job = _messages.MessageField('Job', 1)
  projectId = _messages.StringField(2, required=True)


class BigqueryJobsListRequest(_messages.Message):
  r"""A BigqueryJobsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Restrict information returned to a set of
      selected fields
    StateFilterValueValuesEnum: Filter for job state

  Fields:
    allUsers: Whether to display jobs owned by all users in the project.
      Default False.
    maxCreationTime: Max value for job creation time, in milliseconds since
      the POSIX epoch. If set, only jobs created before or at this timestamp
      are returned.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    minCreationTime: Min value for job creation time, in milliseconds since
      the POSIX epoch. If set, only jobs created after or at this timestamp
      are returned.
    pageToken: Page token, returned by a previous call, to request the next
      page of results.
    parentJobId: If set, show only child jobs of the specified parent.
      Otherwise, show all top-level jobs.
    projectId: Project ID of the jobs to list.
    projection: Restrict information returned to a set of selected fields
    stateFilter: Filter for job state
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Restrict information returned to a set of selected fields

    Values:
      full: Includes all job data
      minimal: Does not include the job configuration
    """
    full = 0
    minimal = 1

  class StateFilterValueValuesEnum(_messages.Enum):
    r"""Filter for job state

    Values:
      done: Finished jobs
      pending: Pending jobs
      running: Running jobs
    """
    done = 0
    pending = 1
    running = 2

  allUsers = _messages.BooleanField(1)
  maxCreationTime = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  minCreationTime = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
  pageToken = _messages.StringField(5)
  parentJobId = _messages.StringField(6)
  projectId = _messages.StringField(7, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 8)
  stateFilter = _messages.EnumField('StateFilterValueValuesEnum', 9, repeated=True)


class BigqueryJobsQueryRequest(_messages.Message):
  r"""A BigqueryJobsQueryRequest object.

  Fields:
    projectId: Required. Project ID of the query request.
    queryRequest: A QueryRequest resource to be passed as the request body.
  """

  projectId = _messages.StringField(1, required=True)
  queryRequest = _messages.MessageField('QueryRequest', 2)


class BigqueryModelsDeleteRequest(_messages.Message):
  r"""A BigqueryModelsDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the model to delete.
    modelId: Required. Model ID of the model to delete.
    projectId: Required. Project ID of the model to delete.
  """

  datasetId = _messages.StringField(1, required=True)
  modelId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryModelsDeleteResponse(_messages.Message):
  r"""An empty BigqueryModelsDelete response."""


class BigqueryModelsGetRequest(_messages.Message):
  r"""A BigqueryModelsGetRequest object.

  Fields:
    datasetId: Required. Dataset ID of the requested model.
    modelId: Required. Model ID of the requested model.
    projectId: Required. Project ID of the requested model.
  """

  datasetId = _messages.StringField(1, required=True)
  modelId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryModelsListRequest(_messages.Message):
  r"""A BigqueryModelsListRequest object.

  Fields:
    datasetId: Required. Dataset ID of the models to list.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call to request the next
      page of results
    projectId: Required. Project ID of the models to list.
  """

  datasetId = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class BigqueryModelsPatchRequest(_messages.Message):
  r"""A BigqueryModelsPatchRequest object.

  Fields:
    datasetId: Required. Dataset ID of the model to patch.
    model: A Model resource to be passed as the request body.
    modelId: Required. Model ID of the model to patch.
    projectId: Required. Project ID of the model to patch.
  """

  datasetId = _messages.StringField(1, required=True)
  model = _messages.MessageField('Model', 2)
  modelId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)


class BigqueryProjectsGetServiceAccountRequest(_messages.Message):
  r"""A BigqueryProjectsGetServiceAccountRequest object.

  Fields:
    projectId: Required. ID of the project.
  """

  projectId = _messages.StringField(1, required=True)


class BigqueryProjectsListRequest(_messages.Message):
  r"""A BigqueryProjectsListRequest object.

  Fields:
    maxResults: `maxResults` unset returns all results, up to 50 per page.
      Additionally, the number of projects in a page may be fewer than
      `maxResults` because projects are retrieved and then filtered to only
      projects with the BigQuery API enabled.
    pageToken: Page token, returned by a previous call, to request the next
      page of results. If not present, no further pages are present.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(2)


class BigqueryRoutinesDeleteRequest(_messages.Message):
  r"""A BigqueryRoutinesDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routine to delete
    projectId: Required. Project ID of the routine to delete
    routineId: Required. Routine ID of the routine to delete
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routineId = _messages.StringField(3, required=True)


class BigqueryRoutinesDeleteResponse(_messages.Message):
  r"""An empty BigqueryRoutinesDelete response."""


class BigqueryRoutinesGetIamPolicyRequest(_messages.Message):
  r"""A BigqueryRoutinesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigqueryRoutinesGetRequest(_messages.Message):
  r"""A BigqueryRoutinesGetRequest object.

  Fields:
    datasetId: Required. Dataset ID of the requested routine
    projectId: Required. Project ID of the requested routine
    readMask: If set, only the Routine fields in the field mask are returned
      in the response. If unset, all Routine fields are returned.
    routineId: Required. Routine ID of the requested routine
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  readMask = _messages.StringField(3)
  routineId = _messages.StringField(4, required=True)


class BigqueryRoutinesInsertRequest(_messages.Message):
  r"""A BigqueryRoutinesInsertRequest object.

  Fields:
    datasetId: Required. Dataset ID of the new routine
    projectId: Required. Project ID of the new routine
    routine: A Routine resource to be passed as the request body.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routine = _messages.MessageField('Routine', 3)


class BigqueryRoutinesListRequest(_messages.Message):
  r"""A BigqueryRoutinesListRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routines to list
    filter: If set, then only the Routines matching this filter are returned.
      The supported format is `routineType:{RoutineType}`, where
      `{RoutineType}` is a RoutineType enum. For example:
      `routineType:SCALAR_FUNCTION`.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Required. Project ID of the routines to list
    readMask: If set, then only the Routine fields in the field mask, as well
      as project_id, dataset_id and routine_id, are returned in the response.
      If unset, then the following Routine fields are returned: etag,
      project_id, dataset_id, routine_id, routine_type, creation_time,
      last_modified_time, and language.
  """

  datasetId = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  readMask = _messages.StringField(6)


class BigqueryRoutinesSetIamPolicyRequest(_messages.Message):
  r"""A BigqueryRoutinesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigqueryRoutinesUpdateRequest(_messages.Message):
  r"""A BigqueryRoutinesUpdateRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routine to update
    projectId: Required. Project ID of the routine to update
    routine: A Routine resource to be passed as the request body.
    routineId: Required. Routine ID of the routine to update
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routine = _messages.MessageField('Routine', 3)
  routineId = _messages.StringField(4, required=True)


class BigqueryRowAccessPoliciesBatchDeleteRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesBatchDeleteRequest object.

  Fields:
    batchDeleteRowAccessPoliciesRequest: A BatchDeleteRowAccessPoliciesRequest
      resource to be passed as the request body.
    datasetId: Required. Dataset ID of the table to delete the row access
      policies.
    projectId: Required. Project ID of the table to delete the row access
      policies.
    tableId: Required. Table ID of the table to delete the row access
      policies.
  """

  batchDeleteRowAccessPoliciesRequest = _messages.MessageField('BatchDeleteRowAccessPoliciesRequest', 1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  tableId = _messages.StringField(4, required=True)


class BigqueryRowAccessPoliciesBatchDeleteResponse(_messages.Message):
  r"""An empty BigqueryRowAccessPoliciesBatchDelete response."""


class BigqueryRowAccessPoliciesDeleteRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the table to delete the row access
      policy.
    force: If set to true, it deletes the row access policy even if it's the
      last row access policy on the table and the deletion will widen the
      access rather narrowing it.
    policyId: Required. Policy ID of the row access policy.
    projectId: Required. Project ID of the table to delete the row access
      policy.
    tableId: Required. Table ID of the table to delete the row access policy.
  """

  datasetId = _messages.StringField(1, required=True)
  force = _messages.BooleanField(2)
  policyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  tableId = _messages.StringField(5, required=True)


class BigqueryRowAccessPoliciesDeleteResponse(_messages.Message):
  r"""An empty BigqueryRowAccessPoliciesDelete response."""


class BigqueryRowAccessPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigqueryRowAccessPoliciesGetRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesGetRequest object.

  Fields:
    datasetId: Required. Dataset ID of the table to get the row access policy.
    policyId: Required. Policy ID of the row access policy.
    projectId: Required. Project ID of the table to get the row access policy.
    tableId: Required. Table ID of the table to get the row access policy.
  """

  datasetId = _messages.StringField(1, required=True)
  policyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  tableId = _messages.StringField(4, required=True)


class BigqueryRowAccessPoliciesInsertRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesInsertRequest object.

  Fields:
    datasetId: Required. Dataset ID of the table to get the row access policy.
    projectId: Required. Project ID of the table to get the row access policy.
    rowAccessPolicy: A RowAccessPolicy resource to be passed as the request
      body.
    tableId: Required. Table ID of the table to get the row access policy.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  rowAccessPolicy = _messages.MessageField('RowAccessPolicy', 3)
  tableId = _messages.StringField(4, required=True)


class BigqueryRowAccessPoliciesListRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesListRequest object.

  Fields:
    datasetId: Required. Dataset ID of row access policies to list.
    pageSize: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results.
    projectId: Required. Project ID of the row access policies to list.
    tableId: Required. Table ID of the table to list row access policies.
  """

  datasetId = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)
  tableId = _messages.StringField(5, required=True)


class BigqueryRowAccessPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigqueryRowAccessPoliciesUpdateRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesUpdateRequest object.

  Fields:
    datasetId: Required. Dataset ID of the table to get the row access policy.
    policyId: Required. Policy ID of the row access policy.
    projectId: Required. Project ID of the table to get the row access policy.
    rowAccessPolicy: A RowAccessPolicy resource to be passed as the request
      body.
    tableId: Required. Table ID of the table to get the row access policy.
  """

  datasetId = _messages.StringField(1, required=True)
  policyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  rowAccessPolicy = _messages.MessageField('RowAccessPolicy', 4)
  tableId = _messages.StringField(5, required=True)


class BigqueryTabledataInsertAllRequest(_messages.Message):
  r"""A BigqueryTabledataInsertAllRequest object.

  Fields:
    datasetId: Required. Dataset ID of the destination.
    projectId: Required. Project ID of the destination.
    tableDataInsertAllRequest: A TableDataInsertAllRequest resource to be
      passed as the request body.
    tableId: Required. Table ID of the destination.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  tableDataInsertAllRequest = _messages.MessageField('TableDataInsertAllRequest', 3)
  tableId = _messages.StringField(4, required=True)


class BigqueryTabledataListRequest(_messages.Message):
  r"""A BigqueryTabledataListRequest object.

  Fields:
    datasetId: Required. Dataset id of the table to list.
    formatOptions_useInt64Timestamp: Optional. Output timestamp as usec int64.
      Default is false.
    maxResults: Row limit of the table.
    pageToken: To retrieve the next page of table data, set this field to the
      string provided in the pageToken field of the response body from your
      previous call to tabledata.list.
    projectId: Required. Project id of the table to list.
    selectedFields: Subset of fields to return, supports select into sub
      fields. Example: selected_fields = "a,e.d.f";
    startIndex: Start row index of the table.
    tableId: Required. Table id of the table to list.
  """

  datasetId = _messages.StringField(1, required=True)
  formatOptions_useInt64Timestamp = _messages.BooleanField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  selectedFields = _messages.StringField(6)
  startIndex = _messages.IntegerField(7, variant=_messages.Variant.UINT64)
  tableId = _messages.StringField(8, required=True)


class BigqueryTablesDeleteRequest(_messages.Message):
  r"""A BigqueryTablesDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the table to delete
    projectId: Required. Project ID of the table to delete
    tableId: Required. Table ID of the table to delete
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  tableId = _messages.StringField(3, required=True)


class BigqueryTablesDeleteResponse(_messages.Message):
  r"""An empty BigqueryTablesDelete response."""


class BigqueryTablesGetIamPolicyRequest(_messages.Message):
  r"""A BigqueryTablesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigqueryTablesGetRequest(_messages.Message):
  r"""A BigqueryTablesGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Specifies the view that determines which
      table information is returned. By default, basic table information and
      storage statistics (STORAGE_STATS) are returned.

  Fields:
    datasetId: Required. Dataset ID of the requested table
    projectId: Required. Project ID of the requested table
    selectedFields: List of table schema fields to return (comma-separated).
      If unspecified, all fields are returned. A fieldMask cannot be used here
      because the fields will automatically be converted from camelCase to
      snake_case and the conversion will fail if there are underscores. Since
      these are fields in BigQuery table schemas, underscores are allowed.
    tableId: Required. Table ID of the requested table
    view: Optional. Specifies the view that determines which table information
      is returned. By default, basic table information and storage statistics
      (STORAGE_STATS) are returned.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the view that determines which table information
    is returned. By default, basic table information and storage statistics
    (STORAGE_STATS) are returned.

    Values:
      TABLE_METADATA_VIEW_UNSPECIFIED: The default value. Default to the
        STORAGE_STATS view.
      BASIC: Includes basic table information including schema and
        partitioning specification. This view does not include storage
        statistics such as numRows or numBytes. This view is significantly
        more efficient and should be used to support high query rates.
      STORAGE_STATS: Includes all information in the BASIC view as well as
        storage statistics (numBytes, numLongTermBytes, numRows and
        lastModifiedTime).
      FULL: Includes all table information, including storage statistics. It
        returns same information as STORAGE_STATS view, but may contain
        additional information in the future.
    """
    TABLE_METADATA_VIEW_UNSPECIFIED = 0
    BASIC = 1
    STORAGE_STATS = 2
    FULL = 3

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  selectedFields = _messages.StringField(3)
  tableId = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class BigqueryTablesInsertRequest(_messages.Message):
  r"""A BigqueryTablesInsertRequest object.

  Fields:
    datasetId: Required. Dataset ID of the new table
    projectId: Required. Project ID of the new table
    table: A Table resource to be passed as the request body.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  table = _messages.MessageField('Table', 3)


class BigqueryTablesListRequest(_messages.Message):
  r"""A BigqueryTablesListRequest object.

  Fields:
    datasetId: Required. Dataset ID of the tables to list
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Required. Project ID of the tables to list
  """

  datasetId = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class BigqueryTablesPatchRequest(_messages.Message):
  r"""A BigqueryTablesPatchRequest object.

  Fields:
    autodetect_schema: Optional.  When true will autodetect schema, else will
      keep original schema
    datasetId: Required. Dataset ID of the table to update
    projectId: Required. Project ID of the table to update
    table: A Table resource to be passed as the request body.
    tableId: Required. Table ID of the table to update
  """

  autodetect_schema = _messages.BooleanField(1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  table = _messages.MessageField('Table', 4)
  tableId = _messages.StringField(5, required=True)


class BigqueryTablesSetIamPolicyRequest(_messages.Message):
  r"""A BigqueryTablesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigqueryTablesTestIamPermissionsRequest(_messages.Message):
  r"""A BigqueryTablesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigqueryTablesUpdateRequest(_messages.Message):
  r"""A BigqueryTablesUpdateRequest object.

  Fields:
    autodetect_schema: Optional.  When true will autodetect schema, else will
      keep original schema
    datasetId: Required. Dataset ID of the table to update
    projectId: Required. Project ID of the table to update
    table: A Table resource to be passed as the request body.
    tableId: Required. Table ID of the table to update
  """

  autodetect_schema = _messages.BooleanField(1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  table = _messages.MessageField('Table', 4)
  tableId = _messages.StringField(5, required=True)


class BigtableColumn(_messages.Message):
  r"""Information related to a Bigtable column.

  Fields:
    encoding: Optional. The encoding of the values when the type is not
      STRING. Acceptable encoding values are: TEXT - indicates values are
      alphanumeric text strings. BINARY - indicates values are encoded using
      HBase Bytes.toBytes family of functions. 'encoding' can also be set at
      the column family level. However, the setting at this level takes
      precedence if 'encoding' is set at both levels.
    fieldName: Optional. If the qualifier is not a valid BigQuery field
      identifier i.e. does not match a-zA-Z*, a valid identifier must be
      provided as the column field name and is used as field name in queries.
    onlyReadLatest: Optional. If this is set, only the latest version of value
      in this column are exposed. 'onlyReadLatest' can also be set at the
      column family level. However, the setting at this level takes precedence
      if 'onlyReadLatest' is set at both levels.
    qualifierEncoded: [Required] Qualifier of the column. Columns in the
      parent column family that has this exact qualifier are exposed as `.`
      field. If the qualifier is valid UTF-8 string, it can be specified in
      the qualifier_string field. Otherwise, a base-64 encoded value must be
      set to qualifier_encoded. The column field name is the same as the
      column qualifier. However, if the qualifier is not a valid BigQuery
      field identifier i.e. does not match a-zA-Z*, a valid identifier must be
      provided as field_name.
    qualifierString: Qualifier string.
    type: Optional. The type to convert the value in cells of this column. The
      values are expected to be encoded using HBase Bytes.toBytes function
      when using the BINARY encoding value. Following BigQuery types are
      allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT * BOOLEAN *
      JSON Default type is BYTES. 'type' can also be set at the column family
      level. However, the setting at this level takes precedence if 'type' is
      set at both levels.
  """

  encoding = _messages.StringField(1)
  fieldName = _messages.StringField(2)
  onlyReadLatest = _messages.BooleanField(3)
  qualifierEncoded = _messages.BytesField(4)
  qualifierString = _messages.StringField(5)
  type = _messages.StringField(6)


class BigtableColumnFamily(_messages.Message):
  r"""Information related to a Bigtable column family.

  Fields:
    columns: Optional. Lists of columns that should be exposed as individual
      fields as opposed to a list of (column name, value) pairs. All columns
      whose qualifier matches a qualifier in this list can be accessed as `.`.
      Other columns can be accessed as a list through the `.Column` field.
    encoding: Optional. The encoding of the values when the type is not
      STRING. Acceptable encoding values are: TEXT - indicates values are
      alphanumeric text strings. BINARY - indicates values are encoded using
      HBase Bytes.toBytes family of functions. This can be overridden for a
      specific column by listing that column in 'columns' and specifying an
      encoding for it.
    familyId: Identifier of the column family.
    onlyReadLatest: Optional. If this is set only the latest version of value
      are exposed for all columns in this column family. This can be
      overridden for a specific column by listing that column in 'columns' and
      specifying a different setting for that column.
    type: Optional. The type to convert the value in cells of this column
      family. The values are expected to be encoded using HBase Bytes.toBytes
      function when using the BINARY encoding value. Following BigQuery types
      are allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT *
      BOOLEAN * JSON Default type is BYTES. This can be overridden for a
      specific column by listing that column in 'columns' and specifying a
      type for it.
  """

  columns = _messages.MessageField('BigtableColumn', 1, repeated=True)
  encoding = _messages.StringField(2)
  familyId = _messages.StringField(3)
  onlyReadLatest = _messages.BooleanField(4)
  type = _messages.StringField(5)


class BigtableOptions(_messages.Message):
  r"""Options specific to Google Cloud Bigtable data sources.

  Fields:
    columnFamilies: Optional. List of column families to expose in the table
      schema along with their types. This list restricts the column families
      that can be referenced in queries and specifies their value types. You
      can use this list to do type conversions - see the 'type' field for more
      details. If you leave this list empty, all column families are present
      in the table schema and their values are read as BYTES. During a query
      only the column families referenced in that query are read from
      Bigtable.
    ignoreUnspecifiedColumnFamilies: Optional. If field is true, then the
      column families that are not specified in columnFamilies list are not
      exposed in the table schema. Otherwise, they are read with BYTES type
      values. The default value is false.
    outputColumnFamiliesAsJson: Optional. If field is true, then each column
      family will be read as a single JSON column. Otherwise they are read as
      a repeated cell structure containing timestamp/value tuples. The default
      value is false.
    readRowkeyAsString: Optional. If field is true, then the rowkey column
      families will be read and converted to string. Otherwise they are read
      with BYTES type values and users need to manually cast them with CAST if
      necessary. The default value is false.
  """

  columnFamilies = _messages.MessageField('BigtableColumnFamily', 1, repeated=True)
  ignoreUnspecifiedColumnFamilies = _messages.BooleanField(2)
  outputColumnFamiliesAsJson = _messages.BooleanField(3)
  readRowkeyAsString = _messages.BooleanField(4)


class BinaryClassificationMetrics(_messages.Message):
  r"""Evaluation metrics for binary classification/classifier models.

  Fields:
    aggregateClassificationMetrics: Aggregate classification metrics.
    binaryConfusionMatrixList: Binary confusion matrix at multiple thresholds.
    negativeLabel: Label representing the negative class.
    positiveLabel: Label representing the positive class.
  """

  aggregateClassificationMetrics = _messages.MessageField('AggregateClassificationMetrics', 1)
  binaryConfusionMatrixList = _messages.MessageField('BinaryConfusionMatrix', 2, repeated=True)
  negativeLabel = _messages.StringField(3)
  positiveLabel = _messages.StringField(4)


class BinaryConfusionMatrix(_messages.Message):
  r"""Confusion matrix for binary classification models.

  Fields:
    accuracy: The fraction of predictions given the correct label.
    f1Score: The equally weighted average of recall and precision.
    falseNegatives: Number of false samples predicted as false.
    falsePositives: Number of false samples predicted as true.
    positiveClassThreshold: Threshold value used when computing each of the
      following metric.
    precision: The fraction of actual positive predictions that had positive
      actual labels.
    recall: The fraction of actual positive labels that were given a positive
      prediction.
    trueNegatives: Number of true samples predicted as false.
    truePositives: Number of true samples predicted as true.
  """

  accuracy = _messages.FloatField(1)
  f1Score = _messages.FloatField(2)
  falseNegatives = _messages.IntegerField(3)
  falsePositives = _messages.IntegerField(4)
  positiveClassThreshold = _messages.FloatField(5)
  precision = _messages.FloatField(6)
  recall = _messages.FloatField(7)
  trueNegatives = _messages.IntegerField(8)
  truePositives = _messages.IntegerField(9)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BqmlIterationResult(_messages.Message):
  r"""A BqmlIterationResult object.

  Fields:
    durationMs: Deprecated.
    evalLoss: Deprecated.
    index: Deprecated.
    learnRate: Deprecated.
    trainingLoss: Deprecated.
  """

  durationMs = _messages.IntegerField(1)
  evalLoss = _messages.FloatField(2)
  index = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  learnRate = _messages.FloatField(4)
  trainingLoss = _messages.FloatField(5)


class BqmlTrainingRun(_messages.Message):
  r"""A BqmlTrainingRun object.

  Messages:
    TrainingOptionsValue: Deprecated.

  Fields:
    iterationResults: Deprecated.
    startTime: Deprecated.
    state: Deprecated.
    trainingOptions: Deprecated.
  """

  class TrainingOptionsValue(_messages.Message):
    r"""Deprecated.

    Fields:
      earlyStop: A boolean attribute.
      l1Reg: A number attribute.
      l2Reg: A number attribute.
      learnRate: A number attribute.
      learnRateStrategy: A string attribute.
      lineSearchInitLearnRate: A number attribute.
      maxIteration: A string attribute.
      minRelProgress: A number attribute.
      warmStart: A boolean attribute.
    """

    earlyStop = _messages.BooleanField(1)
    l1Reg = _messages.FloatField(2)
    l2Reg = _messages.FloatField(3)
    learnRate = _messages.FloatField(4)
    learnRateStrategy = _messages.StringField(5)
    lineSearchInitLearnRate = _messages.FloatField(6)
    maxIteration = _messages.IntegerField(7)
    minRelProgress = _messages.FloatField(8)
    warmStart = _messages.BooleanField(9)

  iterationResults = _messages.MessageField('BqmlIterationResult', 1, repeated=True)
  startTime = _message_types.DateTimeField(2)
  state = _messages.StringField(3)
  trainingOptions = _messages.MessageField('TrainingOptionsValue', 4)


class CategoricalValue(_messages.Message):
  r"""Representative value of a categorical feature.

  Fields:
    categoryCounts: Counts of all categories for the categorical feature. If
      there are more than ten categories, we return top ten (by count) and
      return one more CategoryCount with category "_OTHER_" and count as
      aggregate counts of remaining categories.
  """

  categoryCounts = _messages.MessageField('CategoryCount', 1, repeated=True)


class CategoryCount(_messages.Message):
  r"""Represents the count of a single category within the cluster.

  Fields:
    category: The name of category.
    count: The count of training samples matching the category within the
      cluster.
  """

  category = _messages.StringField(1)
  count = _messages.IntegerField(2)


class CloneDefinition(_messages.Message):
  r"""Information about base table and clone time of a table clone.

  Fields:
    baseTableReference: Required. Reference describing the ID of the table
      that was cloned.
    cloneTime: Required. The time at which the base table was cloned. This
      value is reported in the JSON response using RFC3339 format.
  """

  baseTableReference = _messages.MessageField('TableReference', 1)
  cloneTime = _message_types.DateTimeField(2)


class Cluster(_messages.Message):
  r"""Message containing the information about one cluster.

  Fields:
    centroidId: Centroid id.
    count: Count of training data rows that were assigned to this cluster.
    featureValues: Values of highly variant features for this cluster.
  """

  centroidId = _messages.IntegerField(1)
  count = _messages.IntegerField(2)
  featureValues = _messages.MessageField('FeatureValue', 3, repeated=True)


class ClusterInfo(_messages.Message):
  r"""Information about a single cluster for clustering model.

  Fields:
    centroidId: Centroid id.
    clusterRadius: Cluster radius, the average distance from centroid to each
      point assigned to the cluster.
    clusterSize: Cluster size, the total number of points assigned to the
      cluster.
  """

  centroidId = _messages.IntegerField(1)
  clusterRadius = _messages.FloatField(2)
  clusterSize = _messages.IntegerField(3)


class Clustering(_messages.Message):
  r"""Configures table clustering.

  Fields:
    fields: One or more fields on which data should be clustered. Only top-
      level, non-repeated, simple-type fields are supported. The ordering of
      the clustering fields should be prioritized from most to least important
      for filtering purposes. For additional information, see [Introduction to
      clustered tables](https://cloud.google.com/bigquery/docs/clustered-
      tables#limitations).
  """

  fields = _messages.StringField(1, repeated=True)


class ClusteringMetrics(_messages.Message):
  r"""Evaluation metrics for clustering models.

  Fields:
    clusters: Information for all clusters.
    daviesBouldinIndex: Davies-Bouldin index.
    meanSquaredDistance: Mean of squared distances between each sample to its
      cluster centroid.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  daviesBouldinIndex = _messages.FloatField(2)
  meanSquaredDistance = _messages.FloatField(3)


class ConfusionMatrix(_messages.Message):
  r"""Confusion matrix for multi-class classification models.

  Fields:
    confidenceThreshold: Confidence threshold used when computing the entries
      of the confusion matrix.
    rows: One row per actual label.
  """

  confidenceThreshold = _messages.FloatField(1)
  rows = _messages.MessageField('Row', 2, repeated=True)


class ConnectionProperty(_messages.Message):
  r"""A connection-level property to customize query behavior. Under JDBC,
  these correspond directly to connection properties passed to the
  DriverManager. Under ODBC, these correspond to properties in the connection
  string. Currently supported connection properties: * **dataset_project_id**:
  represents the default project for datasets that are used in the query.
  Setting the system variable `@@dataset_project_id` achieves the same
  behavior. For more information about system variables, see:
  https://cloud.google.com/bigquery/docs/reference/system-variables *
  **time_zone**: represents the default timezone used to run the query. *
  **session_id**: associates the query with a given session. *
  **query_label**: associates the query with a given job label. If set, all
  subsequent queries in a script or session will have this label. For the
  format in which a you can specify a query label, see labels in the
  JobConfiguration resource type: https://cloud.google.com/bigquery/docs/refer
  ence/rest/v2/Job#jobconfiguration * **service_account**: indicates the
  service account to use to run a continuous query. If set, the query job uses
  the service account to access Google Cloud resources. Service account access
  is bounded by the IAM permissions that you have granted to the service
  account. Additional properties are allowed, but ignored. Specifying multiple
  connection properties with the same key returns an error.

  Fields:
    key: The key of the property to set.
    value: The value of the property to set.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class CsvOptions(_messages.Message):
  r"""Information related to a CSV data source.

  Fields:
    allowJaggedRows: Optional. Indicates if BigQuery should accept rows that
      are missing trailing optional columns. If true, BigQuery treats missing
      trailing columns as null values. If false, records with missing trailing
      columns are treated as bad records, and if there are too many bad
      records, an invalid error is returned in the job result. The default
      value is false.
    allowQuotedNewlines: Optional. Indicates if BigQuery should allow quoted
      data sections that contain newline characters in a CSV file. The default
      value is false.
    encoding: Optional. The character encoding of the data. The supported
      values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and
      UTF-32LE. The default value is UTF-8. BigQuery decodes the data after
      the raw, binary data has been split using the values of the quote and
      fieldDelimiter properties.
    fieldDelimiter: Optional. The separator character for fields in a CSV
      file. The separator is interpreted as a single byte. For files encoded
      in ISO-8859-1, any single character can be used as a separator. For
      files encoded in UTF-8, characters represented in decimal range 1-127
      (U+0001-U+007F) can be used without any modification. UTF-8 characters
      encoded with multiple bytes (i.e. U+0080 and above) will have only the
      first byte used for separating fields. The remaining bytes will be
      treated as a part of the field. BigQuery also supports the escape
      sequence "\t" (U+0009) to specify a tab separator. The default value is
      comma (",", U+002C).
    nullMarker: Optional. Specifies a string that represents a null value in a
      CSV file. For example, if you specify "\\N", BigQuery interprets "\\N"
      as a null value when querying a CSV file. The default value is the empty
      string. If you set this property to a custom value, BigQuery throws an
      error if an empty string is present for all data types except for STRING
      and BYTE. For STRING and BYTE columns, BigQuery interprets the empty
      string as an empty value.
    nullMarkers: Optional. A list of strings represented as SQL NULL value in
      a CSV file. null_marker and null_markers can't be set at the same time.
      If null_marker is set, null_markers has to be not set. If null_markers
      is set, null_marker has to be not set. If both null_marker and
      null_markers are set at the same time, a user error would be thrown. Any
      strings listed in null_markers, including empty string would be
      interpreted as SQL NULL. This applies to all column types.
    preserveAsciiControlCharacters: Optional. Indicates if the embedded ASCII
      control characters (the first 32 characters in the ASCII-table, from
      '\x00' to '\x1F') are preserved.
    quote: Optional. The value that is used to quote data sections in a CSV
      file. BigQuery converts the string to ISO-8859-1 encoding, and then uses
      the first byte of the encoded string to split the data in its raw,
      binary state. The default value is a double-quote ("). If your data does
      not contain quoted sections, set the property value to an empty string.
      If your data contains quoted newline characters, you must also set the
      allowQuotedNewlines property to true. To include the specific quote
      character within a quoted value, precede it with an additional matching
      quote character. For example, if you want to escape the default
      character ' " ', use ' "" '.
    skipLeadingRows: Optional. The number of rows at the top of a CSV file
      that BigQuery will skip when reading the data. The default value is 0.
      This property is useful if you have header rows in the file that should
      be skipped. When autodetect is on, the behavior is the following: *
      skipLeadingRows unspecified - Autodetect tries to detect headers in the
      first row. If they are not detected, the row is read as data. Otherwise
      data is read starting from the second row. * skipLeadingRows is 0 -
      Instructs autodetect that there are no headers and data should be read
      starting from the first row. * skipLeadingRows = N > 0 - Autodetect
      skips N-1 rows and tries to detect headers in row N. If headers are not
      detected, row N is just skipped. Otherwise row N is used to extract
      column names for the detected schema.
    sourceColumnMatch: Optional. Controls the strategy used to match loaded
      columns to the schema. If not set, a sensible default is chosen based on
      how the schema is provided. If autodetect is used, then columns are
      matched by name. Otherwise, columns are matched by position. This is
      done to keep the behavior backward-compatible. Acceptable values are:
      POSITION - matches by position. This assumes that the columns are
      ordered the same way as the schema. NAME - matches by name. This reads
      the header row as column names and reorders columns to match the field
      names in the schema.
  """

  allowJaggedRows = _messages.BooleanField(1)
  allowQuotedNewlines = _messages.BooleanField(2)
  encoding = _messages.StringField(3)
  fieldDelimiter = _messages.StringField(4)
  nullMarker = _messages.StringField(5)
  nullMarkers = _messages.StringField(6, repeated=True)
  preserveAsciiControlCharacters = _messages.BooleanField(7)
  quote = _messages.StringField(8, default='"')
  skipLeadingRows = _messages.IntegerField(9)
  sourceColumnMatch = _messages.StringField(10)


class DataFormatOptions(_messages.Message):
  r"""Options for data format adjustments.

  Fields:
    useInt64Timestamp: Optional. Output timestamp as usec int64. Default is
      false.
  """

  useInt64Timestamp = _messages.BooleanField(1)


class DataMaskingStatistics(_messages.Message):
  r"""Statistics for data-masking.

  Fields:
    dataMaskingApplied: Whether any accessed data was protected by the data
      masking.
  """

  dataMaskingApplied = _messages.BooleanField(1)


class DataPolicyOption(_messages.Message):
  r"""Data policy option proto, it currently supports name only, will support
  precedence later.

  Fields:
    name: Data policy resource name in the form of
      projects/project_id/locations/location_id/dataPolicies/data_policy_id.
  """

  name = _messages.StringField(1)


class DataSplitResult(_messages.Message):
  r"""Data split result. This contains references to the training and
  evaluation data tables that were used to train the model.

  Fields:
    evaluationTable: Table reference of the evaluation data after split.
    testTable: Table reference of the test data after split.
    trainingTable: Table reference of the training data after split.
  """

  evaluationTable = _messages.MessageField('TableReference', 1)
  testTable = _messages.MessageField('TableReference', 2)
  trainingTable = _messages.MessageField('TableReference', 3)


class Dataset(_messages.Message):
  r"""Represents a BigQuery dataset.

  Enums:
    DefaultRoundingModeValueValuesEnum: Optional. Defines the default rounding
      mode specification of new tables created within this dataset. During
      table creation, if this field is specified, the table within this
      dataset will inherit the default rounding mode of the dataset. Setting
      the default rounding mode on a table overrides this option. Existing
      tables in the dataset are unaffected. If columns are defined during that
      table creation, they will immediately inherit the table's default
      rounding mode, unless otherwise specified.
    StorageBillingModelValueValuesEnum: Optional. Updates
      storage_billing_model for the dataset.

  Messages:
    AccessValueListEntry: An object that defines dataset access for an entity.
    LabelsValue: The labels associated with this dataset. You can use these to
      organize and group your datasets. You can set this property when
      inserting or updating a dataset. See [Creating and Updating Dataset
      Labels](https://cloud.google.com/bigquery/docs/creating-managing-
      labels#creating_and_updating_dataset_labels) for more information.
    ResourceTagsValue: Optional. The
      [tags](https://cloud.google.com/bigquery/docs/tags) attached to this
      dataset. Tag keys are globally unique. Tag key is expected to be in the
      namespaced format, for example "123456789012/environment" where
      123456789012 is the ID of the parent organization or project resource
      for this tag key. Tag value is expected to be the short name, for
      example "Production". See [Tag
      definitions](https://cloud.google.com/iam/docs/tags-access-
      control#definitions) for more details.
    TagsValueListEntry: A global tag managed by Resource Manager.
      https://cloud.google.com/iam/docs/tags-access-control#definitions

  Fields:
    access: Optional. An array of objects that define dataset access for one
      or more entities. You can set this property when inserting or updating a
      dataset in order to control who is allowed to access the data. If
      unspecified at dataset creation time, BigQuery adds default dataset
      access for the following entities: access.specialGroup: projectReaders;
      access.role: READER; access.specialGroup: projectWriters; access.role:
      WRITER; access.specialGroup: projectOwners; access.role: OWNER;
      access.userByEmail: [dataset creator email]; access.role: OWNER; If you
      patch a dataset, then this field is overwritten by the patched dataset's
      access field. To add entities, you must supply the entire existing
      access array in addition to any new entities that you want to add.
    creationTime: Output only. The time when this dataset was created, in
      milliseconds since the epoch.
    datasetReference: Required. A reference that identifies the dataset.
    defaultCollation: Optional. Defines the default collation specification of
      future tables created in the dataset. If a table is created in this
      dataset without table-level default collation, then the table inherits
      the dataset default collation, which is applied to the string fields
      that do not have explicit collation specified. A change to this field
      affects only tables created afterwards, and does not alter the existing
      tables. The following values are supported: * 'und:ci': undetermined
      locale, case insensitive. * '': empty string. Default to case-sensitive
      behavior.
    defaultEncryptionConfiguration: The default encryption key for all tables
      in the dataset. After this property is set, the encryption key of all
      newly-created tables in the dataset is set to this value unless the
      table creation request or query explicitly overrides the key.
    defaultPartitionExpirationMs: This default partition expiration, expressed
      in milliseconds. When new time-partitioned tables are created in a
      dataset where this property is set, the table will inherit this value,
      propagated as the `TimePartitioning.expirationMs` property on the new
      table. If you set `TimePartitioning.expirationMs` explicitly when
      creating a table, the `defaultPartitionExpirationMs` of the containing
      dataset is ignored. When creating a partitioned table, if
      `defaultPartitionExpirationMs` is set, the `defaultTableExpirationMs`
      value is ignored and the table will not be inherit a table expiration
      deadline.
    defaultRoundingMode: Optional. Defines the default rounding mode
      specification of new tables created within this dataset. During table
      creation, if this field is specified, the table within this dataset will
      inherit the default rounding mode of the dataset. Setting the default
      rounding mode on a table overrides this option. Existing tables in the
      dataset are unaffected. If columns are defined during that table
      creation, they will immediately inherit the table's default rounding
      mode, unless otherwise specified.
    defaultTableExpirationMs: Optional. The default lifetime of all tables in
      the dataset, in milliseconds. The minimum lifetime value is 3600000
      milliseconds (one hour). To clear an existing default expiration with a
      PATCH request, set to 0. Once this property is set, all newly-created
      tables in the dataset will have an expirationTime property set to the
      creation time plus the value in this property, and changing the value
      will only affect new tables, not existing ones. When the expirationTime
      for a given table is reached, that table will be deleted automatically.
      If a table's expirationTime is modified or removed before the table
      expires, or if you provide an explicit expirationTime when creating a
      table, that value takes precedence over the default expiration time
      indicated by this property.
    description: Optional. A user-friendly description of the dataset.
    etag: Output only. A hash of the resource.
    externalCatalogDatasetOptions: Optional. Options defining open source
      compatible datasets living in the BigQuery catalog. Contains metadata of
      open source database, schema or namespace represented by the current
      dataset.
    externalDatasetReference: Optional. Reference to a read-only external
      dataset defined in data catalogs outside of BigQuery. Filled out when
      the dataset type is EXTERNAL.
    friendlyName: Optional. A descriptive name for the dataset.
    id: Output only. The fully-qualified unique name of the dataset in the
      format projectId:datasetId. The dataset name without the project name is
      given in the datasetId field. When creating a new dataset, leave this
      field blank, and instead specify the datasetId field.
    isCaseInsensitive: Optional. TRUE if the dataset and its table names are
      case-insensitive, otherwise FALSE. By default, this is FALSE, which
      means the dataset and its table names are case-sensitive. This field
      does not affect routine references.
    kind: Output only. The resource type.
    labels: The labels associated with this dataset. You can use these to
      organize and group your datasets. You can set this property when
      inserting or updating a dataset. See [Creating and Updating Dataset
      Labels](https://cloud.google.com/bigquery/docs/creating-managing-
      labels#creating_and_updating_dataset_labels) for more information.
    lastModifiedTime: Output only. The date when this dataset was last
      modified, in milliseconds since the epoch.
    linkedDatasetMetadata: Output only. Metadata about the LinkedDataset.
      Filled out when the dataset type is LINKED.
    linkedDatasetSource: Optional. The source dataset reference when the
      dataset is of type LINKED. For all other dataset types it is not set.
      This field cannot be updated once it is set. Any attempt to update this
      field using Update and Patch API Operations will be ignored.
    location: The geographic location where the dataset should reside. See
      https://cloud.google.com/bigquery/docs/locations for supported
      locations.
    maxTimeTravelHours: Optional. Defines the time travel window in hours. The
      value can be from 48 to 168 hours (2 to 7 days). The default value is
      168 hours if this is not set.
    resourceTags: Optional. The
      [tags](https://cloud.google.com/bigquery/docs/tags) attached to this
      dataset. Tag keys are globally unique. Tag key is expected to be in the
      namespaced format, for example "123456789012/environment" where
      123456789012 is the ID of the parent organization or project resource
      for this tag key. Tag value is expected to be the short name, for
      example "Production". See [Tag
      definitions](https://cloud.google.com/iam/docs/tags-access-
      control#definitions) for more details.
    restrictions: Optional. Output only. Restriction config for all tables and
      dataset. If set, restrict certain accesses on the dataset and all its
      tables based on the config. See [Data
      egress](https://cloud.google.com/bigquery/docs/analytics-hub-
      introduction#data_egress) for more details.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    selfLink: Output only. A URL that can be used to access the resource
      again. You can use this URL in Get or Update requests to the resource.
    storageBillingModel: Optional. Updates storage_billing_model for the
      dataset.
    tags: Output only. Tags for the dataset. To provide tags as inputs, use
      the `resourceTags` field.
    type: Output only. Same as `type` in `ListFormatDataset`. The type of the
      dataset, one of: * DEFAULT - only accessible by owner and authorized
      accounts, * PUBLIC - accessible by everyone, * LINKED - linked dataset,
      * EXTERNAL - dataset with definition in external metadata catalog.
  """

  class DefaultRoundingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the default rounding mode specification of new
    tables created within this dataset. During table creation, if this field
    is specified, the table within this dataset will inherit the default
    rounding mode of the dataset. Setting the default rounding mode on a table
    overrides this option. Existing tables in the dataset are unaffected. If
    columns are defined during that table creation, they will immediately
    inherit the table's default rounding mode, unless otherwise specified.

    Values:
      ROUNDING_MODE_UNSPECIFIED: Unspecified will default to using
        ROUND_HALF_AWAY_FROM_ZERO.
      ROUND_HALF_AWAY_FROM_ZERO: ROUND_HALF_AWAY_FROM_ZERO rounds half values
        away from zero when applying precision and scale upon writing of
        NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1
        1.5, 1.6, 1.7, 1.8, 1.9 => 2
      ROUND_HALF_EVEN: ROUND_HALF_EVEN rounds half values to the nearest even
        value when applying precision and scale upon writing of NUMERIC and
        BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6,
        1.7, 1.8, 1.9 => 2 2.5 => 2
    """
    ROUNDING_MODE_UNSPECIFIED = 0
    ROUND_HALF_AWAY_FROM_ZERO = 1
    ROUND_HALF_EVEN = 2

  class StorageBillingModelValueValuesEnum(_messages.Enum):
    r"""Optional. Updates storage_billing_model for the dataset.

    Values:
      STORAGE_BILLING_MODEL_UNSPECIFIED: Value not set.
      LOGICAL: Billing for logical bytes.
      PHYSICAL: Billing for physical bytes.
    """
    STORAGE_BILLING_MODEL_UNSPECIFIED = 0
    LOGICAL = 1
    PHYSICAL = 2

  class AccessValueListEntry(_messages.Message):
    r"""An object that defines dataset access for an entity.

    Fields:
      condition: Optional. condition for the binding. If CEL expression in
        this field is true, this access binding will be considered
      dataset: [Pick one] A grant authorizing all resources of a particular
        type in a particular dataset access to this dataset. Only views are
        supported for now. The role field is not required when this field is
        set. If that dataset is deleted and re-created, its access needs to be
        granted again via an update operation.
      domain: [Pick one] A domain to grant access to. Any users signed in with
        the domain specified will be granted the specified access. Example:
        "example.com". Maps to IAM policy member "domain:DOMAIN".
      groupByEmail: [Pick one] An email address of a Google Group to grant
        access to. Maps to IAM policy member "group:GROUP".
      iamMember: [Pick one] Some other type of member that appears in the IAM
        Policy but isn't a user, group, domain, or special group.
      role: An IAM role ID that should be granted to the user, group, or
        domain specified in this access entry. The following legacy mappings
        will be applied: * `OWNER`: `roles/bigquery.dataOwner` * `WRITER`:
        `roles/bigquery.dataEditor` * `READER`: `roles/bigquery.dataViewer`
        This field will accept any of the above formats, but will return only
        the legacy format. For example, if you set this field to
        "roles/bigquery.dataOwner", it will be returned back as "OWNER".
      routine: [Pick one] A routine from a different dataset to grant access
        to. Queries executed against that routine will have read access to
        views/tables/routines in this dataset. Only UDF is supported for now.
        The role field is not required when this field is set. If that routine
        is updated by any user, access to the routine needs to be granted
        again via an update operation.
      specialGroup: [Pick one] A special group to grant access to. Possible
        values include: * projectOwners: Owners of the enclosing project. *
        projectReaders: Readers of the enclosing project. * projectWriters:
        Writers of the enclosing project. * allAuthenticatedUsers: All
        authenticated BigQuery users. Maps to similarly-named IAM members.
      userByEmail: [Pick one] An email address of a user to grant access to.
        For example: <EMAIL>. Maps to IAM policy member "user:EMAIL"
        or "serviceAccount:EMAIL".
      view: [Pick one] A view from a different dataset to grant access to.
        Queries executed against that view will have read access to
        views/tables/routines in this dataset. The role field is not required
        when this field is set. If that view is updated by any user, access to
        the view needs to be granted again via an update operation.
    """

    condition = _messages.MessageField('Expr', 1)
    dataset = _messages.MessageField('DatasetAccessEntry', 2)
    domain = _messages.StringField(3)
    groupByEmail = _messages.StringField(4)
    iamMember = _messages.StringField(5)
    role = _messages.StringField(6)
    routine = _messages.MessageField('RoutineReference', 7)
    specialGroup = _messages.StringField(8)
    userByEmail = _messages.StringField(9)
    view = _messages.MessageField('TableReference', 10)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this dataset. You can use these to organize
    and group your datasets. You can set this property when inserting or
    updating a dataset. See [Creating and Updating Dataset
    Labels](https://cloud.google.com/bigquery/docs/creating-managing-
    labels#creating_and_updating_dataset_labels) for more information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceTagsValue(_messages.Message):
    r"""Optional. The [tags](https://cloud.google.com/bigquery/docs/tags)
    attached to this dataset. Tag keys are globally unique. Tag key is
    expected to be in the namespaced format, for example
    "123456789012/environment" where 123456789012 is the ID of the parent
    organization or project resource for this tag key. Tag value is expected
    to be the short name, for example "Production". See [Tag
    definitions](https://cloud.google.com/iam/docs/tags-access-
    control#definitions) for more details.

    Messages:
      AdditionalProperty: An additional property for a ResourceTagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class TagsValueListEntry(_messages.Message):
    r"""A global tag managed by Resource Manager.
    https://cloud.google.com/iam/docs/tags-access-control#definitions

    Fields:
      tagKey: Required. The namespaced friendly name of the tag key, e.g.
        "12345/environment" where 12345 is org id.
      tagValue: Required. The friendly short name of the tag value, e.g.
        "production".
    """

    tagKey = _messages.StringField(1)
    tagValue = _messages.StringField(2)

  access = _messages.MessageField('AccessValueListEntry', 1, repeated=True)
  creationTime = _messages.IntegerField(2)
  datasetReference = _messages.MessageField('DatasetReference', 3)
  defaultCollation = _messages.StringField(4)
  defaultEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 5)
  defaultPartitionExpirationMs = _messages.IntegerField(6)
  defaultRoundingMode = _messages.EnumField('DefaultRoundingModeValueValuesEnum', 7)
  defaultTableExpirationMs = _messages.IntegerField(8)
  description = _messages.StringField(9)
  etag = _messages.StringField(10)
  externalCatalogDatasetOptions = _messages.MessageField('ExternalCatalogDatasetOptions', 11)
  externalDatasetReference = _messages.MessageField('ExternalDatasetReference', 12)
  friendlyName = _messages.StringField(13)
  id = _messages.StringField(14)
  isCaseInsensitive = _messages.BooleanField(15)
  kind = _messages.StringField(16, default='bigquery#dataset')
  labels = _messages.MessageField('LabelsValue', 17)
  lastModifiedTime = _messages.IntegerField(18)
  linkedDatasetMetadata = _messages.MessageField('LinkedDatasetMetadata', 19)
  linkedDatasetSource = _messages.MessageField('LinkedDatasetSource', 20)
  location = _messages.StringField(21)
  maxTimeTravelHours = _messages.IntegerField(22)
  resourceTags = _messages.MessageField('ResourceTagsValue', 23)
  restrictions = _messages.MessageField('RestrictionConfig', 24)
  satisfiesPzi = _messages.BooleanField(25)
  satisfiesPzs = _messages.BooleanField(26)
  selfLink = _messages.StringField(27)
  storageBillingModel = _messages.EnumField('StorageBillingModelValueValuesEnum', 28)
  tags = _messages.MessageField('TagsValueListEntry', 29, repeated=True)
  type = _messages.StringField(30)


class DatasetAccessEntry(_messages.Message):
  r"""Grants all resources of particular types in a particular dataset read
  access to the current dataset. Similar to how individually authorized views
  work, updates to any resource granted through its dataset (including
  creation of new resources) requires read permission to referenced resources,
  plus write permission to the authorizing dataset.

  Enums:
    TargetTypesValueListEntryValuesEnum:

  Fields:
    dataset: The dataset this entry applies to
    targetTypes: Which resources in the dataset this entry applies to.
      Currently, only views are supported, but additional target types may be
      added in the future.
  """

  class TargetTypesValueListEntryValuesEnum(_messages.Enum):
    r"""TargetTypesValueListEntryValuesEnum enum type.

    Values:
      TARGET_TYPE_UNSPECIFIED: Do not use. You must set a target type
        explicitly.
      VIEWS: This entry applies to views in the dataset.
      ROUTINES: This entry applies to routines in the dataset.
    """
    TARGET_TYPE_UNSPECIFIED = 0
    VIEWS = 1
    ROUTINES = 2

  dataset = _messages.MessageField('DatasetReference', 1)
  targetTypes = _messages.EnumField('TargetTypesValueListEntryValuesEnum', 2, repeated=True)


class DatasetList(_messages.Message):
  r"""Response format for a page of results when listing datasets.

  Messages:
    DatasetsValueListEntry: A dataset resource with only a subset of fields,
      to be returned in a list of datasets.

  Fields:
    datasets: An array of the dataset resources in the project. Each resource
      contains basic information. For full information about a particular
      dataset resource, use the Datasets: get method. This property is omitted
      when there are no datasets in the project.
    etag: Output only. A hash value of the results page. You can use this
      property to determine if the page has changed since the last request.
    kind: Output only. The resource type. This property always returns the
      value "bigquery#datasetList"
    nextPageToken: A token that can be used to request the next results page.
      This property is omitted on the final results page.
    unreachable: A list of skipped locations that were unreachable. For more
      information about BigQuery locations, see:
      https://cloud.google.com/bigquery/docs/locations. Example: "europe-
      west5"
  """

  class DatasetsValueListEntry(_messages.Message):
    r"""A dataset resource with only a subset of fields, to be returned in a
    list of datasets.

    Messages:
      LabelsValue: The labels associated with this dataset. You can use these
        to organize and group your datasets.

    Fields:
      datasetReference: The dataset reference. Use this property to access
        specific parts of the dataset's ID, such as project ID or dataset ID.
      externalDatasetReference: Output only. Reference to a read-only external
        dataset defined in data catalogs outside of BigQuery. Filled out when
        the dataset type is EXTERNAL.
      friendlyName: An alternate name for the dataset. The friendly name is
        purely decorative in nature.
      id: The fully-qualified, unique, opaque ID of the dataset.
      kind: The resource type. This property always returns the value
        "bigquery#dataset"
      labels: The labels associated with this dataset. You can use these to
        organize and group your datasets.
      location: The geographic location where the dataset resides.
    """

    @encoding.MapUnrecognizedFields('additionalProperties')
    class LabelsValue(_messages.Message):
      r"""The labels associated with this dataset. You can use these to
      organize and group your datasets.

      Messages:
        AdditionalProperty: An additional property for a LabelsValue object.

      Fields:
        additionalProperties: Additional properties of type LabelsValue
      """

      class AdditionalProperty(_messages.Message):
        r"""An additional property for a LabelsValue object.

        Fields:
          key: Name of the additional property.
          value: A string attribute.
        """

        key = _messages.StringField(1)
        value = _messages.StringField(2)

      additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

    datasetReference = _messages.MessageField('DatasetReference', 1)
    externalDatasetReference = _messages.MessageField('ExternalDatasetReference', 2)
    friendlyName = _messages.StringField(3)
    id = _messages.StringField(4)
    kind = _messages.StringField(5)
    labels = _messages.MessageField('LabelsValue', 6)
    location = _messages.StringField(7)

  datasets = _messages.MessageField('DatasetsValueListEntry', 1, repeated=True)
  etag = _messages.StringField(2)
  kind = _messages.StringField(3, default='bigquery#datasetList')
  nextPageToken = _messages.StringField(4)
  unreachable = _messages.StringField(5, repeated=True)


class DatasetReference(_messages.Message):
  r"""Identifier for a dataset.

  Fields:
    datasetId: Required. A unique ID for this dataset, without the project
      name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_). The maximum length is 1,024 characters.
    projectId: Optional. The ID of the project containing this dataset.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)


class DestinationTableProperties(_messages.Message):
  r"""Properties for the destination table.

  Messages:
    LabelsValue: Optional. The labels associated with this table. You can use
      these to organize and group your tables. This will only be used if the
      destination table is newly created. If the table already exists and
      labels are different than the current labels are provided, the job will
      fail.

  Fields:
    description: Optional. The description for the destination table. This
      will only be used if the destination table is newly created. If the
      table already exists and a value different than the current description
      is provided, the job will fail.
    expirationTime: Internal use only.
    friendlyName: Optional. Friendly name for the destination table. If the
      table already exists, it should be same as the existing friendly name.
    labels: Optional. The labels associated with this table. You can use these
      to organize and group your tables. This will only be used if the
      destination table is newly created. If the table already exists and
      labels are different than the current labels are provided, the job will
      fail.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels associated with this table. You can use these to
    organize and group your tables. This will only be used if the destination
    table is newly created. If the table already exists and labels are
    different than the current labels are provided, the job will fail.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  expirationTime = _message_types.DateTimeField(2)
  friendlyName = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)


class DifferentialPrivacyPolicy(_messages.Message):
  r"""Represents privacy policy associated with "differential privacy" method.

  Fields:
    deltaBudget: Optional. The total delta budget for all queries against the
      privacy-protected view. Each subscriber query against this view charges
      the amount of delta that is pre-defined by the contributor through the
      privacy policy delta_per_query field. If there is sufficient budget,
      then the subscriber query attempts to complete. It might still fail due
      to other reasons, in which case the charge is refunded. If there is
      insufficient budget the query is rejected. There might be multiple
      charge attempts if a single query references multiple views. In this
      case there must be sufficient budget for all charges or the query is
      rejected and charges are refunded in best effort. The budget does not
      have a refresh policy and can only be updated via ALTER VIEW or
      circumvented by creating a new view that can be queried with a fresh
      budget.
    deltaBudgetRemaining: Output only. The delta budget remaining. If budget
      is exhausted, no more queries are allowed. Note that the budget for
      queries that are in progress is deducted before the query executes. If
      the query fails or is cancelled then the budget is refunded. In this
      case the amount of budget remaining can increase.
    deltaPerQuery: Optional. The delta value that is used per query. Delta
      represents the probability that any row will fail to be epsilon
      differentially private. Indicates the risk associated with exposing
      aggregate rows in the result of a query.
    epsilonBudget: Optional. The total epsilon budget for all queries against
      the privacy-protected view. Each subscriber query against this view
      charges the amount of epsilon they request in their query. If there is
      sufficient budget, then the subscriber query attempts to complete. It
      might still fail due to other reasons, in which case the charge is
      refunded. If there is insufficient budget the query is rejected. There
      might be multiple charge attempts if a single query references multiple
      views. In this case there must be sufficient budget for all charges or
      the query is rejected and charges are refunded in best effort. The
      budget does not have a refresh policy and can only be updated via ALTER
      VIEW or circumvented by creating a new view that can be queried with a
      fresh budget.
    epsilonBudgetRemaining: Output only. The epsilon budget remaining. If
      budget is exhausted, no more queries are allowed. Note that the budget
      for queries that are in progress is deducted before the query executes.
      If the query fails or is cancelled then the budget is refunded. In this
      case the amount of budget remaining can increase.
    maxEpsilonPerQuery: Optional. The maximum epsilon value that a query can
      consume. If the subscriber specifies epsilon as a parameter in a SELECT
      query, it must be less than or equal to this value. The epsilon
      parameter controls the amount of noise that is added to the groups - a
      higher epsilon means less noise.
    maxGroupsContributed: Optional. The maximum groups contributed value that
      is used per query. Represents the maximum number of groups to which each
      protected entity can contribute. Changing this value does not improve or
      worsen privacy. The best value for accuracy and utility depends on the
      query and data.
    privacyUnitColumn: Optional. The privacy unit column associated with this
      policy. Differential privacy policies can only have one privacy unit
      column per data source object (table, view).
  """

  deltaBudget = _messages.FloatField(1)
  deltaBudgetRemaining = _messages.FloatField(2)
  deltaPerQuery = _messages.FloatField(3)
  epsilonBudget = _messages.FloatField(4)
  epsilonBudgetRemaining = _messages.FloatField(5)
  maxEpsilonPerQuery = _messages.FloatField(6)
  maxGroupsContributed = _messages.IntegerField(7)
  privacyUnitColumn = _messages.StringField(8)


class DimensionalityReductionMetrics(_messages.Message):
  r"""Model evaluation metrics for dimensionality reduction models.

  Fields:
    totalExplainedVarianceRatio: Total percentage of variance explained by the
      selected principal components.
  """

  totalExplainedVarianceRatio = _messages.FloatField(1)


class DmlStatistics(_messages.Message):
  r"""Detailed statistics for DML statements

  Fields:
    deletedRowCount: Output only. Number of deleted Rows. populated by DML
      DELETE, MERGE and TRUNCATE statements.
    insertedRowCount: Output only. Number of inserted Rows. Populated by DML
      INSERT and MERGE statements
    updatedRowCount: Output only. Number of updated Rows. Populated by DML
      UPDATE and MERGE statements.
  """

  deletedRowCount = _messages.IntegerField(1)
  insertedRowCount = _messages.IntegerField(2)
  updatedRowCount = _messages.IntegerField(3)


class DoubleCandidates(_messages.Message):
  r"""Discrete candidates of a double hyperparameter.

  Fields:
    candidates: Candidates for the double parameter in increasing order.
  """

  candidates = _messages.FloatField(1, repeated=True)


class DoubleHparamSearchSpace(_messages.Message):
  r"""Search space for a double hyperparameter.

  Fields:
    candidates: Candidates of the double hyperparameter.
    range: Range of the double hyperparameter.
  """

  candidates = _messages.MessageField('DoubleCandidates', 1)
  range = _messages.MessageField('DoubleRange', 2)


class DoubleRange(_messages.Message):
  r"""Range of a double hyperparameter.

  Fields:
    max: Max value of the double parameter.
    min: Min value of the double parameter.
  """

  max = _messages.FloatField(1)
  min = _messages.FloatField(2)


class EncryptionConfiguration(_messages.Message):
  r"""Configuration for Cloud KMS encryption settings.

  Fields:
    kmsKeyName: Optional. Describes the Cloud KMS encryption key that will be
      used to protect destination BigQuery table. The BigQuery Service Account
      associated with your project requires access to this encryption key.
  """

  kmsKeyName = _messages.StringField(1)


class Entry(_messages.Message):
  r"""A single entry in the confusion matrix.

  Fields:
    itemCount: Number of items being predicted as this label.
    predictedLabel: The predicted label. For confidence_threshold > 0, we will
      also add an entry indicating the number of items under the confidence
      threshold.
  """

  itemCount = _messages.IntegerField(1)
  predictedLabel = _messages.StringField(2)


class ErrorProto(_messages.Message):
  r"""Error details.

  Fields:
    debugInfo: Debugging information. This property is internal to Google and
      should not be used.
    location: Specifies where the error occurred, if present.
    message: A human-readable description of the error.
    reason: A short error code that summarizes the error.
  """

  debugInfo = _messages.StringField(1)
  location = _messages.StringField(2)
  message = _messages.StringField(3)
  reason = _messages.StringField(4)


class EvaluationMetrics(_messages.Message):
  r"""Evaluation metrics of a model. These are either computed on all training
  data or just the eval data based on whether eval data was used during
  training. These are not present for imported models.

  Fields:
    arimaForecastingMetrics: Populated for ARIMA models.
    binaryClassificationMetrics: Populated for binary
      classification/classifier models.
    clusteringMetrics: Populated for clustering models.
    dimensionalityReductionMetrics: Evaluation metrics when the model is a
      dimensionality reduction model, which currently includes PCA.
    multiClassClassificationMetrics: Populated for multi-class
      classification/classifier models.
    rankingMetrics: Populated for implicit feedback type matrix factorization
      models.
    regressionMetrics: Populated for regression models and explicit feedback
      type matrix factorization models.
  """

  arimaForecastingMetrics = _messages.MessageField('ArimaForecastingMetrics', 1)
  binaryClassificationMetrics = _messages.MessageField('BinaryClassificationMetrics', 2)
  clusteringMetrics = _messages.MessageField('ClusteringMetrics', 3)
  dimensionalityReductionMetrics = _messages.MessageField('DimensionalityReductionMetrics', 4)
  multiClassClassificationMetrics = _messages.MessageField('MultiClassClassificationMetrics', 5)
  rankingMetrics = _messages.MessageField('RankingMetrics', 6)
  regressionMetrics = _messages.MessageField('RegressionMetrics', 7)


class ExplainQueryStage(_messages.Message):
  r"""A single stage of query execution.

  Enums:
    ComputeModeValueValuesEnum: Output only. Compute mode for this stage.

  Fields:
    completedParallelInputs: Number of parallel input segments completed.
    computeMode: Output only. Compute mode for this stage.
    computeMsAvg: Milliseconds the average shard spent on CPU-bound tasks.
    computeMsMax: Milliseconds the slowest shard spent on CPU-bound tasks.
    computeRatioAvg: Relative amount of time the average shard spent on CPU-
      bound tasks.
    computeRatioMax: Relative amount of time the slowest shard spent on CPU-
      bound tasks.
    endMs: Stage end time represented as milliseconds since the epoch.
    id: Unique ID for the stage within the plan.
    inputStages: IDs for stages that are inputs to this stage.
    name: Human-readable name for the stage.
    parallelInputs: Number of parallel input segments to be processed
    readMsAvg: Milliseconds the average shard spent reading input.
    readMsMax: Milliseconds the slowest shard spent reading input.
    readRatioAvg: Relative amount of time the average shard spent reading
      input.
    readRatioMax: Relative amount of time the slowest shard spent reading
      input.
    recordsRead: Number of records read into the stage.
    recordsWritten: Number of records written by the stage.
    shuffleOutputBytes: Total number of bytes written to shuffle.
    shuffleOutputBytesSpilled: Total number of bytes written to shuffle and
      spilled to disk.
    slotMs: Slot-milliseconds used by the stage.
    startMs: Stage start time represented as milliseconds since the epoch.
    status: Current status for this stage.
    steps: List of operations within the stage in dependency order
      (approximately chronological).
    waitMsAvg: Milliseconds the average shard spent waiting to be scheduled.
    waitMsMax: Milliseconds the slowest shard spent waiting to be scheduled.
    waitRatioAvg: Relative amount of time the average shard spent waiting to
      be scheduled.
    waitRatioMax: Relative amount of time the slowest shard spent waiting to
      be scheduled.
    writeMsAvg: Milliseconds the average shard spent on writing output.
    writeMsMax: Milliseconds the slowest shard spent on writing output.
    writeRatioAvg: Relative amount of time the average shard spent on writing
      output.
    writeRatioMax: Relative amount of time the slowest shard spent on writing
      output.
  """

  class ComputeModeValueValuesEnum(_messages.Enum):
    r"""Output only. Compute mode for this stage.

    Values:
      COMPUTE_MODE_UNSPECIFIED: ComputeMode type not specified.
      BIGQUERY: This stage was processed using BigQuery slots.
      BI_ENGINE: This stage was processed using BI Engine compute.
    """
    COMPUTE_MODE_UNSPECIFIED = 0
    BIGQUERY = 1
    BI_ENGINE = 2

  completedParallelInputs = _messages.IntegerField(1)
  computeMode = _messages.EnumField('ComputeModeValueValuesEnum', 2)
  computeMsAvg = _messages.IntegerField(3)
  computeMsMax = _messages.IntegerField(4)
  computeRatioAvg = _messages.FloatField(5)
  computeRatioMax = _messages.FloatField(6)
  endMs = _messages.IntegerField(7)
  id = _messages.IntegerField(8)
  inputStages = _messages.IntegerField(9, repeated=True)
  name = _messages.StringField(10)
  parallelInputs = _messages.IntegerField(11)
  readMsAvg = _messages.IntegerField(12)
  readMsMax = _messages.IntegerField(13)
  readRatioAvg = _messages.FloatField(14)
  readRatioMax = _messages.FloatField(15)
  recordsRead = _messages.IntegerField(16)
  recordsWritten = _messages.IntegerField(17)
  shuffleOutputBytes = _messages.IntegerField(18)
  shuffleOutputBytesSpilled = _messages.IntegerField(19)
  slotMs = _messages.IntegerField(20)
  startMs = _messages.IntegerField(21)
  status = _messages.StringField(22)
  steps = _messages.MessageField('ExplainQueryStep', 23, repeated=True)
  waitMsAvg = _messages.IntegerField(24)
  waitMsMax = _messages.IntegerField(25)
  waitRatioAvg = _messages.FloatField(26)
  waitRatioMax = _messages.FloatField(27)
  writeMsAvg = _messages.IntegerField(28)
  writeMsMax = _messages.IntegerField(29)
  writeRatioAvg = _messages.FloatField(30)
  writeRatioMax = _messages.FloatField(31)


class ExplainQueryStep(_messages.Message):
  r"""An operation within a stage.

  Fields:
    kind: Machine-readable operation type.
    substeps: Human-readable description of the step(s).
  """

  kind = _messages.StringField(1)
  substeps = _messages.StringField(2, repeated=True)


class Explanation(_messages.Message):
  r"""Explanation for a single feature.

  Fields:
    attribution: Attribution of feature.
    featureName: The full feature name. For non-numerical features, will be
      formatted like `.`. Overall size of feature name will always be
      truncated to first 120 characters.
  """

  attribution = _messages.FloatField(1)
  featureName = _messages.StringField(2)


class ExportDataStatistics(_messages.Message):
  r"""Statistics for the EXPORT DATA statement as part of Query Job. EXTRACT
  JOB statistics are populated in JobStatistics4.

  Fields:
    fileCount: Number of destination files generated in case of EXPORT DATA
      statement only.
    rowCount: [Alpha] Number of destination rows generated in case of EXPORT
      DATA statement only.
  """

  fileCount = _messages.IntegerField(1)
  rowCount = _messages.IntegerField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalCatalogDatasetOptions(_messages.Message):
  r"""Options defining open source compatible datasets living in the BigQuery
  catalog. Contains metadata of open source database, schema, or namespace
  represented by the current dataset.

  Messages:
    ParametersValue: Optional. A map of key value pairs defining the
      parameters and properties of the open source schema. Maximum size of
      2MiB.

  Fields:
    defaultStorageLocationUri: Optional. The storage location URI for all
      tables in the dataset. Equivalent to hive metastore's database
      locationUri. Maximum length of 1024 characters.
    parameters: Optional. A map of key value pairs defining the parameters and
      properties of the open source schema. Maximum size of 2MiB.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Optional. A map of key value pairs defining the parameters and
    properties of the open source schema. Maximum size of 2MiB.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Additional properties of type ParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultStorageLocationUri = _messages.StringField(1)
  parameters = _messages.MessageField('ParametersValue', 2)


class ExternalCatalogTableOptions(_messages.Message):
  r"""Metadata about open source compatible table. The fields contained in
  these options correspond to Hive metastore's table-level properties.

  Messages:
    ParametersValue: Optional. A map of the key-value pairs defining the
      parameters and properties of the open source table. Corresponds with
      Hive metastore table parameters. Maximum size of 4MiB.

  Fields:
    connectionId: Optional. A connection ID that specifies the credentials to
      be used to read external storage, such as Azure Blob, Cloud Storage, or
      Amazon S3. This connection is needed to read the open source table from
      BigQuery. The connection_id format must be either `..` or
      `projects//locations//connections/`.
    parameters: Optional. A map of the key-value pairs defining the parameters
      and properties of the open source table. Corresponds with Hive metastore
      table parameters. Maximum size of 4MiB.
    storageDescriptor: Optional. A storage descriptor containing information
      about the physical storage of this table.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Optional. A map of the key-value pairs defining the parameters and
    properties of the open source table. Corresponds with Hive metastore table
    parameters. Maximum size of 4MiB.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Additional properties of type ParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectionId = _messages.StringField(1)
  parameters = _messages.MessageField('ParametersValue', 2)
  storageDescriptor = _messages.MessageField('StorageDescriptor', 3)


class ExternalDataConfiguration(_messages.Message):
  r"""A ExternalDataConfiguration object.

  Enums:
    DecimalTargetTypesValueListEntryValuesEnum:
    FileSetSpecTypeValueValuesEnum: Optional. Specifies how source URIs are
      interpreted for constructing the file set to load. By default source
      URIs are expanded against the underlying storage. Other options include
      specifying manifest files. Only applicable to object storage systems.
    JsonExtensionValueValuesEnum: Optional. Load option to be used together
      with source_format newline-delimited JSON to indicate that a variant of
      JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON
      (and source_format must be set to NEWLINE_DELIMITED_JSON).
    MetadataCacheModeValueValuesEnum: Optional. Metadata Cache Mode for the
      table. Set this to enable caching of metadata from external data source.
    ObjectMetadataValueValuesEnum: Optional. ObjectMetadata is used to create
      Object Tables. Object Tables contain a listing of objects (with their
      metadata) found at the source_uris. If ObjectMetadata is set,
      source_format should be omitted. Currently SIMPLE is the only supported
      Object Metadata type.

  Fields:
    autodetect: Try to detect schema and format options automatically. Any
      option specified explicitly will be honored.
    avroOptions: Optional. Additional properties to set if sourceFormat is set
      to AVRO.
    bigtableOptions: Optional. Additional options if sourceFormat is set to
      BIGTABLE.
    compression: Optional. The compression type of the data source. Possible
      values include GZIP and NONE. The default value is NONE. This setting is
      ignored for Google Cloud Bigtable, Google Cloud Datastore backups, Avro,
      ORC and Parquet formats. An empty string is an invalid value.
    connectionId: Optional. The connection specifying the credentials to be
      used to read external storage, such as Azure Blob, Cloud Storage, or S3.
      The connection_id can have the form
      `{project_id}.{location_id};{connection_id}` or `projects/{project_id}/l
      ocations/{location_id}/connections/{connection_id}`.
    csvOptions: Optional. Additional properties to set if sourceFormat is set
      to CSV.
    dateFormat: Optional. Format used to parse DATE values. Supports C-style
      and SQL-style values.
    datetimeFormat: Optional. Format used to parse DATETIME values. Supports
      C-style and SQL-style values.
    decimalTargetTypes: Defines the list of possible SQL data types to which
      the source decimal values are converted. This list and the precision and
      the scale parameters of the decimal field determine the target type. In
      the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is
      in the specified list and if it supports the precision and the scale.
      STRING supports all precision and scale values. If none of the listed
      types supports the precision and the scale, the type supporting the
      widest range in the specified list is picked, and if a value exceeds the
      supported range when reading the data, an error will be thrown. Example:
      Suppose the value of this field is ["NUMERIC", "BIGNUMERIC"]. If
      (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC
      (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC
      (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; *
      (77,38) -> BIGNUMERIC (error if value exceeds supported range). This
      field cannot contain duplicate types. The order of the types in this
      field is ignored. For example, ["BIGNUMERIC", "NUMERIC"] is the same as
      ["NUMERIC", "BIGNUMERIC"] and NUMERIC always takes precedence over
      BIGNUMERIC. Defaults to ["NUMERIC", "STRING"] for ORC and ["NUMERIC"]
      for the other file formats.
    fileSetSpecType: Optional. Specifies how source URIs are interpreted for
      constructing the file set to load. By default source URIs are expanded
      against the underlying storage. Other options include specifying
      manifest files. Only applicable to object storage systems.
    googleSheetsOptions: Optional. Additional options if sourceFormat is set
      to GOOGLE_SHEETS.
    hivePartitioningOptions: Optional. When set, configures hive partitioning
      support. Not all storage formats support hive partitioning -- requesting
      hive partitioning on an unsupported format will lead to an error, as
      will providing an invalid specification.
    ignoreUnknownValues: Optional. Indicates if BigQuery should allow extra
      values that are not represented in the table schema. If true, the extra
      values are ignored. If false, records with extra columns are treated as
      bad records, and if there are too many bad records, an invalid error is
      returned in the job result. The default value is false. The sourceFormat
      property determines what BigQuery treats as an extra value: CSV:
      Trailing columns JSON: Named values that don't match any column names
      Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore
      backups: This setting is ignored. Avro: This setting is ignored. ORC:
      This setting is ignored. Parquet: This setting is ignored.
    jsonExtension: Optional. Load option to be used together with
      source_format newline-delimited JSON to indicate that a variant of JSON
      is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and
      source_format must be set to NEWLINE_DELIMITED_JSON).
    jsonOptions: Optional. Additional properties to set if sourceFormat is set
      to JSON.
    maxBadRecords: Optional. The maximum number of bad records that BigQuery
      can ignore when reading data. If the number of bad records exceeds this
      value, an invalid error is returned in the job result. The default value
      is 0, which requires that all records are valid. This setting is ignored
      for Google Cloud Bigtable, Google Cloud Datastore backups, Avro, ORC and
      Parquet formats.
    metadataCacheMode: Optional. Metadata Cache Mode for the table. Set this
      to enable caching of metadata from external data source.
    objectMetadata: Optional. ObjectMetadata is used to create Object Tables.
      Object Tables contain a listing of objects (with their metadata) found
      at the source_uris. If ObjectMetadata is set, source_format should be
      omitted. Currently SIMPLE is the only supported Object Metadata type.
    parquetOptions: Optional. Additional properties to set if sourceFormat is
      set to PARQUET.
    referenceFileSchemaUri: Optional. When creating an external table, the
      user can provide a reference file with the table schema. This is enabled
      for the following formats: AVRO, PARQUET, ORC.
    schema: Optional. The schema for the data. Schema is required for CSV and
      JSON formats if autodetect is not on. Schema is disallowed for Google
      Cloud Bigtable, Cloud Datastore backups, Avro, ORC and Parquet formats.
    sourceFormat: [Required] The data format. For CSV files, specify "CSV".
      For Google sheets, specify "GOOGLE_SHEETS". For newline-delimited JSON,
      specify "NEWLINE_DELIMITED_JSON". For Avro files, specify "AVRO". For
      Google Cloud Datastore backups, specify "DATASTORE_BACKUP". For Apache
      Iceberg tables, specify "ICEBERG". For ORC files, specify "ORC". For
      Parquet files, specify "PARQUET". [Beta] For Google Cloud Bigtable,
      specify "BIGTABLE".
    sourceUris: [Required] The fully-qualified URIs that point to your data in
      Google Cloud. For Google Cloud Storage URIs: Each URI can contain one
      '*' wildcard character and it must come after the 'bucket' name. Size
      limits related to load jobs apply to external data sources. For Google
      Cloud Bigtable URIs: Exactly one URI can be specified and it has be a
      fully specified and valid HTTPS URL for a Google Cloud Bigtable table.
      For Google Cloud Datastore backups, exactly one URI can be specified.
      Also, the '*' wildcard character is not allowed.
    timeFormat: Optional. Format used to parse TIME values. Supports C-style
      and SQL-style values.
    timeZone: Optional. Time zone used when parsing timestamp values that do
      not have specific time zone information (e.g. 2024-04-20 12:34:56). The
      expected format is a IANA timezone string (e.g. America/Los_Angeles).
    timestampFormat: Optional. Format used to parse TIMESTAMP values. Supports
      C-style and SQL-style values.
  """

  class DecimalTargetTypesValueListEntryValuesEnum(_messages.Enum):
    r"""DecimalTargetTypesValueListEntryValuesEnum enum type.

    Values:
      DECIMAL_TARGET_TYPE_UNSPECIFIED: Invalid type.
      NUMERIC: Decimal values could be converted to NUMERIC type.
      BIGNUMERIC: Decimal values could be converted to BIGNUMERIC type.
      STRING: Decimal values could be converted to STRING type.
    """
    DECIMAL_TARGET_TYPE_UNSPECIFIED = 0
    NUMERIC = 1
    BIGNUMERIC = 2
    STRING = 3

  class FileSetSpecTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how source URIs are interpreted for constructing
    the file set to load. By default source URIs are expanded against the
    underlying storage. Other options include specifying manifest files. Only
    applicable to object storage systems.

    Values:
      FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH: This option expands source URIs by
        listing files from the object store. It is the default behavior if
        FileSetSpecType is not set.
      FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST: This option indicates
        that the provided URIs are newline-delimited manifest files, with one
        URI per line. Wildcard URIs are not supported.
    """
    FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH = 0
    FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST = 1

  class JsonExtensionValueValuesEnum(_messages.Enum):
    r"""Optional. Load option to be used together with source_format newline-
    delimited JSON to indicate that a variant of JSON is being loaded. To load
    newline-delimited GeoJSON, specify GEOJSON (and source_format must be set
    to NEWLINE_DELIMITED_JSON).

    Values:
      JSON_EXTENSION_UNSPECIFIED: The default if provided value is not one
        included in the enum, or the value is not specified. The source format
        is parsed without any modification.
      GEOJSON: Use GeoJSON variant of JSON. See
        https://tools.ietf.org/html/rfc7946.
    """
    JSON_EXTENSION_UNSPECIFIED = 0
    GEOJSON = 1

  class MetadataCacheModeValueValuesEnum(_messages.Enum):
    r"""Optional. Metadata Cache Mode for the table. Set this to enable
    caching of metadata from external data source.

    Values:
      METADATA_CACHE_MODE_UNSPECIFIED: Unspecified metadata cache mode.
      AUTOMATIC: Set this mode to trigger automatic background refresh of
        metadata cache from the external source. Queries will use the latest
        available cache version within the table's maxStaleness interval.
      MANUAL: Set this mode to enable triggering manual refresh of the
        metadata cache from external source. Queries will use the latest
        manually triggered cache version within the table's maxStaleness
        interval.
    """
    METADATA_CACHE_MODE_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  class ObjectMetadataValueValuesEnum(_messages.Enum):
    r"""Optional. ObjectMetadata is used to create Object Tables. Object
    Tables contain a listing of objects (with their metadata) found at the
    source_uris. If ObjectMetadata is set, source_format should be omitted.
    Currently SIMPLE is the only supported Object Metadata type.

    Values:
      OBJECT_METADATA_UNSPECIFIED: Unspecified by default.
      DIRECTORY: A synonym for `SIMPLE`.
      SIMPLE: Directory listing of objects.
    """
    OBJECT_METADATA_UNSPECIFIED = 0
    DIRECTORY = 1
    SIMPLE = 2

  autodetect = _messages.BooleanField(1)
  avroOptions = _messages.MessageField('AvroOptions', 2)
  bigtableOptions = _messages.MessageField('BigtableOptions', 3)
  compression = _messages.StringField(4)
  connectionId = _messages.StringField(5)
  csvOptions = _messages.MessageField('CsvOptions', 6)
  dateFormat = _messages.StringField(7)
  datetimeFormat = _messages.StringField(8)
  decimalTargetTypes = _messages.EnumField('DecimalTargetTypesValueListEntryValuesEnum', 9, repeated=True)
  fileSetSpecType = _messages.EnumField('FileSetSpecTypeValueValuesEnum', 10)
  googleSheetsOptions = _messages.MessageField('GoogleSheetsOptions', 11)
  hivePartitioningOptions = _messages.MessageField('HivePartitioningOptions', 12)
  ignoreUnknownValues = _messages.BooleanField(13)
  jsonExtension = _messages.EnumField('JsonExtensionValueValuesEnum', 14)
  jsonOptions = _messages.MessageField('JsonOptions', 15)
  maxBadRecords = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  metadataCacheMode = _messages.EnumField('MetadataCacheModeValueValuesEnum', 17)
  objectMetadata = _messages.EnumField('ObjectMetadataValueValuesEnum', 18)
  parquetOptions = _messages.MessageField('ParquetOptions', 19)
  referenceFileSchemaUri = _messages.StringField(20)
  schema = _messages.MessageField('TableSchema', 21)
  sourceFormat = _messages.StringField(22)
  sourceUris = _messages.StringField(23, repeated=True)
  timeFormat = _messages.StringField(24)
  timeZone = _messages.StringField(25)
  timestampFormat = _messages.StringField(26)


class ExternalDatasetReference(_messages.Message):
  r"""Configures the access a dataset defined in an external metadata storage.

  Fields:
    connection: Required. The connection id that is used to access the
      external_source. Format: projects/{project_id}/locations/{location_id}/c
      onnections/{connection_id}
    externalSource: Required. External source that backs this dataset.
  """

  connection = _messages.StringField(1)
  externalSource = _messages.StringField(2)


class ExternalServiceCost(_messages.Message):
  r"""The external service cost is a portion of the total cost, these costs
  are not additive with total_bytes_billed. Moreover, this field only track
  external service costs that will show up as BigQuery costs (e.g. training
  BigQuery ML job with google cloud CAIP or Automl Tables services), not other
  costs which may be accrued by running the query (e.g. reading from Bigtable
  or Cloud Storage). The external service costs with different billing sku
  (e.g. CAIP job is charged based on VM usage) are converted to BigQuery
  billed_bytes and slot_ms with equivalent amount of US dollars. Services may
  not directly correlate to these metrics, but these are the equivalents for
  billing purposes. Output only.

  Fields:
    bytesBilled: External service cost in terms of bigquery bytes billed.
    bytesProcessed: External service cost in terms of bigquery bytes
      processed.
    externalService: External service name.
    reservedSlotCount: Non-preemptable reserved slots used for external job.
      For example, reserved slots for Cloua AI Platform job are the VM usages
      converted to BigQuery slot with equivalent mount of price.
    slotMs: External service cost in terms of bigquery slot milliseconds.
  """

  bytesBilled = _messages.IntegerField(1)
  bytesProcessed = _messages.IntegerField(2)
  externalService = _messages.StringField(3)
  reservedSlotCount = _messages.IntegerField(4)
  slotMs = _messages.IntegerField(5)


class FeatureValue(_messages.Message):
  r"""Representative value of a single feature within the cluster.

  Fields:
    categoricalValue: The categorical feature value.
    featureColumn: The feature column name.
    numericalValue: The numerical feature value. This is the centroid value
      for this feature.
  """

  categoricalValue = _messages.MessageField('CategoricalValue', 1)
  featureColumn = _messages.StringField(2)
  numericalValue = _messages.FloatField(3)


class ForeignTypeInfo(_messages.Message):
  r"""Metadata about the foreign data type definition such as the system in
  which the type is defined.

  Enums:
    TypeSystemValueValuesEnum: Required. Specifies the system which defines
      the foreign data type.

  Fields:
    typeSystem: Required. Specifies the system which defines the foreign data
      type.
  """

  class TypeSystemValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the system which defines the foreign data type.

    Values:
      TYPE_SYSTEM_UNSPECIFIED: TypeSystem not specified.
      HIVE: Represents Hive data types.
    """
    TYPE_SYSTEM_UNSPECIFIED = 0
    HIVE = 1

  typeSystem = _messages.EnumField('TypeSystemValueValuesEnum', 1)


class ForeignViewDefinition(_messages.Message):
  r"""A view can be represented in multiple ways. Each representation has its
  own dialect. This message stores the metadata required for these
  representations.

  Fields:
    dialect: Optional. Represents the dialect of the query.
    query: Required. The query that defines the view.
  """

  dialect = _messages.StringField(1)
  query = _messages.StringField(2)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GetQueryResultsResponse(_messages.Message):
  r"""Response object of GetQueryResults.

  Fields:
    cacheHit: Whether the query result was fetched from the query cache.
    errors: Output only. The first errors or warnings encountered during the
      running of the job. The final message includes the number of errors that
      caused the process to stop. Errors here do not necessarily mean that the
      job has completed or was unsuccessful. For more information about error
      messages, see [Error
      messages](https://cloud.google.com/bigquery/docs/error-messages).
    etag: A hash of this response.
    jobComplete: Whether the query has completed or not. If rows or totalRows
      are present, this will always be true. If this is false, totalRows will
      not be available.
    jobReference: Reference to the BigQuery Job that was created to run the
      query. This field will be present even if the original request timed
      out, in which case GetQueryResults can be used to read the results once
      the query has completed. Since this API only returns the first page of
      results, subsequent pages can be fetched via the same mechanism
      (GetQueryResults).
    kind: The resource type of the response.
    numDmlAffectedRows: Output only. The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    pageToken: A token used for paging results. When this token is non-empty,
      it indicates additional results are available.
    rows: An object with as many results as can be contained within the
      maximum permitted reply size. To get any additional rows, you can call
      GetQueryResults and specify the jobReference returned above. Present
      only when the query completes successfully. The REST-based
      representation of this data leverages a series of JSON f,v objects for
      indicating fields and values.
    schema: The schema of the results. Present only when the query completes
      successfully.
    totalBytesProcessed: The total number of bytes processed for this query.
    totalRows: The total number of rows in the complete query result set,
      which can be more than the number of rows in this single page of
      results. Present only when the query completes successfully.
  """

  cacheHit = _messages.BooleanField(1)
  errors = _messages.MessageField('ErrorProto', 2, repeated=True)
  etag = _messages.StringField(3)
  jobComplete = _messages.BooleanField(4)
  jobReference = _messages.MessageField('JobReference', 5)
  kind = _messages.StringField(6, default='bigquery#getQueryResultsResponse')
  numDmlAffectedRows = _messages.IntegerField(7)
  pageToken = _messages.StringField(8)
  rows = _messages.MessageField('TableRow', 9, repeated=True)
  schema = _messages.MessageField('TableSchema', 10)
  totalBytesProcessed = _messages.IntegerField(11)
  totalRows = _messages.IntegerField(12, variant=_messages.Variant.UINT64)


class GetServiceAccountResponse(_messages.Message):
  r"""Response object of GetServiceAccount

  Fields:
    email: The service account email address.
    kind: The resource type of the response.
  """

  email = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#getServiceAccountResponse')


class GlobalExplanation(_messages.Message):
  r"""Global explanations containing the top most important features after
  training.

  Fields:
    classLabel: Class label for this set of global explanations. Will be
      empty/null for binary logistic and linear regression models. Sorted
      alphabetically in descending order.
    explanations: A list of the top global explanations. Sorted by absolute
      value of attribution in descending order.
  """

  classLabel = _messages.StringField(1)
  explanations = _messages.MessageField('Explanation', 2, repeated=True)


class GoogleSheetsOptions(_messages.Message):
  r"""Options specific to Google Sheets data sources.

  Fields:
    range: Optional. Range of a sheet to query from. Only used when non-empty.
      Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id For
      example: sheet1!A1:B20
    skipLeadingRows: Optional. The number of rows at the top of a sheet that
      BigQuery will skip when reading the data. The default value is 0. This
      property is useful if you have header rows that should be skipped. When
      autodetect is on, the behavior is the following: * skipLeadingRows
      unspecified - Autodetect tries to detect headers in the first row. If
      they are not detected, the row is read as data. Otherwise data is read
      starting from the second row. * skipLeadingRows is 0 - Instructs
      autodetect that there are no headers and data should be read starting
      from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1
      rows and tries to detect headers in row N. If headers are not detected,
      row N is just skipped. Otherwise row N is used to extract column names
      for the detected schema.
  """

  range = _messages.StringField(1)
  skipLeadingRows = _messages.IntegerField(2)


class HighCardinalityJoin(_messages.Message):
  r"""High cardinality join detailed information.

  Fields:
    leftRows: Output only. Count of left input rows.
    outputRows: Output only. Count of the output rows.
    rightRows: Output only. Count of right input rows.
    stepIndex: Output only. The index of the join operator in the
      ExplainQueryStep lists.
  """

  leftRows = _messages.IntegerField(1)
  outputRows = _messages.IntegerField(2)
  rightRows = _messages.IntegerField(3)
  stepIndex = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class HivePartitioningOptions(_messages.Message):
  r"""Options for configuring hive partitioning detect.

  Fields:
    fields: Output only. For permanent external tables, this field is
      populated with the hive partition keys in the order they were inferred.
      The types of the partition keys can be deduced by checking the table
      schema (which will include the partition keys). Not every API will
      populate this field in the output. For example, Tables.Get will populate
      it, but Tables.List will not contain this field.
    mode: Optional. When set, what mode of hive partitioning to use when
      reading data. The following modes are supported: * AUTO: automatically
      infer partition key name(s) and type(s). * STRINGS: automatically infer
      partition key name(s). All types are strings. * CUSTOM: partition key
      schema is encoded in the source URI prefix. Not all storage formats
      support hive partitioning. Requesting hive partitioning on an
      unsupported format will lead to an error. Currently supported formats
      are: JSON, CSV, ORC, Avro and Parquet.
    requirePartitionFilter: Optional. If set to true, queries over this table
      require a partition filter that can be used for partition elimination to
      be specified. Note that this field should only be true when creating a
      permanent external table or querying a temporary external table. Hive-
      partitioned loads with require_partition_filter explicitly set to true
      will fail.
    sourceUriPrefix: Optional. When hive partition detection is requested, a
      common prefix for all source uris must be required. The prefix must end
      immediately before the partition key encoding begins. For example,
      consider files following this data layout:
      gs://bucket/path_to_table/dt=2019-06-01/country=USA/id=7/file.avro
      gs://bucket/path_to_table/dt=2019-05-31/country=CA/id=3/file.avro When
      hive partitioning is requested with either AUTO or STRINGS detection,
      the common prefix can be either of gs://bucket/path_to_table or
      gs://bucket/path_to_table/. CUSTOM detection requires encoding the
      partitioning schema immediately after the common prefix. For CUSTOM, any
      of * gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:INTEGER} *
      gs://bucket/path_to_table/{dt:STRING}/{country:STRING}/{id:INTEGER} *
      gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:STRING} would
      all be valid source URI prefixes.
  """

  fields = _messages.StringField(1, repeated=True)
  mode = _messages.StringField(2)
  requirePartitionFilter = _messages.BooleanField(3, default=False)
  sourceUriPrefix = _messages.StringField(4)


class HparamSearchSpaces(_messages.Message):
  r"""Hyperparameter search spaces. These should be a subset of
  training_options.

  Fields:
    activationFn: Activation functions of neural network models.
    batchSize: Mini batch sample size.
    boosterType: Booster type for boosted tree models.
    colsampleBylevel: Subsample ratio of columns for each level for boosted
      tree models.
    colsampleBynode: Subsample ratio of columns for each node(split) for
      boosted tree models.
    colsampleBytree: Subsample ratio of columns when constructing each tree
      for boosted tree models.
    dartNormalizeType: Dart normalization type for boosted tree models.
    dropout: Dropout probability for dnn model training and boosted tree
      models using dart booster.
    hiddenUnits: Hidden units for neural network models.
    l1Reg: L1 regularization coefficient.
    l2Reg: L2 regularization coefficient.
    learnRate: Learning rate of training jobs.
    maxTreeDepth: Maximum depth of a tree for boosted tree models.
    minSplitLoss: Minimum split loss for boosted tree models.
    minTreeChildWeight: Minimum sum of instance weight needed in a child for
      boosted tree models.
    numClusters: Number of clusters for k-means.
    numFactors: Number of latent factors to train on.
    numParallelTree: Number of parallel trees for boosted tree models.
    optimizer: Optimizer of TF models.
    subsample: Subsample the training data to grow tree to prevent overfitting
      for boosted tree models.
    treeMethod: Tree construction algorithm for boosted tree models.
    walsAlpha: Hyperparameter for matrix factoration when implicit feedback
      type is specified.
  """

  activationFn = _messages.MessageField('StringHparamSearchSpace', 1)
  batchSize = _messages.MessageField('IntHparamSearchSpace', 2)
  boosterType = _messages.MessageField('StringHparamSearchSpace', 3)
  colsampleBylevel = _messages.MessageField('DoubleHparamSearchSpace', 4)
  colsampleBynode = _messages.MessageField('DoubleHparamSearchSpace', 5)
  colsampleBytree = _messages.MessageField('DoubleHparamSearchSpace', 6)
  dartNormalizeType = _messages.MessageField('StringHparamSearchSpace', 7)
  dropout = _messages.MessageField('DoubleHparamSearchSpace', 8)
  hiddenUnits = _messages.MessageField('IntArrayHparamSearchSpace', 9)
  l1Reg = _messages.MessageField('DoubleHparamSearchSpace', 10)
  l2Reg = _messages.MessageField('DoubleHparamSearchSpace', 11)
  learnRate = _messages.MessageField('DoubleHparamSearchSpace', 12)
  maxTreeDepth = _messages.MessageField('IntHparamSearchSpace', 13)
  minSplitLoss = _messages.MessageField('DoubleHparamSearchSpace', 14)
  minTreeChildWeight = _messages.MessageField('IntHparamSearchSpace', 15)
  numClusters = _messages.MessageField('IntHparamSearchSpace', 16)
  numFactors = _messages.MessageField('IntHparamSearchSpace', 17)
  numParallelTree = _messages.MessageField('IntHparamSearchSpace', 18)
  optimizer = _messages.MessageField('StringHparamSearchSpace', 19)
  subsample = _messages.MessageField('DoubleHparamSearchSpace', 20)
  treeMethod = _messages.MessageField('StringHparamSearchSpace', 21)
  walsAlpha = _messages.MessageField('DoubleHparamSearchSpace', 22)


class HparamTuningTrial(_messages.Message):
  r"""Training info of a trial in [hyperparameter
  tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
  sql/bigqueryml-syntax-hp-tuning-overview) models.

  Enums:
    StatusValueValuesEnum: The status of the trial.

  Fields:
    endTimeMs: Ending time of the trial.
    errorMessage: Error message for FAILED and INFEASIBLE trial.
    evalLoss: Loss computed on the eval data at the end of trial.
    evaluationMetrics: Evaluation metrics of this trial calculated on the test
      data. Empty in Job API.
    hparamTuningEvaluationMetrics: Hyperparameter tuning evaluation metrics of
      this trial calculated on the eval data. Unlike evaluation_metrics, only
      the fields corresponding to the hparam_tuning_objectives are set.
    hparams: The hyperprameters selected for this trial.
    startTimeMs: Starting time of the trial.
    status: The status of the trial.
    trainingLoss: Loss computed on the training data at the end of trial.
    trialId: 1-based index of the trial.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of the trial.

    Values:
      TRIAL_STATUS_UNSPECIFIED: Default value.
      NOT_STARTED: Scheduled but not started.
      RUNNING: Running state.
      SUCCEEDED: The trial succeeded.
      FAILED: The trial failed.
      INFEASIBLE: The trial is infeasible due to the invalid params.
      STOPPED_EARLY: Trial stopped early because it's not promising.
    """
    TRIAL_STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    INFEASIBLE = 5
    STOPPED_EARLY = 6

  endTimeMs = _messages.IntegerField(1)
  errorMessage = _messages.StringField(2)
  evalLoss = _messages.FloatField(3)
  evaluationMetrics = _messages.MessageField('EvaluationMetrics', 4)
  hparamTuningEvaluationMetrics = _messages.MessageField('EvaluationMetrics', 5)
  hparams = _messages.MessageField('TrainingOptions', 6)
  startTimeMs = _messages.IntegerField(7)
  status = _messages.EnumField('StatusValueValuesEnum', 8)
  trainingLoss = _messages.FloatField(9)
  trialId = _messages.IntegerField(10)


class IndexUnusedReason(_messages.Message):
  r"""Reason about why no search index was used in the search query (or sub-
  query).

  Enums:
    CodeValueValuesEnum: Specifies the high-level reason for the scenario when
      no search index was used.

  Fields:
    baseTable: Specifies the base table involved in the reason that no search
      index was used.
    code: Specifies the high-level reason for the scenario when no search
      index was used.
    indexName: Specifies the name of the unused search index, if available.
    message: Free form human-readable reason for the scenario when no search
      index was used.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Specifies the high-level reason for the scenario when no search index
    was used.

    Values:
      CODE_UNSPECIFIED: Code not specified.
      INDEX_CONFIG_NOT_AVAILABLE: Indicates the search index configuration has
        not been created.
      PENDING_INDEX_CREATION: Indicates the search index creation has not been
        completed.
      BASE_TABLE_TRUNCATED: Indicates the base table has been truncated (rows
        have been removed from table with TRUNCATE TABLE statement) since the
        last time the search index was refreshed.
      INDEX_CONFIG_MODIFIED: Indicates the search index configuration has been
        changed since the last time the search index was refreshed.
      TIME_TRAVEL_QUERY: Indicates the search query accesses data at a
        timestamp before the last time the search index was refreshed.
      NO_PRUNING_POWER: Indicates the usage of search index will not
        contribute to any pruning improvement for the search function, e.g.
        when the search predicate is in a disjunction with other non-search
        predicates.
      UNINDEXED_SEARCH_FIELDS: Indicates the search index does not cover all
        fields in the search function.
      UNSUPPORTED_SEARCH_PATTERN: Indicates the search index does not support
        the given search query pattern.
      OPTIMIZED_WITH_MATERIALIZED_VIEW: Indicates the query has been optimized
        by using a materialized view.
      SECURED_BY_DATA_MASKING: Indicates the query has been secured by data
        masking, and thus search indexes are not applicable.
      MISMATCHED_TEXT_ANALYZER: Indicates that the search index and the search
        function call do not have the same text analyzer.
      BASE_TABLE_TOO_SMALL: Indicates the base table is too small (below a
        certain threshold). The index does not provide noticeable search
        performance gains when the base table is too small.
      BASE_TABLE_TOO_LARGE: Indicates that the total size of indexed base
        tables in your organization exceeds your region's limit and the index
        is not used in the query. To index larger base tables, you can use
        your own reservation for index-management jobs.
      ESTIMATED_PERFORMANCE_GAIN_TOO_LOW: Indicates that the estimated
        performance gain from using the search index is too low for the given
        search query.
      NOT_SUPPORTED_IN_STANDARD_EDITION: Indicates that search indexes can not
        be used for search query with STANDARD edition.
      INDEX_SUPPRESSED_BY_FUNCTION_OPTION: Indicates that an option in the
        search function that cannot make use of the index has been selected.
      QUERY_CACHE_HIT: Indicates that the query was cached, and thus the
        search index was not used.
      STALE_INDEX: The index cannot be used in the search query because it is
        stale.
      INTERNAL_ERROR: Indicates an internal error that causes the search index
        to be unused.
      OTHER_REASON: Indicates that the reason search indexes cannot be used in
        the query is not covered by any of the other IndexUnusedReason
        options.
    """
    CODE_UNSPECIFIED = 0
    INDEX_CONFIG_NOT_AVAILABLE = 1
    PENDING_INDEX_CREATION = 2
    BASE_TABLE_TRUNCATED = 3
    INDEX_CONFIG_MODIFIED = 4
    TIME_TRAVEL_QUERY = 5
    NO_PRUNING_POWER = 6
    UNINDEXED_SEARCH_FIELDS = 7
    UNSUPPORTED_SEARCH_PATTERN = 8
    OPTIMIZED_WITH_MATERIALIZED_VIEW = 9
    SECURED_BY_DATA_MASKING = 10
    MISMATCHED_TEXT_ANALYZER = 11
    BASE_TABLE_TOO_SMALL = 12
    BASE_TABLE_TOO_LARGE = 13
    ESTIMATED_PERFORMANCE_GAIN_TOO_LOW = 14
    NOT_SUPPORTED_IN_STANDARD_EDITION = 15
    INDEX_SUPPRESSED_BY_FUNCTION_OPTION = 16
    QUERY_CACHE_HIT = 17
    STALE_INDEX = 18
    INTERNAL_ERROR = 19
    OTHER_REASON = 20

  baseTable = _messages.MessageField('TableReference', 1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)
  indexName = _messages.StringField(3)
  message = _messages.StringField(4)


class InputDataChange(_messages.Message):
  r"""Details about the input data change insight.

  Fields:
    recordsReadDiffPercentage: Output only. Records read difference percentage
      compared to a previous run.
  """

  recordsReadDiffPercentage = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class IntArray(_messages.Message):
  r"""An array of int.

  Fields:
    elements: Elements in the int array.
  """

  elements = _messages.IntegerField(1, repeated=True)


class IntArrayHparamSearchSpace(_messages.Message):
  r"""Search space for int array.

  Fields:
    candidates: Candidates for the int array parameter.
  """

  candidates = _messages.MessageField('IntArray', 1, repeated=True)


class IntCandidates(_messages.Message):
  r"""Discrete candidates of an int hyperparameter.

  Fields:
    candidates: Candidates for the int parameter in increasing order.
  """

  candidates = _messages.IntegerField(1, repeated=True)


class IntHparamSearchSpace(_messages.Message):
  r"""Search space for an int hyperparameter.

  Fields:
    candidates: Candidates of the int hyperparameter.
    range: Range of the int hyperparameter.
  """

  candidates = _messages.MessageField('IntCandidates', 1)
  range = _messages.MessageField('IntRange', 2)


class IntRange(_messages.Message):
  r"""Range of an int hyperparameter.

  Fields:
    max: Max value of the int parameter.
    min: Min value of the int parameter.
  """

  max = _messages.IntegerField(1)
  min = _messages.IntegerField(2)


class IterationResult(_messages.Message):
  r"""Information about a single iteration of the training run.

  Fields:
    arimaResult: Arima result.
    clusterInfos: Information about top clusters for clustering models.
    durationMs: Time taken to run the iteration in milliseconds.
    evalLoss: Loss computed on the eval data at the end of iteration.
    index: Index of the iteration, 0 based.
    learnRate: Learn rate used for this iteration.
    principalComponentInfos: The information of the principal components.
    trainingLoss: Loss computed on the training data at the end of iteration.
  """

  arimaResult = _messages.MessageField('ArimaResult', 1)
  clusterInfos = _messages.MessageField('ClusterInfo', 2, repeated=True)
  durationMs = _messages.IntegerField(3)
  evalLoss = _messages.FloatField(4)
  index = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  learnRate = _messages.FloatField(6)
  principalComponentInfos = _messages.MessageField('PrincipalComponentInfo', 7, repeated=True)
  trainingLoss = _messages.FloatField(8)


class Job(_messages.Message):
  r"""A Job object.

  Fields:
    configuration: Required. Describes the job configuration.
    etag: Output only. A hash of this resource.
    id: Output only. Opaque ID field of the job.
    jobCreationReason: Output only. The reason why a Job was created.
      [Preview](https://cloud.google.com/products/#product-launch-stages)
    jobReference: Optional. Reference describing the unique-per-user name of
      the job.
    kind: Output only. The type of the resource.
    principal_subject: Output only. [Full-projection-only] String
      representation of identity of requesting party. Populated for both
      first- and third-party identities. Only present for APIs that support
      third-party identities.
    selfLink: Output only. A URL that can be used to access the resource
      again.
    statistics: Output only. Information about the job, including starting
      time and ending time of the job.
    status: Output only. The status of this job. Examine this value when
      polling an asynchronous job to see if the job is complete.
    user_email: Output only. Email address of the user who ran the job.
  """

  configuration = _messages.MessageField('JobConfiguration', 1)
  etag = _messages.StringField(2)
  id = _messages.StringField(3)
  jobCreationReason = _messages.MessageField('JobCreationReason', 4)
  jobReference = _messages.MessageField('JobReference', 5)
  kind = _messages.StringField(6, default='bigquery#job')
  principal_subject = _messages.StringField(7)
  selfLink = _messages.StringField(8)
  statistics = _messages.MessageField('JobStatistics', 9)
  status = _messages.MessageField('JobStatus', 10)
  user_email = _messages.StringField(11)


class JobCancelResponse(_messages.Message):
  r"""Describes format of a jobs cancellation response.

  Fields:
    job: The final state of the job.
    kind: The resource type of the response.
  """

  job = _messages.MessageField('Job', 1)
  kind = _messages.StringField(2, default='bigquery#jobCancelResponse')


class JobConfiguration(_messages.Message):
  r"""A JobConfiguration object.

  Messages:
    LabelsValue: The labels associated with this job. You can use these to
      organize and group your jobs. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    copy: [Pick one] Copies a table.
    dryRun: Optional. If set, don't actually run this job. A valid query will
      return a mostly empty response with some processing statistics, while an
      invalid query will return the same error it would if it wasn't a dry
      run. Behavior of non-query jobs is undefined.
    extract: [Pick one] Configures an extract job.
    jobTimeoutMs: Optional. Job timeout in milliseconds. If this time limit is
      exceeded, BigQuery will attempt to stop a longer job, but may not always
      succeed in canceling it before the job completes. For example, a job
      that takes more than 60 seconds to complete has a better chance of being
      stopped than a job that takes 10 seconds to complete.
    jobType: Output only. The type of the job. Can be QUERY, LOAD, EXTRACT,
      COPY or UNKNOWN.
    labels: The labels associated with this job. You can use these to organize
      and group your jobs. Label keys and values can be no longer than 63
      characters, can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter and each label
      in the list must have a different key.
    load: [Pick one] Configures a load job.
    query: [Pick one] Configures a query job.
    reservation: Optional. The reservation that job would use. User can
      specify a reservation to execute the job. If reservation is not set,
      reservation is determined based on the rules defined by the reservation
      assignments. The expected format is
      `projects/{project}/locations/{location}/reservations/{reservation}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this job. You can use these to organize and
    group your jobs. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  copy = _messages.MessageField('JobConfigurationTableCopy', 1)
  dryRun = _messages.BooleanField(2)
  extract = _messages.MessageField('JobConfigurationExtract', 3)
  jobTimeoutMs = _messages.IntegerField(4)
  jobType = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  load = _messages.MessageField('JobConfigurationLoad', 7)
  query = _messages.MessageField('JobConfigurationQuery', 8)
  reservation = _messages.StringField(9)


class JobConfigurationExtract(_messages.Message):
  r"""JobConfigurationExtract configures a job that exports data from a
  BigQuery table into Google Cloud Storage.

  Fields:
    compression: Optional. The compression type to use for exported files.
      Possible values include DEFLATE, GZIP, NONE, SNAPPY, and ZSTD. The
      default value is NONE. Not all compression formats are support for all
      file formats. DEFLATE is only supported for Avro. ZSTD is only supported
      for Parquet. Not applicable when extracting models.
    destinationFormat: Optional. The exported file format. Possible values
      include CSV, NEWLINE_DELIMITED_JSON, PARQUET, or AVRO for tables and
      ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value
      for tables is CSV. Tables with nested or repeated fields cannot be
      exported as CSV. The default value for models is ML_TF_SAVED_MODEL.
    destinationUri: [Pick one] DEPRECATED: Use destinationUris instead,
      passing only one URI as necessary. The fully-qualified Google Cloud
      Storage URI where the extracted table should be written.
    destinationUris: [Pick one] A list of fully-qualified Google Cloud Storage
      URIs where the extracted table should be written.
    fieldDelimiter: Optional. When extracting data in CSV format, this defines
      the delimiter to use between fields in the exported data. Default is
      ','. Not applicable when extracting models.
    modelExtractOptions: Optional. Model extract options only applicable when
      extracting models.
    printHeader: Optional. Whether to print out a header row in the results.
      Default is true. Not applicable when extracting models.
    sourceModel: A reference to the model being exported.
    sourceTable: A reference to the table being exported.
    useAvroLogicalTypes: Whether to use logical types when extracting to AVRO
      format. Not applicable when extracting models.
  """

  compression = _messages.StringField(1)
  destinationFormat = _messages.StringField(2)
  destinationUri = _messages.StringField(3)
  destinationUris = _messages.StringField(4, repeated=True)
  fieldDelimiter = _messages.StringField(5)
  modelExtractOptions = _messages.MessageField('ModelExtractOptions', 6)
  printHeader = _messages.BooleanField(7, default=True)
  sourceModel = _messages.MessageField('ModelReference', 8)
  sourceTable = _messages.MessageField('TableReference', 9)
  useAvroLogicalTypes = _messages.BooleanField(10)


class JobConfigurationLoad(_messages.Message):
  r"""JobConfigurationLoad contains the configuration properties for loading
  data into a destination table.

  Enums:
    ColumnNameCharacterMapValueValuesEnum: Optional. Character map supported
      for column names in CSV/Parquet loads. Defaults to STRICT and can be
      overridden by Project Config Service. Using this option with
      unsupporting load formats will result in an error.
    DecimalTargetTypesValueListEntryValuesEnum:
    FileSetSpecTypeValueValuesEnum: Optional. Specifies how source URIs are
      interpreted for constructing the file set to load. By default, source
      URIs are expanded against the underlying storage. You can also specify
      manifest files to control how the file set is constructed. This option
      is only applicable to object storage systems.
    JsonExtensionValueValuesEnum: Optional. Load option to be used together
      with source_format newline-delimited JSON to indicate that a variant of
      JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON
      (and source_format must be set to NEWLINE_DELIMITED_JSON).
    SourceColumnMatchValueValuesEnum: Optional. Controls the strategy used to
      match loaded columns to the schema. If not set, a sensible default is
      chosen based on how the schema is provided. If autodetect is used, then
      columns are matched by name. Otherwise, columns are matched by position.
      This is done to keep the behavior backward-compatible.

  Fields:
    allowJaggedRows: Optional. Accept rows that are missing trailing optional
      columns. The missing values are treated as nulls. If false, records with
      missing trailing columns are treated as bad records, and if there are
      too many bad records, an invalid error is returned in the job result.
      The default value is false. Only applicable to CSV, ignored for other
      formats.
    allowQuotedNewlines: Indicates if BigQuery should allow quoted data
      sections that contain newline characters in a CSV file. The default
      value is false.
    autodetect: Optional. Indicates if we should automatically infer the
      options and schema for CSV and JSON sources.
    clustering: Clustering specification for the destination table.
    columnNameCharacterMap: Optional. Character map supported for column names
      in CSV/Parquet loads. Defaults to STRICT and can be overridden by
      Project Config Service. Using this option with unsupporting load formats
      will result in an error.
    connectionProperties: Optional. Connection properties which can modify the
      load job behavior. Currently, only the 'session_id' connection property
      is supported, and is used to resolve _SESSION appearing as the dataset
      id.
    copyFilesOnly: Optional. [Experimental] Configures the load job to copy
      files directly to the destination BigLake managed table, bypassing file
      content reading and rewriting. Copying files only is supported when all
      the following are true: * `source_uris` are located in the same Cloud
      Storage location as the destination table's `storage_uri` location. *
      `source_format` is `PARQUET`. * `destination_table` is an existing
      BigLake managed table. The table's schema does not have flexible column
      names. The table's columns do not have type parameters other than
      precision and scale. * No options other than the above are specified.
    createDisposition: Optional. Specifies whether the job is allowed to
      create new tables. The following values are supported: *
      CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the
      table. * CREATE_NEVER: The table must already exist. If it does not, a
      'notFound' error is returned in the job result. The default value is
      CREATE_IF_NEEDED. Creation, truncation and append actions occur as one
      atomic update upon job completion.
    createSession: Optional. If this property is true, the job creates a new
      session using a randomly generated session_id. To continue using a
      created session with subsequent queries, pass the existing session
      identifier as a `ConnectionProperty` value. The session identifier is
      returned as part of the `SessionInfo` message within the query
      statistics. The new session's location will be set to
      `Job.JobReference.location` if it is present, otherwise it's set to the
      default location based on existing routing logic.
    dateFormat: Optional. Date format used for parsing DATE values.
    datetimeFormat: Optional. Date format used for parsing DATETIME values.
    decimalTargetTypes: Defines the list of possible SQL data types to which
      the source decimal values are converted. This list and the precision and
      the scale parameters of the decimal field determine the target type. In
      the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is
      in the specified list and if it supports the precision and the scale.
      STRING supports all precision and scale values. If none of the listed
      types supports the precision and the scale, the type supporting the
      widest range in the specified list is picked, and if a value exceeds the
      supported range when reading the data, an error will be thrown. Example:
      Suppose the value of this field is ["NUMERIC", "BIGNUMERIC"]. If
      (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC
      (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC
      (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; *
      (77,38) -> BIGNUMERIC (error if value exceeds supported range). This
      field cannot contain duplicate types. The order of the types in this
      field is ignored. For example, ["BIGNUMERIC", "NUMERIC"] is the same as
      ["NUMERIC", "BIGNUMERIC"] and NUMERIC always takes precedence over
      BIGNUMERIC. Defaults to ["NUMERIC", "STRING"] for ORC and ["NUMERIC"]
      for the other file formats.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys)
    destinationTable: [Required] The destination table to load the data into.
    destinationTableProperties: Optional. [Experimental] Properties with which
      to create the destination table if it is new.
    encoding: Optional. The character encoding of the data. The supported
      values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and
      UTF-32LE. The default value is UTF-8. BigQuery decodes the data after
      the raw, binary data has been split using the values of the `quote` and
      `fieldDelimiter` properties. If you don't specify an encoding, or if you
      specify a UTF-8 encoding when the CSV file is not UTF-8 encoded,
      BigQuery attempts to convert the data to UTF-8. Generally, your data
      loads successfully, but it may not match byte-for-byte what you expect.
      To avoid this, specify the correct encoding by using the `--encoding`
      flag. If BigQuery can't convert a character other than the ASCII `0`
      character, BigQuery converts the character to the standard Unicode
      replacement character: \ufffd.
    fieldDelimiter: Optional. The separator character for fields in a CSV
      file. The separator is interpreted as a single byte. For files encoded
      in ISO-8859-1, any single character can be used as a separator. For
      files encoded in UTF-8, characters represented in decimal range 1-127
      (U+0001-U+007F) can be used without any modification. UTF-8 characters
      encoded with multiple bytes (i.e. U+0080 and above) will have only the
      first byte used for separating fields. The remaining bytes will be
      treated as a part of the field. BigQuery also supports the escape
      sequence "\t" (U+0009) to specify a tab separator. The default value is
      comma (",", U+002C).
    fileSetSpecType: Optional. Specifies how source URIs are interpreted for
      constructing the file set to load. By default, source URIs are expanded
      against the underlying storage. You can also specify manifest files to
      control how the file set is constructed. This option is only applicable
      to object storage systems.
    hivePartitioningOptions: Optional. When set, configures hive partitioning
      support. Not all storage formats support hive partitioning -- requesting
      hive partitioning on an unsupported format will lead to an error, as
      will providing an invalid specification.
    ignoreUnknownValues: Optional. Indicates if BigQuery should allow extra
      values that are not represented in the table schema. If true, the extra
      values are ignored. If false, records with extra columns are treated as
      bad records, and if there are too many bad records, an invalid error is
      returned in the job result. The default value is false. The sourceFormat
      property determines what BigQuery treats as an extra value: CSV:
      Trailing columns JSON: Named values that don't match any column names in
      the table schema Avro, Parquet, ORC: Fields in the file schema that
      don't exist in the table schema.
    jsonExtension: Optional. Load option to be used together with
      source_format newline-delimited JSON to indicate that a variant of JSON
      is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and
      source_format must be set to NEWLINE_DELIMITED_JSON).
    maxBadRecords: Optional. The maximum number of bad records that BigQuery
      can ignore when running the job. If the number of bad records exceeds
      this value, an invalid error is returned in the job result. The default
      value is 0, which requires that all records are valid. This is only
      supported for CSV and NEWLINE_DELIMITED_JSON file formats.
    nullMarker: Optional. Specifies a string that represents a null value in a
      CSV file. For example, if you specify "\\N", BigQuery interprets "\\N"
      as a null value when loading a CSV file. The default value is the empty
      string. If you set this property to a custom value, BigQuery throws an
      error if an empty string is present for all data types except for STRING
      and BYTE. For STRING and BYTE columns, BigQuery interprets the empty
      string as an empty value.
    nullMarkers: Optional. A list of strings represented as SQL NULL value in
      a CSV file. null_marker and null_markers can't be set at the same time.
      If null_marker is set, null_markers has to be not set. If null_markers
      is set, null_marker has to be not set. If both null_marker and
      null_markers are set at the same time, a user error would be thrown. Any
      strings listed in null_markers, including empty string would be
      interpreted as SQL NULL. This applies to all column types.
    parquetOptions: Optional. Additional properties to set if sourceFormat is
      set to PARQUET.
    preserveAsciiControlCharacters: Optional. When sourceFormat is set to
      "CSV", this indicates whether the embedded ASCII control characters (the
      first 32 characters in the ASCII-table, from '\x00' to '\x1F') are
      preserved.
    projectionFields: If sourceFormat is set to "DATASTORE_BACKUP", indicates
      which entity properties to load into BigQuery from a Cloud Datastore
      backup. Property names are case sensitive and must be top-level
      properties. If no properties are specified, BigQuery loads all
      properties. If any named property isn't found in the Cloud Datastore
      backup, an invalid error is returned in the job result.
    quote: Optional. The value that is used to quote data sections in a CSV
      file. BigQuery converts the string to ISO-8859-1 encoding, and then uses
      the first byte of the encoded string to split the data in its raw,
      binary state. The default value is a double-quote ('"'). If your data
      does not contain quoted sections, set the property value to an empty
      string. If your data contains quoted newline characters, you must also
      set the allowQuotedNewlines property to true. To include the specific
      quote character within a quoted value, precede it with an additional
      matching quote character. For example, if you want to escape the default
      character ' " ', use ' "" '. @default "
    rangePartitioning: Range partitioning specification for the destination
      table. Only one of timePartitioning and rangePartitioning should be
      specified.
    referenceFileSchemaUri: Optional. The user can provide a reference file
      with the reader schema. This file is only loaded if it is part of source
      URIs, but is not loaded otherwise. It is enabled for the following
      formats: AVRO, PARQUET, ORC.
    schema: Optional. The schema for the destination table. The schema can be
      omitted if the destination table already exists, or if you're loading
      data from Google Cloud Datastore.
    schemaInline: [Deprecated] The inline schema. For CSV schemas, specify as
      "Field1:Type1[,Field2:Type2]*". For example, "foo:STRING, bar:INTEGER,
      baz:FLOAT".
    schemaInlineFormat: [Deprecated] The format of the schemaInline property.
    schemaUpdateOptions: Allows the schema of the destination table to be
      updated as a side effect of the load job if a schema is autodetected or
      supplied in the job configuration. Schema update options are supported
      in two cases: when writeDisposition is WRITE_APPEND; when
      writeDisposition is WRITE_TRUNCATE and the destination table is a
      partition of a table, specified by partition decorators. For normal
      tables, WRITE_TRUNCATE will always overwrite the schema. One or more of
      the following values are specified: * ALLOW_FIELD_ADDITION: allow adding
      a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing
      a required field in the original schema to nullable.
    skipLeadingRows: Optional. The number of rows at the top of a CSV file
      that BigQuery will skip when loading the data. The default value is 0.
      This property is useful if you have header rows in the file that should
      be skipped. When autodetect is on, the behavior is the following: *
      skipLeadingRows unspecified - Autodetect tries to detect headers in the
      first row. If they are not detected, the row is read as data. Otherwise
      data is read starting from the second row. * skipLeadingRows is 0 -
      Instructs autodetect that there are no headers and data should be read
      starting from the first row. * skipLeadingRows = N > 0 - Autodetect
      skips N-1 rows and tries to detect headers in row N. If headers are not
      detected, row N is just skipped. Otherwise row N is used to extract
      column names for the detected schema.
    sourceColumnMatch: Optional. Controls the strategy used to match loaded
      columns to the schema. If not set, a sensible default is chosen based on
      how the schema is provided. If autodetect is used, then columns are
      matched by name. Otherwise, columns are matched by position. This is
      done to keep the behavior backward-compatible.
    sourceFormat: Optional. The format of the data files. For CSV files,
      specify "CSV". For datastore backups, specify "DATASTORE_BACKUP". For
      newline-delimited JSON, specify "NEWLINE_DELIMITED_JSON". For Avro,
      specify "AVRO". For parquet, specify "PARQUET". For orc, specify "ORC".
      The default value is CSV.
    sourceUris: [Required] The fully-qualified URIs that point to your data in
      Google Cloud. For Google Cloud Storage URIs: Each URI can contain one
      '*' wildcard character and it must come after the 'bucket' name. Size
      limits related to load jobs apply to external data sources. For Google
      Cloud Bigtable URIs: Exactly one URI can be specified and it has be a
      fully specified and valid HTTPS URL for a Google Cloud Bigtable table.
      For Google Cloud Datastore backups: Exactly one URI can be specified.
      Also, the '*' wildcard character is not allowed.
    timeFormat: Optional. Date format used for parsing TIME values.
    timePartitioning: Time-based partitioning specification for the
      destination table. Only one of timePartitioning and rangePartitioning
      should be specified.
    timeZone: Optional. Default time zone that will apply when parsing
      timestamp values that have no specific time zone.
    timestampFormat: Optional. Date format used for parsing TIMESTAMP values.
    useAvroLogicalTypes: Optional. If sourceFormat is set to "AVRO", indicates
      whether to interpret logical types as the corresponding BigQuery data
      type (for example, TIMESTAMP), instead of using the raw type (for
      example, INTEGER).
    writeDisposition: Optional. Specifies the action that occurs if the
      destination table already exists. The following values are supported: *
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      data, removes the constraints and uses the schema from the load job. *
      WRITE_TRUNCATE_DATA: If the table already exists, BigQuery overwrites
      the data, but keeps the constraints and schema of the existing table. *
      WRITE_APPEND: If the table already exists, BigQuery appends the data to
      the table. * WRITE_EMPTY: If the table already exists and contains data,
      a 'duplicate' error is returned in the job result. The default value is
      WRITE_APPEND. Each action is atomic and only occurs if BigQuery is able
      to complete the job successfully. Creation, truncation and append
      actions occur as one atomic update upon job completion.
  """

  class ColumnNameCharacterMapValueValuesEnum(_messages.Enum):
    r"""Optional. Character map supported for column names in CSV/Parquet
    loads. Defaults to STRICT and can be overridden by Project Config Service.
    Using this option with unsupporting load formats will result in an error.

    Values:
      COLUMN_NAME_CHARACTER_MAP_UNSPECIFIED: Unspecified column name character
        map.
      STRICT: Support flexible column name and reject invalid column names.
      V1: Support alphanumeric + underscore characters and names must start
        with a letter or underscore. Invalid column names will be normalized.
      V2: Support flexible column name. Invalid column names will be
        normalized.
    """
    COLUMN_NAME_CHARACTER_MAP_UNSPECIFIED = 0
    STRICT = 1
    V1 = 2
    V2 = 3

  class DecimalTargetTypesValueListEntryValuesEnum(_messages.Enum):
    r"""DecimalTargetTypesValueListEntryValuesEnum enum type.

    Values:
      DECIMAL_TARGET_TYPE_UNSPECIFIED: Invalid type.
      NUMERIC: Decimal values could be converted to NUMERIC type.
      BIGNUMERIC: Decimal values could be converted to BIGNUMERIC type.
      STRING: Decimal values could be converted to STRING type.
    """
    DECIMAL_TARGET_TYPE_UNSPECIFIED = 0
    NUMERIC = 1
    BIGNUMERIC = 2
    STRING = 3

  class FileSetSpecTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how source URIs are interpreted for constructing
    the file set to load. By default, source URIs are expanded against the
    underlying storage. You can also specify manifest files to control how the
    file set is constructed. This option is only applicable to object storage
    systems.

    Values:
      FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH: This option expands source URIs by
        listing files from the object store. It is the default behavior if
        FileSetSpecType is not set.
      FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST: This option indicates
        that the provided URIs are newline-delimited manifest files, with one
        URI per line. Wildcard URIs are not supported.
    """
    FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH = 0
    FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST = 1

  class JsonExtensionValueValuesEnum(_messages.Enum):
    r"""Optional. Load option to be used together with source_format newline-
    delimited JSON to indicate that a variant of JSON is being loaded. To load
    newline-delimited GeoJSON, specify GEOJSON (and source_format must be set
    to NEWLINE_DELIMITED_JSON).

    Values:
      JSON_EXTENSION_UNSPECIFIED: The default if provided value is not one
        included in the enum, or the value is not specified. The source format
        is parsed without any modification.
      GEOJSON: Use GeoJSON variant of JSON. See
        https://tools.ietf.org/html/rfc7946.
    """
    JSON_EXTENSION_UNSPECIFIED = 0
    GEOJSON = 1

  class SourceColumnMatchValueValuesEnum(_messages.Enum):
    r"""Optional. Controls the strategy used to match loaded columns to the
    schema. If not set, a sensible default is chosen based on how the schema
    is provided. If autodetect is used, then columns are matched by name.
    Otherwise, columns are matched by position. This is done to keep the
    behavior backward-compatible.

    Values:
      SOURCE_COLUMN_MATCH_UNSPECIFIED: Uses sensible defaults based on how the
        schema is provided. If autodetect is used, then columns are matched by
        name. Otherwise, columns are matched by position. This is done to keep
        the behavior backward-compatible.
      POSITION: Matches by position. This assumes that the columns are ordered
        the same way as the schema.
      NAME: Matches by name. This reads the header row as column names and
        reorders columns to match the field names in the schema.
    """
    SOURCE_COLUMN_MATCH_UNSPECIFIED = 0
    POSITION = 1
    NAME = 2

  allowJaggedRows = _messages.BooleanField(1)
  allowQuotedNewlines = _messages.BooleanField(2)
  autodetect = _messages.BooleanField(3)
  clustering = _messages.MessageField('Clustering', 4)
  columnNameCharacterMap = _messages.EnumField('ColumnNameCharacterMapValueValuesEnum', 5)
  connectionProperties = _messages.MessageField('ConnectionProperty', 6, repeated=True)
  copyFilesOnly = _messages.BooleanField(7)
  createDisposition = _messages.StringField(8)
  createSession = _messages.BooleanField(9)
  dateFormat = _messages.StringField(10)
  datetimeFormat = _messages.StringField(11)
  decimalTargetTypes = _messages.EnumField('DecimalTargetTypesValueListEntryValuesEnum', 12, repeated=True)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 13)
  destinationTable = _messages.MessageField('TableReference', 14)
  destinationTableProperties = _messages.MessageField('DestinationTableProperties', 15)
  encoding = _messages.StringField(16)
  fieldDelimiter = _messages.StringField(17)
  fileSetSpecType = _messages.EnumField('FileSetSpecTypeValueValuesEnum', 18)
  hivePartitioningOptions = _messages.MessageField('HivePartitioningOptions', 19)
  ignoreUnknownValues = _messages.BooleanField(20)
  jsonExtension = _messages.EnumField('JsonExtensionValueValuesEnum', 21)
  maxBadRecords = _messages.IntegerField(22, variant=_messages.Variant.INT32)
  nullMarker = _messages.StringField(23)
  nullMarkers = _messages.StringField(24, repeated=True)
  parquetOptions = _messages.MessageField('ParquetOptions', 25)
  preserveAsciiControlCharacters = _messages.BooleanField(26)
  projectionFields = _messages.StringField(27, repeated=True)
  quote = _messages.StringField(28, default='"')
  rangePartitioning = _messages.MessageField('RangePartitioning', 29)
  referenceFileSchemaUri = _messages.StringField(30)
  schema = _messages.MessageField('TableSchema', 31)
  schemaInline = _messages.StringField(32)
  schemaInlineFormat = _messages.StringField(33)
  schemaUpdateOptions = _messages.StringField(34, repeated=True)
  skipLeadingRows = _messages.IntegerField(35, variant=_messages.Variant.INT32)
  sourceColumnMatch = _messages.EnumField('SourceColumnMatchValueValuesEnum', 36)
  sourceFormat = _messages.StringField(37)
  sourceUris = _messages.StringField(38, repeated=True)
  timeFormat = _messages.StringField(39)
  timePartitioning = _messages.MessageField('TimePartitioning', 40)
  timeZone = _messages.StringField(41)
  timestampFormat = _messages.StringField(42)
  useAvroLogicalTypes = _messages.BooleanField(43)
  writeDisposition = _messages.StringField(44)


class JobConfigurationQuery(_messages.Message):
  r"""JobConfigurationQuery configures a BigQuery query job.

  Messages:
    TableDefinitionsValue: Optional. You can specify external table
      definitions, which operate as ephemeral tables that can be queried.
      These definitions are configured using a JSON map, where the string key
      represents the table identifier, and the value is the corresponding
      external data configuration object.

  Fields:
    allowLargeResults: Optional. If true and query uses legacy SQL dialect,
      allows the query to produce arbitrarily large result tables at a slight
      cost in performance. Requires destinationTable to be set. For GoogleSQL
      queries, this flag is ignored and large results are always allowed.
      However, you must still set destinationTable when result size exceeds
      the allowed maximum response size.
    clustering: Clustering specification for the destination table.
    connectionProperties: Connection properties which can modify the query
      behavior.
    continuous: [Optional] Specifies whether the query should be executed as a
      continuous query. The default value is false.
    createDisposition: Optional. Specifies whether the job is allowed to
      create new tables. The following values are supported: *
      CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the
      table. * CREATE_NEVER: The table must already exist. If it does not, a
      'notFound' error is returned in the job result. The default value is
      CREATE_IF_NEEDED. Creation, truncation and append actions occur as one
      atomic update upon job completion.
    createSession: If this property is true, the job creates a new session
      using a randomly generated session_id. To continue using a created
      session with subsequent queries, pass the existing session identifier as
      a `ConnectionProperty` value. The session identifier is returned as part
      of the `SessionInfo` message within the query statistics. The new
      session's location will be set to `Job.JobReference.location` if it is
      present, otherwise it's set to the default location based on existing
      routing logic.
    defaultDataset: Optional. Specifies the default dataset to use for
      unqualified table names in the query. This setting does not alter
      behavior of unqualified dataset names. Setting the system variable
      `@@dataset_id` achieves the same behavior. See
      https://cloud.google.com/bigquery/docs/reference/system-variables for
      more information on system variables.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys)
    destinationTable: Optional. Describes the table where the query results
      should be stored. This property must be set for large results that
      exceed the maximum response size. For queries that produce anonymous
      (cached) results, this field will be populated by BigQuery.
    flattenResults: Optional. If true and query uses legacy SQL dialect,
      flattens all nested and repeated fields in the query results.
      allowLargeResults must be true if this is set to false. For GoogleSQL
      queries, this flag is ignored and results are never flattened.
    maximumBillingTier: Optional. [Deprecated] Maximum billing tier allowed
      for this query. The billing tier controls the amount of compute
      resources allotted to the query, and multiplies the on-demand cost of
      the query accordingly. A query that runs within its allotted resources
      will succeed and indicate its billing tier in
      statistics.query.billingTier, but if the query exceeds its allotted
      resources, it will fail with billingTierLimitExceeded. WARNING: The
      billed byte amount can be multiplied by an amount up to this number!
      Most users should not need to alter this setting, and we recommend that
      you avoid introducing new uses of it.
    maximumBytesBilled: Limits the bytes billed for this job. Queries that
      will have bytes billed beyond this limit will fail (without incurring a
      charge). If unspecified, this will be set to your project default.
    parameterMode: GoogleSQL only. Set to POSITIONAL to use positional (?)
      query parameters or to NAMED to use named (@myparam) query parameters in
      this query.
    preserveNulls: [Deprecated] This property is deprecated.
    priority: Optional. Specifies a priority for the query. Possible values
      include INTERACTIVE and BATCH. The default value is INTERACTIVE.
    query: [Required] SQL query text to execute. The useLegacySql field can be
      used to indicate whether the query uses legacy SQL or GoogleSQL.
    queryParameters: Query parameters for GoogleSQL queries.
    rangePartitioning: Range partitioning specification for the destination
      table. Only one of timePartitioning and rangePartitioning should be
      specified.
    schemaUpdateOptions: Allows the schema of the destination table to be
      updated as a side effect of the query job. Schema update options are
      supported in two cases: when writeDisposition is WRITE_APPEND; when
      writeDisposition is WRITE_TRUNCATE and the destination table is a
      partition of a table, specified by partition decorators. For normal
      tables, WRITE_TRUNCATE will always overwrite the schema. One or more of
      the following values are specified: * ALLOW_FIELD_ADDITION: allow adding
      a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing
      a required field in the original schema to nullable.
    scriptOptions: Options controlling the execution of scripts.
    systemVariables: Output only. System variables for GoogleSQL queries. A
      system variable is output if the variable is settable and its value
      differs from the system default. "@@" prefix is not included in the name
      of the System variables.
    tableDefinitions: Optional. You can specify external table definitions,
      which operate as ephemeral tables that can be queried. These definitions
      are configured using a JSON map, where the string key represents the
      table identifier, and the value is the corresponding external data
      configuration object.
    timePartitioning: Time-based partitioning specification for the
      destination table. Only one of timePartitioning and rangePartitioning
      should be specified.
    useLegacySql: Optional. Specifies whether to use BigQuery's legacy SQL
      dialect for this query. The default value is true. If set to false, the
      query will use BigQuery's GoogleSQL:
      https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is
      set to false, the value of flattenResults is ignored; query will be run
      as if flattenResults is false.
    useQueryCache: Optional. Whether to look for the result in the query
      cache. The query cache is a best-effort cache that will be flushed
      whenever tables in the query are modified. Moreover, the query cache is
      only available when a query does not have a destination table specified.
      The default value is true.
    userDefinedFunctionResources: Describes user-defined function resources
      used in the query.
    writeDisposition: Optional. Specifies the action that occurs if the
      destination table already exists. The following values are supported: *
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      data, removes the constraints, and uses the schema from the query
      result. * WRITE_TRUNCATE_DATA: If the table already exists, BigQuery
      overwrites the data, but keeps the constraints and schema of the
      existing table. * WRITE_APPEND: If the table already exists, BigQuery
      appends the data to the table. * WRITE_EMPTY: If the table already
      exists and contains data, a 'duplicate' error is returned in the job
      result. The default value is WRITE_EMPTY. Each action is atomic and only
      occurs if BigQuery is able to complete the job successfully. Creation,
      truncation and append actions occur as one atomic update upon job
      completion.
    writeIncrementalResults: Optional. This is only supported for a SELECT
      query using a temporary table. If set, the query is allowed to write
      results incrementally to the temporary result table. This may incur a
      performance penalty. This option cannot be used with Legacy SQL. This
      feature is not yet available.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TableDefinitionsValue(_messages.Message):
    r"""Optional. You can specify external table definitions, which operate as
    ephemeral tables that can be queried. These definitions are configured
    using a JSON map, where the string key represents the table identifier,
    and the value is the corresponding external data configuration object.

    Messages:
      AdditionalProperty: An additional property for a TableDefinitionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        TableDefinitionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TableDefinitionsValue object.

      Fields:
        key: Name of the additional property.
        value: A ExternalDataConfiguration attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ExternalDataConfiguration', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowLargeResults = _messages.BooleanField(1, default=False)
  clustering = _messages.MessageField('Clustering', 2)
  connectionProperties = _messages.MessageField('ConnectionProperty', 3, repeated=True)
  continuous = _messages.BooleanField(4)
  createDisposition = _messages.StringField(5)
  createSession = _messages.BooleanField(6)
  defaultDataset = _messages.MessageField('DatasetReference', 7)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 8)
  destinationTable = _messages.MessageField('TableReference', 9)
  flattenResults = _messages.BooleanField(10, default=True)
  maximumBillingTier = _messages.IntegerField(11, variant=_messages.Variant.INT32, default=1)
  maximumBytesBilled = _messages.IntegerField(12)
  parameterMode = _messages.StringField(13)
  preserveNulls = _messages.BooleanField(14)
  priority = _messages.StringField(15)
  query = _messages.StringField(16)
  queryParameters = _messages.MessageField('QueryParameter', 17, repeated=True)
  rangePartitioning = _messages.MessageField('RangePartitioning', 18)
  schemaUpdateOptions = _messages.StringField(19, repeated=True)
  scriptOptions = _messages.MessageField('ScriptOptions', 20)
  systemVariables = _messages.MessageField('SystemVariables', 21)
  tableDefinitions = _messages.MessageField('TableDefinitionsValue', 22)
  timePartitioning = _messages.MessageField('TimePartitioning', 23)
  useLegacySql = _messages.BooleanField(24, default=True)
  useQueryCache = _messages.BooleanField(25, default=True)
  userDefinedFunctionResources = _messages.MessageField('UserDefinedFunctionResource', 26, repeated=True)
  writeDisposition = _messages.StringField(27)
  writeIncrementalResults = _messages.BooleanField(28)


class JobConfigurationTableCopy(_messages.Message):
  r"""JobConfigurationTableCopy configures a job that copies data from one
  table to another. For more information on copying tables, see [Copy a
  table](https://cloud.google.com/bigquery/docs/managing-tables#copy-table).

  Enums:
    OperationTypeValueValuesEnum: Optional. Supported operation types in table
      copy job.

  Fields:
    createDisposition: Optional. Specifies whether the job is allowed to
      create new tables. The following values are supported: *
      CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the
      table. * CREATE_NEVER: The table must already exist. If it does not, a
      'notFound' error is returned in the job result. The default value is
      CREATE_IF_NEEDED. Creation, truncation and append actions occur as one
      atomic update upon job completion.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys).
    destinationExpirationTime: Optional. The time when the destination table
      expires. Expired tables will be deleted and their storage reclaimed.
    destinationTable: [Required] The destination table.
    operationType: Optional. Supported operation types in table copy job.
    sourceTable: [Pick one] Source table to copy.
    sourceTables: [Pick one] Source tables to copy.
    writeDisposition: Optional. Specifies the action that occurs if the
      destination table already exists. The following values are supported: *
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      table data and uses the schema and table constraints from the source
      table. * WRITE_APPEND: If the table already exists, BigQuery appends the
      data to the table. * WRITE_EMPTY: If the table already exists and
      contains data, a 'duplicate' error is returned in the job result. The
      default value is WRITE_EMPTY. Each action is atomic and only occurs if
      BigQuery is able to complete the job successfully. Creation, truncation
      and append actions occur as one atomic update upon job completion.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Supported operation types in table copy job.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Unspecified operation type.
      COPY: The source and destination table have the same table type.
      SNAPSHOT: The source table type is TABLE and the destination table type
        is SNAPSHOT.
      RESTORE: The source table type is SNAPSHOT and the destination table
        type is TABLE.
      CLONE: The source and destination table have the same table type, but
        only bill for unique data.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    COPY = 1
    SNAPSHOT = 2
    RESTORE = 3
    CLONE = 4

  createDisposition = _messages.StringField(1)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 2)
  destinationExpirationTime = _messages.StringField(3)
  destinationTable = _messages.MessageField('TableReference', 4)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 5)
  sourceTable = _messages.MessageField('TableReference', 6)
  sourceTables = _messages.MessageField('TableReference', 7, repeated=True)
  writeDisposition = _messages.StringField(8)


class JobCreationReason(_messages.Message):
  r"""Reason about why a Job was created from a [`jobs.query`](https://cloud.g
  oogle.com/bigquery/docs/reference/rest/v2/jobs/query) method when used with
  `JOB_CREATION_OPTIONAL` Job creation mode. For [`jobs.insert`](https://cloud
  .google.com/bigquery/docs/reference/rest/v2/jobs/insert) method calls it
  will always be `REQUESTED`.
  [Preview](https://cloud.google.com/products/#product-launch-stages)

  Enums:
    CodeValueValuesEnum: Output only. Specifies the high level reason why a
      Job was created.

  Fields:
    code: Output only. Specifies the high level reason why a Job was created.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies the high level reason why a Job was created.

    Values:
      CODE_UNSPECIFIED: Reason is not specified.
      REQUESTED: Job creation was requested.
      LONG_RUNNING: The query request ran beyond a system defined timeout
        specified by the [timeoutMs field in the QueryRequest](https://cloud.g
        oogle.com/bigquery/docs/reference/rest/v2/jobs/query#queryrequest). As
        a result it was considered a long running operation for which a job
        was created.
      LARGE_RESULTS: The results from the query cannot fit in the response.
      OTHER: BigQuery has determined that the query needs to be executed as a
        Job.
    """
    CODE_UNSPECIFIED = 0
    REQUESTED = 1
    LONG_RUNNING = 2
    LARGE_RESULTS = 3
    OTHER = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class JobList(_messages.Message):
  r"""JobList is the response format for a jobs.list call.

  Messages:
    JobsValueListEntry: ListFormatJob is a partial projection of job
      information returned as part of a jobs.list response.

  Fields:
    etag: A hash of this page of results.
    jobs: List of jobs that were requested.
    kind: The resource type of the response.
    nextPageToken: A token to request the next page of results.
    unreachable: A list of skipped locations that were unreachable. For more
      information about BigQuery locations, see:
      https://cloud.google.com/bigquery/docs/locations. Example: "europe-
      west5"
  """

  class JobsValueListEntry(_messages.Message):
    r"""ListFormatJob is a partial projection of job information returned as
    part of a jobs.list response.

    Fields:
      configuration: Required. Describes the job configuration.
      errorResult: A result object that will be present only if the job has
        failed.
      id: Unique opaque ID of the job.
      jobReference: Unique opaque ID of the job.
      kind: The resource type.
      principal_subject: [Full-projection-only] String representation of
        identity of requesting party. Populated for both first- and third-
        party identities. Only present for APIs that support third-party
        identities.
      state: Running state of the job. When the state is DONE, errorResult can
        be checked to determine whether the job succeeded or failed.
      statistics: Output only. Information about the job, including starting
        time and ending time of the job.
      status: [Full-projection-only] Describes the status of this job.
      user_email: [Full-projection-only] Email address of the user who ran the
        job.
    """

    configuration = _messages.MessageField('JobConfiguration', 1)
    errorResult = _messages.MessageField('ErrorProto', 2)
    id = _messages.StringField(3)
    jobReference = _messages.MessageField('JobReference', 4)
    kind = _messages.StringField(5)
    principal_subject = _messages.StringField(6)
    state = _messages.StringField(7)
    statistics = _messages.MessageField('JobStatistics', 8)
    status = _messages.MessageField('JobStatus', 9)
    user_email = _messages.StringField(10)

  etag = _messages.StringField(1)
  jobs = _messages.MessageField('JobsValueListEntry', 2, repeated=True)
  kind = _messages.StringField(3, default='bigquery#jobList')
  nextPageToken = _messages.StringField(4)
  unreachable = _messages.StringField(5, repeated=True)


class JobReference(_messages.Message):
  r"""A job reference is a fully qualified identifier for referring to a job.

  Fields:
    jobId: Required. The ID of the job. The ID must contain only letters (a-z,
      A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum length
      is 1,024 characters.
    location: Optional. The geographic location of the job. The default value
      is US. For more information about BigQuery locations, see:
      https://cloud.google.com/bigquery/docs/locations
    projectId: Required. The ID of the project containing this job.
  """

  jobId = _messages.StringField(1)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3)


class JobStatistics(_messages.Message):
  r"""Statistics for a single job execution.

  Enums:
    EditionValueValuesEnum: Output only. Name of edition corresponding to the
      reservation for this job at the time of this update.

  Messages:
    ReservationUsageValueListEntry: Job resource usage breakdown by
      reservation.

  Fields:
    completionRatio: Output only. [TrustedTester] Job progress (0.0 -> 1.0)
      for LOAD and EXTRACT jobs.
    copy: Output only. Statistics for a copy job.
    creationTime: Output only. Creation time of this job, in milliseconds
      since the epoch. This field will be present on all jobs.
    dataMaskingStatistics: Output only. Statistics for data-masking. Present
      only for query and extract jobs.
    edition: Output only. Name of edition corresponding to the reservation for
      this job at the time of this update.
    endTime: Output only. End time of this job, in milliseconds since the
      epoch. This field will be present whenever a job is in the DONE state.
    extract: Output only. Statistics for an extract job.
    finalExecutionDurationMs: Output only. The duration in milliseconds of the
      execution of the final attempt of this job, as BigQuery may internally
      re-attempt to execute the job.
    load: Output only. Statistics for a load job.
    numChildJobs: Output only. Number of child jobs executed.
    parentJobId: Output only. If this is a child job, specifies the job ID of
      the parent.
    query: Output only. Statistics for a query job.
    quotaDeferments: Output only. Quotas which delayed this job's start time.
    reservationUsage: Output only. Job resource usage breakdown by
      reservation. This field reported misleading information and will no
      longer be populated.
    reservation_id: Output only. Name of the primary reservation assigned to
      this job. Note that this could be different than reservations reported
      in the reservation usage field if parent reservations were used to
      execute this job.
    rowLevelSecurityStatistics: Output only. Statistics for row-level
      security. Present only for query and extract jobs.
    scriptStatistics: Output only. If this a child job of a script, specifies
      information about the context of this job within the script.
    sessionInfo: Output only. Information of the session if this job is part
      of one.
    startTime: Output only. Start time of this job, in milliseconds since the
      epoch. This field will be present when the job transitions from the
      PENDING state to either RUNNING or DONE.
    totalBytesProcessed: Output only. Total bytes processed for the job.
    totalSlotMs: Output only. Slot-milliseconds for the job.
    transactionInfo: Output only. [Alpha] Information of the multi-statement
      transaction if this job is part of one. This property is only expected
      on a child job or a job that is in a session. A script parent job is not
      part of the transaction started in the script.
  """

  class EditionValueValuesEnum(_messages.Enum):
    r"""Output only. Name of edition corresponding to the reservation for this
    job at the time of this update.

    Values:
      RESERVATION_EDITION_UNSPECIFIED: Default value, which will be treated as
        ENTERPRISE.
      STANDARD: Standard edition.
      ENTERPRISE: Enterprise edition.
      ENTERPRISE_PLUS: Enterprise Plus edition.
    """
    RESERVATION_EDITION_UNSPECIFIED = 0
    STANDARD = 1
    ENTERPRISE = 2
    ENTERPRISE_PLUS = 3

  class ReservationUsageValueListEntry(_messages.Message):
    r"""Job resource usage breakdown by reservation.

    Fields:
      name: Reservation name or "unreserved" for on-demand resource usage and
        multi-statement queries.
      slotMs: Total slot milliseconds used by the reservation for a particular
        job.
    """

    name = _messages.StringField(1)
    slotMs = _messages.IntegerField(2)

  completionRatio = _messages.FloatField(1)
  copy = _messages.MessageField('JobStatistics5', 2)
  creationTime = _messages.IntegerField(3)
  dataMaskingStatistics = _messages.MessageField('DataMaskingStatistics', 4)
  edition = _messages.EnumField('EditionValueValuesEnum', 5)
  endTime = _messages.IntegerField(6)
  extract = _messages.MessageField('JobStatistics4', 7)
  finalExecutionDurationMs = _messages.IntegerField(8)
  load = _messages.MessageField('JobStatistics3', 9)
  numChildJobs = _messages.IntegerField(10)
  parentJobId = _messages.StringField(11)
  query = _messages.MessageField('JobStatistics2', 12)
  quotaDeferments = _messages.StringField(13, repeated=True)
  reservationUsage = _messages.MessageField('ReservationUsageValueListEntry', 14, repeated=True)
  reservation_id = _messages.StringField(15)
  rowLevelSecurityStatistics = _messages.MessageField('RowLevelSecurityStatistics', 16)
  scriptStatistics = _messages.MessageField('ScriptStatistics', 17)
  sessionInfo = _messages.MessageField('SessionInfo', 18)
  startTime = _messages.IntegerField(19)
  totalBytesProcessed = _messages.IntegerField(20)
  totalSlotMs = _messages.IntegerField(21)
  transactionInfo = _messages.MessageField('TransactionInfo', 22)


class JobStatistics2(_messages.Message):
  r"""Statistics for a query job.

  Messages:
    ReservationUsageValueListEntry: Job resource usage breakdown by
      reservation.

  Fields:
    biEngineStatistics: Output only. BI Engine specific Statistics.
    billingTier: Output only. Billing tier for the job. This is a BigQuery-
      specific concept which is not related to the Google Cloud notion of
      "free tier". The value here is a measure of the query's resource
      consumption relative to the amount of data scanned. For on-demand
      queries, the limit is 100, and all queries within this limit are billed
      at the standard on-demand rates. On-demand queries that exceed this
      limit will fail with a billingTierLimitExceeded error.
    cacheHit: Output only. Whether the query result was fetched from the query
      cache.
    dclTargetDataset: Output only. Referenced dataset for DCL statement.
    dclTargetTable: Output only. Referenced table for DCL statement.
    dclTargetView: Output only. Referenced view for DCL statement.
    ddlAffectedRowAccessPolicyCount: Output only. The number of row access
      policies affected by a DDL statement. Present only for DROP ALL ROW
      ACCESS POLICIES queries.
    ddlDestinationTable: Output only. The table after rename. Present only for
      ALTER TABLE RENAME TO query.
    ddlOperationPerformed: Output only. The DDL operation performed, possibly
      dependent on the pre-existence of the DDL target.
    ddlTargetDataset: Output only. The DDL target dataset. Present only for
      CREATE/ALTER/DROP SCHEMA(dataset) queries.
    ddlTargetRoutine: Output only. [Beta] The DDL target routine. Present only
      for CREATE/DROP FUNCTION/PROCEDURE queries.
    ddlTargetRowAccessPolicy: Output only. The DDL target row access policy.
      Present only for CREATE/DROP ROW ACCESS POLICY queries.
    ddlTargetTable: Output only. The DDL target table. Present only for
      CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries.
    dmlStats: Output only. Detailed statistics for DML statements INSERT,
      UPDATE, DELETE, MERGE or TRUNCATE.
    estimatedBytesProcessed: Output only. The original estimate of bytes
      processed for the job.
    exportDataStatistics: Output only. Stats for EXPORT DATA statement.
    externalServiceCosts: Output only. Job cost breakdown as bigquery internal
      cost and external service costs.
    loadQueryStatistics: Output only. Statistics for a LOAD query.
    materializedViewStatistics: Output only. Statistics of materialized views
      of a query job.
    metadataCacheStatistics: Output only. Statistics of metadata cache usage
      in a query for BigLake tables.
    mlStatistics: Output only. Statistics of a BigQuery ML training job.
    modelTraining: Deprecated.
    modelTrainingCurrentIteration: Deprecated.
    modelTrainingExpectedTotalIteration: Deprecated.
    numDmlAffectedRows: Output only. The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    performanceInsights: Output only. Performance insights.
    queryInfo: Output only. Query optimization information for a QUERY job.
    queryPlan: Output only. Describes execution plan for the query.
    referencedRoutines: Output only. Referenced routines for the job.
    referencedTables: Output only. Referenced tables for the job.
    reservationUsage: Output only. Job resource usage breakdown by
      reservation. This field reported misleading information and will no
      longer be populated.
    schema: Output only. The schema of the results. Present only for
      successful dry run of non-legacy SQL queries.
    searchStatistics: Output only. Search query specific statistics.
    sparkStatistics: Output only. Statistics of a Spark procedure job.
    statementType: Output only. The type of query statement, if valid.
      Possible values: * `SELECT`:
      [`SELECT`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/query-syntax#select_list) statement. * `ASSERT`:
      [`ASSERT`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/debugging-statements#assert) statement. * `INSERT`:
      [`INSERT`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/dml-syntax#insert_statement) statement. * `UPDATE`:
      [`UPDATE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/dml-syntax#update_statement) statement. * `DELETE`:
      [`DELETE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-manipulation-language) statement. * `MERGE`:
      [`MERGE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-manipulation-language) statement. * `CREATE_TABLE`: [`CREATE
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_table_statement) statement, without
      `AS SELECT`. * `CREATE_TABLE_AS_SELECT`: [`CREATE TABLE AS
      SELECT`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_table_statement) statement. *
      `CREATE_VIEW`: [`CREATE
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_view_statement) statement. *
      `CREATE_MODEL`: [`CREATE MODEL`](https://cloud.google.com/bigquery-
      ml/docs/reference/standard-sql/bigqueryml-syntax-
      create#create_model_statement) statement. * `CREATE_MATERIALIZED_VIEW`:
      [`CREATE MATERIALIZED
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_materialized_view_statement)
      statement. * `CREATE_FUNCTION`: [`CREATE
      FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_function_statement) statement. *
      `CREATE_TABLE_FUNCTION`: [`CREATE TABLE
      FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_table_function_statement) statement.
      * `CREATE_PROCEDURE`: [`CREATE
      PROCEDURE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_procedure) statement. *
      `CREATE_ROW_ACCESS_POLICY`: [`CREATE ROW ACCESS
      POLICY`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_row_access_policy_statement)
      statement. * `CREATE_SCHEMA`: [`CREATE
      SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_schema_statement) statement. *
      `CREATE_SNAPSHOT_TABLE`: [`CREATE SNAPSHOT
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_snapshot_table_statement) statement.
      * `CREATE_SEARCH_INDEX`: [`CREATE SEARCH
      INDEX`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_search_index_statement) statement. *
      `DROP_TABLE`: [`DROP
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_table_statement) statement. *
      `DROP_EXTERNAL_TABLE`: [`DROP EXTERNAL
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_external_table_statement) statement. *
      `DROP_VIEW`: [`DROP
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_view_statement) statement. *
      `DROP_MODEL`: [`DROP MODEL`](https://cloud.google.com/bigquery-
      ml/docs/reference/standard-sql/bigqueryml-syntax-drop-model) statement.
      * `DROP_MATERIALIZED_VIEW`: [`DROP MATERIALIZED
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_materialized_view_statement)
      statement. * `DROP_FUNCTION` : [`DROP
      FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_function_statement) statement. *
      `DROP_TABLE_FUNCTION` : [`DROP TABLE
      FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_table_function) statement. *
      `DROP_PROCEDURE`: [`DROP
      PROCEDURE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_procedure_statement) statement. *
      `DROP_SEARCH_INDEX`: [`DROP SEARCH
      INDEX`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_search_index) statement. *
      `DROP_SCHEMA`: [`DROP
      SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_schema_statement) statement. *
      `DROP_SNAPSHOT_TABLE`: [`DROP SNAPSHOT
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#drop_snapshot_table_statement) statement. *
      `DROP_ROW_ACCESS_POLICY`: [`DROP [ALL] ROW ACCESS POLICY|POLICIES`](http
      s://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      definition-language#drop_row_access_policy_statement) statement. *
      `ALTER_TABLE`: [`ALTER
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#alter_table_set_options_statement)
      statement. * `ALTER_VIEW`: [`ALTER
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#alter_view_set_options_statement)
      statement. * `ALTER_MATERIALIZED_VIEW`: [`ALTER MATERIALIZED
      VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-
      language#alter_materialized_view_set_options_statement) statement. *
      `ALTER_SCHEMA`: [`ALTER
      SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#alter_schema_set_options_statement)
      statement. * `SCRIPT`:
      [`SCRIPT`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/procedural-language). * `TRUNCATE_TABLE`: [`TRUNCATE
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/dml-syntax#truncate_table_statement) statement. *
      `CREATE_EXTERNAL_TABLE`: [`CREATE EXTERNAL
      TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#create_external_table_statement) statement.
      * `EXPORT_DATA`: [`EXPORT
      DATA`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/other-statements#export_data_statement) statement. * `EXPORT_MODEL`:
      [`EXPORT MODEL`](https://cloud.google.com/bigquery-
      ml/docs/reference/standard-sql/bigqueryml-syntax-export-model)
      statement. * `LOAD_DATA`: [`LOAD
      DATA`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/other-statements#load_data_statement) statement. * `CALL`:
      [`CALL`](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/procedural-language#call) statement.
    timeline: Output only. Describes a timeline of job execution.
    totalBytesBilled: Output only. If the project is configured to use on-
      demand pricing, then this field contains the total bytes billed for the
      job. If the project is configured to use flat-rate pricing, then you are
      not billed for bytes and this field is informational only.
    totalBytesProcessed: Output only. Total bytes processed for the job.
    totalBytesProcessedAccuracy: Output only. For dry-run jobs,
      totalBytesProcessed is an estimate and this field specifies the accuracy
      of the estimate. Possible values can be: UNKNOWN: accuracy of the
      estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate
      is lower bound of what the query would cost. UPPER_BOUND: estimate is
      upper bound of what the query would cost.
    totalPartitionsProcessed: Output only. Total number of partitions
      processed from all partitioned tables referenced in the job.
    totalSlotMs: Output only. Slot-milliseconds for the job.
    transferredBytes: Output only. Total bytes transferred for cross-cloud
      queries such as Cross Cloud Transfer and CREATE TABLE AS SELECT (CTAS).
    undeclaredQueryParameters: Output only. GoogleSQL only: list of undeclared
      query parameters detected during a dry run validation.
    vectorSearchStatistics: Output only. Vector Search query specific
      statistics.
  """

  class ReservationUsageValueListEntry(_messages.Message):
    r"""Job resource usage breakdown by reservation.

    Fields:
      name: Reservation name or "unreserved" for on-demand resource usage and
        multi-statement queries.
      slotMs: Total slot milliseconds used by the reservation for a particular
        job.
    """

    name = _messages.StringField(1)
    slotMs = _messages.IntegerField(2)

  biEngineStatistics = _messages.MessageField('BiEngineStatistics', 1)
  billingTier = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  cacheHit = _messages.BooleanField(3)
  dclTargetDataset = _messages.MessageField('DatasetReference', 4)
  dclTargetTable = _messages.MessageField('TableReference', 5)
  dclTargetView = _messages.MessageField('TableReference', 6)
  ddlAffectedRowAccessPolicyCount = _messages.IntegerField(7)
  ddlDestinationTable = _messages.MessageField('TableReference', 8)
  ddlOperationPerformed = _messages.StringField(9)
  ddlTargetDataset = _messages.MessageField('DatasetReference', 10)
  ddlTargetRoutine = _messages.MessageField('RoutineReference', 11)
  ddlTargetRowAccessPolicy = _messages.MessageField('RowAccessPolicyReference', 12)
  ddlTargetTable = _messages.MessageField('TableReference', 13)
  dmlStats = _messages.MessageField('DmlStatistics', 14)
  estimatedBytesProcessed = _messages.IntegerField(15)
  exportDataStatistics = _messages.MessageField('ExportDataStatistics', 16)
  externalServiceCosts = _messages.MessageField('ExternalServiceCost', 17, repeated=True)
  loadQueryStatistics = _messages.MessageField('LoadQueryStatistics', 18)
  materializedViewStatistics = _messages.MessageField('MaterializedViewStatistics', 19)
  metadataCacheStatistics = _messages.MessageField('MetadataCacheStatistics', 20)
  mlStatistics = _messages.MessageField('MlStatistics', 21)
  modelTraining = _messages.MessageField('BigQueryModelTraining', 22)
  modelTrainingCurrentIteration = _messages.IntegerField(23, variant=_messages.Variant.INT32)
  modelTrainingExpectedTotalIteration = _messages.IntegerField(24)
  numDmlAffectedRows = _messages.IntegerField(25)
  performanceInsights = _messages.MessageField('PerformanceInsights', 26)
  queryInfo = _messages.MessageField('QueryInfo', 27)
  queryPlan = _messages.MessageField('ExplainQueryStage', 28, repeated=True)
  referencedRoutines = _messages.MessageField('RoutineReference', 29, repeated=True)
  referencedTables = _messages.MessageField('TableReference', 30, repeated=True)
  reservationUsage = _messages.MessageField('ReservationUsageValueListEntry', 31, repeated=True)
  schema = _messages.MessageField('TableSchema', 32)
  searchStatistics = _messages.MessageField('SearchStatistics', 33)
  sparkStatistics = _messages.MessageField('SparkStatistics', 34)
  statementType = _messages.StringField(35)
  timeline = _messages.MessageField('QueryTimelineSample', 36, repeated=True)
  totalBytesBilled = _messages.IntegerField(37)
  totalBytesProcessed = _messages.IntegerField(38)
  totalBytesProcessedAccuracy = _messages.StringField(39)
  totalPartitionsProcessed = _messages.IntegerField(40)
  totalSlotMs = _messages.IntegerField(41)
  transferredBytes = _messages.IntegerField(42)
  undeclaredQueryParameters = _messages.MessageField('QueryParameter', 43, repeated=True)
  vectorSearchStatistics = _messages.MessageField('VectorSearchStatistics', 44)


class JobStatistics3(_messages.Message):
  r"""Statistics for a load job.

  Fields:
    badRecords: Output only. The number of bad records encountered. Note that
      if the job has failed because of more bad records encountered than the
      maximum allowed in the load job configuration, then this number can be
      less than the total number of bad records present in the input data.
    inputFileBytes: Output only. Number of bytes of source data in a load job.
    inputFiles: Output only. Number of source files in a load job.
    outputBytes: Output only. Size of the loaded data in bytes. Note that
      while a load job is in the running state, this value may change.
    outputRows: Output only. Number of rows imported in a load job. Note that
      while an import job is in the running state, this value may change.
    timeline: Output only. Describes a timeline of job execution.
  """

  badRecords = _messages.IntegerField(1)
  inputFileBytes = _messages.IntegerField(2)
  inputFiles = _messages.IntegerField(3)
  outputBytes = _messages.IntegerField(4)
  outputRows = _messages.IntegerField(5)
  timeline = _messages.MessageField('QueryTimelineSample', 6, repeated=True)


class JobStatistics4(_messages.Message):
  r"""Statistics for an extract job.

  Fields:
    destinationUriFileCounts: Output only. Number of files per destination URI
      or URI pattern specified in the extract configuration. These values will
      be in the same order as the URIs specified in the 'destinationUris'
      field.
    inputBytes: Output only. Number of user bytes extracted into the result.
      This is the byte count as computed by BigQuery for billing purposes and
      doesn't have any relationship with the number of actual result bytes
      extracted in the desired format.
    timeline: Output only. Describes a timeline of job execution.
  """

  destinationUriFileCounts = _messages.IntegerField(1, repeated=True)
  inputBytes = _messages.IntegerField(2)
  timeline = _messages.MessageField('QueryTimelineSample', 3, repeated=True)


class JobStatistics5(_messages.Message):
  r"""Statistics for a copy job.

  Fields:
    copiedLogicalBytes: Output only. Number of logical bytes copied to the
      destination table.
    copiedRows: Output only. Number of rows copied to the destination table.
  """

  copiedLogicalBytes = _messages.IntegerField(1)
  copiedRows = _messages.IntegerField(2)


class JobStatus(_messages.Message):
  r"""A JobStatus object.

  Fields:
    errorResult: Output only. Final error result of the job. If present,
      indicates that the job has completed and was unsuccessful.
    errors: Output only. The first errors encountered during the running of
      the job. The final message includes the number of errors that caused the
      process to stop. Errors here do not necessarily mean that the job has
      not completed or was unsuccessful.
    state: Output only. Running state of the job. Valid states include
      'PENDING', 'RUNNING', and 'DONE'.
  """

  errorResult = _messages.MessageField('ErrorProto', 1)
  errors = _messages.MessageField('ErrorProto', 2, repeated=True)
  state = _messages.StringField(3)


class JoinRestrictionPolicy(_messages.Message):
  r"""Represents privacy policy associated with "join restrictions". Join
  restriction gives data providers the ability to enforce joins on the
  'join_allowed_columns' when data is queried from a privacy protected view.

  Enums:
    JoinConditionValueValuesEnum: Optional. Specifies if a join is required or
      not on queries for the view. Default is JOIN_CONDITION_UNSPECIFIED.

  Fields:
    joinAllowedColumns: Optional. The only columns that joins are allowed on.
      This field is must be specified for join_conditions JOIN_ANY and
      JOIN_ALL and it cannot be set for JOIN_BLOCKED.
    joinCondition: Optional. Specifies if a join is required or not on queries
      for the view. Default is JOIN_CONDITION_UNSPECIFIED.
  """

  class JoinConditionValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies if a join is required or not on queries for the
    view. Default is JOIN_CONDITION_UNSPECIFIED.

    Values:
      JOIN_CONDITION_UNSPECIFIED: A join is neither required nor restricted on
        any column. Default value.
      JOIN_ANY: A join is required on at least one of the specified columns.
      JOIN_ALL: A join is required on all specified columns.
      JOIN_NOT_REQUIRED: A join is not required, but if present it is only
        permitted on 'join_allowed_columns'
      JOIN_BLOCKED: Joins are blocked for all queries.
    """
    JOIN_CONDITION_UNSPECIFIED = 0
    JOIN_ANY = 1
    JOIN_ALL = 2
    JOIN_NOT_REQUIRED = 3
    JOIN_BLOCKED = 4

  joinAllowedColumns = _messages.StringField(1, repeated=True)
  joinCondition = _messages.EnumField('JoinConditionValueValuesEnum', 2)


@encoding.MapUnrecognizedFields('additionalProperties')
class JsonObject(_messages.Message):
  r"""Represents a single JSON object.

  Messages:
    AdditionalProperty: An additional property for a JsonObject object.

  Fields:
    additionalProperties: Additional properties of type JsonObject
  """

  class AdditionalProperty(_messages.Message):
    r"""An additional property for a JsonObject object.

    Fields:
      key: Name of the additional property.
      value: A JsonValue attribute.
    """

    key = _messages.StringField(1)
    value = _messages.MessageField('JsonValue', 2)

  additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)


class JsonOptions(_messages.Message):
  r"""Json Options for load and make external tables.

  Fields:
    encoding: Optional. The character encoding of the data. The supported
      values are UTF-8, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The
      default value is UTF-8.
  """

  encoding = _messages.StringField(1)


JsonValue = extra_types.JsonValue


class LinkedDatasetMetadata(_messages.Message):
  r"""Metadata about the Linked Dataset.

  Enums:
    LinkStateValueValuesEnum: Output only. Specifies whether Linked Dataset is
      currently in a linked state or not.

  Fields:
    linkState: Output only. Specifies whether Linked Dataset is currently in a
      linked state or not.
  """

  class LinkStateValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies whether Linked Dataset is currently in a linked
    state or not.

    Values:
      LINK_STATE_UNSPECIFIED: The default value. Default to the LINKED state.
      LINKED: Normal Linked Dataset state. Data is queryable via the Linked
        Dataset.
      UNLINKED: Data publisher or owner has unlinked this Linked Dataset. It
        means you can no longer query or see the data in the Linked Dataset.
    """
    LINK_STATE_UNSPECIFIED = 0
    LINKED = 1
    UNLINKED = 2

  linkState = _messages.EnumField('LinkStateValueValuesEnum', 1)


class LinkedDatasetSource(_messages.Message):
  r"""A dataset source type which refers to another BigQuery dataset.

  Fields:
    sourceDataset: The source dataset reference contains project numbers and
      not project ids.
  """

  sourceDataset = _messages.MessageField('DatasetReference', 1)


class ListModelsResponse(_messages.Message):
  r"""Response format for a single page when listing BigQuery ML models.

  Fields:
    models: Models in the requested dataset. Only the following fields are
      populated: model_reference, model_type, creation_time,
      last_modified_time and labels.
    nextPageToken: A token to request the next page of results.
  """

  models = _messages.MessageField('Model', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListRoutinesResponse(_messages.Message):
  r"""Describes the format of a single result page when listing routines.

  Fields:
    nextPageToken: A token to request the next page of results.
    routines: Routines in the requested dataset. Unless read_mask is set in
      the request, only the following fields are populated: etag, project_id,
      dataset_id, routine_id, routine_type, creation_time, last_modified_time,
      language, and remote_function_options.
  """

  nextPageToken = _messages.StringField(1)
  routines = _messages.MessageField('Routine', 2, repeated=True)


class ListRowAccessPoliciesResponse(_messages.Message):
  r"""Response message for the ListRowAccessPolicies method.

  Fields:
    nextPageToken: A token to request the next page of results.
    rowAccessPolicies: Row access policies on the requested table.
  """

  nextPageToken = _messages.StringField(1)
  rowAccessPolicies = _messages.MessageField('RowAccessPolicy', 2, repeated=True)


class LoadQueryStatistics(_messages.Message):
  r"""Statistics for a LOAD query.

  Fields:
    badRecords: Output only. The number of bad records encountered while
      processing a LOAD query. Note that if the job has failed because of more
      bad records encountered than the maximum allowed in the load job
      configuration, then this number can be less than the total number of bad
      records present in the input data.
    bytesTransferred: Output only. This field is deprecated. The number of
      bytes of source data copied over the network for a `LOAD` query.
      `transferred_bytes` has the canonical value for physical transferred
      bytes, which is used for BigQuery Omni billing.
    inputFileBytes: Output only. Number of bytes of source data in a LOAD
      query.
    inputFiles: Output only. Number of source files in a LOAD query.
    outputBytes: Output only. Size of the loaded data in bytes. Note that
      while a LOAD query is in the running state, this value may change.
    outputRows: Output only. Number of rows imported in a LOAD query. Note
      that while a LOAD query is in the running state, this value may change.
  """

  badRecords = _messages.IntegerField(1)
  bytesTransferred = _messages.IntegerField(2)
  inputFileBytes = _messages.IntegerField(3)
  inputFiles = _messages.IntegerField(4)
  outputBytes = _messages.IntegerField(5)
  outputRows = _messages.IntegerField(6)


class LocationMetadata(_messages.Message):
  r"""BigQuery-specific metadata about a location. This will be set on
  google.cloud.location.Location.metadata in Cloud Location API responses.

  Fields:
    legacyLocationId: The legacy BigQuery location ID, e.g. "EU" for the
      "europe" location. This is for any API consumers that need the legacy
      "US" and "EU" locations.
  """

  legacyLocationId = _messages.StringField(1)


class MaterializedView(_messages.Message):
  r"""A materialized view considered for a query job.

  Enums:
    RejectedReasonValueValuesEnum: If present, specifies the reason why the
      materialized view was not chosen for the query.

  Fields:
    chosen: Whether the materialized view is chosen for the query. A
      materialized view can be chosen to rewrite multiple parts of the same
      query. If a materialized view is chosen to rewrite any part of the
      query, then this field is true, even if the materialized view was not
      chosen to rewrite others parts.
    estimatedBytesSaved: If present, specifies a best-effort estimation of the
      bytes saved by using the materialized view rather than its base tables.
    rejectedReason: If present, specifies the reason why the materialized view
      was not chosen for the query.
    tableReference: The candidate materialized view.
  """

  class RejectedReasonValueValuesEnum(_messages.Enum):
    r"""If present, specifies the reason why the materialized view was not
    chosen for the query.

    Values:
      REJECTED_REASON_UNSPECIFIED: Default unspecified value.
      NO_DATA: View has no cached data because it has not refreshed yet.
      COST: The estimated cost of the view is more expensive than another view
        or the base table. Note: The estimate cost might not match the billed
        cost.
      BASE_TABLE_TRUNCATED: View has no cached data because a base table is
        truncated.
      BASE_TABLE_DATA_CHANGE: View is invalidated because of a data change in
        one or more base tables. It could be any recent change if the [`maxSta
        leness`](https://cloud.google.com/bigquery/docs/reference/rest/v2/tabl
        es#Table.FIELDS.max_staleness) option is not set for the view, or
        otherwise any change outside of the staleness window.
      BASE_TABLE_PARTITION_EXPIRATION_CHANGE: View is invalidated because a
        base table's partition expiration has changed.
      BASE_TABLE_EXPIRED_PARTITION: View is invalidated because a base table's
        partition has expired.
      BASE_TABLE_INCOMPATIBLE_METADATA_CHANGE: View is invalidated because a
        base table has an incompatible metadata change.
      TIME_ZONE: View is invalidated because it was refreshed with a time zone
        other than that of the current job.
      OUT_OF_TIME_TRAVEL_WINDOW: View is outside the time travel window.
      BASE_TABLE_FINE_GRAINED_SECURITY_POLICY: View is inaccessible to the
        user because of a fine-grained security policy on one of its base
        tables.
      BASE_TABLE_TOO_STALE: One of the view's base tables is too stale. For
        example, the cached metadata of a BigLake external table needs to be
        updated.
    """
    REJECTED_REASON_UNSPECIFIED = 0
    NO_DATA = 1
    COST = 2
    BASE_TABLE_TRUNCATED = 3
    BASE_TABLE_DATA_CHANGE = 4
    BASE_TABLE_PARTITION_EXPIRATION_CHANGE = 5
    BASE_TABLE_EXPIRED_PARTITION = 6
    BASE_TABLE_INCOMPATIBLE_METADATA_CHANGE = 7
    TIME_ZONE = 8
    OUT_OF_TIME_TRAVEL_WINDOW = 9
    BASE_TABLE_FINE_GRAINED_SECURITY_POLICY = 10
    BASE_TABLE_TOO_STALE = 11

  chosen = _messages.BooleanField(1)
  estimatedBytesSaved = _messages.IntegerField(2)
  rejectedReason = _messages.EnumField('RejectedReasonValueValuesEnum', 3)
  tableReference = _messages.MessageField('TableReference', 4)


class MaterializedViewDefinition(_messages.Message):
  r"""Definition and configuration of a materialized view.

  Fields:
    allowNonIncrementalDefinition: Optional. This option declares the
      intention to construct a materialized view that isn't refreshed
      incrementally. Non-incremental materialized views support an expanded
      range of SQL queries. The `allow_non_incremental_definition` option
      can't be changed after the materialized view is created.
    enableRefresh: Optional. Enable automatic refresh of the materialized view
      when the base table is updated. The default value is "true".
    lastRefreshTime: Output only. The time when this materialized view was
      last refreshed, in milliseconds since the epoch.
    maxStaleness: [Optional] Max staleness of data that could be returned when
      materizlized view is queried (formatted as Google SQL Interval type).
    query: Required. A query whose results are persisted.
    refreshIntervalMs: Optional. The maximum frequency at which this
      materialized view will be refreshed. The default value is "1800000" (30
      minutes).
  """

  allowNonIncrementalDefinition = _messages.BooleanField(1)
  enableRefresh = _messages.BooleanField(2)
  lastRefreshTime = _messages.IntegerField(3)
  maxStaleness = _messages.BytesField(4)
  query = _messages.StringField(5)
  refreshIntervalMs = _messages.IntegerField(6)


class MaterializedViewStatistics(_messages.Message):
  r"""Statistics of materialized views considered in a query job.

  Fields:
    materializedView: Materialized views considered for the query job. Only
      certain materialized views are used. For a detailed list, see the child
      message. If many materialized views are considered, then the list might
      be incomplete.
  """

  materializedView = _messages.MessageField('MaterializedView', 1, repeated=True)


class MaterializedViewStatus(_messages.Message):
  r"""Status of a materialized view. The last refresh timestamp status is
  omitted here, but is present in the MaterializedViewDefinition message.

  Fields:
    lastRefreshStatus: Output only. Error result of the last automatic
      refresh. If present, indicates that the last automatic refresh was
      unsuccessful.
    refreshWatermark: Output only. Refresh watermark of materialized view. The
      base tables' data were collected into the materialized view cache until
      this time.
  """

  lastRefreshStatus = _messages.MessageField('ErrorProto', 1)
  refreshWatermark = _messages.StringField(2)


class MetadataCacheStatistics(_messages.Message):
  r"""Statistics for metadata caching in queried tables.

  Fields:
    tableMetadataCacheUsage: Set for the Metadata caching eligible tables
      referenced in the query.
  """

  tableMetadataCacheUsage = _messages.MessageField('TableMetadataCacheUsage', 1, repeated=True)


class MlStatistics(_messages.Message):
  r"""Job statistics specific to a BigQuery ML training job.

  Enums:
    ModelTypeValueValuesEnum: Output only. The type of the model that is being
      trained.
    TrainingTypeValueValuesEnum: Output only. Training type of the job.

  Fields:
    hparamTrials: Output only. Trials of a [hyperparameter tuning
      job](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) sorted by trial_id.
    iterationResults: Results for all completed iterations. Empty for
      [hyperparameter tuning jobs](https://cloud.google.com/bigquery-
      ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview).
    maxIterations: Output only. Maximum number of iterations specified as
      max_iterations in the 'CREATE MODEL' query. The actual number of
      iterations may be less than this number due to early stop.
    modelType: Output only. The type of the model that is being trained.
    trainingType: Output only. Training type of the job.
  """

  class ModelTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the model that is being trained.

    Values:
      MODEL_TYPE_UNSPECIFIED: Default value.
      LINEAR_REGRESSION: Linear regression model.
      LOGISTIC_REGRESSION: Logistic regression based classification model.
      KMEANS: K-means clustering model.
      MATRIX_FACTORIZATION: Matrix factorization model.
      DNN_CLASSIFIER: DNN classifier model.
      TENSORFLOW: An imported TensorFlow model.
      DNN_REGRESSOR: DNN regressor model.
      XGBOOST: An imported XGBoost model.
      BOOSTED_TREE_REGRESSOR: Boosted tree regressor model.
      BOOSTED_TREE_CLASSIFIER: Boosted tree classifier model.
      ARIMA: ARIMA model.
      AUTOML_REGRESSOR: AutoML Tables regression model.
      AUTOML_CLASSIFIER: AutoML Tables classification model.
      PCA: Prinpical Component Analysis model.
      DNN_LINEAR_COMBINED_CLASSIFIER: Wide-and-deep classifier model.
      DNN_LINEAR_COMBINED_REGRESSOR: Wide-and-deep regressor model.
      AUTOENCODER: Autoencoder model.
      ARIMA_PLUS: New name for the ARIMA model.
      ARIMA_PLUS_XREG: ARIMA with external regressors.
      RANDOM_FOREST_REGRESSOR: Random forest regressor model.
      RANDOM_FOREST_CLASSIFIER: Random forest classifier model.
      TENSORFLOW_LITE: An imported TensorFlow Lite model.
      ONNX: An imported ONNX model.
      TRANSFORM_ONLY: Model to capture the columns and logic in the TRANSFORM
        clause along with statistics useful for ML analytic functions.
      CONTRIBUTION_ANALYSIS: The contribution analysis model.
    """
    MODEL_TYPE_UNSPECIFIED = 0
    LINEAR_REGRESSION = 1
    LOGISTIC_REGRESSION = 2
    KMEANS = 3
    MATRIX_FACTORIZATION = 4
    DNN_CLASSIFIER = 5
    TENSORFLOW = 6
    DNN_REGRESSOR = 7
    XGBOOST = 8
    BOOSTED_TREE_REGRESSOR = 9
    BOOSTED_TREE_CLASSIFIER = 10
    ARIMA = 11
    AUTOML_REGRESSOR = 12
    AUTOML_CLASSIFIER = 13
    PCA = 14
    DNN_LINEAR_COMBINED_CLASSIFIER = 15
    DNN_LINEAR_COMBINED_REGRESSOR = 16
    AUTOENCODER = 17
    ARIMA_PLUS = 18
    ARIMA_PLUS_XREG = 19
    RANDOM_FOREST_REGRESSOR = 20
    RANDOM_FOREST_CLASSIFIER = 21
    TENSORFLOW_LITE = 22
    ONNX = 23
    TRANSFORM_ONLY = 24
    CONTRIBUTION_ANALYSIS = 25

  class TrainingTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Training type of the job.

    Values:
      TRAINING_TYPE_UNSPECIFIED: Unspecified training type.
      SINGLE_TRAINING: Single training with fixed parameter space.
      HPARAM_TUNING: [Hyperparameter tuning
        training](https://cloud.google.com/bigquery-
        ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview).
    """
    TRAINING_TYPE_UNSPECIFIED = 0
    SINGLE_TRAINING = 1
    HPARAM_TUNING = 2

  hparamTrials = _messages.MessageField('HparamTuningTrial', 1, repeated=True)
  iterationResults = _messages.MessageField('IterationResult', 2, repeated=True)
  maxIterations = _messages.IntegerField(3)
  modelType = _messages.EnumField('ModelTypeValueValuesEnum', 4)
  trainingType = _messages.EnumField('TrainingTypeValueValuesEnum', 5)


class Model(_messages.Message):
  r"""A Model object.

  Enums:
    ModelTypeValueValuesEnum: Output only. Type of the model resource.

  Messages:
    LabelsValue: The labels associated with this model. You can use these to
      organize and group your models. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    bestTrialId: The best trial_id across all training runs.
    creationTime: Output only. The time when this model was created, in
      millisecs since the epoch.
    defaultTrialId: Output only. The default trial_id to use in TVFs when the
      trial_id is not passed in. For single-objective [hyperparameter
      tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, this is the best trial
      ID. For multi-objective [hyperparameter
      tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, this is the smallest
      trial ID among all Pareto optimal trials.
    description: Optional. A user-friendly description of this model.
    encryptionConfiguration: Custom encryption configuration (e.g., Cloud KMS
      keys). This shows the encryption configuration of the model data while
      stored in BigQuery storage. This field can be used with PatchModel to
      update encryption key for an already encrypted model.
    etag: Output only. A hash of this resource.
    expirationTime: Optional. The time when this model expires, in
      milliseconds since the epoch. If not present, the model will persist
      indefinitely. Expired models will be deleted and their storage
      reclaimed. The defaultTableExpirationMs property of the encapsulating
      dataset can be used to set a default expirationTime on newly created
      models.
    featureColumns: Output only. Input feature columns for the model
      inference. If the model is trained with TRANSFORM clause, these are the
      input of the TRANSFORM clause.
    friendlyName: Optional. A descriptive name for this model.
    hparamSearchSpaces: Output only. All hyperparameter search spaces in this
      model.
    hparamTrials: Output only. Trials of a [hyperparameter
      tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) model sorted by trial_id.
    labelColumns: Output only. Label columns that were used to train this
      model. The output of the model will have a "predicted_" prefix to these
      columns.
    labels: The labels associated with this model. You can use these to
      organize and group your models. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.
    lastModifiedTime: Output only. The time when this model was last modified,
      in millisecs since the epoch.
    location: Output only. The geographic location where the model resides.
      This value is inherited from the dataset.
    modelReference: Required. Unique identifier for this model.
    modelType: Output only. Type of the model resource.
    optimalTrialIds: Output only. For single-objective [hyperparameter
      tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, it only contains the
      best trial. For multi-objective [hyperparameter
      tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, it contains all Pareto
      optimal trials sorted by trial_id.
    remoteModelInfo: Output only. Remote model info
    trainingRuns: Information for all training runs in increasing order of
      start_time.
    transformColumns: Output only. This field will be populated if a TRANSFORM
      clause was used to train a model. TRANSFORM clause (if used) takes
      feature_columns as input and outputs transform_columns.
      transform_columns then are used to train the model.
  """

  class ModelTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the model resource.

    Values:
      MODEL_TYPE_UNSPECIFIED: Default value.
      LINEAR_REGRESSION: Linear regression model.
      LOGISTIC_REGRESSION: Logistic regression based classification model.
      KMEANS: K-means clustering model.
      MATRIX_FACTORIZATION: Matrix factorization model.
      DNN_CLASSIFIER: DNN classifier model.
      TENSORFLOW: An imported TensorFlow model.
      DNN_REGRESSOR: DNN regressor model.
      XGBOOST: An imported XGBoost model.
      BOOSTED_TREE_REGRESSOR: Boosted tree regressor model.
      BOOSTED_TREE_CLASSIFIER: Boosted tree classifier model.
      ARIMA: ARIMA model.
      AUTOML_REGRESSOR: AutoML Tables regression model.
      AUTOML_CLASSIFIER: AutoML Tables classification model.
      PCA: Prinpical Component Analysis model.
      DNN_LINEAR_COMBINED_CLASSIFIER: Wide-and-deep classifier model.
      DNN_LINEAR_COMBINED_REGRESSOR: Wide-and-deep regressor model.
      AUTOENCODER: Autoencoder model.
      ARIMA_PLUS: New name for the ARIMA model.
      ARIMA_PLUS_XREG: ARIMA with external regressors.
      RANDOM_FOREST_REGRESSOR: Random forest regressor model.
      RANDOM_FOREST_CLASSIFIER: Random forest classifier model.
      TENSORFLOW_LITE: An imported TensorFlow Lite model.
      ONNX: An imported ONNX model.
      TRANSFORM_ONLY: Model to capture the columns and logic in the TRANSFORM
        clause along with statistics useful for ML analytic functions.
      CONTRIBUTION_ANALYSIS: The contribution analysis model.
    """
    MODEL_TYPE_UNSPECIFIED = 0
    LINEAR_REGRESSION = 1
    LOGISTIC_REGRESSION = 2
    KMEANS = 3
    MATRIX_FACTORIZATION = 4
    DNN_CLASSIFIER = 5
    TENSORFLOW = 6
    DNN_REGRESSOR = 7
    XGBOOST = 8
    BOOSTED_TREE_REGRESSOR = 9
    BOOSTED_TREE_CLASSIFIER = 10
    ARIMA = 11
    AUTOML_REGRESSOR = 12
    AUTOML_CLASSIFIER = 13
    PCA = 14
    DNN_LINEAR_COMBINED_CLASSIFIER = 15
    DNN_LINEAR_COMBINED_REGRESSOR = 16
    AUTOENCODER = 17
    ARIMA_PLUS = 18
    ARIMA_PLUS_XREG = 19
    RANDOM_FOREST_REGRESSOR = 20
    RANDOM_FOREST_CLASSIFIER = 21
    TENSORFLOW_LITE = 22
    ONNX = 23
    TRANSFORM_ONLY = 24
    CONTRIBUTION_ANALYSIS = 25

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this model. You can use these to organize
    and group your models. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bestTrialId = _messages.IntegerField(1)
  creationTime = _messages.IntegerField(2)
  defaultTrialId = _messages.IntegerField(3)
  description = _messages.StringField(4)
  encryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 5)
  etag = _messages.StringField(6)
  expirationTime = _messages.IntegerField(7)
  featureColumns = _messages.MessageField('StandardSqlField', 8, repeated=True)
  friendlyName = _messages.StringField(9)
  hparamSearchSpaces = _messages.MessageField('HparamSearchSpaces', 10)
  hparamTrials = _messages.MessageField('HparamTuningTrial', 11, repeated=True)
  labelColumns = _messages.MessageField('StandardSqlField', 12, repeated=True)
  labels = _messages.MessageField('LabelsValue', 13)
  lastModifiedTime = _messages.IntegerField(14)
  location = _messages.StringField(15)
  modelReference = _messages.MessageField('ModelReference', 16)
  modelType = _messages.EnumField('ModelTypeValueValuesEnum', 17)
  optimalTrialIds = _messages.IntegerField(18, repeated=True)
  remoteModelInfo = _messages.MessageField('RemoteModelInfo', 19)
  trainingRuns = _messages.MessageField('TrainingRun', 20, repeated=True)
  transformColumns = _messages.MessageField('TransformColumn', 21, repeated=True)


class ModelDefinition(_messages.Message):
  r"""A ModelDefinition object.

  Messages:
    ModelOptionsValue: Deprecated.

  Fields:
    modelOptions: Deprecated.
    trainingRuns: Deprecated.
  """

  class ModelOptionsValue(_messages.Message):
    r"""Deprecated.

    Fields:
      labels: A string attribute.
      lossType: A string attribute.
      modelType: A string attribute.
    """

    labels = _messages.StringField(1, repeated=True)
    lossType = _messages.StringField(2)
    modelType = _messages.StringField(3)

  modelOptions = _messages.MessageField('ModelOptionsValue', 1)
  trainingRuns = _messages.MessageField('BqmlTrainingRun', 2, repeated=True)


class ModelExtractOptions(_messages.Message):
  r"""Options related to model extraction.

  Fields:
    trialId: The 1-based ID of the trial to be exported from a hyperparameter
      tuning model. If not specified, the trial with id = [Model](https://clou
      d.google.com/bigquery/docs/reference/rest/v2/models#resource:-
      model).defaultTrialId is exported. This field is ignored for models not
      trained with hyperparameter tuning.
  """

  trialId = _messages.IntegerField(1)


class ModelReference(_messages.Message):
  r"""Id path of a model.

  Fields:
    datasetId: Required. The ID of the dataset containing this model.
    modelId: Required. The ID of the model. The ID must contain only letters
      (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is
      1,024 characters.
    projectId: Required. The ID of the project containing this model.
  """

  datasetId = _messages.StringField(1)
  modelId = _messages.StringField(2)
  projectId = _messages.StringField(3)


class MultiClassClassificationMetrics(_messages.Message):
  r"""Evaluation metrics for multi-class classification/classifier models.

  Fields:
    aggregateClassificationMetrics: Aggregate classification metrics.
    confusionMatrixList: Confusion matrix at different thresholds.
  """

  aggregateClassificationMetrics = _messages.MessageField('AggregateClassificationMetrics', 1)
  confusionMatrixList = _messages.MessageField('ConfusionMatrix', 2, repeated=True)


class ParquetOptions(_messages.Message):
  r"""Parquet Options for load and make external tables.

  Enums:
    MapTargetTypeValueValuesEnum: Optional. Indicates how to represent a
      Parquet map if present.

  Fields:
    enableListInference: Optional. Indicates whether to use schema inference
      specifically for Parquet LIST logical type.
    enumAsString: Optional. Indicates whether to infer Parquet ENUM logical
      type as STRING instead of BYTES by default.
    mapTargetType: Optional. Indicates how to represent a Parquet map if
      present.
  """

  class MapTargetTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Indicates how to represent a Parquet map if present.

    Values:
      MAP_TARGET_TYPE_UNSPECIFIED: In this mode, the map will have the
        following schema: struct map_field_name { repeated struct key_value {
        key value } }.
      ARRAY_OF_STRUCT: In this mode, the map will have the following schema:
        repeated struct map_field_name { key value }.
    """
    MAP_TARGET_TYPE_UNSPECIFIED = 0
    ARRAY_OF_STRUCT = 1

  enableListInference = _messages.BooleanField(1)
  enumAsString = _messages.BooleanField(2)
  mapTargetType = _messages.EnumField('MapTargetTypeValueValuesEnum', 3)


class PartitionSkew(_messages.Message):
  r"""Partition skew detailed information.

  Fields:
    skewSources: Output only. Source stages which produce skewed data.
  """

  skewSources = _messages.MessageField('SkewSource', 1, repeated=True)


class PartitionedColumn(_messages.Message):
  r"""The partitioning column information.

  Fields:
    field: Required. The name of the partition column.
  """

  field = _messages.StringField(1)


class PartitioningDefinition(_messages.Message):
  r"""The partitioning information, which includes managed table, external
  table and metastore partitioned table partition information.

  Fields:
    partitionedColumn: Optional. Details about each partitioning column. This
      field is output only for all partitioning types other than metastore
      partitioned tables. BigQuery native tables only support 1 partitioning
      column. Other table types may support 0, 1 or more partitioning columns.
      For metastore partitioned tables, the order must match the definition
      order in the Hive Metastore, where it must match the physical layout of
      the table. For example, CREATE TABLE a_table(id BIGINT, name STRING)
      PARTITIONED BY (city STRING, state STRING). In this case the values must
      be ['city', 'state'] in that order.
  """

  partitionedColumn = _messages.MessageField('PartitionedColumn', 1, repeated=True)


class PerformanceInsights(_messages.Message):
  r"""Performance insights for the job.

  Fields:
    avgPreviousExecutionMs: Output only. Average execution ms of previous
      runs. Indicates the job ran slow compared to previous executions. To
      find previous executions, use INFORMATION_SCHEMA tables and filter jobs
      with same query hash.
    stagePerformanceChangeInsights: Output only. Query stage performance
      insights compared to previous runs, for diagnosing performance
      regression.
    stagePerformanceStandaloneInsights: Output only. Standalone query stage
      performance insights, for exploring potential improvements.
  """

  avgPreviousExecutionMs = _messages.IntegerField(1)
  stagePerformanceChangeInsights = _messages.MessageField('StagePerformanceChangeInsight', 2, repeated=True)
  stagePerformanceStandaloneInsights = _messages.MessageField('StagePerformanceStandaloneInsight', 3, repeated=True)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PrincipalComponentInfo(_messages.Message):
  r"""Principal component infos, used only for eigen decomposition based
  models, e.g., PCA. Ordered by explained_variance in the descending order.

  Fields:
    cumulativeExplainedVarianceRatio: The explained_variance is pre-ordered in
      the descending order to compute the cumulative explained variance ratio.
    explainedVariance: Explained variance by this principal component, which
      is simply the eigenvalue.
    explainedVarianceRatio: Explained_variance over the total explained
      variance.
    principalComponentId: Id of the principal component.
  """

  cumulativeExplainedVarianceRatio = _messages.FloatField(1)
  explainedVariance = _messages.FloatField(2)
  explainedVarianceRatio = _messages.FloatField(3)
  principalComponentId = _messages.IntegerField(4)


class PrivacyPolicy(_messages.Message):
  r"""Represents privacy policy that contains the privacy requirements
  specified by the data owner. Currently, this is only supported on views.

  Fields:
    aggregationThresholdPolicy: Optional. Policy used for aggregation
      thresholds.
    differentialPrivacyPolicy: Optional. Policy used for differential privacy.
    joinRestrictionPolicy: Optional. Join restriction policy is outside of the
      one of policies, since this policy can be set along with other policies.
      This policy gives data providers the ability to enforce joins on the
      'join_allowed_columns' when data is queried from a privacy protected
      view.
  """

  aggregationThresholdPolicy = _messages.MessageField('AggregationThresholdPolicy', 1)
  differentialPrivacyPolicy = _messages.MessageField('DifferentialPrivacyPolicy', 2)
  joinRestrictionPolicy = _messages.MessageField('JoinRestrictionPolicy', 3)


class ProjectList(_messages.Message):
  r"""Response object of ListProjects

  Messages:
    ProjectsValueListEntry: Information about a single project.

  Fields:
    etag: A hash of the page of results.
    kind: The resource type of the response.
    nextPageToken: Use this token to request the next page of results.
    projects: Projects to which the user has at least READ access.
    totalItems: The total number of projects in the page. A wrapper is used
      here because the field should still be in the response when the value is
      0.
  """

  class ProjectsValueListEntry(_messages.Message):
    r"""Information about a single project.

    Fields:
      friendlyName: A descriptive name for this project. A wrapper is used
        here because friendlyName can be set to the empty string.
      id: An opaque ID of this project.
      kind: The resource type.
      numericId: The numeric ID of this project.
      projectReference: A unique reference to this project.
    """

    friendlyName = _messages.StringField(1)
    id = _messages.StringField(2)
    kind = _messages.StringField(3)
    numericId = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
    projectReference = _messages.MessageField('ProjectReference', 5)

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#projectList')
  nextPageToken = _messages.StringField(3)
  projects = _messages.MessageField('ProjectsValueListEntry', 4, repeated=True)
  totalItems = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class ProjectReference(_messages.Message):
  r"""A unique reference to a project.

  Fields:
    projectId: Required. ID of the project. Can be either the numeric ID or
      the assigned ID of the project.
  """

  projectId = _messages.StringField(1)


class QueryInfo(_messages.Message):
  r"""Query optimization information for a QUERY job.

  Messages:
    OptimizationDetailsValue: Output only. Information about query
      optimizations.

  Fields:
    optimizationDetails: Output only. Information about query optimizations.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OptimizationDetailsValue(_messages.Message):
    r"""Output only. Information about query optimizations.

    Messages:
      AdditionalProperty: An additional property for a
        OptimizationDetailsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OptimizationDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  optimizationDetails = _messages.MessageField('OptimizationDetailsValue', 1)


class QueryParameter(_messages.Message):
  r"""A parameter given to a query.

  Fields:
    name: Optional. If unset, this is a positional parameter. Otherwise,
      should be unique within a query.
    parameterType: Required. The type of this parameter.
    parameterValue: Required. The value of this parameter.
  """

  name = _messages.StringField(1)
  parameterType = _messages.MessageField('QueryParameterType', 2)
  parameterValue = _messages.MessageField('QueryParameterValue', 3)


class QueryParameterType(_messages.Message):
  r"""The type of a query parameter.

  Messages:
    StructTypesValueListEntry: The type of a struct parameter.

  Fields:
    arrayType: Optional. The type of the array's elements, if this is an
      array.
    rangeElementType: Optional. The element type of the range, if this is a
      range.
    structTypes: Optional. The types of the fields of this struct, in order,
      if this is a struct.
    type: Required. The top level type of this field.
  """

  class StructTypesValueListEntry(_messages.Message):
    r"""The type of a struct parameter.

    Fields:
      description: Optional. Human-oriented description of the field.
      name: Optional. The name of this field.
      type: Required. The type of this field.
    """

    description = _messages.StringField(1)
    name = _messages.StringField(2)
    type = _messages.MessageField('QueryParameterType', 3)

  arrayType = _messages.MessageField('QueryParameterType', 1)
  rangeElementType = _messages.MessageField('QueryParameterType', 2)
  structTypes = _messages.MessageField('StructTypesValueListEntry', 3, repeated=True)
  type = _messages.StringField(4)


class QueryParameterValue(_messages.Message):
  r"""The value of a query parameter.

  Messages:
    StructValuesValue: The struct field values.

  Fields:
    arrayValues: Optional. The array values, if this is an array type.
    rangeValue: Optional. The range value, if this is a range type.
    structValues: The struct field values.
    value: Optional. The value of this value, if a simple scalar type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class StructValuesValue(_messages.Message):
    r"""The struct field values.

    Messages:
      AdditionalProperty: An additional property for a StructValuesValue
        object.

    Fields:
      additionalProperties: Additional properties of type StructValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a StructValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A QueryParameterValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QueryParameterValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arrayValues = _messages.MessageField('QueryParameterValue', 1, repeated=True)
  rangeValue = _messages.MessageField('RangeValue', 2)
  structValues = _messages.MessageField('StructValuesValue', 3)
  value = _messages.StringField(4)


class QueryRequest(_messages.Message):
  r"""Describes the format of the jobs.query request.

  Enums:
    JobCreationModeValueValuesEnum: Optional. If not set, jobs are always
      required. If set, the query request will follow the behavior described
      JobCreationMode. [Preview](https://cloud.google.com/products/#product-
      launch-stages)

  Messages:
    LabelsValue: Optional. The labels associated with this query. Labels can
      be used to organize and group query jobs. Label keys and values can be
      no longer than 63 characters, can only contain lowercase letters,
      numeric characters, underscores and dashes. International characters are
      allowed. Label keys must start with a letter and each label in the list
      must have a different key.

  Fields:
    connectionProperties: Optional. Connection properties which can modify the
      query behavior.
    continuous: [Optional] Specifies whether the query should be executed as a
      continuous query. The default value is false.
    createSession: Optional. If true, creates a new session using a randomly
      generated session_id. If false, runs query with an existing session_id
      passed in ConnectionProperty, otherwise runs query in non-session mode.
      The session location will be set to QueryRequest.location if it is
      present, otherwise it's set to the default location based on existing
      routing logic.
    defaultDataset: Optional. Specifies the default datasetId and projectId to
      assume for any unqualified table names in the query. If not set, all
      table names in the query string must be qualified in the format
      'datasetId.tableId'.
    destinationEncryptionConfiguration: Optional. Custom encryption
      configuration (e.g., Cloud KMS keys)
    dryRun: Optional. If set to true, BigQuery doesn't run the job. Instead,
      if the query is valid, BigQuery returns statistics about the job such as
      how many bytes would be processed. If the query is invalid, an error
      returns. The default value is false.
    formatOptions: Optional. Output format adjustments.
    jobCreationMode: Optional. If not set, jobs are always required. If set,
      the query request will follow the behavior described JobCreationMode.
      [Preview](https://cloud.google.com/products/#product-launch-stages)
    jobTimeoutMs: Optional. Job timeout in milliseconds. If this time limit is
      exceeded, BigQuery will attempt to stop a longer job, but may not always
      succeed in canceling it before the job completes. For example, a job
      that takes more than 60 seconds to complete has a better chance of being
      stopped than a job that takes 10 seconds to complete. This timeout
      applies to the query even if a job does not need to be created.
    kind: The resource type of the request.
    labels: Optional. The labels associated with this query. Labels can be
      used to organize and group query jobs. Label keys and values can be no
      longer than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label keys must start with a letter and each label in the list
      must have a different key.
    location: The geographic location where the job should run. For more
      information, see how to [specify locations](https://cloud.google.com/big
      query/docs/locations#specify_locations).
    maxResults: Optional. The maximum number of rows of data to return per
      page of results. Setting this flag to a small value such as 1000 and
      then paging through results might improve reliability when the query
      result set is large. In addition to this limit, responses are also
      limited to 10 MB. By default, there is no maximum row count, and only
      the byte limit applies.
    maximumBytesBilled: Optional. Limits the bytes billed for this query.
      Queries with bytes billed above this limit will fail (without incurring
      a charge). If unspecified, the project default is used.
    parameterMode: GoogleSQL only. Set to POSITIONAL to use positional (?)
      query parameters or to NAMED to use named (@myparam) query parameters in
      this query.
    preserveNulls: This property is deprecated.
    query: Required. A query string to execute, using Google Standard SQL or
      legacy SQL syntax. Example: "SELECT COUNT(f1) FROM
      myProjectId.myDatasetId.myTableId".
    queryParameters: Query parameters for GoogleSQL queries.
    requestId: Optional. A unique user provided identifier to ensure
      idempotent behavior for queries. Note that this is different from the
      job_id. It has the following properties: 1. It is case-sensitive,
      limited to up to 36 ASCII characters. A UUID is recommended. 2. Read
      only queries can ignore this token since they are nullipotent by
      definition. 3. For the purposes of idempotency ensured by the
      request_id, a request is considered duplicate of another only if they
      have the same request_id and are actually duplicates. When determining
      whether a request is a duplicate of another request, all parameters in
      the request that may affect the result are considered. For example,
      query, connection_properties, query_parameters, use_legacy_sql are
      parameters that affect the result and are considered when determining
      whether a request is a duplicate, but properties like timeout_ms don't
      affect the result and are thus not considered. Dry run query requests
      are never considered duplicate of another request. 4. When a duplicate
      mutating query request is detected, it returns: a. the results of the
      mutation if it completes successfully within the timeout. b. the running
      operation if it is still in progress at the end of the timeout. 5. Its
      lifetime is limited to 15 minutes. In other words, if two requests are
      sent with the same request_id, but more than 15 minutes apart,
      idempotency is not guaranteed.
    reservation: Optional. The reservation that jobs.query request would use.
      User can specify a reservation to execute the job.query. The expected
      format is
      `projects/{project}/locations/{location}/reservations/{reservation}`.
    timeoutMs: Optional. Optional: Specifies the maximum amount of time, in
      milliseconds, that the client is willing to wait for the query to
      complete. By default, this limit is 10 seconds (10,000 milliseconds). If
      the query is complete, the jobComplete field in the response is true. If
      the query has not yet completed, jobComplete is false. You can request a
      longer timeout period in the timeoutMs field. However, the call is not
      guaranteed to wait for the specified timeout; it typically returns after
      around 200 seconds (200,000 milliseconds), even if the query is not
      complete. If jobComplete is false, you can continue to wait for the
      query to complete by calling the getQueryResults method until the
      jobComplete field in the getQueryResults response is true.
    useLegacySql: Specifies whether to use BigQuery's legacy SQL dialect for
      this query. The default value is true. If set to false, the query will
      use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-
      reference/ When useLegacySql is set to false, the value of
      flattenResults is ignored; query will be run as if flattenResults is
      false.
    useQueryCache: Optional. Whether to look for the result in the query
      cache. The query cache is a best-effort cache that will be flushed
      whenever tables in the query are modified. The default value is true.
    writeIncrementalResults: Optional. This is only supported for SELECT
      query. If set, the query is allowed to write results incrementally to
      the temporary result table. This may incur a performance penalty. This
      option cannot be used with Legacy SQL. This feature is not yet
      available.
  """

  class JobCreationModeValueValuesEnum(_messages.Enum):
    r"""Optional. If not set, jobs are always required. If set, the query
    request will follow the behavior described JobCreationMode.
    [Preview](https://cloud.google.com/products/#product-launch-stages)

    Values:
      JOB_CREATION_MODE_UNSPECIFIED: If unspecified JOB_CREATION_REQUIRED is
        the default.
      JOB_CREATION_REQUIRED: Default. Job creation is always required.
      JOB_CREATION_OPTIONAL: Job creation is optional. Returning immediate
        results is prioritized. BigQuery will automatically determine if a Job
        needs to be created. The conditions under which BigQuery can decide to
        not create a Job are subject to change. If Job creation is required,
        JOB_CREATION_REQUIRED mode should be used, which is the default.
    """
    JOB_CREATION_MODE_UNSPECIFIED = 0
    JOB_CREATION_REQUIRED = 1
    JOB_CREATION_OPTIONAL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels associated with this query. Labels can be used to
    organize and group query jobs. Label keys and values can be no longer than
    63 characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label keys
    must start with a letter and each label in the list must have a different
    key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectionProperties = _messages.MessageField('ConnectionProperty', 1, repeated=True)
  continuous = _messages.BooleanField(2)
  createSession = _messages.BooleanField(3)
  defaultDataset = _messages.MessageField('DatasetReference', 4)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 5)
  dryRun = _messages.BooleanField(6)
  formatOptions = _messages.MessageField('DataFormatOptions', 7)
  jobCreationMode = _messages.EnumField('JobCreationModeValueValuesEnum', 8)
  jobTimeoutMs = _messages.IntegerField(9)
  kind = _messages.StringField(10, default='bigquery#queryRequest')
  labels = _messages.MessageField('LabelsValue', 11)
  location = _messages.StringField(12)
  maxResults = _messages.IntegerField(13, variant=_messages.Variant.UINT32)
  maximumBytesBilled = _messages.IntegerField(14)
  parameterMode = _messages.StringField(15)
  preserveNulls = _messages.BooleanField(16)
  query = _messages.StringField(17)
  queryParameters = _messages.MessageField('QueryParameter', 18, repeated=True)
  requestId = _messages.StringField(19)
  reservation = _messages.StringField(20)
  timeoutMs = _messages.IntegerField(21, variant=_messages.Variant.UINT32)
  useLegacySql = _messages.BooleanField(22, default=True)
  useQueryCache = _messages.BooleanField(23, default=True)
  writeIncrementalResults = _messages.BooleanField(24)


class QueryResponse(_messages.Message):
  r"""A QueryResponse object.

  Fields:
    cacheHit: Whether the query result was fetched from the query cache.
    creationTime: Output only. Creation time of this query, in milliseconds
      since the epoch. This field will be present on all queries.
    dmlStats: Output only. Detailed statistics for DML statements INSERT,
      UPDATE, DELETE, MERGE or TRUNCATE.
    endTime: Output only. End time of this query, in milliseconds since the
      epoch. This field will be present whenever a query job is in the DONE
      state.
    errors: Output only. The first errors or warnings encountered during the
      running of the job. The final message includes the number of errors that
      caused the process to stop. Errors here do not necessarily mean that the
      job has completed or was unsuccessful. For more information about error
      messages, see [Error
      messages](https://cloud.google.com/bigquery/docs/error-messages).
    jobComplete: Whether the query has completed or not. If rows or totalRows
      are present, this will always be true. If this is false, totalRows will
      not be available.
    jobCreationReason: Optional. The reason why a Job was created. Only
      relevant when a job_reference is present in the response. If
      job_reference is not present it will always be unset.
      [Preview](https://cloud.google.com/products/#product-launch-stages)
    jobReference: Reference to the Job that was created to run the query. This
      field will be present even if the original request timed out, in which
      case GetQueryResults can be used to read the results once the query has
      completed. Since this API only returns the first page of results,
      subsequent pages can be fetched via the same mechanism
      (GetQueryResults). If job_creation_mode was set to
      `JOB_CREATION_OPTIONAL` and the query completes without creating a job,
      this field will be empty.
    kind: The resource type.
    location: Output only. The geographic location of the query. For more
      information about BigQuery locations, see:
      https://cloud.google.com/bigquery/docs/locations
    numDmlAffectedRows: Output only. The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    pageToken: A token used for paging results. A non-empty token indicates
      that additional results are available. To see additional results, query
      the [`jobs.getQueryResults`](https://cloud.google.com/bigquery/docs/refe
      rence/rest/v2/jobs/getQueryResults) method. For more information, see
      [Paging through table
      data](https://cloud.google.com/bigquery/docs/paging-results).
    queryId: Auto-generated ID for the query.
      [Preview](https://cloud.google.com/products/#product-launch-stages)
    rows: An object with as many results as can be contained within the
      maximum permitted reply size. To get any additional rows, you can call
      GetQueryResults and specify the jobReference returned above.
    schema: The schema of the results. Present only when the query completes
      successfully.
    sessionInfo: Output only. Information of the session if this job is part
      of one.
    startTime: Output only. Start time of this query, in milliseconds since
      the epoch. This field will be present when the query job transitions
      from the PENDING state to either RUNNING or DONE.
    totalBytesBilled: Output only. If the project is configured to use on-
      demand pricing, then this field contains the total bytes billed for the
      job. If the project is configured to use flat-rate pricing, then you are
      not billed for bytes and this field is informational only.
    totalBytesProcessed: The total number of bytes processed for this query.
      If this query was a dry run, this is the number of bytes that would be
      processed if the query were run.
    totalRows: The total number of rows in the complete query result set,
      which can be more than the number of rows in this single page of
      results.
    totalSlotMs: Output only. Number of slot ms the user is actually billed
      for.
  """

  cacheHit = _messages.BooleanField(1)
  creationTime = _messages.IntegerField(2)
  dmlStats = _messages.MessageField('DmlStatistics', 3)
  endTime = _messages.IntegerField(4)
  errors = _messages.MessageField('ErrorProto', 5, repeated=True)
  jobComplete = _messages.BooleanField(6)
  jobCreationReason = _messages.MessageField('JobCreationReason', 7)
  jobReference = _messages.MessageField('JobReference', 8)
  kind = _messages.StringField(9, default='bigquery#queryResponse')
  location = _messages.StringField(10)
  numDmlAffectedRows = _messages.IntegerField(11)
  pageToken = _messages.StringField(12)
  queryId = _messages.StringField(13)
  rows = _messages.MessageField('TableRow', 14, repeated=True)
  schema = _messages.MessageField('TableSchema', 15)
  sessionInfo = _messages.MessageField('SessionInfo', 16)
  startTime = _messages.IntegerField(17)
  totalBytesBilled = _messages.IntegerField(18)
  totalBytesProcessed = _messages.IntegerField(19)
  totalRows = _messages.IntegerField(20, variant=_messages.Variant.UINT64)
  totalSlotMs = _messages.IntegerField(21)


class QueryTimelineSample(_messages.Message):
  r"""Summary of the state of query execution at a given time.

  Fields:
    activeUnits: Total number of active workers. This does not correspond
      directly to slot usage. This is the largest value observed since the
      last sample.
    completedUnits: Total parallel units of work completed by this query.
    elapsedMs: Milliseconds elapsed since the start of query execution.
    estimatedRunnableUnits: Units of work that can be scheduled immediately.
      Providing additional slots for these units of work will accelerate the
      query, if no other query in the reservation needs additional slots.
    pendingUnits: Total units of work remaining for the query. This number can
      be revised (increased or decreased) while the query is running.
    shuffleRamUsageRatio: Total shuffle usage ratio in shuffle RAM per
      reservation of this query. This will be provided for reservation
      customers only.
    totalSlotMs: Cumulative slot-ms consumed by the query.
  """

  activeUnits = _messages.IntegerField(1)
  completedUnits = _messages.IntegerField(2)
  elapsedMs = _messages.IntegerField(3)
  estimatedRunnableUnits = _messages.IntegerField(4)
  pendingUnits = _messages.IntegerField(5)
  shuffleRamUsageRatio = _messages.FloatField(6)
  totalSlotMs = _messages.IntegerField(7)


class RangePartitioning(_messages.Message):
  r"""A RangePartitioning object.

  Messages:
    RangeValue: [Experimental] Defines the ranges for range partitioning.

  Fields:
    field: Required. The name of the column to partition the table on. It must
      be a top-level, INT64 column whose mode is NULLABLE or REQUIRED.
    range: [Experimental] Defines the ranges for range partitioning.
  """

  class RangeValue(_messages.Message):
    r"""[Experimental] Defines the ranges for range partitioning.

    Fields:
      end: [Experimental] The end of range partitioning, exclusive.
      interval: [Experimental] The width of each interval.
      start: [Experimental] The start of range partitioning, inclusive.
    """

    end = _messages.IntegerField(1)
    interval = _messages.IntegerField(2)
    start = _messages.IntegerField(3)

  field = _messages.StringField(1)
  range = _messages.MessageField('RangeValue', 2)


class RangeValue(_messages.Message):
  r"""Represents the value of a range.

  Fields:
    end: Optional. The end value of the range. A missing value represents an
      unbounded end.
    start: Optional. The start value of the range. A missing value represents
      an unbounded start.
  """

  end = _messages.MessageField('QueryParameterValue', 1)
  start = _messages.MessageField('QueryParameterValue', 2)


class RankingMetrics(_messages.Message):
  r"""Evaluation metrics used by weighted-ALS models specified by
  feedback_type=implicit.

  Fields:
    averageRank: Determines the goodness of a ranking by computing the
      percentile rank from the predicted confidence and dividing it by the
      original rank.
    meanAveragePrecision: Calculates a precision per user for all the items by
      ranking them and then averages all the precisions across all the users.
    meanSquaredError: Similar to the mean squared error computed in regression
      and explicit recommendation models except instead of computing the
      rating directly, the output from evaluate is computed against a
      preference which is 1 or 0 depending on if the rating exists or not.
    normalizedDiscountedCumulativeGain: A metric to determine the goodness of
      a ranking calculated from the predicted confidence by comparing it to an
      ideal rank measured by the original ratings.
  """

  averageRank = _messages.FloatField(1)
  meanAveragePrecision = _messages.FloatField(2)
  meanSquaredError = _messages.FloatField(3)
  normalizedDiscountedCumulativeGain = _messages.FloatField(4)


class RegressionMetrics(_messages.Message):
  r"""Evaluation metrics for regression and explicit feedback type matrix
  factorization models.

  Fields:
    meanAbsoluteError: Mean absolute error.
    meanSquaredError: Mean squared error.
    meanSquaredLogError: Mean squared log error.
    medianAbsoluteError: Median absolute error.
    rSquared: R^2 score. This corresponds to r2_score in ML.EVALUATE.
  """

  meanAbsoluteError = _messages.FloatField(1)
  meanSquaredError = _messages.FloatField(2)
  meanSquaredLogError = _messages.FloatField(3)
  medianAbsoluteError = _messages.FloatField(4)
  rSquared = _messages.FloatField(5)


class RemoteFunctionOptions(_messages.Message):
  r"""Options for a remote user-defined function.

  Messages:
    UserDefinedContextValue: User-defined context as a set of key/value pairs,
      which will be sent as function invocation context together with batched
      arguments in the requests to the remote service. The total number of
      bytes of keys and values must be less than 8KB.

  Fields:
    connection: Fully qualified name of the user-provided connection object
      which holds the authentication information to send requests to the
      remote service. Format: ```"projects/{projectId}/locations/{locationId}/
      connections/{connectionId}"```
    endpoint: Endpoint of the user-provided remote service, e.g.
      ```https://us-east1-my_gcf_project.cloudfunctions.net/remote_add```
    maxBatchingRows: Max number of rows in each batch sent to the remote
      service. If absent or if 0, BigQuery dynamically decides the number of
      rows in a batch.
    userDefinedContext: User-defined context as a set of key/value pairs,
      which will be sent as function invocation context together with batched
      arguments in the requests to the remote service. The total number of
      bytes of keys and values must be less than 8KB.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserDefinedContextValue(_messages.Message):
    r"""User-defined context as a set of key/value pairs, which will be sent
    as function invocation context together with batched arguments in the
    requests to the remote service. The total number of bytes of keys and
    values must be less than 8KB.

    Messages:
      AdditionalProperty: An additional property for a UserDefinedContextValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        UserDefinedContextValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserDefinedContextValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connection = _messages.StringField(1)
  endpoint = _messages.StringField(2)
  maxBatchingRows = _messages.IntegerField(3)
  userDefinedContext = _messages.MessageField('UserDefinedContextValue', 4)


class RemoteModelInfo(_messages.Message):
  r"""Remote Model Info

  Enums:
    RemoteServiceTypeValueValuesEnum: Output only. The remote service type for
      remote model.

  Fields:
    connection: Output only. Fully qualified name of the user-provided
      connection object of the remote model. Format: ```"projects/{project_id}
      /locations/{location_id}/connections/{connection_id}"```
    endpoint: Output only. The endpoint for remote model.
    maxBatchingRows: Output only. Max number of rows in each batch sent to the
      remote service. If unset, the number of rows in each batch is set
      dynamically.
    remoteModelVersion: Output only. The model version for LLM.
    remoteServiceType: Output only. The remote service type for remote model.
    speechRecognizer: Output only. The name of the speech recognizer to use
      for speech recognition. The expected format is
      `projects/{project}/locations/{location}/recognizers/{recognizer}`.
      Customers can specify this field at model creation. If not specified, a
      default recognizer `projects/{model
      project}/locations/global/recognizers/_` will be used. See more details
      at [recognizers](https://cloud.google.com/speech-to-
      text/v2/docs/reference/rest/v2/projects.locations.recognizers)
  """

  class RemoteServiceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The remote service type for remote model.

    Values:
      REMOTE_SERVICE_TYPE_UNSPECIFIED: Unspecified remote service type.
      CLOUD_AI_TRANSLATE_V3: V3 Cloud AI Translation API. See more details at
        [Cloud Translation API]
        (https://cloud.google.com/translate/docs/reference/rest).
      CLOUD_AI_VISION_V1: V1 Cloud AI Vision API See more details at [Cloud
        Vision API] (https://cloud.google.com/vision/docs/reference/rest).
      CLOUD_AI_NATURAL_LANGUAGE_V1: V1 Cloud AI Natural Language API. See more
        details at [REST Resource:
        documents](https://cloud.google.com/natural-
        language/docs/reference/rest/v1/documents).
      CLOUD_AI_SPEECH_TO_TEXT_V2: V2 Speech-to-Text API. See more details at
        [Google Cloud Speech-to-Text V2 API](https://cloud.google.com/speech-
        to-text/v2/docs)
    """
    REMOTE_SERVICE_TYPE_UNSPECIFIED = 0
    CLOUD_AI_TRANSLATE_V3 = 1
    CLOUD_AI_VISION_V1 = 2
    CLOUD_AI_NATURAL_LANGUAGE_V1 = 3
    CLOUD_AI_SPEECH_TO_TEXT_V2 = 4

  connection = _messages.StringField(1)
  endpoint = _messages.StringField(2)
  maxBatchingRows = _messages.IntegerField(3)
  remoteModelVersion = _messages.StringField(4)
  remoteServiceType = _messages.EnumField('RemoteServiceTypeValueValuesEnum', 5)
  speechRecognizer = _messages.StringField(6)


class RestrictionConfig(_messages.Message):
  r"""A RestrictionConfig object.

  Enums:
    TypeValueValuesEnum: Output only. Specifies the type of dataset/table
      restriction.

  Fields:
    type: Output only. Specifies the type of dataset/table restriction.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies the type of dataset/table restriction.

    Values:
      RESTRICTION_TYPE_UNSPECIFIED: Should never be used.
      RESTRICTED_DATA_EGRESS: Restrict data egress. See [Data
        egress](https://cloud.google.com/bigquery/docs/analytics-hub-
        introduction#data_egress) for more details.
    """
    RESTRICTION_TYPE_UNSPECIFIED = 0
    RESTRICTED_DATA_EGRESS = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Routine(_messages.Message):
  r"""A user-defined function or a stored procedure.

  Enums:
    DataGovernanceTypeValueValuesEnum: Optional. If set to `DATA_MASKING`, the
      function is validated and made available as a masking function. For more
      information, see [Create custom masking
      routines](https://cloud.google.com/bigquery/docs/user-defined-
      functions#custom-mask).
    DeterminismLevelValueValuesEnum: Optional. The determinism level of the
      JavaScript UDF, if defined.
    LanguageValueValuesEnum: Optional. Defaults to "SQL" if
      remote_function_options field is absent, not set otherwise.
    RoutineTypeValueValuesEnum: Required. The type of routine.
    SecurityModeValueValuesEnum: Optional. The security mode of the routine,
      if defined. If not defined, the security mode is automatically
      determined from the routine's configuration.

  Fields:
    arguments: Optional.
    creationTime: Output only. The time when this routine was created, in
      milliseconds since the epoch.
    dataGovernanceType: Optional. If set to `DATA_MASKING`, the function is
      validated and made available as a masking function. For more
      information, see [Create custom masking
      routines](https://cloud.google.com/bigquery/docs/user-defined-
      functions#custom-mask).
    definitionBody: Required. The body of the routine. For functions, this is
      the expression in the AS clause. If language=SQL, it is the substring
      inside (but excluding) the parentheses. For example, for the function
      created with the following statement: `CREATE FUNCTION JoinLines(x
      string, y string) as (concat(x, "\n", y))` The definition_body is
      `concat(x, "\n", y)` (\n is not replaced with linebreak). If
      language=JAVASCRIPT, it is the evaluated string in the AS clause. For
      example, for the function created with the following statement: `CREATE
      FUNCTION f() RETURNS STRING LANGUAGE js AS 'return "\n";\n'` The
      definition_body is `return "\n";\n` Note that both \n are replaced with
      linebreaks.
    description: Optional. The description of the routine, if defined.
    determinismLevel: Optional. The determinism level of the JavaScript UDF,
      if defined.
    etag: Output only. A hash of this resource.
    importedLibraries: Optional. If language = "JAVASCRIPT", this field stores
      the path of the imported JAVASCRIPT libraries.
    language: Optional. Defaults to "SQL" if remote_function_options field is
      absent, not set otherwise.
    lastModifiedTime: Output only. The time when this routine was last
      modified, in milliseconds since the epoch.
    remoteFunctionOptions: Optional. Remote function specific options.
    returnTableType: Optional. Can be set only if routine_type =
      "TABLE_VALUED_FUNCTION". If absent, the return table type is inferred
      from definition_body at query time in each query that references this
      routine. If present, then the columns in the evaluated table result will
      be cast to match the column types specified in return table type, at
      query time.
    returnType: Optional if language = "SQL"; required otherwise. Cannot be
      set if routine_type = "TABLE_VALUED_FUNCTION". If absent, the return
      type is inferred from definition_body at query time in each query that
      references this routine. If present, then the evaluated result will be
      cast to the specified returned type at query time. For example, for the
      functions created with the following statements: * `CREATE FUNCTION
      Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE
      FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION
      Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type
      is `{type_kind: "FLOAT64"}` for `Add` and `Decrement`, and is absent for
      `Increment` (inferred as FLOAT64 at query time). Suppose the function
      `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64)
      AS (x + y);` Then the inferred return type of `Increment` is
      automatically changed to INT64 at query time, while the return type of
      `Decrement` remains FLOAT64.
    routineReference: Required. Reference describing the ID of this routine.
    routineType: Required. The type of routine.
    securityMode: Optional. The security mode of the routine, if defined. If
      not defined, the security mode is automatically determined from the
      routine's configuration.
    sparkOptions: Optional. Spark specific options.
    strictMode: Optional. Use this option to catch many common errors. Error
      checking is not exhaustive, and successfully creating a procedure
      doesn't guarantee that the procedure will successfully execute at
      runtime. If `strictMode` is set to `TRUE`, the procedure body is further
      checked for errors such as non-existent tables or columns. The `CREATE
      PROCEDURE` statement fails if the body fails any of these checks. If
      `strictMode` is set to `FALSE`, the procedure body is checked only for
      syntax. For procedures that invoke themselves recursively, specify
      `strictMode=FALSE` to avoid non-existent procedure errors during
      validation. Default value is `TRUE`.
  """

  class DataGovernanceTypeValueValuesEnum(_messages.Enum):
    r"""Optional. If set to `DATA_MASKING`, the function is validated and made
    available as a masking function. For more information, see [Create custom
    masking routines](https://cloud.google.com/bigquery/docs/user-defined-
    functions#custom-mask).

    Values:
      DATA_GOVERNANCE_TYPE_UNSPECIFIED: The data governance type is
        unspecified.
      DATA_MASKING: The data governance type is data masking.
    """
    DATA_GOVERNANCE_TYPE_UNSPECIFIED = 0
    DATA_MASKING = 1

  class DeterminismLevelValueValuesEnum(_messages.Enum):
    r"""Optional. The determinism level of the JavaScript UDF, if defined.

    Values:
      DETERMINISM_LEVEL_UNSPECIFIED: The determinism of the UDF is
        unspecified.
      DETERMINISTIC: The UDF is deterministic, meaning that 2 function calls
        with the same inputs always produce the same result, even across 2
        query runs.
      NOT_DETERMINISTIC: The UDF is not deterministic.
    """
    DETERMINISM_LEVEL_UNSPECIFIED = 0
    DETERMINISTIC = 1
    NOT_DETERMINISTIC = 2

  class LanguageValueValuesEnum(_messages.Enum):
    r"""Optional. Defaults to "SQL" if remote_function_options field is
    absent, not set otherwise.

    Values:
      LANGUAGE_UNSPECIFIED: Default value.
      SQL: SQL language.
      JAVASCRIPT: JavaScript language.
      PYTHON: Python language.
      JAVA: Java language.
      SCALA: Scala language.
    """
    LANGUAGE_UNSPECIFIED = 0
    SQL = 1
    JAVASCRIPT = 2
    PYTHON = 3
    JAVA = 4
    SCALA = 5

  class RoutineTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of routine.

    Values:
      ROUTINE_TYPE_UNSPECIFIED: Default value.
      SCALAR_FUNCTION: Non-built-in persistent scalar function.
      PROCEDURE: Stored procedure.
      TABLE_VALUED_FUNCTION: Non-built-in persistent TVF.
      AGGREGATE_FUNCTION: Non-built-in persistent aggregate function.
    """
    ROUTINE_TYPE_UNSPECIFIED = 0
    SCALAR_FUNCTION = 1
    PROCEDURE = 2
    TABLE_VALUED_FUNCTION = 3
    AGGREGATE_FUNCTION = 4

  class SecurityModeValueValuesEnum(_messages.Enum):
    r"""Optional. The security mode of the routine, if defined. If not
    defined, the security mode is automatically determined from the routine's
    configuration.

    Values:
      SECURITY_MODE_UNSPECIFIED: The security mode of the routine is
        unspecified.
      DEFINER: The routine is to be executed with the privileges of the user
        who defines it.
      INVOKER: The routine is to be executed with the privileges of the user
        who invokes it.
    """
    SECURITY_MODE_UNSPECIFIED = 0
    DEFINER = 1
    INVOKER = 2

  arguments = _messages.MessageField('Argument', 1, repeated=True)
  creationTime = _messages.IntegerField(2)
  dataGovernanceType = _messages.EnumField('DataGovernanceTypeValueValuesEnum', 3)
  definitionBody = _messages.StringField(4)
  description = _messages.StringField(5)
  determinismLevel = _messages.EnumField('DeterminismLevelValueValuesEnum', 6)
  etag = _messages.StringField(7)
  importedLibraries = _messages.StringField(8, repeated=True)
  language = _messages.EnumField('LanguageValueValuesEnum', 9)
  lastModifiedTime = _messages.IntegerField(10)
  remoteFunctionOptions = _messages.MessageField('RemoteFunctionOptions', 11)
  returnTableType = _messages.MessageField('StandardSqlTableType', 12)
  returnType = _messages.MessageField('StandardSqlDataType', 13)
  routineReference = _messages.MessageField('RoutineReference', 14)
  routineType = _messages.EnumField('RoutineTypeValueValuesEnum', 15)
  securityMode = _messages.EnumField('SecurityModeValueValuesEnum', 16)
  sparkOptions = _messages.MessageField('SparkOptions', 17)
  strictMode = _messages.BooleanField(18)


class RoutineReference(_messages.Message):
  r"""Id path of a routine.

  Fields:
    datasetId: Required. The ID of the dataset containing this routine.
    projectId: Required. The ID of the project containing this routine.
    routineId: Required. The ID of the routine. The ID must contain only
      letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum
      length is 256 characters.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)
  routineId = _messages.StringField(3)


class Row(_messages.Message):
  r"""A single row in the confusion matrix.

  Fields:
    actualLabel: The original label of this row.
    entries: Info describing predicted label distribution.
  """

  actualLabel = _messages.StringField(1)
  entries = _messages.MessageField('Entry', 2, repeated=True)


class RowAccessPolicy(_messages.Message):
  r"""Represents access on a subset of rows on the specified table, defined by
  its filter predicate. Access to the subset of rows is controlled by its IAM
  policy.

  Fields:
    creationTime: Output only. The time when this row access policy was
      created, in milliseconds since the epoch.
    etag: Output only. A hash of this resource.
    filterPredicate: Required. A SQL boolean expression that represents the
      rows defined by this row access policy, similar to the boolean
      expression in a WHERE clause of a SELECT query on a table. References to
      other tables, routines, and temporary functions are not supported.
      Examples: region="EU" date_field = CAST('2019-9-27' as DATE)
      nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0
    grantees: Optional. Input only. The optional list of iam_member users or
      groups that specifies the initial members that the row-level access
      policy should be created with. grantees types: -
      "user:<EMAIL>": An email address that represents a specific
      Google account. - "serviceAccount:my-other-
      <EMAIL>": An email address that represents a
      service account. - "group:<EMAIL>": An email address that
      represents a Google group. - "domain:example.com":The Google Workspace
      domain (primary) that represents all the users of that domain. -
      "allAuthenticatedUsers": A special identifier that represents all
      service accounts and all users on the internet who have authenticated
      with a Google Account. This identifier includes accounts that aren't
      connected to a Google Workspace or Cloud Identity domain, such as
      personal Gmail accounts. Users who aren't authenticated, such as
      anonymous visitors, aren't included. - "allUsers":A special identifier
      that represents anyone who is on the internet, including authenticated
      and unauthenticated users. Because BigQuery requires authentication
      before a user can access the service, allUsers includes only
      authenticated users.
    lastModifiedTime: Output only. The time when this row access policy was
      last modified, in milliseconds since the epoch.
    rowAccessPolicyReference: Required. Reference describing the ID of this
      row access policy.
  """

  creationTime = _messages.StringField(1)
  etag = _messages.StringField(2)
  filterPredicate = _messages.StringField(3)
  grantees = _messages.StringField(4, repeated=True)
  lastModifiedTime = _messages.StringField(5)
  rowAccessPolicyReference = _messages.MessageField('RowAccessPolicyReference', 6)


class RowAccessPolicyReference(_messages.Message):
  r"""Id path of a row access policy.

  Fields:
    datasetId: Required. The ID of the dataset containing this row access
      policy.
    policyId: Required. The ID of the row access policy. The ID must contain
      only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum
      length is 256 characters.
    projectId: Required. The ID of the project containing this row access
      policy.
    tableId: Required. The ID of the table containing this row access policy.
  """

  datasetId = _messages.StringField(1)
  policyId = _messages.StringField(2)
  projectId = _messages.StringField(3)
  tableId = _messages.StringField(4)


class RowLevelSecurityStatistics(_messages.Message):
  r"""Statistics for row-level security.

  Fields:
    rowLevelSecurityApplied: Whether any accessed data was protected by row
      access policies.
  """

  rowLevelSecurityApplied = _messages.BooleanField(1)


class ScriptOptions(_messages.Message):
  r"""Options related to script execution.

  Enums:
    KeyResultStatementValueValuesEnum: Determines which statement in the
      script represents the "key result", used to populate the schema and
      query results of the script job. Default is LAST.

  Fields:
    keyResultStatement: Determines which statement in the script represents
      the "key result", used to populate the schema and query results of the
      script job. Default is LAST.
    statementByteBudget: Limit on the number of bytes billed per statement.
      Exceeding this budget results in an error.
    statementTimeoutMs: Timeout period for each statement in a script.
  """

  class KeyResultStatementValueValuesEnum(_messages.Enum):
    r"""Determines which statement in the script represents the "key result",
    used to populate the schema and query results of the script job. Default
    is LAST.

    Values:
      KEY_RESULT_STATEMENT_KIND_UNSPECIFIED: Default value.
      LAST: The last result determines the key result.
      FIRST_SELECT: The first SELECT statement determines the key result.
    """
    KEY_RESULT_STATEMENT_KIND_UNSPECIFIED = 0
    LAST = 1
    FIRST_SELECT = 2

  keyResultStatement = _messages.EnumField('KeyResultStatementValueValuesEnum', 1)
  statementByteBudget = _messages.IntegerField(2)
  statementTimeoutMs = _messages.IntegerField(3)


class ScriptStackFrame(_messages.Message):
  r"""Represents the location of the statement/expression being evaluated.
  Line and column numbers are defined as follows: - Line and column numbers
  start with one. That is, line 1 column 1 denotes the start of the script. -
  When inside a stored procedure, all line/column numbers are relative to the
  procedure body, not the script in which the procedure was defined. -
  Start/end positions exclude leading/trailing comments and whitespace. The
  end position always ends with a ";", when present. - Multi-byte Unicode
  characters are treated as just one column. - If the original script (or
  procedure definition) contains TAB characters, a tab "snaps" the indentation
  forward to the nearest multiple of 8 characters, plus 1. For example, a TAB
  on column 1, 2, 3, 4, 5, 6 , or 8 will advance the next character to column
  9. A TAB on column 9, 10, 11, 12, 13, 14, 15, or 16 will advance the next
  character to column 17.

  Fields:
    endColumn: Output only. One-based end column.
    endLine: Output only. One-based end line.
    procedureId: Output only. Name of the active procedure, empty if in a top-
      level script.
    startColumn: Output only. One-based start column.
    startLine: Output only. One-based start line.
    text: Output only. Text of the current statement/expression.
  """

  endColumn = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  endLine = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  procedureId = _messages.StringField(3)
  startColumn = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startLine = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  text = _messages.StringField(6)


class ScriptStatistics(_messages.Message):
  r"""Job statistics specific to the child job of a script.

  Enums:
    EvaluationKindValueValuesEnum: Whether this child job was a statement or
      expression.

  Fields:
    evaluationKind: Whether this child job was a statement or expression.
    stackFrames: Stack trace showing the line/column/procedure name of each
      frame on the stack at the point where the current evaluation happened.
      The leaf frame is first, the primary script is last. Never empty.
  """

  class EvaluationKindValueValuesEnum(_messages.Enum):
    r"""Whether this child job was a statement or expression.

    Values:
      EVALUATION_KIND_UNSPECIFIED: Default value.
      STATEMENT: The statement appears directly in the script.
      EXPRESSION: The statement evaluates an expression that appears in the
        script.
    """
    EVALUATION_KIND_UNSPECIFIED = 0
    STATEMENT = 1
    EXPRESSION = 2

  evaluationKind = _messages.EnumField('EvaluationKindValueValuesEnum', 1)
  stackFrames = _messages.MessageField('ScriptStackFrame', 2, repeated=True)


class SearchStatistics(_messages.Message):
  r"""Statistics for a search query. Populated as part of JobStatistics2.

  Enums:
    IndexUsageModeValueValuesEnum: Specifies the index usage mode for the
      query.

  Fields:
    indexUnusedReasons: When `indexUsageMode` is `UNUSED` or `PARTIALLY_USED`,
      this field explains why indexes were not used in all or part of the
      search query. If `indexUsageMode` is `FULLY_USED`, this field is not
      populated.
    indexUsageMode: Specifies the index usage mode for the query.
  """

  class IndexUsageModeValueValuesEnum(_messages.Enum):
    r"""Specifies the index usage mode for the query.

    Values:
      INDEX_USAGE_MODE_UNSPECIFIED: Index usage mode not specified.
      UNUSED: No search indexes were used in the search query. See
        [`indexUnusedReasons`]
        (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for detailed
        reasons.
      PARTIALLY_USED: Part of the search query used search indexes. See
        [`indexUnusedReasons`]
        (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for why other
        parts of the query did not use search indexes.
      FULLY_USED: The entire search query used search indexes.
    """
    INDEX_USAGE_MODE_UNSPECIFIED = 0
    UNUSED = 1
    PARTIALLY_USED = 2
    FULLY_USED = 3

  indexUnusedReasons = _messages.MessageField('IndexUnusedReason', 1, repeated=True)
  indexUsageMode = _messages.EnumField('IndexUsageModeValueValuesEnum', 2)


class SerDeInfo(_messages.Message):
  r"""Serializer and deserializer information.

  Messages:
    ParametersValue: Optional. Key-value pairs that define the initialization
      parameters for the serialization library. Maximum size 10 Kib.

  Fields:
    name: Optional. Name of the SerDe. The maximum length is 256 characters.
    parameters: Optional. Key-value pairs that define the initialization
      parameters for the serialization library. Maximum size 10 Kib.
    serializationLibrary: Required. Specifies a fully-qualified class name of
      the serialization library that is responsible for the translation of
      data between table representation and the underlying low-level input and
      output format structures. The maximum length is 256 characters.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Optional. Key-value pairs that define the initialization parameters
    for the serialization library. Maximum size 10 Kib.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Additional properties of type ParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  parameters = _messages.MessageField('ParametersValue', 2)
  serializationLibrary = _messages.StringField(3)


class SessionInfo(_messages.Message):
  r"""[Preview] Information related to sessions.

  Fields:
    sessionId: Output only. The id of the session.
  """

  sessionId = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SkewSource(_messages.Message):
  r"""Details about source stages which produce skewed data.

  Fields:
    stageId: Output only. Stage id of the skew source stage.
  """

  stageId = _messages.IntegerField(1)


class SnapshotDefinition(_messages.Message):
  r"""Information about base table and snapshot time of the snapshot.

  Fields:
    baseTableReference: Required. Reference describing the ID of the table
      that was snapshot.
    snapshotTime: Required. The time at which the base table was snapshot.
      This value is reported in the JSON response using RFC3339 format.
  """

  baseTableReference = _messages.MessageField('TableReference', 1)
  snapshotTime = _message_types.DateTimeField(2)


class SparkLoggingInfo(_messages.Message):
  r"""Spark job logs can be filtered by these fields in Cloud Logging.

  Fields:
    projectId: Output only. Project ID where the Spark logs were written.
    resourceType: Output only. Resource type used for logging.
  """

  projectId = _messages.StringField(1)
  resourceType = _messages.StringField(2)


class SparkOptions(_messages.Message):
  r"""Options for a user-defined Spark routine.

  Messages:
    PropertiesValue: Configuration properties as a set of key/value pairs,
      which will be passed on to the Spark application. For more information,
      see [Apache Spark](https://spark.apache.org/docs/latest/index.html) and
      the [procedure option
      list](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#procedure_option_list).

  Fields:
    archiveUris: Archive files to be extracted into the working directory of
      each executor. For more information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    connection: Fully qualified name of the user-provided Spark connection
      object. Format: ```"projects/{project_id}/locations/{location_id}/connec
      tions/{connection_id}"```
    containerImage: Custom container image for the runtime environment.
    fileUris: Files to be placed in the working directory of each executor.
      For more information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    jarUris: JARs to include on the driver and executor CLASSPATH. For more
      information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    mainClass: The fully qualified name of a class in jar_uris, for example,
      com.example.wordcount. Exactly one of main_class and main_jar_uri field
      should be set for Java/Scala language type.
    mainFileUri: The main file/jar URI of the Spark application. Exactly one
      of the definition_body field and the main_file_uri field must be set for
      Python. Exactly one of main_class and main_file_uri field should be set
      for Java/Scala language type.
    properties: Configuration properties as a set of key/value pairs, which
      will be passed on to the Spark application. For more information, see
      [Apache Spark](https://spark.apache.org/docs/latest/index.html) and the
      [procedure option
      list](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#procedure_option_list).
    pyFileUris: Python files to be placed on the PYTHONPATH for PySpark
      application. Supported file types: `.py`, `.egg`, and `.zip`. For more
      information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    runtimeVersion: Runtime version. If not specified, the default runtime
      version is used.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Configuration properties as a set of key/value pairs, which will be
    passed on to the Spark application. For more information, see [Apache
    Spark](https://spark.apache.org/docs/latest/index.html) and the [procedure
    option list](https://cloud.google.com/bigquery/docs/reference/standard-
    sql/data-definition-language#procedure_option_list).

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  archiveUris = _messages.StringField(1, repeated=True)
  connection = _messages.StringField(2)
  containerImage = _messages.StringField(3)
  fileUris = _messages.StringField(4, repeated=True)
  jarUris = _messages.StringField(5, repeated=True)
  mainClass = _messages.StringField(6)
  mainFileUri = _messages.StringField(7)
  properties = _messages.MessageField('PropertiesValue', 8)
  pyFileUris = _messages.StringField(9, repeated=True)
  runtimeVersion = _messages.StringField(10)


class SparkStatistics(_messages.Message):
  r"""Statistics for a BigSpark query. Populated as part of JobStatistics2

  Messages:
    EndpointsValue: Output only. Endpoints returned from Dataproc. Key list: -
      history_server_endpoint: A link to Spark job UI.

  Fields:
    endpoints: Output only. Endpoints returned from Dataproc. Key list: -
      history_server_endpoint: A link to Spark job UI.
    gcsStagingBucket: Output only. The Google Cloud Storage bucket that is
      used as the default file system by the Spark application. This field is
      only filled when the Spark procedure uses the invoker security mode. The
      `gcsStagingBucket` bucket is inferred from the
      `@@spark_proc_properties.staging_bucket` system variable (if it is
      provided). Otherwise, BigQuery creates a default staging bucket for the
      job and returns the bucket name in this field. Example: *
      `gs://[bucket_name]`
    kmsKeyName: Output only. The Cloud KMS encryption key that is used to
      protect the resources created by the Spark job. If the Spark procedure
      uses the invoker security mode, the Cloud KMS encryption key is either
      inferred from the provided system variable,
      `@@spark_proc_properties.kms_key_name`, or the default key of the
      BigQuery job's project (if the CMEK organization policy is enforced).
      Otherwise, the Cloud KMS key is either inferred from the Spark
      connection associated with the procedure (if it is provided), or from
      the default key of the Spark connection's project if the CMEK
      organization policy is enforced. Example: * `projects/[kms_project_id]/l
      ocations/[region]/keyRings/[key_region]/cryptoKeys/[key]`
    loggingInfo: Output only. Logging info is used to generate a link to Cloud
      Logging.
    sparkJobId: Output only. Spark job ID if a Spark job is created
      successfully.
    sparkJobLocation: Output only. Location where the Spark job is executed. A
      location is selected by BigQueury for jobs configured to run in a multi-
      region.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EndpointsValue(_messages.Message):
    r"""Output only. Endpoints returned from Dataproc. Key list: -
    history_server_endpoint: A link to Spark job UI.

    Messages:
      AdditionalProperty: An additional property for a EndpointsValue object.

    Fields:
      additionalProperties: Additional properties of type EndpointsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EndpointsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  endpoints = _messages.MessageField('EndpointsValue', 1)
  gcsStagingBucket = _messages.StringField(2)
  kmsKeyName = _messages.StringField(3)
  loggingInfo = _messages.MessageField('SparkLoggingInfo', 4)
  sparkJobId = _messages.StringField(5)
  sparkJobLocation = _messages.StringField(6)


class StagePerformanceChangeInsight(_messages.Message):
  r"""Performance insights compared to the previous executions for a specific
  stage.

  Fields:
    inputDataChange: Output only. Input data change insight of the query
      stage.
    stageId: Output only. The stage id that the insight mapped to.
  """

  inputDataChange = _messages.MessageField('InputDataChange', 1)
  stageId = _messages.IntegerField(2)


class StagePerformanceStandaloneInsight(_messages.Message):
  r"""Standalone performance insights for a specific stage.

  Fields:
    biEngineReasons: Output only. If present, the stage had the following
      reasons for being disqualified from BI Engine execution.
    highCardinalityJoins: Output only. High cardinality joins in the stage.
    insufficientShuffleQuota: Output only. True if the stage has insufficient
      shuffle quota.
    partitionSkew: Output only. Partition skew in the stage.
    slotContention: Output only. True if the stage has a slot contention
      issue.
    stageId: Output only. The stage id that the insight mapped to.
  """

  biEngineReasons = _messages.MessageField('BiEngineReason', 1, repeated=True)
  highCardinalityJoins = _messages.MessageField('HighCardinalityJoin', 2, repeated=True)
  insufficientShuffleQuota = _messages.BooleanField(3)
  partitionSkew = _messages.MessageField('PartitionSkew', 4)
  slotContention = _messages.BooleanField(5)
  stageId = _messages.IntegerField(6)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StandardSqlDataType(_messages.Message):
  r"""The data type of a variable such as a function argument. Examples
  include: * INT64: `{"typeKind": "INT64"}` * ARRAY: { "typeKind": "ARRAY",
  "arrayElementType": {"typeKind": "STRING"} } * STRUCT>: { "typeKind":
  "STRUCT", "structType": { "fields": [ { "name": "x", "type": {"typeKind":
  "STRING"} }, { "name": "y", "type": { "typeKind": "ARRAY",
  "arrayElementType": {"typeKind": "DATE"} } } ] } } * RANGE: { "typeKind":
  "RANGE", "rangeElementType": {"typeKind": "DATE"} }

  Enums:
    TypeKindValueValuesEnum: Required. The top level type of this field. Can
      be any GoogleSQL data type (e.g., "INT64", "DATE", "ARRAY").

  Fields:
    arrayElementType: The type of the array's elements, if type_kind =
      "ARRAY".
    rangeElementType: The type of the range's elements, if type_kind =
      "RANGE".
    structType: The fields of this struct, in order, if type_kind = "STRUCT".
    typeKind: Required. The top level type of this field. Can be any GoogleSQL
      data type (e.g., "INT64", "DATE", "ARRAY").
  """

  class TypeKindValueValuesEnum(_messages.Enum):
    r"""Required. The top level type of this field. Can be any GoogleSQL data
    type (e.g., "INT64", "DATE", "ARRAY").

    Values:
      TYPE_KIND_UNSPECIFIED: Invalid type.
      INT64: Encoded as a string in decimal format.
      BOOL: Encoded as a boolean "false" or "true".
      FLOAT64: Encoded as a number, or string "NaN", "Infinity" or
        "-Infinity".
      STRING: Encoded as a string value.
      BYTES: Encoded as a base64 string per RFC 4648, section 4.
      TIMESTAMP: Encoded as an RFC 3339 timestamp with mandatory "Z" time zone
        string: 1985-04-12T23:20:50.52Z
      DATE: Encoded as RFC 3339 full-date format string: 1985-04-12
      TIME: Encoded as RFC 3339 partial-time format string: 23:20:50.52
      DATETIME: Encoded as RFC 3339 full-date "T" partial-time:
        1985-04-12T23:20:50.52
      INTERVAL: Encoded as fully qualified 3 part: 0-5 15 2:30:45.6
      GEOGRAPHY: Encoded as WKT
      NUMERIC: Encoded as a decimal string.
      BIGNUMERIC: Encoded as a decimal string.
      JSON: Encoded as a string.
      ARRAY: Encoded as a list with types matching Type.array_type.
      STRUCT: Encoded as a list with fields of type Type.struct_type[i]. List
        is used because a JSON object cannot have duplicate field names.
      RANGE: Encoded as a pair with types matching range_element_type. Pairs
        must begin with "[", end with ")", and be separated by ", ".
    """
    TYPE_KIND_UNSPECIFIED = 0
    INT64 = 1
    BOOL = 2
    FLOAT64 = 3
    STRING = 4
    BYTES = 5
    TIMESTAMP = 6
    DATE = 7
    TIME = 8
    DATETIME = 9
    INTERVAL = 10
    GEOGRAPHY = 11
    NUMERIC = 12
    BIGNUMERIC = 13
    JSON = 14
    ARRAY = 15
    STRUCT = 16
    RANGE = 17

  arrayElementType = _messages.MessageField('StandardSqlDataType', 1)
  rangeElementType = _messages.MessageField('StandardSqlDataType', 2)
  structType = _messages.MessageField('StandardSqlStructType', 3)
  typeKind = _messages.EnumField('TypeKindValueValuesEnum', 4)


class StandardSqlField(_messages.Message):
  r"""A field or a column.

  Fields:
    name: Optional. The name of this field. Can be absent for struct fields.
    type: Optional. The type of this parameter. Absent if not explicitly
      specified (e.g., CREATE FUNCTION statement can omit the return type; in
      this case the output parameter does not have this "type" field).
  """

  name = _messages.StringField(1)
  type = _messages.MessageField('StandardSqlDataType', 2)


class StandardSqlStructType(_messages.Message):
  r"""The representation of a SQL STRUCT type.

  Fields:
    fields: Fields within the struct.
  """

  fields = _messages.MessageField('StandardSqlField', 1, repeated=True)


class StandardSqlTableType(_messages.Message):
  r"""A table type

  Fields:
    columns: The columns in this table type
  """

  columns = _messages.MessageField('StandardSqlField', 1, repeated=True)


class StorageDescriptor(_messages.Message):
  r"""Contains information about how a table's data is stored and accessed by
  open source query engines.

  Fields:
    inputFormat: Optional. Specifies the fully qualified class name of the
      InputFormat (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcInputFormat").
      The maximum length is 128 characters.
    locationUri: Optional. The physical location of the table (e.g.
      `gs://spark-dataproc-data/pangea-data/case_sensitive/` or `gs://spark-
      dataproc-data/pangea-data/*`). The maximum length is 2056 bytes.
    outputFormat: Optional. Specifies the fully qualified class name of the
      OutputFormat (e.g. "org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat").
      The maximum length is 128 characters.
    serdeInfo: Optional. Serializer and deserializer information.
  """

  inputFormat = _messages.StringField(1)
  locationUri = _messages.StringField(2)
  outputFormat = _messages.StringField(3)
  serdeInfo = _messages.MessageField('SerDeInfo', 4)


class StoredColumnsUnusedReason(_messages.Message):
  r"""If the stored column was not used, explain why.

  Enums:
    CodeValueValuesEnum: Specifies the high-level reason for the unused
      scenario, each reason must have a code associated.

  Fields:
    code: Specifies the high-level reason for the unused scenario, each reason
      must have a code associated.
    message: Specifies the detailed description for the scenario.
    uncoveredColumns: Specifies which columns were not covered by the stored
      columns for the specified code up to 20 columns. This is populated when
      the code is STORED_COLUMNS_COVER_INSUFFICIENT and BASE_TABLE_HAS_CLS.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Specifies the high-level reason for the unused scenario, each reason
    must have a code associated.

    Values:
      CODE_UNSPECIFIED: Default value.
      STORED_COLUMNS_COVER_INSUFFICIENT: If stored columns do not fully cover
        the columns.
      BASE_TABLE_HAS_RLS: If the base table has RLS (Row Level Security).
      BASE_TABLE_HAS_CLS: If the base table has CLS (Column Level Security).
      UNSUPPORTED_PREFILTER: If the provided prefilter is not supported.
      INTERNAL_ERROR: If an internal error is preventing stored columns from
        being used.
      OTHER_REASON: Indicates that the reason stored columns cannot be used in
        the query is not covered by any of the other StoredColumnsUnusedReason
        options.
    """
    CODE_UNSPECIFIED = 0
    STORED_COLUMNS_COVER_INSUFFICIENT = 1
    BASE_TABLE_HAS_RLS = 2
    BASE_TABLE_HAS_CLS = 3
    UNSUPPORTED_PREFILTER = 4
    INTERNAL_ERROR = 5
    OTHER_REASON = 6

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)
  uncoveredColumns = _messages.StringField(3, repeated=True)


class StoredColumnsUsage(_messages.Message):
  r"""Indicates the stored columns usage in the query.

  Fields:
    baseTable: Specifies the base table.
    isQueryAccelerated: Specifies whether the query was accelerated with
      stored columns.
    storedColumnsUnusedReasons: If stored columns were not used, explain why.
  """

  baseTable = _messages.MessageField('TableReference', 1)
  isQueryAccelerated = _messages.BooleanField(2)
  storedColumnsUnusedReasons = _messages.MessageField('StoredColumnsUnusedReason', 3, repeated=True)


class Streamingbuffer(_messages.Message):
  r"""A Streamingbuffer object.

  Fields:
    estimatedBytes: Output only. A lower-bound estimate of the number of bytes
      currently in the streaming buffer.
    estimatedRows: Output only. A lower-bound estimate of the number of rows
      currently in the streaming buffer.
    oldestEntryTime: Output only. Contains the timestamp of the oldest entry
      in the streaming buffer, in milliseconds since the epoch, if the
      streaming buffer is available.
  """

  estimatedBytes = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  estimatedRows = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  oldestEntryTime = _messages.IntegerField(3, variant=_messages.Variant.UINT64)


class StringHparamSearchSpace(_messages.Message):
  r"""Search space for string and enum.

  Fields:
    candidates: Canididates for the string or enum parameter in lower case.
  """

  candidates = _messages.StringField(1, repeated=True)


class SystemVariables(_messages.Message):
  r"""System variables given to a query.

  Messages:
    TypesValue: Output only. Data type for each system variable.
    ValuesValue: Output only. Value for each system variable.

  Fields:
    types: Output only. Data type for each system variable.
    values: Output only. Value for each system variable.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TypesValue(_messages.Message):
    r"""Output only. Data type for each system variable.

    Messages:
      AdditionalProperty: An additional property for a TypesValue object.

    Fields:
      additionalProperties: Additional properties of type TypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TypesValue object.

      Fields:
        key: Name of the additional property.
        value: A StandardSqlDataType attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('StandardSqlDataType', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Output only. Value for each system variable.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  types = _messages.MessageField('TypesValue', 1)
  values = _messages.MessageField('ValuesValue', 2)


class Table(_messages.Message):
  r"""A Table object.

  Enums:
    DefaultRoundingModeValueValuesEnum: Optional. Defines the default rounding
      mode specification of new decimal fields (NUMERIC OR BIGNUMERIC) in the
      table. During table creation or update, if a decimal field is added to
      this table without an explicit rounding mode specified, then the field
      inherits the table default rounding mode. Changing this field doesn't
      affect existing fields.
    ManagedTableTypeValueValuesEnum: Optional. If set, overrides the default
      managed table type configured in the dataset.

  Messages:
    LabelsValue: The labels associated with this table. You can use these to
      organize and group your tables. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.
    ResourceTagsValue: [Optional] The tags associated with this table. Tag
      keys are globally unique. See additional information on
      [tags](https://cloud.google.com/iam/docs/tags-access-
      control#definitions). An object containing a list of "key": value pairs.
      The key is the namespaced friendly name of the tag key, e.g.
      "12345/environment" where 12345 is parent id. The value is the friendly
      short name of the tag value, e.g. "production".

  Fields:
    biglakeConfiguration: Optional. Specifies the configuration of a BigQuery
      table for Apache Iceberg.
    cloneDefinition: Output only. Contains information about the clone. This
      value is set via the clone operation.
    clustering: Clustering specification for the table. Must be specified with
      time-based partitioning, data in the table will be first partitioned and
      subsequently clustered.
    creationTime: Output only. The time when this table was created, in
      milliseconds since the epoch.
    defaultCollation: Optional. Defines the default collation specification of
      new STRING fields in the table. During table creation or update, if a
      STRING field is added to this table without explicit collation
      specified, then the table inherits the table default collation. A change
      to this field affects only fields added afterwards, and does not alter
      the existing fields. The following values are supported: * 'und:ci':
      undetermined locale, case insensitive. * '': empty string. Default to
      case-sensitive behavior.
    defaultRoundingMode: Optional. Defines the default rounding mode
      specification of new decimal fields (NUMERIC OR BIGNUMERIC) in the
      table. During table creation or update, if a decimal field is added to
      this table without an explicit rounding mode specified, then the field
      inherits the table default rounding mode. Changing this field doesn't
      affect existing fields.
    description: Optional. A user-friendly description of this table.
    encryptionConfiguration: Custom encryption configuration (e.g., Cloud KMS
      keys).
    etag: Output only. A hash of this resource.
    expirationTime: Optional. The time when this table expires, in
      milliseconds since the epoch. If not present, the table will persist
      indefinitely. Expired tables will be deleted and their storage
      reclaimed. The defaultTableExpirationMs property of the encapsulating
      dataset can be used to set a default expirationTime on newly created
      tables.
    externalCatalogTableOptions: Optional. Options defining open source
      compatible table.
    externalDataConfiguration: Optional. Describes the data format, location,
      and other properties of a table stored outside of BigQuery. By defining
      these properties, the data source can then be queried as if it were a
      standard BigQuery table.
    friendlyName: Optional. A descriptive name for this table.
    id: Output only. An opaque ID uniquely identifying the table.
    kind: The type of resource ID.
    labels: The labels associated with this table. You can use these to
      organize and group your tables. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.
    lastModifiedTime: Output only. The time when this table was last modified,
      in milliseconds since the epoch.
    location: Output only. The geographic location where the table resides.
      This value is inherited from the dataset.
    managedTableType: Optional. If set, overrides the default managed table
      type configured in the dataset.
    materializedView: Optional. The materialized view definition.
    materializedViewStatus: Output only. The materialized view status.
    maxStaleness: Optional. The maximum staleness of data that could be
      returned when the table (or stale MV) is queried. Staleness encoded as a
      string encoding of sql IntervalValue type.
    model: Deprecated.
    numActiveLogicalBytes: Output only. Number of logical bytes that are less
      than 90 days old.
    numActivePhysicalBytes: Output only. Number of physical bytes less than 90
      days old. This data is not kept in real time, and might be delayed by a
      few seconds to a few minutes.
    numBytes: Output only. The size of this table in logical bytes, excluding
      any data in the streaming buffer.
    numCurrentPhysicalBytes: Output only. Number of physical bytes used by
      current live data storage. This data is not kept in real time, and might
      be delayed by a few seconds to a few minutes.
    numLongTermBytes: Output only. The number of logical bytes in the table
      that are considered "long-term storage".
    numLongTermLogicalBytes: Output only. Number of logical bytes that are
      more than 90 days old.
    numLongTermPhysicalBytes: Output only. Number of physical bytes more than
      90 days old. This data is not kept in real time, and might be delayed by
      a few seconds to a few minutes.
    numPartitions: Output only. The number of partitions present in the table
      or materialized view. This data is not kept in real time, and might be
      delayed by a few seconds to a few minutes.
    numPhysicalBytes: Output only. The physical size of this table in bytes.
      This includes storage used for time travel.
    numRows: Output only. The number of rows of data in this table, excluding
      any data in the streaming buffer.
    numTimeTravelPhysicalBytes: Output only. Number of physical bytes used by
      time travel storage (deleted or changed data). This data is not kept in
      real time, and might be delayed by a few seconds to a few minutes.
    numTotalLogicalBytes: Output only. Total number of logical bytes in the
      table or materialized view.
    numTotalPhysicalBytes: Output only. The physical size of this table in
      bytes. This also includes storage used for time travel. This data is not
      kept in real time, and might be delayed by a few seconds to a few
      minutes.
    partitionDefinition: Optional. The partition information for all table
      formats, including managed partitioned tables, hive partitioned tables,
      iceberg partitioned, and metastore partitioned tables. This field is
      only populated for metastore partitioned tables. For other table
      formats, this is an output only field.
    rangePartitioning: If specified, configures range partitioning for this
      table.
    replicas: Optional. Output only. Table references of all replicas
      currently active on the table.
    requirePartitionFilter: Optional. If set to true, queries over this table
      require a partition filter that can be used for partition elimination to
      be specified.
    resourceTags: [Optional] The tags associated with this table. Tag keys are
      globally unique. See additional information on
      [tags](https://cloud.google.com/iam/docs/tags-access-
      control#definitions). An object containing a list of "key": value pairs.
      The key is the namespaced friendly name of the tag key, e.g.
      "12345/environment" where 12345 is parent id. The value is the friendly
      short name of the tag value, e.g. "production".
    restrictions: Optional. Output only. Restriction config for table. If set,
      restrict certain accesses on the table based on the config. See [Data
      egress](https://cloud.google.com/bigquery/docs/analytics-hub-
      introduction#data_egress) for more details.
    schema: Optional. Describes the schema of this table.
    selfLink: Output only. A URL that can be used to access this resource
      again.
    snapshotDefinition: Output only. Contains information about the snapshot.
      This value is set via snapshot creation.
    streamingBuffer: Output only. Contains information regarding this table's
      streaming buffer, if one is present. This field will be absent if the
      table is not being streamed to or if there is no data in the streaming
      buffer.
    tableConstraints: Optional. Tables Primary Key and Foreign Key information
    tableReference: Required. Reference describing the ID of this table.
    tableReplicationInfo: Optional. Table replication info for table created
      `AS REPLICA` DDL like: `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF
      src_mv`
    timePartitioning: If specified, configures time-based partitioning for
      this table.
    type: Output only. Describes the table type. The following values are
      supported: * `TABLE`: A normal BigQuery table. * `VIEW`: A virtual table
      defined by a SQL query. * `EXTERNAL`: A table that references data
      stored in an external storage system, such as Google Cloud Storage. *
      `MATERIALIZED_VIEW`: A precomputed view defined by a SQL query. *
      `SNAPSHOT`: An immutable BigQuery table that preserves the contents of a
      base table at a particular time. See additional information on [table
      snapshots](https://cloud.google.com/bigquery/docs/table-snapshots-
      intro). The default value is `TABLE`.
    view: Optional. The view definition.
  """

  class DefaultRoundingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the default rounding mode specification of new
    decimal fields (NUMERIC OR BIGNUMERIC) in the table. During table creation
    or update, if a decimal field is added to this table without an explicit
    rounding mode specified, then the field inherits the table default
    rounding mode. Changing this field doesn't affect existing fields.

    Values:
      ROUNDING_MODE_UNSPECIFIED: Unspecified will default to using
        ROUND_HALF_AWAY_FROM_ZERO.
      ROUND_HALF_AWAY_FROM_ZERO: ROUND_HALF_AWAY_FROM_ZERO rounds half values
        away from zero when applying precision and scale upon writing of
        NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1
        1.5, 1.6, 1.7, 1.8, 1.9 => 2
      ROUND_HALF_EVEN: ROUND_HALF_EVEN rounds half values to the nearest even
        value when applying precision and scale upon writing of NUMERIC and
        BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6,
        1.7, 1.8, 1.9 => 2 2.5 => 2
    """
    ROUNDING_MODE_UNSPECIFIED = 0
    ROUND_HALF_AWAY_FROM_ZERO = 1
    ROUND_HALF_EVEN = 2

  class ManagedTableTypeValueValuesEnum(_messages.Enum):
    r"""Optional. If set, overrides the default managed table type configured
    in the dataset.

    Values:
      MANAGED_TABLE_TYPE_UNSPECIFIED: No managed table type specified.
      NATIVE: The managed table is a native BigQuery table.
      ICEBERG: The managed table is a BigQuery table for Apache Iceberg.
    """
    MANAGED_TABLE_TYPE_UNSPECIFIED = 0
    NATIVE = 1
    ICEBERG = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this table. You can use these to organize
    and group your tables. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceTagsValue(_messages.Message):
    r"""[Optional] The tags associated with this table. Tag keys are globally
    unique. See additional information on
    [tags](https://cloud.google.com/iam/docs/tags-access-control#definitions).
    An object containing a list of "key": value pairs. The key is the
    namespaced friendly name of the tag key, e.g. "12345/environment" where
    12345 is parent id. The value is the friendly short name of the tag value,
    e.g. "production".

    Messages:
      AdditionalProperty: An additional property for a ResourceTagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  biglakeConfiguration = _messages.MessageField('BigLakeConfiguration', 1)
  cloneDefinition = _messages.MessageField('CloneDefinition', 2)
  clustering = _messages.MessageField('Clustering', 3)
  creationTime = _messages.IntegerField(4)
  defaultCollation = _messages.StringField(5)
  defaultRoundingMode = _messages.EnumField('DefaultRoundingModeValueValuesEnum', 6)
  description = _messages.StringField(7)
  encryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 8)
  etag = _messages.StringField(9)
  expirationTime = _messages.IntegerField(10)
  externalCatalogTableOptions = _messages.MessageField('ExternalCatalogTableOptions', 11)
  externalDataConfiguration = _messages.MessageField('ExternalDataConfiguration', 12)
  friendlyName = _messages.StringField(13)
  id = _messages.StringField(14)
  kind = _messages.StringField(15, default='bigquery#table')
  labels = _messages.MessageField('LabelsValue', 16)
  lastModifiedTime = _messages.IntegerField(17, variant=_messages.Variant.UINT64)
  location = _messages.StringField(18)
  managedTableType = _messages.EnumField('ManagedTableTypeValueValuesEnum', 19)
  materializedView = _messages.MessageField('MaterializedViewDefinition', 20)
  materializedViewStatus = _messages.MessageField('MaterializedViewStatus', 21)
  maxStaleness = _messages.StringField(22)
  model = _messages.MessageField('ModelDefinition', 23)
  numActiveLogicalBytes = _messages.IntegerField(24)
  numActivePhysicalBytes = _messages.IntegerField(25)
  numBytes = _messages.IntegerField(26)
  numCurrentPhysicalBytes = _messages.IntegerField(27)
  numLongTermBytes = _messages.IntegerField(28)
  numLongTermLogicalBytes = _messages.IntegerField(29)
  numLongTermPhysicalBytes = _messages.IntegerField(30)
  numPartitions = _messages.IntegerField(31)
  numPhysicalBytes = _messages.IntegerField(32)
  numRows = _messages.IntegerField(33, variant=_messages.Variant.UINT64)
  numTimeTravelPhysicalBytes = _messages.IntegerField(34)
  numTotalLogicalBytes = _messages.IntegerField(35)
  numTotalPhysicalBytes = _messages.IntegerField(36)
  partitionDefinition = _messages.MessageField('PartitioningDefinition', 37)
  rangePartitioning = _messages.MessageField('RangePartitioning', 38)
  replicas = _messages.MessageField('TableReference', 39, repeated=True)
  requirePartitionFilter = _messages.BooleanField(40, default=False)
  resourceTags = _messages.MessageField('ResourceTagsValue', 41)
  restrictions = _messages.MessageField('RestrictionConfig', 42)
  schema = _messages.MessageField('TableSchema', 43)
  selfLink = _messages.StringField(44)
  snapshotDefinition = _messages.MessageField('SnapshotDefinition', 45)
  streamingBuffer = _messages.MessageField('Streamingbuffer', 46)
  tableConstraints = _messages.MessageField('TableConstraints', 47)
  tableReference = _messages.MessageField('TableReference', 48)
  tableReplicationInfo = _messages.MessageField('TableReplicationInfo', 49)
  timePartitioning = _messages.MessageField('TimePartitioning', 50)
  type = _messages.StringField(51)
  view = _messages.MessageField('ViewDefinition', 52)


class TableCell(_messages.Message):
  r"""A TableCell object.

  Fields:
    v: A extra_types.JsonValue attribute.
  """

  v = _messages.MessageField('extra_types.JsonValue', 1)


class TableConstraints(_messages.Message):
  r"""The TableConstraints defines the primary key and foreign key.

  Messages:
    ForeignKeysValueListEntry: Represents a foreign key constraint on a
      table's columns.
    PrimaryKeyValue: Represents the primary key constraint on a table's
      columns.

  Fields:
    foreignKeys: Optional. Present only if the table has a foreign key. The
      foreign key is not enforced.
    primaryKey: Represents the primary key constraint on a table's columns.
  """

  class ForeignKeysValueListEntry(_messages.Message):
    r"""Represents a foreign key constraint on a table's columns.

    Messages:
      ColumnReferencesValueListEntry: The pair of the foreign key column and
        primary key column.
      ReferencedTableValue: A ReferencedTableValue object.

    Fields:
      columnReferences: Required. The columns that compose the foreign key.
      name: Optional. Set only if the foreign key constraint is named.
      referencedTable: A ReferencedTableValue attribute.
    """

    class ColumnReferencesValueListEntry(_messages.Message):
      r"""The pair of the foreign key column and primary key column.

      Fields:
        referencedColumn: Required. The column in the primary key that are
          referenced by the referencing_column.
        referencingColumn: Required. The column that composes the foreign key.
      """

      referencedColumn = _messages.StringField(1)
      referencingColumn = _messages.StringField(2)

    class ReferencedTableValue(_messages.Message):
      r"""A ReferencedTableValue object.

      Fields:
        datasetId: A string attribute.
        projectId: A string attribute.
        tableId: A string attribute.
      """

      datasetId = _messages.StringField(1)
      projectId = _messages.StringField(2)
      tableId = _messages.StringField(3)

    columnReferences = _messages.MessageField('ColumnReferencesValueListEntry', 1, repeated=True)
    name = _messages.StringField(2)
    referencedTable = _messages.MessageField('ReferencedTableValue', 3)

  class PrimaryKeyValue(_messages.Message):
    r"""Represents the primary key constraint on a table's columns.

    Fields:
      columns: Required. The columns that are composed of the primary key
        constraint.
    """

    columns = _messages.StringField(1, repeated=True)

  foreignKeys = _messages.MessageField('ForeignKeysValueListEntry', 1, repeated=True)
  primaryKey = _messages.MessageField('PrimaryKeyValue', 2)


class TableDataInsertAllRequest(_messages.Message):
  r"""Request for sending a single streaming insert.

  Messages:
    RowsValueListEntry: Data for a single insertion row.

  Fields:
    ignoreUnknownValues: Optional. Accept rows that contain values that do not
      match the schema. The unknown values are ignored. Default is false,
      which treats unknown values as errors.
    kind: Optional. The resource type of the response. The value is not
      checked at the backend. Historically, it has been set to
      "bigquery#tableDataInsertAllRequest" but you are not required to set it.
    rows: A RowsValueListEntry attribute.
    skipInvalidRows: Optional. Insert all valid rows of a request, even if
      invalid rows exist. The default value is false, which causes the entire
      request to fail if any invalid rows exist.
    templateSuffix: Optional. If specified, treats the destination table as a
      base template, and inserts the rows into an instance table named
      "{destination}{templateSuffix}". BigQuery will manage creation of the
      instance table, using the schema of the base template table. See
      https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-
      tables for considerations when working with templates tables.
    traceId: Optional. Unique request trace id. Used for debugging purposes
      only. It is case-sensitive, limited to up to 36 ASCII characters. A UUID
      is recommended.
  """

  class RowsValueListEntry(_messages.Message):
    r"""Data for a single insertion row.

    Fields:
      insertId: Insertion ID for best-effort deduplication. This feature is
        not recommended, and users seeking stronger insertion semantics are
        encouraged to use other mechanisms such as the BigQuery Write API.
      json: Data for a single row.
    """

    insertId = _messages.StringField(1)
    json = _messages.MessageField('JsonObject', 2)

  ignoreUnknownValues = _messages.BooleanField(1)
  kind = _messages.StringField(2, default='bigquery#tableDataInsertAllRequest')
  rows = _messages.MessageField('RowsValueListEntry', 3, repeated=True)
  skipInvalidRows = _messages.BooleanField(4)
  templateSuffix = _messages.StringField(5)
  traceId = _messages.StringField(6)


class TableDataInsertAllResponse(_messages.Message):
  r"""Describes the format of a streaming insert response.

  Messages:
    InsertErrorsValueListEntry: Error details about a single row's insertion.

  Fields:
    insertErrors: Describes specific errors encountered while processing the
      request.
    kind: Returns "bigquery#tableDataInsertAllResponse".
  """

  class InsertErrorsValueListEntry(_messages.Message):
    r"""Error details about a single row's insertion.

    Fields:
      errors: Error information for the row indicated by the index property.
      index: The index of the row that error applies to.
    """

    errors = _messages.MessageField('ErrorProto', 1, repeated=True)
    index = _messages.IntegerField(2, variant=_messages.Variant.UINT32)

  insertErrors = _messages.MessageField('InsertErrorsValueListEntry', 1, repeated=True)
  kind = _messages.StringField(2, default='bigquery#tableDataInsertAllResponse')


class TableDataList(_messages.Message):
  r"""A TableDataList object.

  Fields:
    etag: A hash of this page of results.
    kind: The resource type of the response.
    pageToken: A token used for paging results. Providing this token instead
      of the startIndex parameter can help you retrieve stable results when an
      underlying table is changing.
    rows: Rows of results.
    totalRows: Total rows of the entire table. In order to show default value
      0 we have to present it as string.
  """

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#tableDataList')
  pageToken = _messages.StringField(3)
  rows = _messages.MessageField('TableRow', 4, repeated=True)
  totalRows = _messages.IntegerField(5)


class TableFieldSchema(_messages.Message):
  r"""A field in TableSchema

  Enums:
    RoundingModeValueValuesEnum: Optional. Specifies the rounding mode to be
      used when storing values of NUMERIC and BIGNUMERIC type.

  Messages:
    CategoriesValue: Deprecated.
    PolicyTagsValue: Optional. The policy tags attached to this field, used
      for field-level access control. If not set, defaults to empty
      policy_tags.
    RangeElementTypeValue: Represents the type of a field element.

  Fields:
    categories: Deprecated.
    collation: Optional. Field collation can be set only when the type of
      field is STRING. The following values are supported: * 'und:ci':
      undetermined locale, case insensitive. * '': empty string. Default to
      case-sensitive behavior.
    dataPolicies: Optional. Data policy options, will replace the
      data_policies.
    defaultValueExpression: Optional. A SQL expression to specify the [default
      value] (https://cloud.google.com/bigquery/docs/default-values) for this
      field.
    description: Optional. The field description. The maximum length is 1,024
      characters.
    fields: Optional. Describes the nested schema fields if the type property
      is set to RECORD.
    foreignTypeDefinition: Optional. Definition of the foreign data type. Only
      valid for top-level schema fields (not nested fields). If the type is
      FOREIGN, this field is required.
    maxLength: Optional. Maximum length of values of this field for STRINGS or
      BYTES. If max_length is not specified, no maximum length constraint is
      imposed on this field. If type = "STRING", then max_length represents
      the maximum UTF-8 length of strings in this field. If type = "BYTES",
      then max_length represents the maximum number of bytes in this field. It
      is invalid to set this field if type \u2260 "STRING" and \u2260 "BYTES".
    mode: Optional. The field mode. Possible values include NULLABLE, REQUIRED
      and REPEATED. The default value is NULLABLE.
    name: Required. The field name. The name must contain only letters (a-z,
      A-Z), numbers (0-9), or underscores (_), and must start with a letter or
      underscore. The maximum length is 300 characters.
    policyTags: Optional. The policy tags attached to this field, used for
      field-level access control. If not set, defaults to empty policy_tags.
    precision: Optional. Precision (maximum number of total digits in base 10)
      and scale (maximum number of digits in the fractional part in base 10)
      constraints for values of this field for NUMERIC or BIGNUMERIC. It is
      invalid to set precision or scale if type \u2260 "NUMERIC" and \u2260
      "BIGNUMERIC". If precision and scale are not specified, no value range
      constraint is imposed on this field insofar as values are permitted by
      the type. Values of this NUMERIC or BIGNUMERIC field must be in this
      range when: * Precision (P) and scale (S) are specified: [-10P-S + 10-S,
      10P-S - 10-S] * Precision (P) is specified but not scale (and thus scale
      is interpreted to be equal to zero): [-10P + 1, 10P - 1]. Acceptable
      values for precision and scale if both are specified: * If type =
      "NUMERIC": 1 \u2264 precision - scale \u2264 29 and 0 \u2264 scale
      \u2264 9. * If type = "BIGNUMERIC": 1 \u2264 precision - scale \u2264 38
      and 0 \u2264 scale \u2264 38. Acceptable values for precision if only
      precision is specified but not scale (and thus scale is interpreted to
      be equal to zero): * If type = "NUMERIC": 1 \u2264 precision \u2264 29.
      * If type = "BIGNUMERIC": 1 \u2264 precision \u2264 38. If scale is
      specified but not precision, then it is invalid.
    rangeElementType: Represents the type of a field element.
    roundingMode: Optional. Specifies the rounding mode to be used when
      storing values of NUMERIC and BIGNUMERIC type.
    scale: Optional. See documentation for precision.
    type: Required. The field data type. Possible values include: * STRING *
      BYTES * INTEGER (or INT64) * FLOAT (or FLOAT64) * BOOLEAN (or BOOL) *
      TIMESTAMP * DATE * TIME * DATETIME * GEOGRAPHY * NUMERIC * BIGNUMERIC *
      JSON * RECORD (or STRUCT) * RANGE Use of RECORD/STRUCT indicates that
      the field contains a nested schema.
  """

  class RoundingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the rounding mode to be used when storing values
    of NUMERIC and BIGNUMERIC type.

    Values:
      ROUNDING_MODE_UNSPECIFIED: Unspecified will default to using
        ROUND_HALF_AWAY_FROM_ZERO.
      ROUND_HALF_AWAY_FROM_ZERO: ROUND_HALF_AWAY_FROM_ZERO rounds half values
        away from zero when applying precision and scale upon writing of
        NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1
        1.5, 1.6, 1.7, 1.8, 1.9 => 2
      ROUND_HALF_EVEN: ROUND_HALF_EVEN rounds half values to the nearest even
        value when applying precision and scale upon writing of NUMERIC and
        BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6,
        1.7, 1.8, 1.9 => 2 2.5 => 2
    """
    ROUNDING_MODE_UNSPECIFIED = 0
    ROUND_HALF_AWAY_FROM_ZERO = 1
    ROUND_HALF_EVEN = 2

  class CategoriesValue(_messages.Message):
    r"""Deprecated.

    Fields:
      names: Deprecated.
    """

    names = _messages.StringField(1, repeated=True)

  class PolicyTagsValue(_messages.Message):
    r"""Optional. The policy tags attached to this field, used for field-level
    access control. If not set, defaults to empty policy_tags.

    Fields:
      names: A list of policy tag resource names. For example,
        "projects/1/locations/eu/taxonomies/2/policyTags/3". At most 1 policy
        tag is currently allowed.
    """

    names = _messages.StringField(1, repeated=True)

  class RangeElementTypeValue(_messages.Message):
    r"""Represents the type of a field element.

    Fields:
      type: Required. The type of a field element. For more information, see
        TableFieldSchema.type.
    """

    type = _messages.StringField(1)

  categories = _messages.MessageField('CategoriesValue', 1)
  collation = _messages.StringField(2)
  dataPolicies = _messages.MessageField('DataPolicyOption', 3, repeated=True)
  defaultValueExpression = _messages.StringField(4)
  description = _messages.StringField(5)
  fields = _messages.MessageField('TableFieldSchema', 6, repeated=True)
  foreignTypeDefinition = _messages.StringField(7)
  maxLength = _messages.IntegerField(8)
  mode = _messages.StringField(9)
  name = _messages.StringField(10)
  policyTags = _messages.MessageField('PolicyTagsValue', 11)
  precision = _messages.IntegerField(12)
  rangeElementType = _messages.MessageField('RangeElementTypeValue', 13)
  roundingMode = _messages.EnumField('RoundingModeValueValuesEnum', 14)
  scale = _messages.IntegerField(15)
  type = _messages.StringField(16)


class TableList(_messages.Message):
  r"""Partial projection of the metadata for a given table in a list response.

  Messages:
    TablesValueListEntry: A TablesValueListEntry object.

  Fields:
    etag: A hash of this page of results.
    kind: The type of list.
    nextPageToken: A token to request the next page of results.
    tables: Tables in the requested dataset.
    totalItems: The total number of tables in the dataset.
  """

  class TablesValueListEntry(_messages.Message):
    r"""A TablesValueListEntry object.

    Messages:
      LabelsValue: The labels associated with this table. You can use these to
        organize and group your tables.
      ViewValue: Information about a logical view.

    Fields:
      clustering: Clustering specification for this table, if configured.
      creationTime: Output only. The time when this table was created, in
        milliseconds since the epoch.
      expirationTime: The time when this table expires, in milliseconds since
        the epoch. If not present, the table will persist indefinitely.
        Expired tables will be deleted and their storage reclaimed.
      friendlyName: The user-friendly name for this table.
      id: An opaque ID of the table.
      kind: The resource type.
      labels: The labels associated with this table. You can use these to
        organize and group your tables.
      rangePartitioning: The range partitioning for this table.
      requirePartitionFilter: Optional. If set to true, queries including this
        table must specify a partition filter. This filter is used for
        partition elimination.
      tableReference: A reference uniquely identifying table.
      timePartitioning: The time-based partitioning for this table.
      type: The type of table.
      view: Information about a logical view.
    """

    @encoding.MapUnrecognizedFields('additionalProperties')
    class LabelsValue(_messages.Message):
      r"""The labels associated with this table. You can use these to organize
      and group your tables.

      Messages:
        AdditionalProperty: An additional property for a LabelsValue object.

      Fields:
        additionalProperties: Additional properties of type LabelsValue
      """

      class AdditionalProperty(_messages.Message):
        r"""An additional property for a LabelsValue object.

        Fields:
          key: Name of the additional property.
          value: A string attribute.
        """

        key = _messages.StringField(1)
        value = _messages.StringField(2)

      additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

    class ViewValue(_messages.Message):
      r"""Information about a logical view.

      Fields:
        privacyPolicy: Specifies the privacy policy for the view.
        useLegacySql: True if view is defined in legacy SQL dialect, false if
          in GoogleSQL.
      """

      privacyPolicy = _messages.MessageField('PrivacyPolicy', 1)
      useLegacySql = _messages.BooleanField(2)

    clustering = _messages.MessageField('Clustering', 1)
    creationTime = _messages.IntegerField(2)
    expirationTime = _messages.IntegerField(3)
    friendlyName = _messages.StringField(4)
    id = _messages.StringField(5)
    kind = _messages.StringField(6)
    labels = _messages.MessageField('LabelsValue', 7)
    rangePartitioning = _messages.MessageField('RangePartitioning', 8)
    requirePartitionFilter = _messages.BooleanField(9, default=False)
    tableReference = _messages.MessageField('TableReference', 10)
    timePartitioning = _messages.MessageField('TimePartitioning', 11)
    type = _messages.StringField(12)
    view = _messages.MessageField('ViewValue', 13)

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#tableList')
  nextPageToken = _messages.StringField(3)
  tables = _messages.MessageField('TablesValueListEntry', 4, repeated=True)
  totalItems = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class TableMetadataCacheUsage(_messages.Message):
  r"""Table level detail on the usage of metadata caching. Only set for
  Metadata caching eligible tables referenced in the query.

  Enums:
    UnusedReasonValueValuesEnum: Reason for not using metadata caching for the
      table.

  Fields:
    explanation: Free form human-readable reason metadata caching was unused
      for the job.
    staleness: Duration since last refresh as of this job for managed tables
      (indicates metadata cache staleness as seen by this job).
    tableReference: Metadata caching eligible table referenced in the query.
    tableType: [Table type](https://cloud.google.com/bigquery/docs/reference/r
      est/v2/tables#Table.FIELDS.type).
    unusedReason: Reason for not using metadata caching for the table.
  """

  class UnusedReasonValueValuesEnum(_messages.Enum):
    r"""Reason for not using metadata caching for the table.

    Values:
      UNUSED_REASON_UNSPECIFIED: Unused reasons not specified.
      EXCEEDED_MAX_STALENESS: Metadata cache was outside the table's
        maxStaleness.
      METADATA_CACHING_NOT_ENABLED: Metadata caching feature is not enabled.
        [Update BigLake tables] (/bigquery/docs/create-cloud-storage-table-
        biglake#update-biglake-tables) to enable the metadata caching.
      OTHER_REASON: Other unknown reason.
    """
    UNUSED_REASON_UNSPECIFIED = 0
    EXCEEDED_MAX_STALENESS = 1
    METADATA_CACHING_NOT_ENABLED = 2
    OTHER_REASON = 3

  explanation = _messages.StringField(1)
  staleness = _messages.StringField(2)
  tableReference = _messages.MessageField('TableReference', 3)
  tableType = _messages.StringField(4)
  unusedReason = _messages.EnumField('UnusedReasonValueValuesEnum', 5)


class TableReference(_messages.Message):
  r"""A TableReference object.

  Fields:
    datasetId: Required. The ID of the dataset containing this table.
    projectId: Required. The ID of the project containing this table.
    tableId: Required. The ID of the table. The ID can contain Unicode
      characters in category L (letter), M (mark), N (number), Pc (connector,
      including underscore), Pd (dash), and Zs (space). For more information,
      see [General Category](https://wikipedia.org/wiki/Unicode_character_prop
      erty#General_Category). The maximum length is 1,024 characters. Certain
      operations allow suffixing of the table ID with a partition decorator,
      such as `sample_table$20190123`.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)
  tableId = _messages.StringField(3)


class TableReplicationInfo(_messages.Message):
  r"""Replication info of a table created using `AS REPLICA` DDL like: `CREATE
  MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`

  Enums:
    ReplicationStatusValueValuesEnum: Optional. Output only. Replication
      status of configured replication.

  Fields:
    replicatedSourceLastRefreshTime: Optional. Output only. If source is a
      materialized view, this field signifies the last refresh time of the
      source.
    replicationError: Optional. Output only. Replication error that will
      permanently stopped table replication.
    replicationIntervalMs: Optional. Specifies the interval at which the
      source table is polled for updates. It's Optional. If not specified,
      default replication interval would be applied.
    replicationStatus: Optional. Output only. Replication status of configured
      replication.
    sourceTable: Required. Source table reference that is replicated.
  """

  class ReplicationStatusValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. Replication status of configured replication.

    Values:
      REPLICATION_STATUS_UNSPECIFIED: Default value.
      ACTIVE: Replication is Active with no errors.
      SOURCE_DELETED: Source object is deleted.
      PERMISSION_DENIED: Source revoked replication permissions.
      UNSUPPORTED_CONFIGURATION: Source configuration doesn't allow
        replication.
    """
    REPLICATION_STATUS_UNSPECIFIED = 0
    ACTIVE = 1
    SOURCE_DELETED = 2
    PERMISSION_DENIED = 3
    UNSUPPORTED_CONFIGURATION = 4

  replicatedSourceLastRefreshTime = _messages.IntegerField(1)
  replicationError = _messages.MessageField('ErrorProto', 2)
  replicationIntervalMs = _messages.IntegerField(3)
  replicationStatus = _messages.EnumField('ReplicationStatusValueValuesEnum', 4)
  sourceTable = _messages.MessageField('TableReference', 5)


class TableRow(_messages.Message):
  r"""A TableRow object.

  Fields:
    f: Represents a single row in the result set, consisting of one or more
      fields.
  """

  f = _messages.MessageField('TableCell', 1, repeated=True)


class TableSchema(_messages.Message):
  r"""Schema of a table

  Fields:
    fields: Describes the fields in a table.
    foreignTypeInfo: Optional. Specifies metadata of the foreign data type
      definition in field schema (TableFieldSchema.foreign_type_definition).
  """

  fields = _messages.MessageField('TableFieldSchema', 1, repeated=True)
  foreignTypeInfo = _messages.MessageField('ForeignTypeInfo', 2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimePartitioning(_messages.Message):
  r"""A TimePartitioning object.

  Fields:
    expirationMs: Optional. Number of milliseconds for which to keep the
      storage for a partition. A wrapper is used here because 0 is an invalid
      value.
    field: Optional. If not set, the table is partitioned by pseudo column
      '_PARTITIONTIME'; if set, the table is partitioned by this field. The
      field must be a top-level TIMESTAMP or DATE field. Its mode must be
      NULLABLE or REQUIRED. A wrapper is used here because an empty string is
      an invalid value.
    requirePartitionFilter: If set to true, queries over this table require a
      partition filter that can be used for partition elimination to be
      specified. This field is deprecated; please set the field with the same
      name on the table itself instead. This field needs a wrapper because we
      want to output the default value, false, if the user explicitly set it.
    type: Required. The supported types are DAY, HOUR, MONTH, and YEAR, which
      will generate one partition per day, hour, month, and year,
      respectively.
  """

  expirationMs = _messages.IntegerField(1)
  field = _messages.StringField(2)
  requirePartitionFilter = _messages.BooleanField(3, default=False)
  type = _messages.StringField(4)


class TrainingOptions(_messages.Message):
  r"""Options used in model training.

  Enums:
    BoosterTypeValueValuesEnum: Booster type for boosted tree models.
    CategoryEncodingMethodValueValuesEnum: Categorical feature encoding
      method.
    ColorSpaceValueValuesEnum: Enums for color space, used for processing
      images in Object Table. See more details at
      https://www.tensorflow.org/io/tutorials/colorspace.
    DartNormalizeTypeValueValuesEnum: Type of normalization algorithm for
      boosted tree models using dart booster.
    DataFrequencyValueValuesEnum: The data frequency of a time series.
    DataSplitMethodValueValuesEnum: The data split type for training and
      evaluation, e.g. RANDOM.
    DistanceTypeValueValuesEnum: Distance type for clustering models.
    FeedbackTypeValueValuesEnum: Feedback type that specifies which algorithm
      to run for matrix factorization.
    HolidayRegionValueValuesEnum: The geographical region based on which the
      holidays are considered in time series modeling. If a valid value is
      specified, then holiday effects modeling is enabled.
    HolidayRegionsValueListEntryValuesEnum:
    HparamTuningObjectivesValueListEntryValuesEnum:
    KmeansInitializationMethodValueValuesEnum: The method used to initialize
      the centroids for kmeans algorithm.
    LearnRateStrategyValueValuesEnum: The strategy to determine learn rate for
      the current iteration.
    LossTypeValueValuesEnum: Type of loss function used during training run.
    ModelRegistryValueValuesEnum: The model registry.
    OptimizationStrategyValueValuesEnum: Optimization strategy for training
      linear regression models.
    PcaSolverValueValuesEnum: The solver for PCA.
    TreeMethodValueValuesEnum: Tree construction algorithm for boosted tree
      models.

  Messages:
    LabelClassWeightsValue: Weights associated with each label class, for
      rebalancing the training data. Only applicable for classification
      models.

  Fields:
    activationFn: Activation function of the neural nets.
    adjustStepChanges: If true, detect step changes and make data adjustment
      in the input time series.
    approxGlobalFeatureContrib: Whether to use approximate feature
      contribution method in XGBoost model explanation for global explain.
    autoArima: Whether to enable auto ARIMA or not.
    autoArimaMaxOrder: The max value of the sum of non-seasonal p and q.
    autoArimaMinOrder: The min value of the sum of non-seasonal p and q.
    autoClassWeights: Whether to calculate class weights automatically based
      on the popularity of each label.
    batchSize: Batch size for dnn models.
    boosterType: Booster type for boosted tree models.
    budgetHours: Budget in hours for AutoML training.
    calculatePValues: Whether or not p-value test should be computed for this
      model. Only available for linear and logistic regression models.
    categoryEncodingMethod: Categorical feature encoding method.
    cleanSpikesAndDips: If true, clean spikes and dips in the input time
      series.
    colorSpace: Enums for color space, used for processing images in Object
      Table. See more details at
      https://www.tensorflow.org/io/tutorials/colorspace.
    colsampleBylevel: Subsample ratio of columns for each level for boosted
      tree models.
    colsampleBynode: Subsample ratio of columns for each node(split) for
      boosted tree models.
    colsampleBytree: Subsample ratio of columns when constructing each tree
      for boosted tree models.
    contributionMetric: The contribution metric. Applies to contribution
      analysis models. Allowed formats supported are for summable and summable
      ratio contribution metrics. These include expressions such as `SUM(x)`
      or `SUM(x)/SUM(y)`, where x and y are column names from the base table.
    dartNormalizeType: Type of normalization algorithm for boosted tree models
      using dart booster.
    dataFrequency: The data frequency of a time series.
    dataSplitColumn: The column to split data with. This column won't be used
      as a feature. 1. When data_split_method is CUSTOM, the corresponding
      column should be boolean. The rows with true value tag are eval data,
      and the false are training data. 2. When data_split_method is SEQ, the
      first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the
      corresponding column are used as training data, and the rest are eval
      data. It respects the order in Orderable data types:
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      types#data_type_properties
    dataSplitEvalFraction: The fraction of evaluation data over the whole
      input data. The rest of data will be used as training data. The format
      should be double. Accurate to two decimal places. Default value is 0.2.
    dataSplitMethod: The data split type for training and evaluation, e.g.
      RANDOM.
    decomposeTimeSeries: If true, perform decompose time series and save the
      results.
    dimensionIdColumns: Optional. Names of the columns to slice on. Applies to
      contribution analysis models.
    distanceType: Distance type for clustering models.
    dropout: Dropout probability for dnn models.
    earlyStop: Whether to stop early when the loss doesn't improve
      significantly any more (compared to min_relative_progress). Used only
      for iterative training algorithms.
    enableGlobalExplain: If true, enable global explanation during training.
    feedbackType: Feedback type that specifies which algorithm to run for
      matrix factorization.
    fitIntercept: Whether the model should include intercept during model
      training.
    forecastLimitLowerBound: The forecast limit lower bound that was used
      during ARIMA model training with limits. To see more details of the
      algorithm: https://otexts.com/fpp2/limits.html
    forecastLimitUpperBound: The forecast limit upper bound that was used
      during ARIMA model training with limits.
    hiddenUnits: Hidden units for dnn models.
    holidayRegion: The geographical region based on which the holidays are
      considered in time series modeling. If a valid value is specified, then
      holiday effects modeling is enabled.
    holidayRegions: A list of geographical regions that are used for time
      series modeling.
    horizon: The number of periods ahead that need to be forecasted.
    hparamTuningObjectives: The target evaluation metrics to optimize the
      hyperparameters for.
    includeDrift: Include drift when fitting an ARIMA model.
    initialLearnRate: Specifies the initial learning rate for the line search
      learn rate strategy.
    inputLabelColumns: Name of input label columns in training data.
    instanceWeightColumn: Name of the instance weight column for training
      data. This column isn't be used as a feature.
    integratedGradientsNumSteps: Number of integral steps for the integrated
      gradients explain method.
    isTestColumn: Name of the column used to determine the rows corresponding
      to control and test. Applies to contribution analysis models.
    itemColumn: Item column specified for matrix factorization models.
    kmeansInitializationColumn: The column used to provide the initial
      centroids for kmeans algorithm when kmeans_initialization_method is
      CUSTOM.
    kmeansInitializationMethod: The method used to initialize the centroids
      for kmeans algorithm.
    l1RegActivation: L1 regularization coefficient to activations.
    l1Regularization: L1 regularization coefficient.
    l2Regularization: L2 regularization coefficient.
    labelClassWeights: Weights associated with each label class, for
      rebalancing the training data. Only applicable for classification
      models.
    learnRate: Learning rate in training. Used only for iterative training
      algorithms.
    learnRateStrategy: The strategy to determine learn rate for the current
      iteration.
    lossType: Type of loss function used during training run.
    maxIterations: The maximum number of iterations in training. Used only for
      iterative training algorithms.
    maxParallelTrials: Maximum number of trials to run in parallel.
    maxTimeSeriesLength: The maximum number of time points in a time series
      that can be used in modeling the trend component of the time series.
      Don't use this option with the `timeSeriesLengthFraction` or
      `minTimeSeriesLength` options.
    maxTreeDepth: Maximum depth of a tree for boosted tree models.
    minAprioriSupport: The apriori support minimum. Applies to contribution
      analysis models.
    minRelativeProgress: When early_stop is true, stops training when accuracy
      improvement is less than 'min_relative_progress'. Used only for
      iterative training algorithms.
    minSplitLoss: Minimum split loss for boosted tree models.
    minTimeSeriesLength: The minimum number of time points in a time series
      that are used in modeling the trend component of the time series. If you
      use this option you must also set the `timeSeriesLengthFraction` option.
      This training option ensures that enough time points are available when
      you use `timeSeriesLengthFraction` in trend modeling. This is
      particularly important when forecasting multiple time series in a single
      query using `timeSeriesIdColumn`. If the total number of time points is
      less than the `minTimeSeriesLength` value, then the query uses all
      available time points.
    minTreeChildWeight: Minimum sum of instance weight needed in a child for
      boosted tree models.
    modelRegistry: The model registry.
    modelUri: Google Cloud Storage URI from which the model was imported. Only
      applicable for imported models.
    nonSeasonalOrder: A specification of the non-seasonal part of the ARIMA
      model: the three components (p, d, q) are the AR order, the degree of
      differencing, and the MA order.
    numClusters: Number of clusters for clustering models.
    numFactors: Num factors specified for matrix factorization models.
    numParallelTree: Number of parallel trees constructed during each
      iteration for boosted tree models.
    numPrincipalComponents: Number of principal components to keep in the PCA
      model. Must be <= the number of features.
    numTrials: Number of trials to run this hyperparameter tuning job.
    optimizationStrategy: Optimization strategy for training linear regression
      models.
    optimizer: Optimizer used for training the neural nets.
    pcaExplainedVarianceRatio: The minimum ratio of cumulative explained
      variance that needs to be given by the PCA model.
    pcaSolver: The solver for PCA.
    sampledShapleyNumPaths: Number of paths for the sampled Shapley explain
      method.
    scaleFeatures: If true, scale the feature values by dividing the feature
      standard deviation. Currently only apply to PCA.
    standardizeFeatures: Whether to standardize numerical features. Default to
      true.
    subsample: Subsample fraction of the training data to grow tree to prevent
      overfitting for boosted tree models.
    tfVersion: Based on the selected TF version, the corresponding docker
      image is used to train external models.
    timeSeriesDataColumn: Column to be designated as time series data for
      ARIMA model.
    timeSeriesIdColumn: The time series id column that was used during ARIMA
      model training.
    timeSeriesIdColumns: The time series id columns that were used during
      ARIMA model training.
    timeSeriesLengthFraction: The fraction of the interpolated length of the
      time series that's used to model the time series trend component. All of
      the time points of the time series are used to model the non-trend
      component. This training option accelerates modeling training without
      sacrificing much forecasting accuracy. You can use this option with
      `minTimeSeriesLength` but not with `maxTimeSeriesLength`.
    timeSeriesTimestampColumn: Column to be designated as time series
      timestamp for ARIMA model.
    treeMethod: Tree construction algorithm for boosted tree models.
    trendSmoothingWindowSize: Smoothing window size for the trend component.
      When a positive value is specified, a center moving average smoothing is
      applied on the history trend. When the smoothing window is out of the
      boundary at the beginning or the end of the trend, the first element or
      the last element is padded to fill the smoothing window before the
      average is applied.
    userColumn: User column specified for matrix factorization models.
    vertexAiModelVersionAliases: The version aliases to apply in Vertex AI
      model registry. Always overwrite if the version aliases exists in a
      existing model.
    walsAlpha: Hyperparameter for matrix factoration when implicit feedback
      type is specified.
    warmStart: Whether to train a model from the last checkpoint.
    xgboostVersion: User-selected XGBoost versions for training of XGBoost
      models.
  """

  class BoosterTypeValueValuesEnum(_messages.Enum):
    r"""Booster type for boosted tree models.

    Values:
      BOOSTER_TYPE_UNSPECIFIED: Unspecified booster type.
      GBTREE: Gbtree booster.
      DART: Dart booster.
    """
    BOOSTER_TYPE_UNSPECIFIED = 0
    GBTREE = 1
    DART = 2

  class CategoryEncodingMethodValueValuesEnum(_messages.Enum):
    r"""Categorical feature encoding method.

    Values:
      ENCODING_METHOD_UNSPECIFIED: Unspecified encoding method.
      ONE_HOT_ENCODING: Applies one-hot encoding.
      LABEL_ENCODING: Applies label encoding.
      DUMMY_ENCODING: Applies dummy encoding.
    """
    ENCODING_METHOD_UNSPECIFIED = 0
    ONE_HOT_ENCODING = 1
    LABEL_ENCODING = 2
    DUMMY_ENCODING = 3

  class ColorSpaceValueValuesEnum(_messages.Enum):
    r"""Enums for color space, used for processing images in Object Table. See
    more details at https://www.tensorflow.org/io/tutorials/colorspace.

    Values:
      COLOR_SPACE_UNSPECIFIED: Unspecified color space
      RGB: RGB
      HSV: HSV
      YIQ: YIQ
      YUV: YUV
      GRAYSCALE: GRAYSCALE
    """
    COLOR_SPACE_UNSPECIFIED = 0
    RGB = 1
    HSV = 2
    YIQ = 3
    YUV = 4
    GRAYSCALE = 5

  class DartNormalizeTypeValueValuesEnum(_messages.Enum):
    r"""Type of normalization algorithm for boosted tree models using dart
    booster.

    Values:
      DART_NORMALIZE_TYPE_UNSPECIFIED: Unspecified dart normalize type.
      TREE: New trees have the same weight of each of dropped trees.
      FOREST: New trees have the same weight of sum of dropped trees.
    """
    DART_NORMALIZE_TYPE_UNSPECIFIED = 0
    TREE = 1
    FOREST = 2

  class DataFrequencyValueValuesEnum(_messages.Enum):
    r"""The data frequency of a time series.

    Values:
      DATA_FREQUENCY_UNSPECIFIED: Default value.
      AUTO_FREQUENCY: Automatically inferred from timestamps.
      YEARLY: Yearly data.
      QUARTERLY: Quarterly data.
      MONTHLY: Monthly data.
      WEEKLY: Weekly data.
      DAILY: Daily data.
      HOURLY: Hourly data.
      PER_MINUTE: Per-minute data.
    """
    DATA_FREQUENCY_UNSPECIFIED = 0
    AUTO_FREQUENCY = 1
    YEARLY = 2
    QUARTERLY = 3
    MONTHLY = 4
    WEEKLY = 5
    DAILY = 6
    HOURLY = 7
    PER_MINUTE = 8

  class DataSplitMethodValueValuesEnum(_messages.Enum):
    r"""The data split type for training and evaluation, e.g. RANDOM.

    Values:
      DATA_SPLIT_METHOD_UNSPECIFIED: Default value.
      RANDOM: Splits data randomly.
      CUSTOM: Splits data with the user provided tags.
      SEQUENTIAL: Splits data sequentially.
      NO_SPLIT: Data split will be skipped.
      AUTO_SPLIT: Splits data automatically: Uses NO_SPLIT if the data size is
        small. Otherwise uses RANDOM.
    """
    DATA_SPLIT_METHOD_UNSPECIFIED = 0
    RANDOM = 1
    CUSTOM = 2
    SEQUENTIAL = 3
    NO_SPLIT = 4
    AUTO_SPLIT = 5

  class DistanceTypeValueValuesEnum(_messages.Enum):
    r"""Distance type for clustering models.

    Values:
      DISTANCE_TYPE_UNSPECIFIED: Default value.
      EUCLIDEAN: Eculidean distance.
      COSINE: Cosine distance.
    """
    DISTANCE_TYPE_UNSPECIFIED = 0
    EUCLIDEAN = 1
    COSINE = 2

  class FeedbackTypeValueValuesEnum(_messages.Enum):
    r"""Feedback type that specifies which algorithm to run for matrix
    factorization.

    Values:
      FEEDBACK_TYPE_UNSPECIFIED: Default value.
      IMPLICIT: Use weighted-als for implicit feedback problems.
      EXPLICIT: Use nonweighted-als for explicit feedback problems.
    """
    FEEDBACK_TYPE_UNSPECIFIED = 0
    IMPLICIT = 1
    EXPLICIT = 2

  class HolidayRegionValueValuesEnum(_messages.Enum):
    r"""The geographical region based on which the holidays are considered in
    time series modeling. If a valid value is specified, then holiday effects
    modeling is enabled.

    Values:
      HOLIDAY_REGION_UNSPECIFIED: Holiday region unspecified.
      GLOBAL: Global.
      NA: North America.
      JAPAC: Japan and Asia Pacific: Korea, Greater China, India, Australia,
        and New Zealand.
      EMEA: Europe, the Middle East and Africa.
      LAC: Latin America and the Caribbean.
      AE: United Arab Emirates
      AR: Argentina
      AT: Austria
      AU: Australia
      BE: Belgium
      BR: Brazil
      CA: Canada
      CH: Switzerland
      CL: Chile
      CN: China
      CO: Colombia
      CS: Czechoslovakia
      CZ: Czech Republic
      DE: Germany
      DK: Denmark
      DZ: Algeria
      EC: Ecuador
      EE: Estonia
      EG: Egypt
      ES: Spain
      FI: Finland
      FR: France
      GB: Great Britain (United Kingdom)
      GR: Greece
      HK: Hong Kong
      HU: Hungary
      ID: Indonesia
      IE: Ireland
      IL: Israel
      IN: India
      IR: Iran
      IT: Italy
      JP: Japan
      KR: Korea (South)
      LV: Latvia
      MA: Morocco
      MX: Mexico
      MY: Malaysia
      NG: Nigeria
      NL: Netherlands
      NO: Norway
      NZ: New Zealand
      PE: Peru
      PH: Philippines
      PK: Pakistan
      PL: Poland
      PT: Portugal
      RO: Romania
      RS: Serbia
      RU: Russian Federation
      SA: Saudi Arabia
      SE: Sweden
      SG: Singapore
      SI: Slovenia
      SK: Slovakia
      TH: Thailand
      TR: Turkey
      TW: Taiwan
      UA: Ukraine
      US: United States
      VE: Venezuela
      VN: Vietnam
      ZA: South Africa
    """
    HOLIDAY_REGION_UNSPECIFIED = 0
    GLOBAL = 1
    NA = 2
    JAPAC = 3
    EMEA = 4
    LAC = 5
    AE = 6
    AR = 7
    AT = 8
    AU = 9
    BE = 10
    BR = 11
    CA = 12
    CH = 13
    CL = 14
    CN = 15
    CO = 16
    CS = 17
    CZ = 18
    DE = 19
    DK = 20
    DZ = 21
    EC = 22
    EE = 23
    EG = 24
    ES = 25
    FI = 26
    FR = 27
    GB = 28
    GR = 29
    HK = 30
    HU = 31
    ID = 32
    IE = 33
    IL = 34
    IN = 35
    IR = 36
    IT = 37
    JP = 38
    KR = 39
    LV = 40
    MA = 41
    MX = 42
    MY = 43
    NG = 44
    NL = 45
    NO = 46
    NZ = 47
    PE = 48
    PH = 49
    PK = 50
    PL = 51
    PT = 52
    RO = 53
    RS = 54
    RU = 55
    SA = 56
    SE = 57
    SG = 58
    SI = 59
    SK = 60
    TH = 61
    TR = 62
    TW = 63
    UA = 64
    US = 65
    VE = 66
    VN = 67
    ZA = 68

  class HolidayRegionsValueListEntryValuesEnum(_messages.Enum):
    r"""HolidayRegionsValueListEntryValuesEnum enum type.

    Values:
      HOLIDAY_REGION_UNSPECIFIED: Holiday region unspecified.
      GLOBAL: Global.
      NA: North America.
      JAPAC: Japan and Asia Pacific: Korea, Greater China, India, Australia,
        and New Zealand.
      EMEA: Europe, the Middle East and Africa.
      LAC: Latin America and the Caribbean.
      AE: United Arab Emirates
      AR: Argentina
      AT: Austria
      AU: Australia
      BE: Belgium
      BR: Brazil
      CA: Canada
      CH: Switzerland
      CL: Chile
      CN: China
      CO: Colombia
      CS: Czechoslovakia
      CZ: Czech Republic
      DE: Germany
      DK: Denmark
      DZ: Algeria
      EC: Ecuador
      EE: Estonia
      EG: Egypt
      ES: Spain
      FI: Finland
      FR: France
      GB: Great Britain (United Kingdom)
      GR: Greece
      HK: Hong Kong
      HU: Hungary
      ID: Indonesia
      IE: Ireland
      IL: Israel
      IN: India
      IR: Iran
      IT: Italy
      JP: Japan
      KR: Korea (South)
      LV: Latvia
      MA: Morocco
      MX: Mexico
      MY: Malaysia
      NG: Nigeria
      NL: Netherlands
      NO: Norway
      NZ: New Zealand
      PE: Peru
      PH: Philippines
      PK: Pakistan
      PL: Poland
      PT: Portugal
      RO: Romania
      RS: Serbia
      RU: Russian Federation
      SA: Saudi Arabia
      SE: Sweden
      SG: Singapore
      SI: Slovenia
      SK: Slovakia
      TH: Thailand
      TR: Turkey
      TW: Taiwan
      UA: Ukraine
      US: United States
      VE: Venezuela
      VN: Vietnam
      ZA: South Africa
    """
    HOLIDAY_REGION_UNSPECIFIED = 0
    GLOBAL = 1
    NA = 2
    JAPAC = 3
    EMEA = 4
    LAC = 5
    AE = 6
    AR = 7
    AT = 8
    AU = 9
    BE = 10
    BR = 11
    CA = 12
    CH = 13
    CL = 14
    CN = 15
    CO = 16
    CS = 17
    CZ = 18
    DE = 19
    DK = 20
    DZ = 21
    EC = 22
    EE = 23
    EG = 24
    ES = 25
    FI = 26
    FR = 27
    GB = 28
    GR = 29
    HK = 30
    HU = 31
    ID = 32
    IE = 33
    IL = 34
    IN = 35
    IR = 36
    IT = 37
    JP = 38
    KR = 39
    LV = 40
    MA = 41
    MX = 42
    MY = 43
    NG = 44
    NL = 45
    NO = 46
    NZ = 47
    PE = 48
    PH = 49
    PK = 50
    PL = 51
    PT = 52
    RO = 53
    RS = 54
    RU = 55
    SA = 56
    SE = 57
    SG = 58
    SI = 59
    SK = 60
    TH = 61
    TR = 62
    TW = 63
    UA = 64
    US = 65
    VE = 66
    VN = 67
    ZA = 68

  class HparamTuningObjectivesValueListEntryValuesEnum(_messages.Enum):
    r"""HparamTuningObjectivesValueListEntryValuesEnum enum type.

    Values:
      HPARAM_TUNING_OBJECTIVE_UNSPECIFIED: Unspecified evaluation metric.
      MEAN_ABSOLUTE_ERROR: Mean absolute error. mean_absolute_error =
        AVG(ABS(label - predicted))
      MEAN_SQUARED_ERROR: Mean squared error. mean_squared_error =
        AVG(POW(label - predicted, 2))
      MEAN_SQUARED_LOG_ERROR: Mean squared log error. mean_squared_log_error =
        AVG(POW(LN(1 + label) - LN(1 + predicted), 2))
      MEDIAN_ABSOLUTE_ERROR: Mean absolute error. median_absolute_error =
        APPROX_QUANTILES(absolute_error, 2)[OFFSET(1)]
      R_SQUARED: R^2 score. This corresponds to r2_score in ML.EVALUATE.
        r_squared = 1 - SUM(squared_error)/(COUNT(label)*VAR_POP(label))
      EXPLAINED_VARIANCE: Explained variance. explained_variance = 1 -
        VAR_POP(label_error)/VAR_POP(label)
      PRECISION: Precision is the fraction of actual positive predictions that
        had positive actual labels. For multiclass this is a macro-averaged
        metric treating each class as a binary classifier.
      RECALL: Recall is the fraction of actual positive labels that were given
        a positive prediction. For multiclass this is a macro-averaged metric.
      ACCURACY: Accuracy is the fraction of predictions given the correct
        label. For multiclass this is a globally micro-averaged metric.
      F1_SCORE: The F1 score is an average of recall and precision. For
        multiclass this is a macro-averaged metric.
      LOG_LOSS: Logarithmic Loss. For multiclass this is a macro-averaged
        metric.
      ROC_AUC: Area Under an ROC Curve. For multiclass this is a macro-
        averaged metric.
      DAVIES_BOULDIN_INDEX: Davies-Bouldin Index.
      MEAN_AVERAGE_PRECISION: Mean Average Precision.
      NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN: Normalized Discounted Cumulative
        Gain.
      AVERAGE_RANK: Average Rank.
    """
    HPARAM_TUNING_OBJECTIVE_UNSPECIFIED = 0
    MEAN_ABSOLUTE_ERROR = 1
    MEAN_SQUARED_ERROR = 2
    MEAN_SQUARED_LOG_ERROR = 3
    MEDIAN_ABSOLUTE_ERROR = 4
    R_SQUARED = 5
    EXPLAINED_VARIANCE = 6
    PRECISION = 7
    RECALL = 8
    ACCURACY = 9
    F1_SCORE = 10
    LOG_LOSS = 11
    ROC_AUC = 12
    DAVIES_BOULDIN_INDEX = 13
    MEAN_AVERAGE_PRECISION = 14
    NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN = 15
    AVERAGE_RANK = 16

  class KmeansInitializationMethodValueValuesEnum(_messages.Enum):
    r"""The method used to initialize the centroids for kmeans algorithm.

    Values:
      KMEANS_INITIALIZATION_METHOD_UNSPECIFIED: Unspecified initialization
        method.
      RANDOM: Initializes the centroids randomly.
      CUSTOM: Initializes the centroids using data specified in
        kmeans_initialization_column.
      KMEANS_PLUS_PLUS: Initializes with kmeans++.
    """
    KMEANS_INITIALIZATION_METHOD_UNSPECIFIED = 0
    RANDOM = 1
    CUSTOM = 2
    KMEANS_PLUS_PLUS = 3

  class LearnRateStrategyValueValuesEnum(_messages.Enum):
    r"""The strategy to determine learn rate for the current iteration.

    Values:
      LEARN_RATE_STRATEGY_UNSPECIFIED: Default value.
      LINE_SEARCH: Use line search to determine learning rate.
      CONSTANT: Use a constant learning rate.
    """
    LEARN_RATE_STRATEGY_UNSPECIFIED = 0
    LINE_SEARCH = 1
    CONSTANT = 2

  class LossTypeValueValuesEnum(_messages.Enum):
    r"""Type of loss function used during training run.

    Values:
      LOSS_TYPE_UNSPECIFIED: Default value.
      MEAN_SQUARED_LOSS: Mean squared loss, used for linear regression.
      MEAN_LOG_LOSS: Mean log loss, used for logistic regression.
    """
    LOSS_TYPE_UNSPECIFIED = 0
    MEAN_SQUARED_LOSS = 1
    MEAN_LOG_LOSS = 2

  class ModelRegistryValueValuesEnum(_messages.Enum):
    r"""The model registry.

    Values:
      MODEL_REGISTRY_UNSPECIFIED: Default value.
      VERTEX_AI: Vertex AI.
    """
    MODEL_REGISTRY_UNSPECIFIED = 0
    VERTEX_AI = 1

  class OptimizationStrategyValueValuesEnum(_messages.Enum):
    r"""Optimization strategy for training linear regression models.

    Values:
      OPTIMIZATION_STRATEGY_UNSPECIFIED: Default value.
      BATCH_GRADIENT_DESCENT: Uses an iterative batch gradient descent
        algorithm.
      NORMAL_EQUATION: Uses a normal equation to solve linear regression
        problem.
    """
    OPTIMIZATION_STRATEGY_UNSPECIFIED = 0
    BATCH_GRADIENT_DESCENT = 1
    NORMAL_EQUATION = 2

  class PcaSolverValueValuesEnum(_messages.Enum):
    r"""The solver for PCA.

    Values:
      UNSPECIFIED: Default value.
      FULL: Full eigen-decoposition.
      RANDOMIZED: Randomized SVD.
      AUTO: Auto.
    """
    UNSPECIFIED = 0
    FULL = 1
    RANDOMIZED = 2
    AUTO = 3

  class TreeMethodValueValuesEnum(_messages.Enum):
    r"""Tree construction algorithm for boosted tree models.

    Values:
      TREE_METHOD_UNSPECIFIED: Unspecified tree method.
      AUTO: Use heuristic to choose the fastest method.
      EXACT: Exact greedy algorithm.
      APPROX: Approximate greedy algorithm using quantile sketch and gradient
        histogram.
      HIST: Fast histogram optimized approximate greedy algorithm.
    """
    TREE_METHOD_UNSPECIFIED = 0
    AUTO = 1
    EXACT = 2
    APPROX = 3
    HIST = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelClassWeightsValue(_messages.Message):
    r"""Weights associated with each label class, for rebalancing the training
    data. Only applicable for classification models.

    Messages:
      AdditionalProperty: An additional property for a LabelClassWeightsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        LabelClassWeightsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelClassWeightsValue object.

      Fields:
        key: Name of the additional property.
        value: A number attribute.
      """

      key = _messages.StringField(1)
      value = _messages.FloatField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationFn = _messages.StringField(1)
  adjustStepChanges = _messages.BooleanField(2)
  approxGlobalFeatureContrib = _messages.BooleanField(3)
  autoArima = _messages.BooleanField(4)
  autoArimaMaxOrder = _messages.IntegerField(5)
  autoArimaMinOrder = _messages.IntegerField(6)
  autoClassWeights = _messages.BooleanField(7)
  batchSize = _messages.IntegerField(8)
  boosterType = _messages.EnumField('BoosterTypeValueValuesEnum', 9)
  budgetHours = _messages.FloatField(10)
  calculatePValues = _messages.BooleanField(11)
  categoryEncodingMethod = _messages.EnumField('CategoryEncodingMethodValueValuesEnum', 12)
  cleanSpikesAndDips = _messages.BooleanField(13)
  colorSpace = _messages.EnumField('ColorSpaceValueValuesEnum', 14)
  colsampleBylevel = _messages.FloatField(15)
  colsampleBynode = _messages.FloatField(16)
  colsampleBytree = _messages.FloatField(17)
  contributionMetric = _messages.StringField(18)
  dartNormalizeType = _messages.EnumField('DartNormalizeTypeValueValuesEnum', 19)
  dataFrequency = _messages.EnumField('DataFrequencyValueValuesEnum', 20)
  dataSplitColumn = _messages.StringField(21)
  dataSplitEvalFraction = _messages.FloatField(22)
  dataSplitMethod = _messages.EnumField('DataSplitMethodValueValuesEnum', 23)
  decomposeTimeSeries = _messages.BooleanField(24)
  dimensionIdColumns = _messages.StringField(25, repeated=True)
  distanceType = _messages.EnumField('DistanceTypeValueValuesEnum', 26)
  dropout = _messages.FloatField(27)
  earlyStop = _messages.BooleanField(28)
  enableGlobalExplain = _messages.BooleanField(29)
  feedbackType = _messages.EnumField('FeedbackTypeValueValuesEnum', 30)
  fitIntercept = _messages.BooleanField(31)
  forecastLimitLowerBound = _messages.FloatField(32)
  forecastLimitUpperBound = _messages.FloatField(33)
  hiddenUnits = _messages.IntegerField(34, repeated=True)
  holidayRegion = _messages.EnumField('HolidayRegionValueValuesEnum', 35)
  holidayRegions = _messages.EnumField('HolidayRegionsValueListEntryValuesEnum', 36, repeated=True)
  horizon = _messages.IntegerField(37)
  hparamTuningObjectives = _messages.EnumField('HparamTuningObjectivesValueListEntryValuesEnum', 38, repeated=True)
  includeDrift = _messages.BooleanField(39)
  initialLearnRate = _messages.FloatField(40)
  inputLabelColumns = _messages.StringField(41, repeated=True)
  instanceWeightColumn = _messages.StringField(42)
  integratedGradientsNumSteps = _messages.IntegerField(43)
  isTestColumn = _messages.StringField(44)
  itemColumn = _messages.StringField(45)
  kmeansInitializationColumn = _messages.StringField(46)
  kmeansInitializationMethod = _messages.EnumField('KmeansInitializationMethodValueValuesEnum', 47)
  l1RegActivation = _messages.FloatField(48)
  l1Regularization = _messages.FloatField(49)
  l2Regularization = _messages.FloatField(50)
  labelClassWeights = _messages.MessageField('LabelClassWeightsValue', 51)
  learnRate = _messages.FloatField(52)
  learnRateStrategy = _messages.EnumField('LearnRateStrategyValueValuesEnum', 53)
  lossType = _messages.EnumField('LossTypeValueValuesEnum', 54)
  maxIterations = _messages.IntegerField(55)
  maxParallelTrials = _messages.IntegerField(56)
  maxTimeSeriesLength = _messages.IntegerField(57)
  maxTreeDepth = _messages.IntegerField(58)
  minAprioriSupport = _messages.FloatField(59)
  minRelativeProgress = _messages.FloatField(60)
  minSplitLoss = _messages.FloatField(61)
  minTimeSeriesLength = _messages.IntegerField(62)
  minTreeChildWeight = _messages.IntegerField(63)
  modelRegistry = _messages.EnumField('ModelRegistryValueValuesEnum', 64)
  modelUri = _messages.StringField(65)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 66)
  numClusters = _messages.IntegerField(67)
  numFactors = _messages.IntegerField(68)
  numParallelTree = _messages.IntegerField(69)
  numPrincipalComponents = _messages.IntegerField(70)
  numTrials = _messages.IntegerField(71)
  optimizationStrategy = _messages.EnumField('OptimizationStrategyValueValuesEnum', 72)
  optimizer = _messages.StringField(73)
  pcaExplainedVarianceRatio = _messages.FloatField(74)
  pcaSolver = _messages.EnumField('PcaSolverValueValuesEnum', 75)
  sampledShapleyNumPaths = _messages.IntegerField(76)
  scaleFeatures = _messages.BooleanField(77)
  standardizeFeatures = _messages.BooleanField(78)
  subsample = _messages.FloatField(79)
  tfVersion = _messages.StringField(80)
  timeSeriesDataColumn = _messages.StringField(81)
  timeSeriesIdColumn = _messages.StringField(82)
  timeSeriesIdColumns = _messages.StringField(83, repeated=True)
  timeSeriesLengthFraction = _messages.FloatField(84)
  timeSeriesTimestampColumn = _messages.StringField(85)
  treeMethod = _messages.EnumField('TreeMethodValueValuesEnum', 86)
  trendSmoothingWindowSize = _messages.IntegerField(87)
  userColumn = _messages.StringField(88)
  vertexAiModelVersionAliases = _messages.StringField(89, repeated=True)
  walsAlpha = _messages.FloatField(90)
  warmStart = _messages.BooleanField(91)
  xgboostVersion = _messages.StringField(92)


class TrainingRun(_messages.Message):
  r"""Information about a single training query run for the model.

  Fields:
    classLevelGlobalExplanations: Output only. Global explanation contains the
      explanation of top features on the class level. Applies to
      classification models only.
    dataSplitResult: Output only. Data split result of the training run. Only
      set when the input data is actually split.
    evaluationMetrics: Output only. The evaluation metrics over training/eval
      data that were computed at the end of training.
    modelLevelGlobalExplanation: Output only. Global explanation contains the
      explanation of top features on the model level. Applies to both
      regression and classification models.
    results: Output only. Output of each iteration run, results.size() <=
      max_iterations.
    startTime: Output only. The start time of this training run.
    trainingOptions: Output only. Options that were used for this training
      run, includes user specified and default options that were used.
    trainingStartTime: Output only. The start time of this training run, in
      milliseconds since epoch.
    vertexAiModelId: The model id in the [Vertex AI Model
      Registry](https://cloud.google.com/vertex-ai/docs/model-
      registry/introduction) for this training run.
    vertexAiModelVersion: Output only. The model version in the [Vertex AI
      Model Registry](https://cloud.google.com/vertex-ai/docs/model-
      registry/introduction) for this training run.
  """

  classLevelGlobalExplanations = _messages.MessageField('GlobalExplanation', 1, repeated=True)
  dataSplitResult = _messages.MessageField('DataSplitResult', 2)
  evaluationMetrics = _messages.MessageField('EvaluationMetrics', 3)
  modelLevelGlobalExplanation = _messages.MessageField('GlobalExplanation', 4)
  results = _messages.MessageField('IterationResult', 5, repeated=True)
  startTime = _messages.StringField(6)
  trainingOptions = _messages.MessageField('TrainingOptions', 7)
  trainingStartTime = _messages.IntegerField(8)
  vertexAiModelId = _messages.StringField(9)
  vertexAiModelVersion = _messages.StringField(10)


class TransactionInfo(_messages.Message):
  r"""[Alpha] Information of a multi-statement transaction.

  Fields:
    transactionId: Output only. [Alpha] Id of the transaction.
  """

  transactionId = _messages.StringField(1)


class TransformColumn(_messages.Message):
  r"""Information about a single transform column.

  Fields:
    name: Output only. Name of the column.
    transformSql: Output only. The SQL expression used in the column
      transform.
    type: Output only. Data type of the column after the transform.
  """

  name = _messages.StringField(1)
  transformSql = _messages.StringField(2)
  type = _messages.MessageField('StandardSqlDataType', 3)


class UndeleteDatasetRequest(_messages.Message):
  r"""Request format for undeleting a dataset.

  Fields:
    deletionTime: Optional. The exact time when the dataset was deleted. If
      not specified, the most recently deleted version is undeleted.
      Undeleting a dataset using deletion time is not supported.
  """

  deletionTime = _messages.StringField(1)


class UserDefinedFunctionResource(_messages.Message):
  r""" This is used for defining User Defined Function (UDF) resources only
  when using legacy SQL. Users of GoogleSQL should leverage either DDL (e.g.
  CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF
  resources. For additional information on migrating, see:
  https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-
  from-legacy-sql#differences_in_user-defined_javascript_functions

  Fields:
    inlineCode: [Pick one] An inline resource that contains code for a user-
      defined function (UDF). Providing a inline code resource is equivalent
      to providing a URI for a file containing the same code.
    resourceUri: [Pick one] A code resource to load from a Google Cloud
      Storage URI (gs://bucket/path).
  """

  inlineCode = _messages.StringField(1)
  resourceUri = _messages.StringField(2)


class VectorSearchStatistics(_messages.Message):
  r"""Statistics for a vector search query. Populated as part of
  JobStatistics2.

  Enums:
    IndexUsageModeValueValuesEnum: Specifies the index usage mode for the
      query.

  Fields:
    indexUnusedReasons: When `indexUsageMode` is `UNUSED` or `PARTIALLY_USED`,
      this field explains why indexes were not used in all or part of the
      vector search query. If `indexUsageMode` is `FULLY_USED`, this field is
      not populated.
    indexUsageMode: Specifies the index usage mode for the query.
    storedColumnsUsages: Specifies the usage of stored columns in the query
      when stored columns are used in the query.
  """

  class IndexUsageModeValueValuesEnum(_messages.Enum):
    r"""Specifies the index usage mode for the query.

    Values:
      INDEX_USAGE_MODE_UNSPECIFIED: Index usage mode not specified.
      UNUSED: No vector indexes were used in the vector search query. See
        [`indexUnusedReasons`]
        (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for detailed
        reasons.
      PARTIALLY_USED: Part of the vector search query used vector indexes. See
        [`indexUnusedReasons`]
        (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for why other
        parts of the query did not use vector indexes.
      FULLY_USED: The entire vector search query used vector indexes.
    """
    INDEX_USAGE_MODE_UNSPECIFIED = 0
    UNUSED = 1
    PARTIALLY_USED = 2
    FULLY_USED = 3

  indexUnusedReasons = _messages.MessageField('IndexUnusedReason', 1, repeated=True)
  indexUsageMode = _messages.EnumField('IndexUsageModeValueValuesEnum', 2)
  storedColumnsUsages = _messages.MessageField('StoredColumnsUsage', 3, repeated=True)


class ViewDefinition(_messages.Message):
  r"""Describes the definition of a logical view.

  Fields:
    foreignDefinitions: Optional. Foreign view representations.
    privacyPolicy: Optional. Specifies the privacy policy for the view.
    query: Required. A query that BigQuery executes when the view is
      referenced.
    useExplicitColumnNames: True if the column names are explicitly specified.
      For example by using the 'CREATE VIEW v(c1, c2) AS ...' syntax. Can only
      be set for GoogleSQL views.
    useLegacySql: Specifies whether to use BigQuery's legacy SQL for this
      view. The default value is true. If set to false, the view will use
      BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/
      Queries and views that reference this view must use the same flag value.
      A wrapper is used here because the default value is True.
    userDefinedFunctionResources: Describes user-defined function resources
      used in the query.
  """

  foreignDefinitions = _messages.MessageField('ForeignViewDefinition', 1, repeated=True)
  privacyPolicy = _messages.MessageField('PrivacyPolicy', 2)
  query = _messages.StringField(3)
  useExplicitColumnNames = _messages.BooleanField(4)
  useLegacySql = _messages.BooleanField(5)
  userDefinedFunctionResources = _messages.MessageField('UserDefinedFunctionResource', 6, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    BigqueryJobsGetQueryResultsRequest, 'formatOptions_useInt64Timestamp', 'formatOptions.useInt64Timestamp')
encoding.AddCustomJsonFieldMapping(
    BigqueryTabledataListRequest, 'formatOptions_useInt64Timestamp', 'formatOptions.useInt64Timestamp')
