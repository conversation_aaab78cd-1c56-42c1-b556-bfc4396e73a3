"""Generated message classes for accessapproval version v1.

An API for controlling access to data by Google personnel.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'accessapproval'


class AccessApprovalServiceAccount(_messages.Message):
  r"""Access Approval service account related to a
  project/folder/organization.

  Fields:
    accountEmail: Email address of the service account.
    name: The resource name of the Access Approval service account. Format is
      one of: * "projects/{project}/serviceAccount" *
      "folders/{folder}/serviceAccount" *
      "organizations/{organization}/serviceAccount"
  """

  accountEmail = _messages.StringField(1)
  name = _messages.StringField(2)


class AccessApprovalSettings(_messages.Message):
  r"""Settings on a Project/Folder/Organization related to Access Approval.

  Enums:
    RequestScopeMaxWidthPreferenceValueValuesEnum: Optional. A setting to
      indicate the maximum width of an Access Approval request.

  Fields:
    activeKeyVersion: The asymmetric crypto key version to use for signing
      approval requests. Empty active_key_version indicates that a Google-
      managed key should be used for signing. This property will be ignored if
      set by an ancestor of this resource, and new non-empty values may not be
      set.
    ancestorHasActiveKeyVersion: Output only. This field is read only (not
      settable via UpdateAccessApprovalSettings method). If the field is true,
      that indicates that an ancestor of this Project or Folder has set
      active_key_version (this field will always be unset for the organization
      since organizations do not have ancestors).
    approvalPolicy: Optional. Policy for approval. This contains all policies.
    effectiveApprovalPolicy: Output only. Policy for approval included
      inherited settings to understand the exact policy applied to this
      resource. This is a read-only field.
    enrolledAncestor: Output only. This field is read only (not settable via
      UpdateAccessApprovalSettings method). If the field is true, that
      indicates that at least one service is enrolled for Access Approval in
      one or more ancestors of the Project or Folder (this field will always
      be unset for the organization since organizations do not have
      ancestors).
    enrolledServices: A list of Google Cloud Services for which the given
      resource has Access Approval enrolled. Access requests for the resource
      given by name against any of these services contained here will be
      required to have explicit approval. If name refers to an organization,
      enrollment can be done for individual services. If name refers to a
      folder or project, enrollment can only be done on an all or nothing
      basis. If a cloud_product is repeated in this list, the first entry will
      be honored and all following entries will be discarded. A maximum of 10
      enrolled services will be enforced, to be expanded as the set of
      supported services is expanded.
    invalidKeyVersion: Output only. This field is read only (not settable via
      UpdateAccessApprovalSettings method). If the field is true, that
      indicates that there is some configuration issue with the
      active_key_version configured at this level in the resource hierarchy
      (e.g. it doesn't exist or the Access Approval service account doesn't
      have the correct permissions on it, etc.) This key version is not
      necessarily the effective key version at this level, as key versions are
      inherited top-down.
    name: The resource name of the settings. Format is one of: *
      "projects/{project}/accessApprovalSettings" *
      "folders/{folder}/accessApprovalSettings" *
      "organizations/{organization}/accessApprovalSettings"
    notificationEmails: A list of email addresses to which notifications
      relating to approval requests should be sent. Notifications relating to
      a resource will be sent to all emails in the settings of ancestor
      resources of that resource. A maximum of 50 email addresses are allowed.
    notificationPubsubTopic: Optional. A pubsub topic to which notifications
      relating to approval requests should be sent.
    preferNoBroadApprovalRequests: This preference is communicated to Google
      personnel when sending an approval request but can be overridden if
      necessary.
    preferredRequestExpirationDays: This preference is shared with Google
      personnel, but can be overridden if said personnel deems necessary. The
      approver ultimately can set the expiration at approval time.
    requestScopeMaxWidthPreference: Optional. A setting to indicate the
      maximum width of an Access Approval request.
    requireCustomerVisibleJustification: Optional. A setting to require
      approval request justifications to be customer visible.
  """

  class RequestScopeMaxWidthPreferenceValueValuesEnum(_messages.Enum):
    r"""Optional. A setting to indicate the maximum width of an Access
    Approval request.

    Values:
      REQUEST_SCOPE_MAX_WIDTH_PREFERENCE_UNSPECIFIED: Default value for proto,
        shouldn't be used.
      ORGANIZATION: This is the widest scope possible. It means the customer
        has no scope restriction when it comes to Access Approval requests.
      FOLDER: Customer allows the scope of Access Approval requests as broad
        as the Folder level.
      PROJECT: Customer allows the scope of Access Approval requests as broad
        as the Project level.
    """
    REQUEST_SCOPE_MAX_WIDTH_PREFERENCE_UNSPECIFIED = 0
    ORGANIZATION = 1
    FOLDER = 2
    PROJECT = 3

  activeKeyVersion = _messages.StringField(1)
  ancestorHasActiveKeyVersion = _messages.BooleanField(2)
  approvalPolicy = _messages.MessageField('CustomerApprovalApprovalPolicy', 3)
  effectiveApprovalPolicy = _messages.MessageField('CustomerApprovalApprovalPolicy', 4)
  enrolledAncestor = _messages.BooleanField(5)
  enrolledServices = _messages.MessageField('EnrolledService', 6, repeated=True)
  invalidKeyVersion = _messages.BooleanField(7)
  name = _messages.StringField(8)
  notificationEmails = _messages.StringField(9, repeated=True)
  notificationPubsubTopic = _messages.StringField(10)
  preferNoBroadApprovalRequests = _messages.BooleanField(11)
  preferredRequestExpirationDays = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  requestScopeMaxWidthPreference = _messages.EnumField('RequestScopeMaxWidthPreferenceValueValuesEnum', 13)
  requireCustomerVisibleJustification = _messages.BooleanField(14)


class AccessLocations(_messages.Message):
  r"""Home office and physical location of the principal.

  Fields:
    principalOfficeCountry: The "home office" location of the principal. A
      two-letter country code (ISO 3166-1 alpha-2), such as "US", "DE" or "GB"
      or a region code. In some limited situations Google systems may refer
      refer to a region code instead of a country code. Possible Region Codes:
      * ASI: Asia * EUR: Europe * OCE: Oceania * AFR: Africa * NAM: North
      America * SAM: South America * ANT: Antarctica * ANY: Any location
    principalPhysicalLocationCountry: Physical location of the principal at
      the time of the access. A two-letter country code (ISO 3166-1 alpha-2),
      such as "US", "DE" or "GB" or a region code. In some limited situations
      Google systems may refer refer to a region code instead of a country
      code. Possible Region Codes: * ASI: Asia * EUR: Europe * OCE: Oceania *
      AFR: Africa * NAM: North America * SAM: South America * ANT: Antarctica
      * ANY: Any location
  """

  principalOfficeCountry = _messages.StringField(1)
  principalPhysicalLocationCountry = _messages.StringField(2)


class AccessReason(_messages.Message):
  r"""A AccessReason object.

  Enums:
    TypeValueValuesEnum: Type of access justification.

  Fields:
    detail: More detail about certain reason types. See comments for each type
      above.
    type: Type of access justification.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of access justification.

    Values:
      TYPE_UNSPECIFIED: Default value for proto, shouldn't be used.
      CUSTOMER_INITIATED_SUPPORT: Customer made a request or raised an issue
        that required the principal to access customer data. `detail` is of
        the form ("#####" is the issue ID): * "Feedback Report: #####" * "Case
        Number: #####" * "Case ID: #####" * "E-PIN Reference: #####" *
        "Google-#####" * "T-#####"
      GOOGLE_INITIATED_SERVICE: The principal accessed customer data in order
        to diagnose or resolve a suspected issue in services. Often this
        access is used to confirm that customers are not affected by a
        suspected service issue or to remediate a reversible system issue.
      GOOGLE_INITIATED_REVIEW: Google initiated service for security, fraud,
        abuse, or compliance purposes.
      THIRD_PARTY_DATA_REQUEST: The principal was compelled to access customer
        data in order to respond to a legal third party data request or
        process, including legal processes from customers themselves.
      GOOGLE_RESPONSE_TO_PRODUCTION_ALERT: The principal accessed customer
        data in order to diagnose or resolve a suspected issue in services or
        a known outage.
      CLOUD_INITIATED_ACCESS: Similar to 'GOOGLE_INITIATED_SERVICE' or
        'GOOGLE_INITIATED_REVIEW', but with universe agnostic naming. The
        principal accessed customer data in order to diagnose or resolve a
        suspected issue in services or a known outage, or for security, fraud,
        abuse, or compliance review purposes.
    """
    TYPE_UNSPECIFIED = 0
    CUSTOMER_INITIATED_SUPPORT = 1
    GOOGLE_INITIATED_SERVICE = 2
    GOOGLE_INITIATED_REVIEW = 3
    THIRD_PARTY_DATA_REQUEST = 4
    GOOGLE_RESPONSE_TO_PRODUCTION_ALERT = 5
    CLOUD_INITIATED_ACCESS = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class AccessapprovalFoldersApprovalRequestsApproveRequest(_messages.Message):
  r"""A AccessapprovalFoldersApprovalRequestsApproveRequest object.

  Fields:
    approveApprovalRequestMessage: A ApproveApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the approval request to approve.
  """

  approveApprovalRequestMessage = _messages.MessageField('ApproveApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalFoldersApprovalRequestsDismissRequest(_messages.Message):
  r"""A AccessapprovalFoldersApprovalRequestsDismissRequest object.

  Fields:
    dismissApprovalRequestMessage: A DismissApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the ApprovalRequest to dismiss.
  """

  dismissApprovalRequestMessage = _messages.MessageField('DismissApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalFoldersApprovalRequestsGetRequest(_messages.Message):
  r"""A AccessapprovalFoldersApprovalRequestsGetRequest object.

  Fields:
    name: The name of the approval request to retrieve. Format: "{projects|fol
      ders|organizations}/{id}/approvalRequests/{approval_request}"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalFoldersApprovalRequestsInvalidateRequest(_messages.Message):
  r"""A AccessapprovalFoldersApprovalRequestsInvalidateRequest object.

  Fields:
    invalidateApprovalRequestMessage: A InvalidateApprovalRequestMessage
      resource to be passed as the request body.
    name: Name of the ApprovalRequest to invalidate.
  """

  invalidateApprovalRequestMessage = _messages.MessageField('InvalidateApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalFoldersApprovalRequestsListRequest(_messages.Message):
  r"""A AccessapprovalFoldersApprovalRequestsListRequest object.

  Fields:
    filter: A filter on the type of approval requests to retrieve. Must be one
      of the following values: * [not set]: Requests that are pending or have
      active approvals. * ALL: All requests. * PENDING: Only pending requests.
      * ACTIVE: Only active (i.e. currently approved) requests. * DISMISSED:
      Only requests that have been dismissed, or requests that are not
      approved and past expiration. * EXPIRED: Only requests that have been
      approved, and the approval has expired. * HISTORY: Active, dismissed and
      expired requests.
    pageSize: Requested page size.
    pageToken: A token identifying the page of results to return.
    parent: The parent resource. This may be "projects/{project}",
      "folders/{folder}", or "organizations/{organization}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AccessapprovalFoldersDeleteAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalFoldersDeleteAccessApprovalSettingsRequest object.

  Fields:
    name: Name of the AccessApprovalSettings to delete.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalFoldersGetAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalFoldersGetAccessApprovalSettingsRequest object.

  Fields:
    name: The name of the AccessApprovalSettings to retrieve. Format:
      "{projects|folders|organizations}/{id}/accessApprovalSettings"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalFoldersGetServiceAccountRequest(_messages.Message):
  r"""A AccessapprovalFoldersGetServiceAccountRequest object.

  Fields:
    name: Name of the AccessApprovalServiceAccount to retrieve.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalFoldersUpdateAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalFoldersUpdateAccessApprovalSettingsRequest object.

  Fields:
    accessApprovalSettings: A AccessApprovalSettings resource to be passed as
      the request body.
    name: The resource name of the settings. Format is one of: *
      "projects/{project}/accessApprovalSettings" *
      "folders/{folder}/accessApprovalSettings" *
      "organizations/{organization}/accessApprovalSettings"
    updateMask: The update mask applies to the settings. Only the top level
      fields of AccessApprovalSettings (notification_emails &
      enrolled_services) are supported. For each field, if it is included, the
      currently stored value will be entirely overwritten with the value of
      the field passed in this request. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If this field is left
      unset, only the notification_emails field will be updated.
  """

  accessApprovalSettings = _messages.MessageField('AccessApprovalSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AccessapprovalOrganizationsApprovalRequestsApproveRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsApprovalRequestsApproveRequest object.

  Fields:
    approveApprovalRequestMessage: A ApproveApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the approval request to approve.
  """

  approveApprovalRequestMessage = _messages.MessageField('ApproveApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalOrganizationsApprovalRequestsDismissRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsApprovalRequestsDismissRequest object.

  Fields:
    dismissApprovalRequestMessage: A DismissApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the ApprovalRequest to dismiss.
  """

  dismissApprovalRequestMessage = _messages.MessageField('DismissApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalOrganizationsApprovalRequestsGetRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsApprovalRequestsGetRequest object.

  Fields:
    name: The name of the approval request to retrieve. Format: "{projects|fol
      ders|organizations}/{id}/approvalRequests/{approval_request}"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalOrganizationsApprovalRequestsInvalidateRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsApprovalRequestsInvalidateRequest object.

  Fields:
    invalidateApprovalRequestMessage: A InvalidateApprovalRequestMessage
      resource to be passed as the request body.
    name: Name of the ApprovalRequest to invalidate.
  """

  invalidateApprovalRequestMessage = _messages.MessageField('InvalidateApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalOrganizationsApprovalRequestsListRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsApprovalRequestsListRequest object.

  Fields:
    filter: A filter on the type of approval requests to retrieve. Must be one
      of the following values: * [not set]: Requests that are pending or have
      active approvals. * ALL: All requests. * PENDING: Only pending requests.
      * ACTIVE: Only active (i.e. currently approved) requests. * DISMISSED:
      Only requests that have been dismissed, or requests that are not
      approved and past expiration. * EXPIRED: Only requests that have been
      approved, and the approval has expired. * HISTORY: Active, dismissed and
      expired requests.
    pageSize: Requested page size.
    pageToken: A token identifying the page of results to return.
    parent: The parent resource. This may be "projects/{project}",
      "folders/{folder}", or "organizations/{organization}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AccessapprovalOrganizationsDeleteAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsDeleteAccessApprovalSettingsRequest object.

  Fields:
    name: Name of the AccessApprovalSettings to delete.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalOrganizationsGetAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsGetAccessApprovalSettingsRequest object.

  Fields:
    name: The name of the AccessApprovalSettings to retrieve. Format:
      "{projects|folders|organizations}/{id}/accessApprovalSettings"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalOrganizationsGetServiceAccountRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsGetServiceAccountRequest object.

  Fields:
    name: Name of the AccessApprovalServiceAccount to retrieve.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalOrganizationsUpdateAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalOrganizationsUpdateAccessApprovalSettingsRequest object.

  Fields:
    accessApprovalSettings: A AccessApprovalSettings resource to be passed as
      the request body.
    name: The resource name of the settings. Format is one of: *
      "projects/{project}/accessApprovalSettings" *
      "folders/{folder}/accessApprovalSettings" *
      "organizations/{organization}/accessApprovalSettings"
    updateMask: The update mask applies to the settings. Only the top level
      fields of AccessApprovalSettings (notification_emails &
      enrolled_services) are supported. For each field, if it is included, the
      currently stored value will be entirely overwritten with the value of
      the field passed in this request. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If this field is left
      unset, only the notification_emails field will be updated.
  """

  accessApprovalSettings = _messages.MessageField('AccessApprovalSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AccessapprovalProjectsApprovalRequestsApproveRequest(_messages.Message):
  r"""A AccessapprovalProjectsApprovalRequestsApproveRequest object.

  Fields:
    approveApprovalRequestMessage: A ApproveApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the approval request to approve.
  """

  approveApprovalRequestMessage = _messages.MessageField('ApproveApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalProjectsApprovalRequestsDismissRequest(_messages.Message):
  r"""A AccessapprovalProjectsApprovalRequestsDismissRequest object.

  Fields:
    dismissApprovalRequestMessage: A DismissApprovalRequestMessage resource to
      be passed as the request body.
    name: Name of the ApprovalRequest to dismiss.
  """

  dismissApprovalRequestMessage = _messages.MessageField('DismissApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalProjectsApprovalRequestsGetRequest(_messages.Message):
  r"""A AccessapprovalProjectsApprovalRequestsGetRequest object.

  Fields:
    name: The name of the approval request to retrieve. Format: "{projects|fol
      ders|organizations}/{id}/approvalRequests/{approval_request}"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalProjectsApprovalRequestsInvalidateRequest(_messages.Message):
  r"""A AccessapprovalProjectsApprovalRequestsInvalidateRequest object.

  Fields:
    invalidateApprovalRequestMessage: A InvalidateApprovalRequestMessage
      resource to be passed as the request body.
    name: Name of the ApprovalRequest to invalidate.
  """

  invalidateApprovalRequestMessage = _messages.MessageField('InvalidateApprovalRequestMessage', 1)
  name = _messages.StringField(2, required=True)


class AccessapprovalProjectsApprovalRequestsListRequest(_messages.Message):
  r"""A AccessapprovalProjectsApprovalRequestsListRequest object.

  Fields:
    filter: A filter on the type of approval requests to retrieve. Must be one
      of the following values: * [not set]: Requests that are pending or have
      active approvals. * ALL: All requests. * PENDING: Only pending requests.
      * ACTIVE: Only active (i.e. currently approved) requests. * DISMISSED:
      Only requests that have been dismissed, or requests that are not
      approved and past expiration. * EXPIRED: Only requests that have been
      approved, and the approval has expired. * HISTORY: Active, dismissed and
      expired requests.
    pageSize: Requested page size.
    pageToken: A token identifying the page of results to return.
    parent: The parent resource. This may be "projects/{project}",
      "folders/{folder}", or "organizations/{organization}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AccessapprovalProjectsDeleteAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalProjectsDeleteAccessApprovalSettingsRequest object.

  Fields:
    name: Name of the AccessApprovalSettings to delete.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalProjectsGetAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalProjectsGetAccessApprovalSettingsRequest object.

  Fields:
    name: The name of the AccessApprovalSettings to retrieve. Format:
      "{projects|folders|organizations}/{id}/accessApprovalSettings"
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalProjectsGetServiceAccountRequest(_messages.Message):
  r"""A AccessapprovalProjectsGetServiceAccountRequest object.

  Fields:
    name: Name of the AccessApprovalServiceAccount to retrieve.
  """

  name = _messages.StringField(1, required=True)


class AccessapprovalProjectsUpdateAccessApprovalSettingsRequest(_messages.Message):
  r"""A AccessapprovalProjectsUpdateAccessApprovalSettingsRequest object.

  Fields:
    accessApprovalSettings: A AccessApprovalSettings resource to be passed as
      the request body.
    name: The resource name of the settings. Format is one of: *
      "projects/{project}/accessApprovalSettings" *
      "folders/{folder}/accessApprovalSettings" *
      "organizations/{organization}/accessApprovalSettings"
    updateMask: The update mask applies to the settings. Only the top level
      fields of AccessApprovalSettings (notification_emails &
      enrolled_services) are supported. For each field, if it is included, the
      currently stored value will be entirely overwritten with the value of
      the field passed in this request. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If this field is left
      unset, only the notification_emails field will be updated.
  """

  accessApprovalSettings = _messages.MessageField('AccessApprovalSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApprovalRequest(_messages.Message):
  r"""A request for the customer to approve access to a resource.

  Fields:
    approve: Access was approved.
    dismiss: The request was dismissed.
    name: The resource name of the request. Format is "{projects|folders|organ
      izations}/{id}/approvalRequests/{approval_request}".
    requestTime: The time at which approval was requested.
    requestedAugmentedInfo: This field contains the augmented information of
      the request.
    requestedDuration: The requested access duration.
    requestedExpiration: The original requested expiration for the approval.
      Calculated by adding the requested_duration to the request_time.
    requestedLocations: The locations for which approval is being requested.
    requestedReason: The justification for which approval is being requested.
    requestedResourceName: The resource for which approval is being requested.
      The format of the resource name is defined at
      https://cloud.google.com/apis/design/resource_names. The resource name
      here may either be a "full" resource name (e.g.
      "//library.googleapis.com/shelves/shelf1/books/book2") or a "relative"
      resource name (e.g. "shelves/shelf1/books/book2") as described in the
      resource name specification.
    requestedResourceProperties: Properties related to the resource
      represented by requested_resource_name.
  """

  approve = _messages.MessageField('ApproveDecision', 1)
  dismiss = _messages.MessageField('DismissDecision', 2)
  name = _messages.StringField(3)
  requestTime = _messages.StringField(4)
  requestedAugmentedInfo = _messages.MessageField('AugmentedInfo', 5)
  requestedDuration = _messages.StringField(6)
  requestedExpiration = _messages.StringField(7)
  requestedLocations = _messages.MessageField('AccessLocations', 8)
  requestedReason = _messages.MessageField('AccessReason', 9)
  requestedResourceName = _messages.StringField(10)
  requestedResourceProperties = _messages.MessageField('ResourceProperties', 11)


class ApproveApprovalRequestMessage(_messages.Message):
  r"""Request to approve an ApprovalRequest.

  Fields:
    expireTime: The expiration time of this approval.
  """

  expireTime = _messages.StringField(1)


class ApproveDecision(_messages.Message):
  r"""A decision that has been made to approve access to a resource.

  Fields:
    approveTime: The time at which approval was granted.
    autoApproved: True when the request has been auto-approved.
    expireTime: The time at which the approval expires.
    invalidateTime: If set, denotes the timestamp at which the approval is
      invalidated.
    policyApproved: True when the request has been approved by the customer's
      defined policy.
    signatureInfo: The signature for the ApprovalRequest and details on how it
      was signed.
  """

  approveTime = _messages.StringField(1)
  autoApproved = _messages.BooleanField(2)
  expireTime = _messages.StringField(3)
  invalidateTime = _messages.StringField(4)
  policyApproved = _messages.BooleanField(5)
  signatureInfo = _messages.MessageField('SignatureInfo', 6)


class AugmentedInfo(_messages.Message):
  r"""This field contains the augmented information of the request.

  Fields:
    command: For command-line tools, the full command-line exactly as entered
      by the actor without adding any additional characters (such as quotation
      marks).
  """

  command = _messages.StringField(1)


class CustomerApprovalApprovalPolicy(_messages.Message):
  r"""Represents all the policies that can be set for Customer Approval.

  Enums:
    JustificationBasedApprovalPolicyValueValuesEnum: Optional. Policy for
      approval based on the justification given.

  Fields:
    justificationBasedApprovalPolicy: Optional. Policy for approval based on
      the justification given.
  """

  class JustificationBasedApprovalPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Policy for approval based on the justification given.

    Values:
      JUSTIFICATION_BASED_APPROVAL_POLICY_UNSPECIFIED: Default value for
        proto.
      JUSTIFICATION_BASED_APPROVAL_ENABLED_ALL: Instant approval is enabled
        for all accesses.
      JUSTIFICATION_BASED_APPROVAL_ENABLED_EXTERNAL_JUSTIFICATIONS: Instant
        approval is enabled for external justifications.
      JUSTIFICATION_BASED_APPROVAL_NOT_ENABLED: Instant approval is not
        enabled for any accesses.
      JUSTIFICATION_BASED_APPROVAL_INHERITED: Instant approval is inherited
        from the parent.
    """
    JUSTIFICATION_BASED_APPROVAL_POLICY_UNSPECIFIED = 0
    JUSTIFICATION_BASED_APPROVAL_ENABLED_ALL = 1
    JUSTIFICATION_BASED_APPROVAL_ENABLED_EXTERNAL_JUSTIFICATIONS = 2
    JUSTIFICATION_BASED_APPROVAL_NOT_ENABLED = 3
    JUSTIFICATION_BASED_APPROVAL_INHERITED = 4

  justificationBasedApprovalPolicy = _messages.EnumField('JustificationBasedApprovalPolicyValueValuesEnum', 1)


class DismissApprovalRequestMessage(_messages.Message):
  r"""Request to dismiss an approval request."""


class DismissDecision(_messages.Message):
  r"""A decision that has been made to dismiss an approval request.

  Fields:
    dismissTime: The time at which the approval request was dismissed.
    implicit: This field will be true if the ApprovalRequest was implicitly
      dismissed due to inaction by the access approval approvers (the request
      is not acted on by the approvers before the exiration time).
  """

  dismissTime = _messages.StringField(1)
  implicit = _messages.BooleanField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnrolledService(_messages.Message):
  r"""Represents the enrollment of a cloud resource into a specific service.

  Enums:
    EnrollmentLevelValueValuesEnum: The enrollment level of the service.

  Fields:
    cloudProduct: The product for which Access Approval will be enrolled.
      Allowed values are listed below (case-sensitive): * all * GA * App
      Engine * Artifact Registry * BigQuery * Certificate Authority Service *
      Cloud Bigtable * Cloud Key Management Service * Compute Engine * Cloud
      Composer * Cloud Dataflow * Cloud Dataproc * Cloud DLP * Cloud EKM *
      Cloud Firestore * Cloud HSM * Cloud Identity and Access Management *
      Cloud Logging * Cloud NAT * Cloud Pub/Sub * Cloud Spanner * Cloud SQL *
      Cloud Storage * Eventarc * Google Kubernetes Engine * Organization
      Policy Serivice * Persistent Disk * Resource Manager * Secret Manager *
      Speaker ID Note: These values are supported as input for legacy
      purposes, but will not be returned from the API. * all * ga-only *
      appengine.googleapis.com * artifactregistry.googleapis.com *
      bigquery.googleapis.com * bigtable.googleapis.com *
      container.googleapis.com * cloudkms.googleapis.com *
      cloudresourcemanager.googleapis.com * cloudsql.googleapis.com *
      compute.googleapis.com * dataflow.googleapis.com *
      dataproc.googleapis.com * dlp.googleapis.com * iam.googleapis.com *
      logging.googleapis.com * orgpolicy.googleapis.com *
      pubsub.googleapis.com * spanner.googleapis.com *
      secretmanager.googleapis.com * speakerid.googleapis.com *
      storage.googleapis.com Calls to UpdateAccessApprovalSettings using 'all'
      or any of the XXX.googleapis.com will be translated to the associated
      product name ('all', 'App Engine', etc.). Note: 'all' will enroll the
      resource in all products supported at both 'GA' and 'Preview' levels.
      More information about levels of support is available at
      https://cloud.google.com/access-approval/docs/supported-services
    enrollmentLevel: The enrollment level of the service.
  """

  class EnrollmentLevelValueValuesEnum(_messages.Enum):
    r"""The enrollment level of the service.

    Values:
      ENROLLMENT_LEVEL_UNSPECIFIED: Default value for proto, shouldn't be
        used.
      BLOCK_ALL: Service is enrolled in Access Approval for all requests
    """
    ENROLLMENT_LEVEL_UNSPECIFIED = 0
    BLOCK_ALL = 1

  cloudProduct = _messages.StringField(1)
  enrollmentLevel = _messages.EnumField('EnrollmentLevelValueValuesEnum', 2)


class InvalidateApprovalRequestMessage(_messages.Message):
  r"""Request to invalidate an existing approval."""


class ListApprovalRequestsResponse(_messages.Message):
  r"""Response to listing of ApprovalRequest objects.

  Fields:
    approvalRequests: Approval request details.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more.
  """

  approvalRequests = _messages.MessageField('ApprovalRequest', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ResourceProperties(_messages.Message):
  r"""The properties associated with the resource of the request.

  Fields:
    excludesDescendants: Whether an approval will exclude the descendants of
      the resource being requested.
  """

  excludesDescendants = _messages.BooleanField(1)


class SignatureInfo(_messages.Message):
  r"""Information about the digital signature of the resource.

  Enums:
    GoogleKeyAlgorithmValueValuesEnum: The hashing algorithm used for
      signature verification. It will only be present in the case of Google
      managed keys.

  Fields:
    customerKmsKeyVersion: The resource name of the customer CryptoKeyVersion
      used for signing.
    googleKeyAlgorithm: The hashing algorithm used for signature verification.
      It will only be present in the case of Google managed keys.
    googlePublicKeyPem: The public key for the Google default signing, encoded
      in PEM format. The signature was created using a private key which may
      be verified using this public key.
    serializedApprovalRequest: The ApprovalRequest that is serialized without
      the SignatureInfo message field. This data is used with the hashing
      algorithm to generate the digital signature, and it can be used for
      signature verification.
    signature: The digital signature.
  """

  class GoogleKeyAlgorithmValueValuesEnum(_messages.Enum):
    r"""The hashing algorithm used for signature verification. It will only be
    present in the case of Google managed keys.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_ED25519: EdDSA on the Curve25519 in pure mode (taking data as
        input).
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
      PQ_SIGN_ML_DSA_65: The post-quantum Module-Lattice-Based Digital
        Signature Algorithm, at security level 3. Randomized version.
      PQ_SIGN_SLH_DSA_SHA2_128S: The post-quantum stateless hash-based digital
        signature algorithm, at security level 1. Randomized version.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    EC_SIGN_ED25519 = 29
    HMAC_SHA256 = 30
    HMAC_SHA1 = 31
    HMAC_SHA384 = 32
    HMAC_SHA512 = 33
    HMAC_SHA224 = 34
    EXTERNAL_SYMMETRIC_ENCRYPTION = 35
    PQ_SIGN_ML_DSA_65 = 36
    PQ_SIGN_SLH_DSA_SHA2_128S = 37

  customerKmsKeyVersion = _messages.StringField(1)
  googleKeyAlgorithm = _messages.EnumField('GoogleKeyAlgorithmValueValuesEnum', 2)
  googlePublicKeyPem = _messages.StringField(3)
  serializedApprovalRequest = _messages.BytesField(4)
  signature = _messages.BytesField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
