"""Generated message classes for blockchainnodeengine version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'blockchainnodeengine'


class BlockchainNode(_messages.Message):
  r"""A representation of a blockchain node.

  Enums:
    BlockchainTypeValueValuesEnum: Immutable. The blockchain type of the node.
    StateValueValuesEnum: Output only. A status representing the state of the
      node.

  Messages:
    LabelsValue: User-provided key-value pairs.

  Fields:
    blockchainType: Immutable. The blockchain type of the node.
    connectionInfo: Output only. The connection information used to interact
      with a blockchain node.
    createTime: Output only. The timestamp at which the blockchain node was
      first created.
    ethereumDetails: Ethereum-specific blockchain node details.
    labels: User-provided key-value pairs.
    name: Output only. The fully qualified name of the blockchain node. e.g.
      `projects/my-project/locations/us-central1/blockchainNodes/my-node`.
    privateServiceConnectEnabled: Optional. When true, the node is only
      accessible via Private Service Connect; no public endpoints are exposed.
      Otherwise, the node is only accessible via public endpoints. Warning:
      These nodes are deprecated, please use public endpoints instead.
    state: Output only. A status representing the state of the node.
    updateTime: Output only. The timestamp at which the blockchain node was
      last updated.
  """

  class BlockchainTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The blockchain type of the node.

    Values:
      BLOCKCHAIN_TYPE_UNSPECIFIED: Blockchain type has not been specified, but
        should be.
      ETHEREUM: The blockchain type is Ethereum.
    """
    BLOCKCHAIN_TYPE_UNSPECIFIED = 0
    ETHEREUM = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. A status representing the state of the node.

    Values:
      STATE_UNSPECIFIED: The state has not been specified.
      CREATING: The node has been requested and is in the process of being
        created.
      DELETING: The existing node is undergoing deletion, but is not yet
        finished.
      RUNNING: The node is running and ready for use.
      ERROR: The node is in an unexpected or errored state.
      UPDATING: The node is currently being updated.
      REPAIRING: The node is currently being repaired.
      RECONCILING: The node is currently being reconciled.
      SYNCING: The node is syncing, which is the process by which it obtains
        the latest block and current global state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    DELETING = 2
    RUNNING = 3
    ERROR = 4
    UPDATING = 5
    REPAIRING = 6
    RECONCILING = 7
    SYNCING = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-provided key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  blockchainType = _messages.EnumField('BlockchainTypeValueValuesEnum', 1)
  connectionInfo = _messages.MessageField('ConnectionInfo', 2)
  createTime = _messages.StringField(3)
  ethereumDetails = _messages.MessageField('EthereumDetails', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  privateServiceConnectEnabled = _messages.BooleanField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class BlockchainnodeengineProjectsLocationsBlockchainNodesCreateRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsBlockchainNodesCreateRequest
  object.

  Fields:
    blockchainNode: A BlockchainNode resource to be passed as the request
      body.
    blockchainNodeId: Required. ID of the requesting object.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  blockchainNode = _messages.MessageField('BlockchainNode', 1)
  blockchainNodeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BlockchainnodeengineProjectsLocationsBlockchainNodesDeleteRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsBlockchainNodesDeleteRequest
  object.

  Fields:
    name: Required. The fully qualified name of the blockchain node to delete.
      e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-
      node`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BlockchainnodeengineProjectsLocationsBlockchainNodesGetRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsBlockchainNodesGetRequest object.

  Fields:
    name: Required. The fully qualified name of the blockchain node to fetch.
      e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-
      node`.
  """

  name = _messages.StringField(1, required=True)


class BlockchainnodeengineProjectsLocationsBlockchainNodesListRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsBlockchainNodesListRequest
  object.

  Fields:
    filter: Filtering results.
    orderBy: Hint for how to order the results.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for `ListNodesRequest`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BlockchainnodeengineProjectsLocationsBlockchainNodesPatchRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsBlockchainNodesPatchRequest
  object.

  Fields:
    blockchainNode: A BlockchainNode resource to be passed as the request
      body.
    name: Output only. The fully qualified name of the blockchain node. e.g.
      `projects/my-project/locations/us-central1/blockchainNodes/my-node`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Blockchain node resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  blockchainNode = _messages.MessageField('BlockchainNode', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BlockchainnodeengineProjectsLocationsGetRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BlockchainnodeengineProjectsLocationsListRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class BlockchainnodeengineProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BlockchainnodeengineProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BlockchainnodeengineProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BlockchainnodeengineProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A BlockchainnodeengineProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ConnectionInfo(_messages.Message):
  r"""The connection information through which to interact with a blockchain
  node.

  Fields:
    endpointInfo: Output only. The endpoint information through which to
      interact with a blockchain node.
    serviceAttachment: Output only. A service attachment that exposes a node,
      and has the following format: projects/{project}/regions/{region}/servic
      eAttachments/{service_attachment_name}
  """

  endpointInfo = _messages.MessageField('EndpointInfo', 1)
  serviceAttachment = _messages.StringField(2)


class EndpointInfo(_messages.Message):
  r"""Contains endpoint information through which to interact with a
  blockchain node.

  Fields:
    jsonRpcApiEndpoint: Output only. The assigned URL for the node JSON-RPC
      API endpoint.
    websocketsApiEndpoint: Output only. The assigned URL for the node
      WebSockets API endpoint.
  """

  jsonRpcApiEndpoint = _messages.StringField(1)
  websocketsApiEndpoint = _messages.StringField(2)


class EthereumDetails(_messages.Message):
  r"""Ethereum-specific blockchain node details.

  Enums:
    ConsensusClientValueValuesEnum: Immutable. The consensus client.
    ExecutionClientValueValuesEnum: Immutable. The execution client
    NetworkValueValuesEnum: Immutable. The Ethereum environment being
      accessed.
    NodeTypeValueValuesEnum: Immutable. The type of Ethereum node.

  Fields:
    additionalEndpoints: Output only. Ethereum-specific endpoint information.
    apiEnableAdmin: Immutable. Enables JSON-RPC access to functions in the
      `admin` namespace. Defaults to `false`.
    apiEnableDebug: Immutable. Enables JSON-RPC access to functions in the
      `debug` namespace. Defaults to `false`.
    consensusClient: Immutable. The consensus client.
    executionClient: Immutable. The execution client
    gethDetails: Details for the Geth execution client.
    network: Immutable. The Ethereum environment being accessed.
    nodeType: Immutable. The type of Ethereum node.
    validatorConfig: Configuration for validator-related parameters on the
      beacon client, and for any GCP-managed validator client.
  """

  class ConsensusClientValueValuesEnum(_messages.Enum):
    r"""Immutable. The consensus client.

    Values:
      CONSENSUS_CLIENT_UNSPECIFIED: Consensus client has not been specified,
        but should be.
      LIGHTHOUSE: Consensus client implementation written in Rust, maintained
        by Sigma Prime. See [Lighthouse - Sigma
        Prime](https://lighthouse.sigmaprime.io/) for details.
      ERIGON_EMBEDDED_CONSENSUS_LAYER: Erigon's embedded consensus client
        embedded in the execution client. Note this option is not currently
        available when creating new blockchain nodes. See [Erigon on
        GitHub](https://github.com/ledgerwatch/erigon#embedded-consensus-
        layer) for details.
    """
    CONSENSUS_CLIENT_UNSPECIFIED = 0
    LIGHTHOUSE = 1
    ERIGON_EMBEDDED_CONSENSUS_LAYER = 2

  class ExecutionClientValueValuesEnum(_messages.Enum):
    r"""Immutable. The execution client

    Values:
      EXECUTION_CLIENT_UNSPECIFIED: Execution client has not been specified,
        but should be.
      GETH: Official Go implementation of the Ethereum protocol. See [go-
        ethereum](https://geth.ethereum.org/) for details.
      ERIGON: An implementation of Ethereum (execution client), on the
        efficiency frontier, written in Go. See [Erigon on
        GitHub](https://github.com/ledgerwatch/erigon) for details.
    """
    EXECUTION_CLIENT_UNSPECIFIED = 0
    GETH = 1
    ERIGON = 2

  class NetworkValueValuesEnum(_messages.Enum):
    r"""Immutable. The Ethereum environment being accessed.

    Values:
      NETWORK_UNSPECIFIED: The network has not been specified, but should be.
      MAINNET: The Ethereum Mainnet.
      TESTNET_GOERLI_PRATER: Deprecated: The Ethereum Testnet based on Goerli
        protocol. Please use another test network.
      TESTNET_SEPOLIA: The Ethereum Testnet based on Sepolia/Bepolia protocol.
        See https://github.com/eth-clients/sepolia.
      TESTNET_HOLESKY: The Ethereum Testnet based on Holesky specification.
        See https://github.com/eth-clients/holesky.
    """
    NETWORK_UNSPECIFIED = 0
    MAINNET = 1
    TESTNET_GOERLI_PRATER = 2
    TESTNET_SEPOLIA = 3
    TESTNET_HOLESKY = 4

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of Ethereum node.

    Values:
      NODE_TYPE_UNSPECIFIED: Node type has not been specified, but should be.
      LIGHT: An Ethereum node that only downloads Ethereum block headers.
      FULL: Keeps a complete copy of the blockchain data, and contributes to
        the network by receiving, validating, and forwarding transactions.
      ARCHIVE: Holds the same data as full node as well as all of the
        blockchain's history state data dating back to the Genesis Block.
    """
    NODE_TYPE_UNSPECIFIED = 0
    LIGHT = 1
    FULL = 2
    ARCHIVE = 3

  additionalEndpoints = _messages.MessageField('EthereumEndpoints', 1)
  apiEnableAdmin = _messages.BooleanField(2)
  apiEnableDebug = _messages.BooleanField(3)
  consensusClient = _messages.EnumField('ConsensusClientValueValuesEnum', 4)
  executionClient = _messages.EnumField('ExecutionClientValueValuesEnum', 5)
  gethDetails = _messages.MessageField('GethDetails', 6)
  network = _messages.EnumField('NetworkValueValuesEnum', 7)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 8)
  validatorConfig = _messages.MessageField('ValidatorConfig', 9)


class EthereumEndpoints(_messages.Message):
  r"""Contains endpoint information specific to Ethereum nodes.

  Fields:
    beaconApiEndpoint: Output only. The assigned URL for the node's Beacon API
      endpoint.
    beaconPrometheusMetricsApiEndpoint: Output only. The assigned URL for the
      node's Beacon Prometheus metrics endpoint. See [Prometheus
      Metrics](https://lighthouse-book.sigmaprime.io/advanced_metrics.html)
      for more details.
    executionClientPrometheusMetricsApiEndpoint: Output only. The assigned URL
      for the node's execution client's Prometheus metrics endpoint.
  """

  beaconApiEndpoint = _messages.StringField(1)
  beaconPrometheusMetricsApiEndpoint = _messages.StringField(2)
  executionClientPrometheusMetricsApiEndpoint = _messages.StringField(3)


class GethDetails(_messages.Message):
  r"""Options for the Geth execution client. See [Command-line
  Options](https://geth.ethereum.org/docs/fundamentals/command-line-options)
  for more details.

  Enums:
    GarbageCollectionModeValueValuesEnum: Immutable. Blockchain garbage
      collection mode.

  Fields:
    garbageCollectionMode: Immutable. Blockchain garbage collection mode.
  """

  class GarbageCollectionModeValueValuesEnum(_messages.Enum):
    r"""Immutable. Blockchain garbage collection mode.

    Values:
      GARBAGE_COLLECTION_MODE_UNSPECIFIED: The garbage collection has not been
        specified.
      FULL: Configures Geth's garbage collection so that older data not needed
        for a full node is deleted. This is the default mode when creating a
        full node.
      ARCHIVE: Configures Geth's garbage collection so that old data is never
        deleted. This is the default mode when creating an archive node. This
        value can also be chosen when creating a full node in order to create
        a partial/recent archive node. See [Sync
        modes](https://geth.ethereum.org/docs/fundamentals/sync-modes) for
        more details.
    """
    GARBAGE_COLLECTION_MODE_UNSPECIFIED = 0
    FULL = 1
    ARCHIVE = 2

  garbageCollectionMode = _messages.EnumField('GarbageCollectionModeValueValuesEnum', 1)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ListBlockchainNodesResponse(_messages.Message):
  r"""Message for response to listing blockchain nodes.

  Fields:
    blockchainNodes: The list of nodes
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  blockchainNodes = _messages.MessageField('BlockchainNode', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have `Operation.error` value with a
      `google.rpc.Status.code` of `1`, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class ValidatorConfig(_messages.Message):
  r"""Configuration for validator-related parameters on the beacon client, and
  for any GCP-managed validator client.

  Fields:
    beaconFeeRecipient: An Ethereum address which the beacon client will send
      fee rewards to if no recipient is configured in the validator client.
      See https://lighthouse-book.sigmaprime.io/suggested-fee-recipient.html
      or https://docs.prylabs.network/docs/execution-node/fee-recipient for
      examples of how this is used. Note that while this is often described as
      "suggested", as we run the execution node we can trust the execution
      node, and therefore this is considered enforced.
    managedValidatorClient: Immutable. When true, deploys a GCP-managed
      validator client alongside the beacon client.
    mevRelayUrls: URLs for MEV-relay services to use for block building. When
      set, a GCP-managed MEV-boost service is configured on the beacon client.
  """

  beaconFeeRecipient = _messages.StringField(1)
  managedValidatorClient = _messages.BooleanField(2)
  mevRelayUrls = _messages.StringField(3, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
