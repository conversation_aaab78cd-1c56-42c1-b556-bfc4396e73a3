"""Generated message classes for blockchainvalidatormanager version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'blockchainvalidatormanager'


class BlockchainValidatorConfig(_messages.Message):
  r"""Represents the configuration of a blockchain validator, as it would be
  configured on a validator client.

  Enums:
    BlockchainTypeValueValuesEnum: Required. Immutable. The blockchain type of
      the validator.
    KeySourceValueValuesEnum: Required. Immutable. The source of the voting
      key for the blockchain validator.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    blockchainNodeId: Optional. The fully qualified name of the blockchain
      node which carries out work on behalf of the validator. If not set, the
      validator must either be operated outside of Blockchain Validator
      Manager, or it will be offline (no attestations or blocks will be
      produced). If this node is offline or deleted, the validator will be
      offline.
    blockchainType: Required. Immutable. The blockchain type of the validator.
    createTime: Output only. [Output only] Create time stamp
    ethereumProtocolDetails: Optional. Ethereum-specific configuration for a
      blockchain validator.
    existingSeedPhraseReference: Optional. An existing seed phrase, read from
      Secret Manager.
    keySource: Required. Immutable. The source of the voting key for the
      blockchain validator.
    labels: Optional. Labels as key value pairs
    name: Identifier. The name of the validator. It must have the format `"pro
      jects/{project}/locations/{location}/blockchainValidatorConfigs/{validat
      or}"`. `{validator}` must contain only letters (`[A-Za-z]`), numbers
      (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`),
      plus (`+`) or percent signs (`%`). It must be between 3 and 255
      characters in length, and it must not start with `"goog"`.
    remoteWeb3Signer: Optional. Connection details of a remote Web3Signer
      service to use for signing attestations and blocks.
    seedPhraseReference: Optional. A new seed phrase, optionally written to
      Secret Manager.
    updateTime: Output only. [Output only] Update time stamp
    validationWorkEnabled: Required. True if the blockchain node requests and
      signs attestations and blocks on behalf of this validator, false if not.
      This does NOT define whether the blockchain expects work to occur, only
      whether the blockchain node specified above is carrying out validation
      tasks. This should be enabled under normal conditions, but may be useful
      when migrating validators to/from Blockchain Node Engine, where the
      validator may be paused during the migration.
    votingPublicKey: Output only. Immutable. The public key identifier of the
      validator, as a hexadecimal string prefixed with "0x". Note content of
      this field varies depending on the blockchain. This is provided by the
      server when creating or importing keys, and copied from the remote key
      signer configuration when configuring an external signing service.
  """

  class BlockchainTypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The blockchain type of the validator.

    Values:
      BLOCKCHAIN_TYPE_UNSPECIFIED: Blockchain type has not been specified, but
        should be.
      ETHEREUM: The blockchain type is Ethereum.
    """
    BLOCKCHAIN_TYPE_UNSPECIFIED = 0
    ETHEREUM = 1

  class KeySourceValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The source of the voting key for the blockchain
    validator.

    Values:
      KEY_SOURCE_UNSPECIFIED: Voting key source has not been specified, but
        should be.
      REMOTE_WEB3_SIGNER: The voting key is stored in a remote signing service
        (Web3Signer) and signing requests are delegated.
      SEED_PHRASE_REFERENCE: Derive voting keys from new seed material.
      EXISTING_SEED_PHRASE_REFERENCE: Derive voting keys from existing seed
        material.
    """
    KEY_SOURCE_UNSPECIFIED = 0
    REMOTE_WEB3_SIGNER = 1
    SEED_PHRASE_REFERENCE = 2
    EXISTING_SEED_PHRASE_REFERENCE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  blockchainNodeId = _messages.StringField(1)
  blockchainType = _messages.EnumField('BlockchainTypeValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  ethereumProtocolDetails = _messages.MessageField('EthereumDetails', 4)
  existingSeedPhraseReference = _messages.MessageField('ExistingSeedPhraseReference', 5)
  keySource = _messages.EnumField('KeySourceValueValuesEnum', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  remoteWeb3Signer = _messages.MessageField('RemoteWeb3Signer', 9)
  seedPhraseReference = _messages.MessageField('SeedPhraseReference', 10)
  updateTime = _messages.StringField(11)
  validationWorkEnabled = _messages.BooleanField(12)
  votingPublicKey = _messages.StringField(13)


class BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsCreateRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsC
  reateRequest object.

  Fields:
    blockchainValidatorConfig: A BlockchainValidatorConfig resource to be
      passed as the request body.
    blockchainValidatorConfigId: Required. Id of the requesting object If
      auto-generating Id server-side, remove this field and
      blockchain_validator_config_id from the method_signature of Create RPC
    parent: Required. The parent location. Format:
      projects/{project_number}/locations/{location}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  blockchainValidatorConfig = _messages.MessageField('BlockchainValidatorConfig', 1)
  blockchainValidatorConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsDeleteRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsD
  eleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsGetRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsG
  etRequest object.

  Fields:
    name: Required. The resource name of the validator config. This is derived
      from the public key, however it is sensitive due to the inclusion of the
      project ID in the resource name. e.g. `projects/my-project/locations/us-
      central1/blockchainValidatorConfigs/0xa75dbe920352d3e91d06bd8cfe8eb67867
      7127f8748854a7a8894e3c121b63169259448a4b63e5cfb992da412ac91c30`.
  """

  name = _messages.StringField(1, required=True)


class BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsListRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsL
  istRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The location to list blockchain validator configurations
      within.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsPatchRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsBlockchainValidatorConfigsP
  atchRequest object.

  Fields:
    blockchainValidatorConfig: A BlockchainValidatorConfig resource to be
      passed as the request body.
    name: Identifier. The name of the validator. It must have the format `"pro
      jects/{project}/locations/{location}/blockchainValidatorConfigs/{validat
      or}"`. `{validator}` must contain only letters (`[A-Za-z]`), numbers
      (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`),
      plus (`+`) or percent signs (`%`). It must be between 3 and 255
      characters in length, and it must not start with `"goog"`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the blockchain validator resource by the update. The
      fields specified in the `update_mask` are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  blockchainValidatorConfig = _messages.MessageField('BlockchainValidatorConfig', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BlockchainvalidatormanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BlockchainvalidatormanagerProjectsLocationsListRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BlockchainvalidatormanagerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BlockchainvalidatormanagerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BlockchainvalidatormanagerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BlockchainvalidatormanagerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A BlockchainvalidatormanagerProjectsLocationsOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class DeleteBlockchainValidatorConfigResponse(_messages.Message):
  r"""Response message for DeleteBlockchainValidatorConfig

  Fields:
    slashingProtectionData: Optional. Slashing protection data for a set of
      Ethereum voting keys, as described in EIP-3076, deserialized into a
      string.
  """

  slashingProtectionData = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EthereumDetails(_messages.Message):
  r"""Blockchain validator configuration unique to Ethereum blockchains.

  Fields:
    gasLimit: Optional. Immutable. Optionally requested (not enforced) maximum
      gas per block. This is sent to the block builder service, however
      whether it is followed depends on the service. This field is only read
      if the field use_block_builder_proposals is set to true. If not
      specified, the validator client will use a default value.
    graffiti: Optional. Input only. Graffiti is a custom string published in
      blocks proposed by the validator. This can only be written, as the
      current value cannot be read back from the validator client API. See
      https://lighthouse-book.sigmaprime.io/graffiti.html for an example of
      how this is used. If not set, the validator client's default is used. If
      no blockchain node is specified, this has no effect as no validator
      client is run.
    suggestedFeeRecipient: Immutable. The Ethereum address to which fee
      rewards should be sent. This can only be set when creating the
      validator. If no blockchain node is specified for the validator, this
      has no effect as no validator client is run. See also
      https://lighthouse-book.sigmaprime.io/suggested-fee-recipient.html for
      more context.
    useBlockBuilderProposals: Optional. Immutable. Enable use of the external
      block building services (MEV).
  """

  gasLimit = _messages.IntegerField(1)
  graffiti = _messages.StringField(2)
  suggestedFeeRecipient = _messages.StringField(3)
  useBlockBuilderProposals = _messages.BooleanField(4)


class ExistingSeedPhraseReference(_messages.Message):
  r"""Location of existing seed material, and derivation path used to generate
  the voting key.

  Fields:
    depositTxData: Output only. Immutable. The deposit transaction data
      corresponding to the derived key.
    derivationIndex: Optional. Immutable. The index to derive the voting key
      at, used as part of a derivation path. The derivation path is built from
      this as "m/12381/3600//0/0" See also
      https://eips.ethereum.org/EIPS/eip-2334#eth2-specific-parameters
    seedPhraseSecret: Required. Immutable. Reference into Secret Manager for
      where the seed phrase is stored.
  """

  depositTxData = _messages.StringField(1)
  derivationIndex = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  seedPhraseSecret = _messages.StringField(3)


class ListBlockchainValidatorConfigsResponse(_messages.Message):
  r"""A message representing all blockchain validator configs in the project.

  Fields:
    blockchainValidatorConfigs: The validator configurations defined within
      the project and location.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  blockchainValidatorConfigs = _messages.MessageField('BlockchainValidatorConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class RemoteWeb3Signer(_messages.Message):
  r"""Configuration to use an external key signing service, such as the
  service endpoint. The external key signer is expected to be managed entirely
  by the customer. For reference see https://docs.web3signer.consensys.net/
  for details on Web3Signer and
  https://docs.web3signer.consensys.net/reference/api/json-rpc for the API
  exposed by the external service.

  Fields:
    timeoutDuration: Optional. Timeout for requests to the Web3Signer service.
    votingPublicKey: Required. Immutable. The public key of the validator, as
      a hexadecimal string prefixed with "0x". This is used as the identifier
      for the key when sending requests to the Web3Signer service.
    web3signerUri: Required. URI of the Web3Signer service the validator
      client connects to, to request signing of attestations, blocks, etc.
  """

  timeoutDuration = _messages.StringField(1)
  votingPublicKey = _messages.StringField(2)
  web3signerUri = _messages.StringField(3)


class SeedPhraseReference(_messages.Message):
  r"""Derivation path used to generate the voting key, and optionally Secret
  Manager secret to backup the seed phrase to.

  Fields:
    depositTxData: Output only. Immutable. The deposit transaction data
      corresponding to the derived key.
    derivationIndex: Output only. Immutable. The index to derive the voting
      key at, used as part of a derivation path. The derivation path is built
      from this as "m/12381/3600//0/0" See also
      https://eips.ethereum.org/EIPS/eip-2334#eth2-specific-parameters
    exportSeedPhrase: Optional. Immutable. True to export the seed phrase to
      Secret Manager.
    seedPhraseSecret: Required. Immutable. Reference into Secret Manager for
      where the seed phrase is stored.
  """

  depositTxData = _messages.StringField(1)
  derivationIndex = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  exportSeedPhrase = _messages.BooleanField(3)
  seedPhraseSecret = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
