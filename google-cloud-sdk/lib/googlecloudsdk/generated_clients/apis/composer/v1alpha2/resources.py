# -*- coding: utf-8 -*- #
# Copyright 2023 Google LLC. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Resource definitions for Cloud Platform Apis generated from apitools."""

import enum


BASE_URL = 'https://composer.googleapis.com/v1alpha2/'
DOCS_URL = 'https://cloud.google.com/composer/'


class Collections(enum.Enum):
  """Collections for all supported apis."""

  PROJECTS = (
      'projects',
      'projects/{projectsId}',
      {},
      ['projectsId'],
      True
  )
  PROJECTS_LOCATIONS = (
      'projects.locations',
      'projects/{projectsId}/locations/{locationsId}',
      {},
      ['projectsId', 'locationsId'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS = (
      'projects.locations.environments',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS_DAGS = (
      'projects.locations.environments.dags',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}/dags/{dagsId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS_DAGS_DAGRUNS = (
      'projects.locations.environments.dags.dagRuns',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}/dags/{dagsId}/dagRuns/{dagRunsId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS_DAGS_DAGRUNS_TASKINSTANCES = (
      'projects.locations.environments.dags.dagRuns.taskInstances',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}/dags/{dagsId}/dagRuns/{dagRunsId}/'
              'taskInstances/{taskInstancesId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS_USERWORKLOADSCONFIGMAPS = (
      'projects.locations.environments.userWorkloadsConfigMaps',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}/userWorkloadsConfigMaps/'
              '{userWorkloadsConfigMapsId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_ENVIRONMENTS_USERWORKLOADSSECRETS = (
      'projects.locations.environments.userWorkloadsSecrets',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/environments/'
              '{environmentsId}/userWorkloadsSecrets/{userWorkloadsSecretsId}',
      },
      ['name'],
      True
  )
  PROJECTS_LOCATIONS_OPERATIONS = (
      'projects.locations.operations',
      '{+name}',
      {
          '':
              'projects/{projectsId}/locations/{locationsId}/operations/'
              '{operationsId}',
      },
      ['name'],
      True
  )

  def __init__(self, collection_name, path, flat_paths, params,
               enable_uri_parsing):
    self.collection_name = collection_name
    self.path = path
    self.flat_paths = flat_paths
    self.params = params
    self.enable_uri_parsing = enable_uri_parsing
