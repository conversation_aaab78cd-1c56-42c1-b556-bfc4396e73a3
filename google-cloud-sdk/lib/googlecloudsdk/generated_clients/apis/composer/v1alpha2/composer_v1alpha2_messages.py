"""Generated message classes for composer version v1alpha2.

Manages Apache Airflow environments on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'composer'


class ActivateDagRequest(_messages.Message):
  r"""Request to unpause a DAG."""


class AirflowMetadataRetentionPolicyConfig(_messages.Message):
  r"""The policy for airflow metadata database retention.

  Enums:
    RetentionModeValueValuesEnum: Optional. Retention can be either enabled or
      disabled.

  Fields:
    retentionDays: Optional. How many days data should be retained for.
    retentionMode: Optional. Retention can be either enabled or disabled.
  """

  class RetentionModeValueValuesEnum(_messages.Enum):
    r"""Optional. Retention can be either enabled or disabled.

    Values:
      RETENTION_MODE_UNSPECIFIED: Default mode doesn't change environment
        parameters.
      RETENTION_MODE_ENABLED: Retention policy is enabled.
      RETENTION_MODE_DISABLED: Retention policy is disabled.
    """
    RETENTION_MODE_UNSPECIFIED = 0
    RETENTION_MODE_ENABLED = 1
    RETENTION_MODE_DISABLED = 2

  retentionDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  retentionMode = _messages.EnumField('RetentionModeValueValuesEnum', 2)


class AllowedIpRange(_messages.Message):
  r"""Allowed IP range with user-provided description.

  Fields:
    description: Optional. User-provided description. It must contain at most
      300 characters.
    value: IP address or range, defined using CIDR notation, of requests that
      this rule applies to. Examples: `***********` or `***********/16` or
      `2001:db8::/32` or `2001:0db8:0000:0042:0000:8a2e:0370:7334`. IP range
      prefixes should be properly truncated. For example, `*******/24` should
      be truncated to `*******/24`. Similarly, for IPv6, `2001:db8::1/32`
      should be truncated to `2001:db8::/32`.
  """

  description = _messages.StringField(1)
  value = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CheckUpgradeRequest(_messages.Message):
  r"""Request to check whether image upgrade will succeed.

  Fields:
    imageVersion: The version of the software running in the environment. This
      encapsulates both the version of Cloud Composer functionality and the
      version of Apache Airflow. It must match the regular expression `compose
      r-([0-9]+(\.[0-9]+\.[0-9]+(-preview\.[0-9]+)?)?|latest)-airflow-([0-
      9]+(\.[0-9]+(\.[0-9]+)?)?)`. When used as input, the server also checks
      if the provided version is supported and denies the request for an
      unsupported version. The Cloud Composer portion of the image version is
      a full [semantic version](https://semver.org), or an alias in the form
      of major version number or `latest`. When an alias is provided, the
      server replaces it with the current Cloud Composer version that
      satisfies the alias. The Apache Airflow portion of the image version is
      a full semantic version that points to one of the supported Apache
      Airflow versions, or an alias in the form of only major or major.minor
      versions specified. When an alias is provided, the server replaces it
      with the latest Apache Airflow version that satisfies the alias and is
      supported in the given Cloud Composer version. In all cases, the
      resolved image version is stored in the same field. See also [version
      list](/composer/docs/concepts/versioning/composer-versions) and
      [versioning overview](/composer/docs/concepts/versioning/composer-
      versioning-overview).
  """

  imageVersion = _messages.StringField(1)


class CheckUpgradeResponse(_messages.Message):
  r"""Message containing information about the result of an upgrade check
  operation.

  Enums:
    ContainsPypiModulesConflictValueValuesEnum: Output only. Whether build has
      succeeded or failed on modules conflicts.

  Messages:
    PypiDependenciesValue: Pypi dependencies specified in the environment
      configuration, at the time when the build was triggered.

  Fields:
    buildLogUri: Output only. Url for a docker build log of an upgraded image.
    containsPypiModulesConflict: Output only. Whether build has succeeded or
      failed on modules conflicts.
    imageVersion: Composer image for which the build was happening.
    pypiConflictBuildLogExtract: Output only. Extract from a docker image
      build log containing information about pypi modules conflicts.
    pypiDependencies: Pypi dependencies specified in the environment
      configuration, at the time when the build was triggered.
  """

  class ContainsPypiModulesConflictValueValuesEnum(_messages.Enum):
    r"""Output only. Whether build has succeeded or failed on modules
    conflicts.

    Values:
      CONFLICT_RESULT_UNSPECIFIED: It is unknown whether build had conflicts
        or not.
      CONFLICT: There were python packages conflicts.
      NO_CONFLICT: There were no python packages conflicts.
    """
    CONFLICT_RESULT_UNSPECIFIED = 0
    CONFLICT = 1
    NO_CONFLICT = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PypiDependenciesValue(_messages.Message):
    r"""Pypi dependencies specified in the environment configuration, at the
    time when the build was triggered.

    Messages:
      AdditionalProperty: An additional property for a PypiDependenciesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        PypiDependenciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PypiDependenciesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildLogUri = _messages.StringField(1)
  containsPypiModulesConflict = _messages.EnumField('ContainsPypiModulesConflictValueValuesEnum', 2)
  imageVersion = _messages.StringField(3)
  pypiConflictBuildLogExtract = _messages.StringField(4)
  pypiDependencies = _messages.MessageField('PypiDependenciesValue', 5)


class CidrBlock(_messages.Message):
  r"""CIDR block with an optional name.

  Fields:
    cidrBlock: CIDR block that must be specified in CIDR notation.
    displayName: User-defined name that identifies the CIDR block.
  """

  cidrBlock = _messages.StringField(1)
  displayName = _messages.StringField(2)


class CloudDataLineageIntegration(_messages.Message):
  r"""Configuration for Cloud Data Lineage integration.

  Fields:
    enabled: Optional. Whether or not Cloud Data Lineage integration is
      enabled.
  """

  enabled = _messages.BooleanField(1)


class ComposerProjectsLocationsEnvironmentsCheckUpgradeRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsCheckUpgradeRequest object.

  Fields:
    checkUpgradeRequest: A CheckUpgradeRequest resource to be passed as the
      request body.
    environment: The resource name of the environment to check upgrade for, in
      the form: "projects/{projectId}/locations/{locationId}/environments/{env
      ironmentId}"
  """

  checkUpgradeRequest = _messages.MessageField('CheckUpgradeRequest', 1)
  environment = _messages.StringField(2, required=True)


class ComposerProjectsLocationsEnvironmentsCreateRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsCreateRequest object.

  Fields:
    environment: A Environment resource to be passed as the request body.
    parent: The parent must be of the form
      "projects/{projectId}/locations/{locationId}".
  """

  environment = _messages.MessageField('Environment', 1)
  parent = _messages.StringField(2, required=True)


class ComposerProjectsLocationsEnvironmentsDagsActivateRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsActivateRequest object.

  Fields:
    activateDagRequest: A ActivateDagRequest resource to be passed as the
      request body.
    name: Required. The name of dag to pause.
  """

  activateDagRequest = _messages.MessageField('ActivateDagRequest', 1)
  name = _messages.StringField(2, required=True)


class ComposerProjectsLocationsEnvironmentsDagsDagRunsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsDagRunsGetRequest object.

  Fields:
    name: Required. The resource name of the DAG to retrieve. Must be in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}/dags/{dagId}/dagRuns/{dagRunId}".
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsDagsDagRunsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsDagRunsListRequest object.

  Fields:
    filter: An expression for filtering the results. For example:
      executionDate<="2022-02-22T22:22:00Z"
    pageSize: The maximum number of DAG runs to return.
    pageToken: The next_page_token returned from a previous List request.
    parent: Required. List DAG runs in the given parent resource. Parent must
      be in the form: "projects/{projectId}/locations/{locationId}/environment
      s/{environmentId}/dags/{dagId}". If dagId is specified as a wildcard
      '-', the response will contain the runs of all DAGs.
    runsCountPerDag: Optional. If set to value greater than 0, the cross-DAG
      list mode will be used and response will contain the specified number of
      last DAG runs for selected DAGs. Pagination is not supported if this
      field is set. Sort order is descending by DAG run execution_date if this
      field is set. Filter is supported.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  runsCountPerDag = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class ComposerProjectsLocationsEnvironmentsDagsDagRunsTaskInstancesGetRequest(_messages.Message):
  r"""A
  ComposerProjectsLocationsEnvironmentsDagsDagRunsTaskInstancesGetRequest
  object.

  Fields:
    name: Required. The resource name of the task instance to retrieve. Must
      be in the form: "projects/{projectId}/locations/{locationId}/environment
      s/{environmentId}/dags/{dagId}/dagRuns/{dagRunId}/taskInstances/{taskIdW
      ithOptionalMapIndex}".
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsDagsDagRunsTaskInstancesListRequest(_messages.Message):
  r"""A
  ComposerProjectsLocationsEnvironmentsDagsDagRunsTaskInstancesListRequest
  object.

  Fields:
    filter: An expression for filtering the results. For example:
      executionDate<="2022-02-22T22:22:00Z"
    pageSize: The maximum number of tasks to return.
    pageToken: The next_page_token returned from a previous List request.
    parent: Required. List task instances in the given parent DAG run. Parent
      must be in the form: "projects/{projectId}/locations/{locationId}/enviro
      nments/{environmentId}/dags/{dagId}/dagRuns/{dagRunId}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ComposerProjectsLocationsEnvironmentsDagsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsGetRequest object.

  Fields:
    name: Required. The resource name of the DAG to retrieve. Must be in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}/dags/{dagId}".
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsDagsGetSourceCodeRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsGetSourceCodeRequest object.

  Fields:
    dag: Required. The resource name of the DAG to fetch source code of. Must
      be in the form: "projects/{projectId}/locations/{locationId}/environment
      s/{environmentId}/dags/{dagId}".
  """

  dag = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsDagsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsListRequest object.

  Fields:
    pageSize: The maximum number of DAGs to return.
    pageToken: The next_page_token returned from a previous List request.
    parent: Required. List DAGs in the given parent resource. Parent must be
      in the form: "projects/{projectId}/locations/{locationId}/environments/{
      environmentId}".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ComposerProjectsLocationsEnvironmentsDagsListStatsRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsListStatsRequest object.

  Fields:
    environment: Required. List DAGs in the given Composer environment. Parent
      must be in the form: "projects/{projectId}/locations/{locationId}/enviro
      nments/{environmentId}".
    interval_endTime: Optional. Exclusive end of the interval. If specified, a
      Timestamp matching this interval will have to be before the end.
    interval_startTime: Optional. Inclusive start of the interval. If
      specified, a Timestamp matching this interval will have to be the same
      or after the start.
    pageSize: The maximum number of DAGs to return.
    pageToken: The next_page_token returned from a previous List request.
  """

  environment = _messages.StringField(1, required=True)
  interval_endTime = _messages.StringField(2)
  interval_startTime = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ComposerProjectsLocationsEnvironmentsDagsPauseRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsPauseRequest object.

  Fields:
    name: Required. The name of dag to pause.
    pauseDagRequest: A PauseDagRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  pauseDagRequest = _messages.MessageField('PauseDagRequest', 2)


class ComposerProjectsLocationsEnvironmentsDagsTasksListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsTasksListRequest object.

  Fields:
    pageSize: The maximum number of tasks to return.
    pageToken: The next_page_token returned from a previous List request.
    parent: Required. List tasks in the given parent DAG. Parent must be in
      the form: "projects/{projectId}/locations/{locationId}/environments/{env
      ironmentId}/dags/{dagId}".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ComposerProjectsLocationsEnvironmentsDagsTriggerRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDagsTriggerRequest object.

  Fields:
    dag: Required. The resource name of the DAG to trigger. Must be in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}/dags/{dagId}".
    triggerDagRequest: A TriggerDagRequest resource to be passed as the
      request body.
  """

  dag = _messages.StringField(1, required=True)
  triggerDagRequest = _messages.MessageField('TriggerDagRequest', 2)


class ComposerProjectsLocationsEnvironmentsDatabaseFailoverRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDatabaseFailoverRequest object.

  Fields:
    databaseFailoverRequest: A DatabaseFailoverRequest resource to be passed
      as the request body.
    environment: Target environment:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  databaseFailoverRequest = _messages.MessageField('DatabaseFailoverRequest', 1)
  environment = _messages.StringField(2, required=True)


class ComposerProjectsLocationsEnvironmentsDeleteRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsDeleteRequest object.

  Fields:
    name: The environment to delete, in the form: "projects/{projectId}/locati
      ons/{locationId}/environments/{environmentId}".
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsExecuteAirflowCommandRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsExecuteAirflowCommandRequest
  object.

  Fields:
    environment: The resource name of the environment in the form: "projects/{
      projectId}/locations/{locationId}/environments/{environmentId}".
    executeAirflowCommandRequest: A ExecuteAirflowCommandRequest resource to
      be passed as the request body.
  """

  environment = _messages.StringField(1, required=True)
  executeAirflowCommandRequest = _messages.MessageField('ExecuteAirflowCommandRequest', 2)


class ComposerProjectsLocationsEnvironmentsFetchDatabasePropertiesRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsFetchDatabasePropertiesRequest
  object.

  Fields:
    environment: Required. The resource name of the environment, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  environment = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsGetRequest object.

  Fields:
    name: The resource name of the environment to get, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsListImportErrorsRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsListImportErrorsRequest object.

  Fields:
    environment: Required. List import errors in the given Composer
      environment. Environment name must be in the form: "projects/{projectId}
      /locations/{locationId}/environments/{environmentId}".
    pageSize: The maximum number of DAGs to return.
    pageToken: The next_page_token returned from a previous List request.
  """

  environment = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class ComposerProjectsLocationsEnvironmentsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsListRequest object.

  Fields:
    pageSize: The maximum number of environments to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: List environments in the given project and location, in the form:
      "projects/{projectId}/locations/{locationId}"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ComposerProjectsLocationsEnvironmentsLoadSnapshotRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsLoadSnapshotRequest object.

  Fields:
    environment: The resource name of the target environment in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
    loadSnapshotRequest: A LoadSnapshotRequest resource to be passed as the
      request body.
  """

  environment = _messages.StringField(1, required=True)
  loadSnapshotRequest = _messages.MessageField('LoadSnapshotRequest', 2)


class ComposerProjectsLocationsEnvironmentsPatchRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsPatchRequest object.

  Fields:
    environment: A Environment resource to be passed as the request body.
    name: The relative resource name of the environment to update, in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}"
    updateMask: Required. A comma-separated list of paths, relative to
      `Environment`, of fields to update. For example, to set the version of
      scikit-learn to install in the environment to 0.19.0 and to remove an
      existing installation of argparse, the `updateMask` parameter would
      include the following two `paths` values:
      "config.softwareConfig.pypiPackages.scikit-learn" and
      "config.softwareConfig.pypiPackages.argparse". The included patch
      environment would specify the scikit-learn version as follows: {
      "config":{ "softwareConfig":{ "pypiPackages":{ "scikit-learn":"==0.19.0"
      } } } } Note that in the above example, any existing PyPI packages other
      than scikit-learn and argparse will be unaffected. Only one update type
      may be included in a single request's `updateMask`. For example, one
      cannot update both the PyPI packages and labels in the same request.
      However, it is possible to update multiple members of a map field
      simultaneously in the same request. For example, to set the labels
      "label1" and "label2" while clearing "label3" (assuming it already
      exists), one can provide the paths "labels.label1", "labels.label2", and
      "labels.label3" and populate the patch environment as follows: {
      "labels":{ "label1":"new-label1-value" "label2":"new-label2-value" } }
      Note that in the above example, any existing labels that are not
      included in the `updateMask` will be unaffected. It is also possible to
      replace an entire map field by providing the map field's path in the
      `updateMask`. The new value of the field will be that which is provided
      in the patch environment. For example, to delete all pre-existing user-
      specified PyPI packages and install botocore at version 1.7.14, the
      `updateMask` would contain the path
      "config.softwareConfig.pypiPackages", and the patch environment would be
      the following: { "config":{ "softwareConfig":{ "pypiPackages":{
      "botocore":"==1.7.14" } } } } **Note:** Only the following fields can be
      updated: * `config.softwareConfig.pypiPackages` * Replace all custom
      custom PyPI packages. If a replacement package map is not included in
      `environment`, all custom PyPI packages are cleared. It is an error to
      provide both this mask and a mask specifying an individual package. *
      `config.softwareConfig.pypiPackages.`packagename * Update the custom
      PyPI package *packagename*, preserving other packages. To delete the
      package, include it in `updateMask`, and omit the mapping for it in
      `environment.config.softwareConfig.pypiPackages`. It is an error to
      provide both a mask of this form and the
      `config.softwareConfig.pypiPackages` mask. * `labels` * Replace all
      environment labels. If a replacement labels map is not included in
      `environment`, all labels are cleared. It is an error to provide both
      this mask and a mask specifying one or more individual labels. *
      `labels.`labelName * Set the label named *labelName*, while preserving
      other labels. To delete the label, include it in `updateMask` and omit
      its mapping in `environment.labels`. It is an error to provide both a
      mask of this form and the `labels` mask. * `config.nodeCount` *
      Horizontally scale the number of nodes in the environment. An integer
      greater than or equal to 3 must be provided in the `config.nodeCount`
      field. Supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*. * `config.webServerNetworkAccessControl` *
      Replace the environment's current `WebServerNetworkAccessControl`. *
      `config.softwareConfig.airflowConfigOverrides` * Replace all Apache
      Airflow config overrides. If a replacement config overrides map is not
      included in `environment`, all config overrides are cleared. It is an
      error to provide both this mask and a mask specifying one or more
      individual config overrides. *
      `config.softwareConfig.airflowConfigOverrides.`section-name * Override
      the Apache Airflow config property *name* in the section named
      *section*, preserving other properties. To delete the property override,
      include it in `updateMask` and omit its mapping in
      `environment.config.softwareConfig.airflowConfigOverrides`. It is an
      error to provide both a mask of this form and the
      `config.softwareConfig.airflowConfigOverrides` mask. *
      `config.softwareConfig.envVariables` * Replace all environment
      variables. If a replacement environment variable map is not included in
      `environment`, all custom environment variables are cleared. *
      `config.softwareConfig.imageVersion` * Upgrade the version of the
      environment in-place. Refer to `SoftwareConfig.image_version` for
      information on how to format the new image version. Additionally, the
      new image version cannot effect a version downgrade, and must match the
      current image version's Composer and Airflow major versions. Consult the
      [Cloud Composer version
      list](/composer/docs/concepts/versioning/composer-versions) for valid
      values. * `config.softwareConfig.schedulerCount` * Horizontally scale
      the number of schedulers in Airflow. A positive integer not greater than
      the number of nodes must be provided in the
      `config.softwareConfig.schedulerCount` field. Supported for Cloud
      Composer environments in versions composer-1.*.*-airflow-2.*.*. *
      `config.softwareConfig.cloudDataLineageIntegration` * Configuration for
      Cloud Data Lineage integration. * `config.databaseConfig.machineType` *
      Cloud SQL machine type used by Airflow database. It has to be one of:
      db-n1-standard-2, db-n1-standard-4, db-n1-standard-8 or
      db-n1-standard-16. Supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*. * `config.webServerConfig.machineType` *
      Machine type on which Airflow web server is running. It has to be one
      of: composer-n1-webserver-2, composer-n1-webserver-4 or
      composer-n1-webserver-8. Supported for Cloud Composer environments in
      versions composer-1.*.*-airflow-*.*.*. * `config.maintenanceWindow` *
      Maintenance window during which Cloud Composer components may be under
      maintenance. * `config.workloadsConfig` * The workloads configuration
      settings for the GKE cluster associated with the Cloud Composer
      environment. Supported for Cloud Composer environments in versions
      composer-2.*.*-airflow-*.*.* and newer. * `config.environmentSize` * The
      size of the Cloud Composer environment. Supported for Cloud Composer
      environments in versions composer-2.*.*-airflow-*.*.* and newer.
  """

  environment = _messages.MessageField('Environment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ComposerProjectsLocationsEnvironmentsPollAirflowCommandRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsPollAirflowCommandRequest object.

  Fields:
    environment: The resource name of the environment in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
    pollAirflowCommandRequest: A PollAirflowCommandRequest resource to be
      passed as the request body.
  """

  environment = _messages.StringField(1, required=True)
  pollAirflowCommandRequest = _messages.MessageField('PollAirflowCommandRequest', 2)


class ComposerProjectsLocationsEnvironmentsRestartWebServerRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsRestartWebServerRequest object.

  Fields:
    name: Required. The resource name of the environment to restart the web
      server for, in the form: "projects/{projectId}/locations/{locationId}/en
      vironments/{environmentId}"
    restartWebServerRequest: A RestartWebServerRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  restartWebServerRequest = _messages.MessageField('RestartWebServerRequest', 2)


class ComposerProjectsLocationsEnvironmentsSaveSnapshotRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsSaveSnapshotRequest object.

  Fields:
    environment: The resource name of the source environment in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
    saveSnapshotRequest: A SaveSnapshotRequest resource to be passed as the
      request body.
  """

  environment = _messages.StringField(1, required=True)
  saveSnapshotRequest = _messages.MessageField('SaveSnapshotRequest', 2)


class ComposerProjectsLocationsEnvironmentsStopAirflowCommandRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsStopAirflowCommandRequest object.

  Fields:
    environment: The resource name of the environment in the form: "projects/{
      projectId}/locations/{locationId}/environments/{environmentId}".
    stopAirflowCommandRequest: A StopAirflowCommandRequest resource to be
      passed as the request body.
  """

  environment = _messages.StringField(1, required=True)
  stopAirflowCommandRequest = _messages.MessageField('StopAirflowCommandRequest', 2)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsCreateRequest(_messages.Message):
  r"""A
  ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsCreateRequest
  object.

  Fields:
    parent: Required. The environment name to create a ConfigMap for, in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}"
    userWorkloadsConfigMap: A UserWorkloadsConfigMap resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  userWorkloadsConfigMap = _messages.MessageField('UserWorkloadsConfigMap', 2)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsDeleteRequest(_messages.Message):
  r"""A
  ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsDeleteRequest
  object.

  Fields:
    name: Required. The ConfigMap to delete, in the form: "projects/{projectId
      }/locations/{locationId}/environments/{environmentId}/userWorkloadsConfi
      gMaps/{userWorkloadsConfigMapId}"
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsGetRequest
  object.

  Fields:
    name: Required. The resource name of the ConfigMap to get, in the form: "p
      rojects/{projectId}/locations/{locationId}/environments/{environmentId}/
      userWorkloadsConfigMaps/{userWorkloadsConfigMapId}"
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsListRequest(_messages.Message):
  r"""A
  ComposerProjectsLocationsEnvironmentsUserWorkloadsConfigMapsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of ConfigMaps to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. List ConfigMaps in the given environment, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsCreateRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsCreateRequest
  object.

  Fields:
    parent: Required. The environment name to create a Secret for, in the
      form: "projects/{projectId}/locations/{locationId}/environments/{environ
      mentId}"
    userWorkloadsSecret: A UserWorkloadsSecret resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  userWorkloadsSecret = _messages.MessageField('UserWorkloadsSecret', 2)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsDeleteRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsDeleteRequest
  object.

  Fields:
    name: Required. The Secret to delete, in the form: "projects/{projectId}/l
      ocations/{locationId}/environments/{environmentId}/userWorkloadsSecrets/
      {userWorkloadsSecretId}"
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Secret to get, in the form: "proj
      ects/{projectId}/locations/{locationId}/environments/{environmentId}/use
      rWorkloadsSecrets/{userWorkloadsSecretId}"
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsUserWorkloadsSecretsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of Secrets to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. List Secrets in the given environment, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ComposerProjectsLocationsEnvironmentsWorkloadsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsEnvironmentsWorkloadsListRequest object.

  Fields:
    filter: Optional. The list filter. Currently only supports equality on the
      type field. The value of a field specified in the filter expression must
      be one ComposerWorkloadType enum option. It's possible to get multiple
      types using "OR" operator, e.g.: "type=SCHEDULER OR type=CELERY_WORKER".
      If not specified, all items are returned.
    pageSize: Optional. The maximum number of environments to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The environment name to get workloads for, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ComposerProjectsLocationsImageVersionsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsImageVersionsListRequest object.

  Fields:
    includePastReleases: Whether or not image versions from old releases
      should be included.
    pageSize: The maximum number of image_versions to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: List ImageVersions in the given project and location, in the form:
      "projects/{projectId}/locations/{locationId}"
  """

  includePastReleases = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ComposerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ComposerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ComposerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ComposerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ComposerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ComposerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ComposerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ComposerWorkload(_messages.Message):
  r"""Information about a single workload.

  Enums:
    TypeValueValuesEnum: Type of a workload.

  Fields:
    name: Name of a workload.
    status: Output only. Status of a workload.
    type: Type of a workload.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of a workload.

    Values:
      COMPOSER_WORKLOAD_TYPE_UNSPECIFIED: Not able to determine the type of
        the workload.
      CELERY_WORKER: Celery worker.
      KUBERNETES_WORKER: Kubernetes worker.
      KUBERNETES_OPERATOR_POD: Workload created by Kubernetes Pod Operator.
      SCHEDULER: Airflow scheduler.
      DAG_PROCESSOR: Airflow Dag processor.
      TRIGGERER: Airflow triggerer.
      WEB_SERVER: Airflow web server UI.
      REDIS: Redis.
    """
    COMPOSER_WORKLOAD_TYPE_UNSPECIFIED = 0
    CELERY_WORKER = 1
    KUBERNETES_WORKER = 2
    KUBERNETES_OPERATOR_POD = 3
    SCHEDULER = 4
    DAG_PROCESSOR = 5
    TRIGGERER = 6
    WEB_SERVER = 7
    REDIS = 8

  name = _messages.StringField(1)
  status = _messages.MessageField('ComposerWorkloadStatus', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ComposerWorkloadStatus(_messages.Message):
  r"""Workload status.

  Enums:
    StateValueValuesEnum: Output only. Workload state.

  Fields:
    detailedStatusMessage: Output only. Detailed message of the status.
    state: Output only. Workload state.
    statusMessage: Output only. Text to provide more descriptive status.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Workload state.

    Values:
      COMPOSER_WORKLOAD_STATE_UNSPECIFIED: Not able to determine the status of
        the workload.
      PENDING: Workload is in pending state and has not yet started.
      OK: Workload is running fine.
      WARNING: Workload is running but there are some non-critical problems.
      ERROR: Workload is not running due to an error.
      SUCCEEDED: Workload has finished execution with success.
      FAILED: Workload has finished execution with failure.
    """
    COMPOSER_WORKLOAD_STATE_UNSPECIFIED = 0
    PENDING = 1
    OK = 2
    WARNING = 3
    ERROR = 4
    SUCCEEDED = 5
    FAILED = 6

  detailedStatusMessage = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  statusMessage = _messages.StringField(3)


class Dag(_messages.Message):
  r"""A Composer DAG resource.

  Enums:
    CatchupValueValuesEnum: Whether the catchup is enabled for the DAG.
    StateValueValuesEnum: Output only. The current state of the DAG.

  Fields:
    catchup: Whether the catchup is enabled for the DAG.
    cronSchedule: The DAG's schedule in cron format.
    dagId: Required. The DAG ID.
    dagrunTimeout: Maximum runtime of a DAG run before termination with a
      timeout.
    description: The description of the DAG.
    durationSchedule: The DAG's schedule as a time duration between runs.
    endDate: The end_date parameter of the DAG (if set).
    failStop: Whether a "fail_stop" mode is enabled for the DAG.
    fileloc: File location relative to the Cloud Storage bucket root folder.
    lastRunEndTime: The end timestamp of the last completed DAG run.
    lastUpdated: The last time the DAG has been serialized.
    maxActiveRuns: Maximum number of simultaneous active runs of the DAG.
      Default is zero.
    maxActiveTasks: Maximum number of simultaneous active tasks of the DAG.
      Default is zero.
    name: Required. The resource name of the DAG, in the form: "projects/{proj
      ectId}/locations/{locationId}/environments/{environmentId}/dags/{dagId}"
      .
    runningCount: The number of running instances of the DAG.
    startDate: The start_date parameter of the DAG.
    state: Output only. The current state of the DAG.
  """

  class CatchupValueValuesEnum(_messages.Enum):
    r"""Whether the catchup is enabled for the DAG.

    Values:
      CATCHUP_VALUE_UNSPECIFIED: The state of the Cachup is unknown.
      ENABLED: The catchup is enabled.
      DISABLED: The catchup is disabled.
    """
    CATCHUP_VALUE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the DAG.

    Values:
      STATE_UNSPECIFIED: The state of the DAG is unknown.
      ACTIVE: The DAG is available for execution.
      PAUSED: The DAG is paused.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PAUSED = 2

  catchup = _messages.EnumField('CatchupValueValuesEnum', 1)
  cronSchedule = _messages.StringField(2)
  dagId = _messages.StringField(3)
  dagrunTimeout = _messages.StringField(4)
  description = _messages.StringField(5)
  durationSchedule = _messages.StringField(6)
  endDate = _messages.StringField(7)
  failStop = _messages.BooleanField(8)
  fileloc = _messages.StringField(9)
  lastRunEndTime = _messages.StringField(10)
  lastUpdated = _messages.StringField(11)
  maxActiveRuns = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  maxActiveTasks = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  name = _messages.StringField(14)
  runningCount = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  startDate = _messages.StringField(16)
  state = _messages.EnumField('StateValueValuesEnum', 17)


class DagProcessorResource(_messages.Message):
  r"""Configuration for resources used by Airflow DAG processors. This field
  is supported for Cloud Composer environments in versions
  composer-3-airflow-*.*.*-build.* and newer.

  Fields:
    count: Optional. The number of DAG processors. If not provided or set to
      0, a single DAG processor instance will be created.
    cpu: Optional. CPU request and limit for a single Airflow DAG processor
      replica.
    memoryGb: Optional. Memory (GB) request and limit for a single Airflow DAG
      processor replica.
    storageGb: Optional. Storage (GB) request and limit for a single Airflow
      DAG processor replica.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  cpu = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  memoryGb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  storageGb = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class DagRun(_messages.Message):
  r"""A single DAG run.

  Enums:
    StateValueValuesEnum: DAG run state.
    TypeValueValuesEnum: DAG run type (how it got created/executed).

  Fields:
    dagId: The DAG ID of the DAG whose execution is described by this DAG run.
    dagRunId: The DAG run ID.
    dataIntervalEnd: End of the data interval. Added in version 2.2. If run
      has been triggered manually, this field is equal to execution_date.
    dataIntervalStart: Start of the data interval. Added in version 2.2. If
      run has been triggered manually, this field is equal to execution_date.
    endDate: Timestamp when the DAG run ended. Set only if the DAG run has
      finished.
    executionDate: The logical date and time which the DAG run and its task
      instances are running for.
    name: The resource name of the DAG, in the form: "projects/{projectId}/loc
      ations/{locationId}/environments/{environmentId}/dags/{dagId}/dagRuns/{d
      agRunId}".
    startDate: Timestamp when the DAG run started.
    state: DAG run state.
    type: DAG run type (how it got created/executed).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""DAG run state.

    Values:
      STATE_UNSPECIFIED: The state of the DAG run is unknown.
      RUNNING: The DAG run is being executed.
      SUCCEEDED: The DAG run is finished successfully.
      FAILED: The DAG run is finished with an error.
      QUEUED: The DAG run is queued for execution.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    SUCCEEDED = 2
    FAILED = 3
    QUEUED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""DAG run type (how it got created/executed).

    Values:
      TYPE_UNSPECIFIED: The type of the DAG run is unknown.
      BACKFILL: Backfill run.
      SCHEDULED: Scheduled run.
      MANUAL: Manually triggered run.
      DATASET_TRIGGERED: Triggered by a dataset update.
    """
    TYPE_UNSPECIFIED = 0
    BACKFILL = 1
    SCHEDULED = 2
    MANUAL = 3
    DATASET_TRIGGERED = 4

  dagId = _messages.StringField(1)
  dagRunId = _messages.StringField(2)
  dataIntervalEnd = _messages.StringField(3)
  dataIntervalStart = _messages.StringField(4)
  endDate = _messages.StringField(5)
  executionDate = _messages.StringField(6)
  name = _messages.StringField(7)
  startDate = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)


class DagStats(_messages.Message):
  r"""Statistics of a DAG in a specific time interval.

  Fields:
    dag: DAG name.
    failedRunCount: Number of DAG runs finished with a failure in the time
      interval.
    successfulRunCount: Number of DAG runs successfully finished in the time
      interval.
  """

  dag = _messages.StringField(1)
  failedRunCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  successfulRunCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DataRetentionConfig(_messages.Message):
  r"""The configuration setting for Airflow database data retention mechanism.

  Enums:
    TaskLogsStorageModeValueValuesEnum: Optional. The mode of storage for
      Airflow workers task logs.

  Fields:
    airflowDatabaseRetentionDays: Optional. The number of days describing for
      how long to store event-based records in airflow database. If the
      retention mechanism is enabled this value must be a positive integer
      otherwise, value should be set to 0.
    airflowMetadataRetentionConfig: Optional. The retention policy for airflow
      metadata database.
    taskLogsRetentionConfig: Optional. The configuration settings for task
      logs retention
    taskLogsRetentionDays: Optional. The number of days to retain task logs in
      the Cloud Logging bucket.
    taskLogsStorageMode: Optional. The mode of storage for Airflow workers
      task logs.
  """

  class TaskLogsStorageModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode of storage for Airflow workers task logs.

    Values:
      TASK_LOGS_STORAGE_MODE_UNSPECIFIED: This configuration is not specified
        by the user.
      CLOUD_LOGGING_AND_CLOUD_STORAGE: Store task logs in Cloud Logging and in
        the environment's Cloud Storage bucket.
      CLOUD_LOGGING_ONLY: Store task logs in Cloud Logging only.
    """
    TASK_LOGS_STORAGE_MODE_UNSPECIFIED = 0
    CLOUD_LOGGING_AND_CLOUD_STORAGE = 1
    CLOUD_LOGGING_ONLY = 2

  airflowDatabaseRetentionDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  airflowMetadataRetentionConfig = _messages.MessageField('AirflowMetadataRetentionPolicyConfig', 2)
  taskLogsRetentionConfig = _messages.MessageField('TaskLogsRetentionConfig', 3)
  taskLogsRetentionDays = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  taskLogsStorageMode = _messages.EnumField('TaskLogsStorageModeValueValuesEnum', 5)


class DatabaseConfig(_messages.Message):
  r"""The configuration of Cloud SQL instance that is used by the Apache
  Airflow software.

  Fields:
    machineType: Optional. Cloud SQL machine type used by Airflow database. It
      has to be one of: db-n1-standard-2, db-n1-standard-4, db-n1-standard-8
      or db-n1-standard-16. If not specified, db-n1-standard-2 will be used.
      Supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*.
    zone: Optional. The Compute Engine zone where the Airflow database is
      created. If zone is provided, it must be in the region selected for the
      environment. If zone is not provided, a zone is automatically selected.
      The zone can only be set during environment creation. Supported for
      Cloud Composer environments in versions composer-2.*.*-airflow-*.*.*.
  """

  machineType = _messages.StringField(1)
  zone = _messages.StringField(2)


class DatabaseFailoverRequest(_messages.Message):
  r"""Request to trigger database failover (only for highly resilient
  environments).
  """



class DatabaseFailoverResponse(_messages.Message):
  r"""Response for DatabaseFailoverRequest."""


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""The encryption options for the Cloud Composer environment and its
  dependencies. Supported for Cloud Composer environments in versions
  composer-1.*.*-airflow-*.*.*.

  Fields:
    kmsKeyName: Optional. Customer-managed Encryption Key available through
      Google's Key Management Service. Cannot be updated. If not specified,
      Google-managed key will be used.
  """

  kmsKeyName = _messages.StringField(1)


class Environment(_messages.Message):
  r"""An environment for running orchestration tasks.

  Enums:
    StateValueValuesEnum: The current state of the environment.

  Messages:
    LabelsValue: Optional. User-defined labels for this environment. The
      labels map can contain no more than 64 entries. Entries of the labels
      map are UTF8 strings that comply with the following restrictions: * Keys
      must conform to regexp: \p{Ll}\p{Lo}{0,62} * Values must conform to
      regexp: [\p{Ll}\p{Lo}\p{N}_-]{0,63} * Both keys and values are
      additionally constrained to be <= 128 bytes in size.

  Fields:
    config: Optional. Configuration parameters for this environment.
    createTime: Output only. The time at which this environment was created.
    labels: Optional. User-defined labels for this environment. The labels map
      can contain no more than 64 entries. Entries of the labels map are UTF8
      strings that comply with the following restrictions: * Keys must conform
      to regexp: \p{Ll}\p{Lo}{0,62} * Values must conform to regexp:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} * Both keys and values are additionally
      constrained to be <= 128 bytes in size.
    name: Identifier. The resource name of the environment, in the form:
      "projects/{projectId}/locations/{locationId}/environments/{environmentId
      }" EnvironmentId must start with a lowercase letter followed by up to 63
      lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: The current state of the environment.
    storageConfig: Optional. Storage configuration for this environment.
    updateTime: Output only. The time at which this environment was last
      modified.
    uuid: Output only. The UUID (Universally Unique IDentifier) associated
      with this environment. This value is generated when the environment is
      created.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the environment.

    Values:
      STATE_UNSPECIFIED: The state of the environment is unknown.
      CREATING: The environment is in the process of being created.
      RUNNING: The environment is currently running and healthy. It is ready
        for use.
      UPDATING: The environment is being updated. It remains usable but cannot
        receive additional update requests or be deleted at this time.
      DELETING: The environment is undergoing deletion. It cannot be used.
      ERROR: The environment has encountered an error and cannot be used.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    UPDATING = 3
    DELETING = 4
    ERROR = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for this environment. The labels map can
    contain no more than 64 entries. Entries of the labels map are UTF8
    strings that comply with the following restrictions: * Keys must conform
    to regexp: \p{Ll}\p{Lo}{0,62} * Values must conform to regexp:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} * Both keys and values are additionally
    constrained to be <= 128 bytes in size.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.MessageField('EnvironmentConfig', 1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  satisfiesPzi = _messages.BooleanField(5)
  satisfiesPzs = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  storageConfig = _messages.MessageField('StorageConfig', 8)
  updateTime = _messages.StringField(9)
  uuid = _messages.StringField(10)


class EnvironmentConfig(_messages.Message):
  r"""Configuration information for an environment.

  Enums:
    EnvironmentSizeValueValuesEnum: Optional. The size of the Cloud Composer
      environment. This field is supported for Cloud Composer environments in
      versions composer-2.*.*-airflow-*.*.* and newer.
    ResilienceModeValueValuesEnum: Optional. Resilience mode of the Cloud
      Composer Environment. This field is supported for Cloud Composer
      environments in versions composer-2.2.0-airflow-*.*.* and newer.

  Fields:
    airflowByoidUri: Output only. The 'bring your own identity' variant of the
      URI of the Apache Airflow Web UI hosted within this environment, to be
      accessed with external identities using workforce identity federation
      (see [Access environments with workforce identity
      federation](/composer/docs/composer-2/access-environments-with-
      workforce-identity-federation)).
    airflowUri: Output only. The URI of the Apache Airflow Web UI hosted
      within this environment (see [Airflow web interface](/composer/docs/how-
      to/accessing/airflow-web-interface)).
    dagGcsPrefix: Output only. The Cloud Storage prefix of the DAGs for this
      environment. Although Cloud Storage objects reside in a flat namespace,
      a hierarchical file tree can be simulated using "/"-delimited object
      name prefixes. DAG objects for this environment reside in a simulated
      directory with the given prefix.
    dataRetentionConfig: Optional. The configuration setting for Airflow
      database data retention mechanism.
    databaseConfig: Optional. The configuration settings for Cloud SQL
      instance used internally by Apache Airflow software.
    encryptionConfig: Optional. The encryption options for the Cloud Composer
      environment and its dependencies. Cannot be updated.
    environmentSize: Optional. The size of the Cloud Composer environment.
      This field is supported for Cloud Composer environments in versions
      composer-2.*.*-airflow-*.*.* and newer.
    gkeCluster: Output only. The Kubernetes Engine cluster used to run this
      environment.
    maintenanceWindow: Optional. The maintenance window is the period when
      Cloud Composer components may undergo maintenance. It is defined so that
      maintenance is not executed during peak hours or critical time periods.
      The system will not be under maintenance for every occurrence of this
      window, but when maintenance is planned, it will be scheduled during the
      window. The maintenance window period must encompass at least 12 hours
      per week. This may be split into multiple chunks, each with a size of at
      least 4 hours. If this value is omitted, the default value for
      maintenance window is applied. By default, maintenance windows are from
      00:00:00 to 04:00:00 (GMT) on Friday, Saturday, and Sunday every week.
    masterAuthorizedNetworksConfig: Optional. The configuration options for
      GKE cluster master authorized networks. By default master authorized
      networks feature is: - in case of private environment: enabled with no
      external networks allowlisted. - in case of public environment:
      disabled.
    nodeConfig: Optional. The configuration used for the Kubernetes Engine
      cluster.
    nodeCount: The number of nodes in the Kubernetes Engine cluster that will
      be used to run this environment. This field is supported for Cloud
      Composer environments in versions composer-1.*.*-airflow-*.*.*.
    privateEnvironmentConfig: Optional. The configuration used for the Private
      IP Cloud Composer environment.
    recoveryConfig: Optional. The Recovery settings configuration of an
      environment. This field is supported for Cloud Composer environments in
      versions composer-2.*.*-airflow-*.*.* and newer.
    resilienceMode: Optional. Resilience mode of the Cloud Composer
      Environment. This field is supported for Cloud Composer environments in
      versions composer-2.2.0-airflow-*.*.* and newer.
    softwareConfig: Optional. The configuration settings for software inside
      the environment.
    webServerConfig: Optional. The configuration settings for the Airflow web
      server App Engine instance. This field is supported for Cloud Composer
      environments in versions composer-1.*.*-airflow-*.*.*.
    webServerNetworkAccessControl: Optional. The network-level access control
      policy for the Airflow web server. If unspecified, no network-level
      access restrictions will be applied.
    workloadsConfig: Optional. The workloads configuration settings for the
      GKE cluster associated with the Cloud Composer environment. The GKE
      cluster runs Airflow scheduler and workers workloads. This field is
      supported for Cloud Composer environments in versions
      composer-2.*.*-airflow-*.*.* and newer.
  """

  class EnvironmentSizeValueValuesEnum(_messages.Enum):
    r"""Optional. The size of the Cloud Composer environment. This field is
    supported for Cloud Composer environments in versions
    composer-2.*.*-airflow-*.*.* and newer.

    Values:
      ENVIRONMENT_SIZE_UNSPECIFIED: The size of the environment is
        unspecified.
      ENVIRONMENT_SIZE_SMALL: The environment size is small.
      ENVIRONMENT_SIZE_MEDIUM: The environment size is medium.
      ENVIRONMENT_SIZE_LARGE: The environment size is large.
    """
    ENVIRONMENT_SIZE_UNSPECIFIED = 0
    ENVIRONMENT_SIZE_SMALL = 1
    ENVIRONMENT_SIZE_MEDIUM = 2
    ENVIRONMENT_SIZE_LARGE = 3

  class ResilienceModeValueValuesEnum(_messages.Enum):
    r"""Optional. Resilience mode of the Cloud Composer Environment. This
    field is supported for Cloud Composer environments in versions
    composer-2.2.0-airflow-*.*.* and newer.

    Values:
      RESILIENCE_MODE_UNSPECIFIED: Default mode doesn't change environment
        parameters.
      HIGH_RESILIENCE: Enabled High Resilience mode, including Cloud SQL HA.
    """
    RESILIENCE_MODE_UNSPECIFIED = 0
    HIGH_RESILIENCE = 1

  airflowByoidUri = _messages.StringField(1)
  airflowUri = _messages.StringField(2)
  dagGcsPrefix = _messages.StringField(3)
  dataRetentionConfig = _messages.MessageField('DataRetentionConfig', 4)
  databaseConfig = _messages.MessageField('DatabaseConfig', 5)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 6)
  environmentSize = _messages.EnumField('EnvironmentSizeValueValuesEnum', 7)
  gkeCluster = _messages.StringField(8)
  maintenanceWindow = _messages.MessageField('MaintenanceWindow', 9)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 10)
  nodeConfig = _messages.MessageField('NodeConfig', 11)
  nodeCount = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  privateEnvironmentConfig = _messages.MessageField('PrivateEnvironmentConfig', 13)
  recoveryConfig = _messages.MessageField('RecoveryConfig', 14)
  resilienceMode = _messages.EnumField('ResilienceModeValueValuesEnum', 15)
  softwareConfig = _messages.MessageField('SoftwareConfig', 16)
  webServerConfig = _messages.MessageField('WebServerConfig', 17)
  webServerNetworkAccessControl = _messages.MessageField('WebServerNetworkAccessControl', 18)
  workloadsConfig = _messages.MessageField('WorkloadsConfig', 19)


class ExecuteAirflowCommandRequest(_messages.Message):
  r"""Execute Airflow Command request.

  Fields:
    command: Airflow command.
    parameters: Parameters for the Airflow command/subcommand as an array of
      arguments. It may contain positional arguments like `["my-dag-id"]`,
      key-value parameters like `["--foo=bar"]` or `["--foo","bar"]`, or other
      flags like `["-f"]`.
    subcommand: Airflow subcommand.
  """

  command = _messages.StringField(1)
  parameters = _messages.StringField(2, repeated=True)
  subcommand = _messages.StringField(3)


class ExecuteAirflowCommandResponse(_messages.Message):
  r"""Response to ExecuteAirflowCommandRequest.

  Fields:
    error: Error message. Empty if there was no error.
    executionId: The unique ID of the command execution for polling.
    pod: The name of the pod where the command is executed.
    podNamespace: The namespace of the pod where the command is executed.
  """

  error = _messages.StringField(1)
  executionId = _messages.StringField(2)
  pod = _messages.StringField(3)
  podNamespace = _messages.StringField(4)


class ExitInfo(_messages.Message):
  r"""Information about how a command ended.

  Fields:
    error: Error message. Empty if there was no error.
    exitCode: The exit code from the command execution.
  """

  error = _messages.StringField(1)
  exitCode = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class FetchDatabasePropertiesResponse(_messages.Message):
  r"""Response for FetchDatabasePropertiesRequest.

  Fields:
    isFailoverReplicaAvailable: The availability status of the failover
      replica. A false status indicates that the failover replica is out of
      sync. The primary instance can only fail over to the failover replica
      when the status is true.
    primaryGceZone: The Compute Engine zone that the instance is currently
      serving from.
    secondaryGceZone: The Compute Engine zone that the failover instance is
      currently serving from for a regional Cloud SQL instance.
  """

  isFailoverReplicaAvailable = _messages.BooleanField(1)
  primaryGceZone = _messages.StringField(2)
  secondaryGceZone = _messages.StringField(3)


class IPAllocationPolicy(_messages.Message):
  r"""Configuration for controlling how IPs are allocated in the GKE cluster
  running the Apache Airflow software.

  Fields:
    clusterIpv4CidrBlock: Optional. The IP address range used to allocate IP
      addresses to pods in the GKE cluster. For Cloud Composer environments in
      versions composer-1.*.*-airflow-*.*.*, this field is applicable only
      when `use_ip_aliases` is true. Set to blank to have GKE choose a range
      with the default size. Set to /netmask (e.g. `/14`) to have GKE choose a
      range with a specific netmask. Set to a
      [CIDR](https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    clusterSecondaryRangeName: Optional. The name of the GKE cluster's
      secondary range used to allocate IP addresses to pods. For Cloud
      Composer environments in versions composer-1.*.*-airflow-*.*.*, this
      field is applicable only when `use_ip_aliases` is true.
    servicesIpv4CidrBlock: Optional. The IP address range of the services IP
      addresses in this GKE cluster. For Cloud Composer environments in
      versions composer-1.*.*-airflow-*.*.*, this field is applicable only
      when `use_ip_aliases` is true. Set to blank to have GKE choose a range
      with the default size. Set to /netmask (e.g. `/14`) to have GKE choose a
      range with a specific netmask. Set to a
      [CIDR](https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    servicesSecondaryRangeName: Optional. The name of the services' secondary
      range used to allocate IP addresses to the GKE cluster. For Cloud
      Composer environments in versions composer-1.*.*-airflow-*.*.*, this
      field is applicable only when `use_ip_aliases` is true.
    useIpAliases: Optional. Whether or not to enable Alias IPs in the GKE
      cluster. If `true`, a VPC-native cluster is created. This field is only
      supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*. Environments in newer versions always use
      VPC-native GKE clusters.
  """

  clusterIpv4CidrBlock = _messages.StringField(1)
  clusterSecondaryRangeName = _messages.StringField(2)
  servicesIpv4CidrBlock = _messages.StringField(3)
  servicesSecondaryRangeName = _messages.StringField(4)
  useIpAliases = _messages.BooleanField(5)


class ImageVersion(_messages.Message):
  r"""Image Version information

  Fields:
    creationDisabled: Whether it is impossible to create an environment with
      the image version.
    imageVersionId: The string identifier of the ImageVersion, in the form:
      "composer-x.y.z-airflow-a.b.c"
    isDefault: Whether this is the default ImageVersion used by Composer
      during environment creation if no input ImageVersion is specified.
    releaseDate: The date of the version release.
    supportedPythonVersions: supported python versions
    upgradeDisabled: Whether it is impossible to upgrade an environment
      running with the image version.
  """

  creationDisabled = _messages.BooleanField(1)
  imageVersionId = _messages.StringField(2)
  isDefault = _messages.BooleanField(3)
  releaseDate = _messages.MessageField('Date', 4)
  supportedPythonVersions = _messages.StringField(5, repeated=True)
  upgradeDisabled = _messages.BooleanField(6)


class ImportError(_messages.Message):
  r"""DAG import error details.

  Fields:
    creationTime: Time when the error was created.
    filename: Location of the file with the error relative to the Cloud
      Storage bucket root folder.
    stackTrace: Full stack trace.
  """

  creationTime = _messages.StringField(1)
  filename = _messages.StringField(2)
  stackTrace = _messages.StringField(3)


class Line(_messages.Message):
  r"""Contains information about a single line from logs.

  Fields:
    content: Text content of the log line.
    lineNumber: Number of the line.
  """

  content = _messages.StringField(1)
  lineNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ListDagRunsResponse(_messages.Message):
  r"""Response to `ListDagRunsRequest`.

  Fields:
    dagRuns: The list of DAG runs returned.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  dagRuns = _messages.MessageField('DagRun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDagStatsResponse(_messages.Message):
  r"""Response to `ListDagStatsRequest`.

  Fields:
    dagStats: List of DAGs with statistics.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  dagStats = _messages.MessageField('DagStats', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDagsResponse(_messages.Message):
  r"""Response to `ListDagsRequest`.

  Fields:
    dags: The list of DAGs returned.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  dags = _messages.MessageField('Dag', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEnvironmentsResponse(_messages.Message):
  r"""The environments in a project and location.

  Fields:
    environments: The list of environments returned by a
      ListEnvironmentsRequest.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  environments = _messages.MessageField('Environment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListImageVersionsResponse(_messages.Message):
  r"""The ImageVersions in a project and location.

  Fields:
    imageVersions: The list of supported ImageVersions in a location.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  imageVersions = _messages.MessageField('ImageVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListImportErrorsResponse(_messages.Message):
  r"""Response to `ListImportErrorsRequest`.

  Fields:
    importErrors: List of DAGs with statistics.
    nextPageToken: The page token used to query for the next page if one
      exists.
  """

  importErrors = _messages.MessageField('ImportError', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListTaskInstancesResponse(_messages.Message):
  r"""Response to `ListTaskInstancesRequest`.

  Fields:
    nextPageToken: The page token used to query for the next page if one
      exists.
    taskInstances: The list of tasks returned.
  """

  nextPageToken = _messages.StringField(1)
  taskInstances = _messages.MessageField('TaskInstance', 2, repeated=True)


class ListTasksResponse(_messages.Message):
  r"""Response to `ListTasksRequest`.

  Fields:
    nextPageToken: The page token used to query for the next page if one
      exists.
    tasks: The list of tasks returned.
  """

  nextPageToken = _messages.StringField(1)
  tasks = _messages.MessageField('Task', 2, repeated=True)


class ListUserWorkloadsConfigMapsResponse(_messages.Message):
  r"""The user workloads ConfigMaps for a given environment.

  Fields:
    nextPageToken: The page token used to query for the next page if one
      exists.
    userWorkloadsConfigMaps: The list of ConfigMaps returned by a
      ListUserWorkloadsConfigMapsRequest.
  """

  nextPageToken = _messages.StringField(1)
  userWorkloadsConfigMaps = _messages.MessageField('UserWorkloadsConfigMap', 2, repeated=True)


class ListUserWorkloadsSecretsResponse(_messages.Message):
  r"""The user workloads Secrets for a given environment.

  Fields:
    nextPageToken: The page token used to query for the next page if one
      exists.
    userWorkloadsSecrets: The list of Secrets returned by a
      ListUserWorkloadsSecretsRequest.
  """

  nextPageToken = _messages.StringField(1)
  userWorkloadsSecrets = _messages.MessageField('UserWorkloadsSecret', 2, repeated=True)


class ListWorkloadsResponse(_messages.Message):
  r"""Response to ListWorkloadsRequest.

  Fields:
    nextPageToken: The page token used to query for the next page if one
      exists.
    workloads: The list of environment workloads.
  """

  nextPageToken = _messages.StringField(1)
  workloads = _messages.MessageField('ComposerWorkload', 2, repeated=True)


class LoadSnapshotRequest(_messages.Message):
  r"""Request to load a snapshot into a Cloud Composer environment.

  Fields:
    skipAirflowOverridesSetting: Whether or not to skip setting Airflow
      overrides when loading the environment's state.
    skipEnvironmentVariablesSetting: Whether or not to skip setting
      environment variables when loading the environment's state.
    skipGcsDataCopying: Whether or not to skip copying Cloud Storage data when
      loading the environment's state.
    skipPypiPackagesInstallation: Whether or not to skip installing Pypi
      packages when loading the environment's state.
    snapshotPath: A Cloud Storage path to a snapshot to load, e.g.: "gs://my-
      bucket/snapshots/project_location_environment_timestamp".
  """

  skipAirflowOverridesSetting = _messages.BooleanField(1)
  skipEnvironmentVariablesSetting = _messages.BooleanField(2)
  skipGcsDataCopying = _messages.BooleanField(3)
  skipPypiPackagesInstallation = _messages.BooleanField(4)
  snapshotPath = _messages.StringField(5)


class LoadSnapshotResponse(_messages.Message):
  r"""Response to LoadSnapshotRequest."""


class MaintenanceWindow(_messages.Message):
  r"""The configuration settings for Cloud Composer maintenance window. The
  following example: ``` { "startTime":"2019-08-01T01:00:00Z"
  "endTime":"2019-08-01T07:00:00Z" "recurrence":"FREQ=WEEKLY;BYDAY=TU,WE" }
  ``` would define a maintenance window between 01 and 07 hours UTC during
  each Tuesday and Wednesday.

  Fields:
    endTime: Required. Maintenance window end time. It is used only to
      calculate the duration of the maintenance window. The value for end_time
      must be in the future, relative to `start_time`.
    recurrence: Required. Maintenance window recurrence. Format is a subset of
      [RFC-5545](https://tools.ietf.org/html/rfc5545) `RRULE`. The only
      allowed values for `FREQ` field are `FREQ=DAILY` and
      `FREQ=WEEKLY;BYDAY=...` Example values: `FREQ=WEEKLY;BYDAY=TU,WE`,
      `FREQ=DAILY`.
    startTime: Required. Start time of the first recurrence of the maintenance
      window.
  """

  endTime = _messages.StringField(1)
  recurrence = _messages.StringField(2)
  startTime = _messages.StringField(3)


class MasterAuthorizedNetworksConfig(_messages.Message):
  r"""Configuration options for the master authorized networks feature.
  Enabled master authorized networks will disallow all external traffic to
  access Kubernetes master through HTTPS except traffic from the given CIDR
  blocks, Google Compute Engine Public IPs and Google Prod IPs.

  Fields:
    cidrBlocks: Up to 50 external networks that could access Kubernetes master
      through HTTPS.
    enabled: Optional. Whether or not master authorized networks feature is
      enabled.
  """

  cidrBlocks = _messages.MessageField('CidrBlock', 1, repeated=True)
  enabled = _messages.BooleanField(2)


class NetworkingConfig(_messages.Message):
  r"""Configuration options for networking connections in the Composer 2
  environment.

  Enums:
    ConnectionTypeValueValuesEnum: Optional. Indicates the user requested
      specific connection type between Tenant and Customer projects. You
      cannot set networking connection type in public IP environment.

  Fields:
    connectionType: Optional. Indicates the user requested specific connection
      type between Tenant and Customer projects. You cannot set networking
      connection type in public IP environment.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Indicates the user requested specific connection type
    between Tenant and Customer projects. You cannot set networking connection
    type in public IP environment.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: No specific connection type was requested,
        so the environment uses the default value corresponding to the rest of
        its configuration.
      VPC_PEERING: Requests the use of VPC peerings for connecting the
        Customer and Tenant projects.
      PRIVATE_SERVICE_CONNECT: Requests the use of Private Service Connect for
        connecting the Customer and Tenant projects.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    VPC_PEERING = 1
    PRIVATE_SERVICE_CONNECT = 2

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)


class NodeConfig(_messages.Message):
  r"""The configuration information for the Kubernetes Engine nodes running
  the Apache Airflow software.

  Fields:
    composerInternalIpv4CidrBlock: Optional. The IP range in CIDR notation to
      use internally by Cloud Composer. IP addresses are not reserved - and
      the same range can be used by multiple Cloud Composer environments. In
      case of overlap, IPs from this range will not be accessible in the
      user's VPC network. Cannot be updated. If not specified, the default
      value of '************/20' is used. This field is supported for Cloud
      Composer environments in versions composer-3-airflow-*.*.*-build.* and
      newer.
    composerNetworkAttachment: Optional. Network Attachment that Cloud
      Composer environment is connected to, which provides connectivity with a
      user's VPC network. Takes precedence over network and subnetwork
      settings. If not provided, but network and subnetwork are defined during
      environment, it will be provisioned. If not provided and network and
      subnetwork are also empty, then connectivity to user's VPC network is
      disabled. Network attachment must be provided in format projects/{projec
      t}/regions/{region}/networkAttachments/{networkAttachment}. This field
      is supported for Cloud Composer environments in versions
      composer-3-airflow-*.*.*-build.* and newer.
    diskSizeGb: Optional. The disk size in GB used for node VMs. Minimum size
      is 30GB. If unspecified, defaults to 100GB. Cannot be updated. This
      field is supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*.
    enableIpMasqAgent: Optional. Deploys 'ip-masq-agent' daemon set in the GKE
      cluster and defines nonMasqueradeCIDRs equals to pod IP range so IP
      masquerading is used for all destination addresses, except between pods
      traffic. See: https://cloud.google.com/kubernetes-engine/docs/how-to/ip-
      masquerade-agent
    ipAllocationPolicy: Optional. The IPAllocationPolicy fields for the GKE
      cluster.
    location: Optional. The Compute Engine [zone](/compute/docs/regions-zones)
      in which to deploy the VMs used to run the Apache Airflow software,
      specified as a [relative resource
      name](/apis/design/resource_names#relative_resource_name). For example:
      "projects/{projectId}/zones/{zoneId}". This `location` must belong to
      the enclosing environment's project and location. If both this field and
      `nodeConfig.machineType` are specified, `nodeConfig.machineType` must
      belong to this `location`; if both are unspecified, the service will
      pick a zone in the Compute Engine region corresponding to the Cloud
      Composer location, and propagate that choice to both fields. If only one
      field (`location` or `nodeConfig.machineType`) is specified, the
      location information from the specified field will be propagated to the
      unspecified field. This field is supported for Cloud Composer
      environments in versions composer-1.*.*-airflow-*.*.*.
    machineType: Optional. The Compute Engine [machine
      type](/compute/docs/machine-types) used for cluster instances, specified
      as a [relative resource
      name](/apis/design/resource_names#relative_resource_name). For example:
      "projects/{projectId}/zones/{zoneId}/machineTypes/{machineTypeId}". The
      `machineType` must belong to the enclosing environment's project and
      location. If both this field and `nodeConfig.location` are specified,
      this `machineType` must belong to the `nodeConfig.location`; if both are
      unspecified, the service will pick a zone in the Compute Engine region
      corresponding to the Cloud Composer location, and propagate that choice
      to both fields. If exactly one of this field and `nodeConfig.location`
      is specified, the location information from the specified field will be
      propagated to the unspecified field. The `machineTypeId` must not be a
      [shared-core machine type](/compute/docs/machine-types#sharedcore). If
      this field is unspecified, the `machineTypeId` defaults to
      "n1-standard-1". This field is supported for Cloud Composer environments
      in versions composer-1.*.*-airflow-*.*.*.
    maxPodsPerNode: Optional. The maximum number of pods per node in the Cloud
      Composer GKE cluster. The value must be between 8 and 110 and it can be
      set only if the environment is VPC-native. The default value is 32.
      Values of this field will be propagated both to the `default-pool` node
      pool of the newly created GKE cluster, and to the default "Maximum Pods
      per Node" value which is used for newly created node pools if their
      value is not explicitly set during node pool creation. For more
      information, see [Optimizing IP address allocation]
      (https://cloud.google.com/kubernetes-engine/docs/how-to/flexible-pod-
      cidr). Cannot be updated. This field is supported for Cloud Composer
      environments in versions composer-1.*.*-airflow-*.*.*.
    network: Optional. The Compute Engine network to be used for machine
      communications, specified as a [relative resource
      name](/apis/design/resource_names#relative_resource_name). For example:
      "projects/{projectId}/global/networks/{networkId}". If unspecified, the
      default network in the environment's project is used. If a [Custom
      Subnet Network](/vpc/docs/vpc#vpc_networks_and_subnets) is provided,
      `nodeConfig.subnetwork` must also be provided. For [Shared
      VPC](/vpc/docs/shared-vpc) subnetwork requirements, see
      `nodeConfig.subnetwork`.
    oauthScopes: Optional. The set of Google API scopes to be made available
      on all node VMs. If `oauth_scopes` is empty, defaults to
      ["https://www.googleapis.com/auth/cloud-platform"]. Cannot be updated.
      This field is supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*.
    serviceAccount: Optional. The Google Cloud Platform Service Account to be
      used by the workloads. If a service account is not specified, the
      "default" Compute Engine service account is used. Cannot be updated.
    subnetwork: Optional. The Compute Engine subnetwork to be used for machine
      communications, specified as a [relative resource
      name](/apis/design/resource_names#relative_resource_name). For example:
      "projects/{projectId}/regions/{regionId}/subnetworks/{subnetworkId}" If
      a subnetwork is provided, `nodeConfig.network` must also be provided,
      and the subnetwork must belong to the enclosing environment's project
      and location.
    tags: Optional. The list of instance tags applied to all node VMs. Tags
      are used to identify valid sources or targets for network firewalls.
      Each tag within the list must comply with
      [RFC1035](https://www.ietf.org/rfc/rfc1035.txt). Cannot be updated.
  """

  composerInternalIpv4CidrBlock = _messages.StringField(1)
  composerNetworkAttachment = _messages.StringField(2)
  diskSizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  enableIpMasqAgent = _messages.BooleanField(4)
  ipAllocationPolicy = _messages.MessageField('IPAllocationPolicy', 5)
  location = _messages.StringField(6)
  machineType = _messages.StringField(7)
  maxPodsPerNode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  network = _messages.StringField(9)
  oauthScopes = _messages.StringField(10, repeated=True)
  serviceAccount = _messages.StringField(11)
  subnetwork = _messages.StringField(12)
  tags = _messages.StringField(13, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata describing an operation.

  Enums:
    OperationTypeValueValuesEnum: Output only. The type of operation being
      performed.
    StateValueValuesEnum: Output only. The current operation state.

  Fields:
    createTime: Output only. The time the operation was submitted to the
      server.
    endTime: Output only. The time when the operation terminated, regardless
      of its success. This field is unset if the operation is still ongoing.
    operationType: Output only. The type of operation being performed.
    resource: Output only. The resource being operated on, as a [relative
      resource name]( /apis/design/resource_names#relative_resource_name).
    resourceUuid: Output only. The UUID of the resource being operated on.
    state: Output only. The current operation state.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of operation being performed.

    Values:
      TYPE_UNSPECIFIED: Unused.
      CREATE: A resource creation operation.
      DELETE: A resource deletion operation.
      UPDATE: A resource update operation.
      CHECK: A resource check operation.
      SAVE_SNAPSHOT: Saves snapshot of the resource operation.
      LOAD_SNAPSHOT: Loads snapshot of the resource operation.
      DATABASE_FAILOVER: Triggers failover of environment's Cloud SQL instance
        (only for highly resilient environments).
    """
    TYPE_UNSPECIFIED = 0
    CREATE = 1
    DELETE = 2
    UPDATE = 3
    CHECK = 4
    SAVE_SNAPSHOT = 5
    LOAD_SNAPSHOT = 6
    DATABASE_FAILOVER = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current operation state.

    Values:
      STATE_UNSPECIFIED: Unused.
      PENDING: The operation has been created but is not yet started.
      RUNNING: The operation is underway.
      SUCCEEDED: The operation completed successfully.
      SUCCESSFUL: <no description>
      FAILED: The operation is no longer running but did not succeed.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    SUCCESSFUL = 4
    FAILED = 5

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 3)
  resource = _messages.StringField(4)
  resourceUuid = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class PauseDagRequest(_messages.Message):
  r"""Request to pause a DAG."""


class PollAirflowCommandRequest(_messages.Message):
  r"""Poll Airflow Command request.

  Fields:
    executionId: The unique ID of the command execution.
    nextLineNumber: Line number from which new logs should be fetched.
    pod: The name of the pod where the command is executed.
    podNamespace: The namespace of the pod where the command is executed.
  """

  executionId = _messages.StringField(1)
  nextLineNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pod = _messages.StringField(3)
  podNamespace = _messages.StringField(4)


class PollAirflowCommandResponse(_messages.Message):
  r"""Response to PollAirflowCommandRequest.

  Fields:
    exitInfo: The result exit status of the command.
    output: Output from the command execution. It may not contain the full
      output and the caller may need to poll for more lines.
    outputEnd: Whether the command execution has finished and there is no more
      output.
  """

  exitInfo = _messages.MessageField('ExitInfo', 1)
  output = _messages.MessageField('Line', 2, repeated=True)
  outputEnd = _messages.BooleanField(3)


class PrivateClusterConfig(_messages.Message):
  r"""Configuration options for the private GKE cluster in a Cloud Composer
  environment.

  Fields:
    enablePrivateEndpoint: Optional. If `true`, access to the public endpoint
      of the GKE cluster is denied.
    masterIpv4CidrBlock: Optional. The CIDR block from which IPv4 range for
      GKE master will be reserved. If left blank, the default value of
      '**********/23' is used.
    masterIpv4ReservedRange: Output only. The IP range in CIDR notation to use
      for the hosted master network. This range is used for assigning internal
      IP addresses to the GKE cluster master or set of masters and to the
      internal load balancer virtual IP. This range must not overlap with any
      other ranges in use within the cluster's network.
  """

  enablePrivateEndpoint = _messages.BooleanField(1)
  masterIpv4CidrBlock = _messages.StringField(2)
  masterIpv4ReservedRange = _messages.StringField(3)


class PrivateEnvironmentConfig(_messages.Message):
  r"""The configuration information for configuring a Private IP Cloud
  Composer environment.

  Fields:
    cloudComposerConnectionSubnetwork: Optional. When specified, the
      environment will use Private Service Connect instead of VPC peerings to
      connect to Cloud SQL in the Tenant Project, and the PSC endpoint in the
      Customer Project will use an IP address from this subnetwork.
    cloudComposerNetworkIpv4CidrBlock: Optional. The CIDR block from which IP
      range for Cloud Composer Network in tenant project will be reserved.
      Needs to be disjoint from private_cluster_config.master_ipv4_cidr_block
      and cloud_sql_ipv4_cidr_block. This field is supported for Cloud
      Composer environments in versions composer-2.*.*-airflow-*.*.* and
      newer.
    cloudComposerNetworkIpv4ReservedRange: Output only. The IP range reserved
      for the tenant project's Cloud Composer network. This field is supported
      for Cloud Composer environments in versions composer-2.*.*-airflow-*.*.*
      and newer.
    cloudSqlIpv4CidrBlock: Optional. The CIDR block from which IP range in
      tenant project will be reserved for Cloud SQL. Needs to be disjoint from
      `web_server_ipv4_cidr_block`.
    enablePrivateBuildsOnly: Optional. If `true`, builds performed during
      operations that install Python packages have only private connectivity
      to Google services (including Artifact Registry) and VPC network (if
      either `NodeConfig.network` and `NodeConfig.subnetwork` fields or
      `NodeConfig.composer_network_attachment` field are specified). If
      `false`, the builds also have access to the internet. This field is
      supported for Cloud Composer environments in versions
      composer-3-airflow-*.*.*-build.* and newer.
    enablePrivateEnvironment: Optional. If `true`, a Private IP Cloud Composer
      environment is created. If this field is set to true,
      `IPAllocationPolicy.use_ip_aliases` must be set to true for Cloud
      Composer environments in versions composer-1.*.*-airflow-*.*.*.
    enablePrivatelyUsedPublicIps: Optional. When enabled, IPs from public
      (non-RFC1918) ranges can be used for
      `IPAllocationPolicy.cluster_ipv4_cidr_block` and
      `IPAllocationPolicy.service_ipv4_cidr_block`.
    networkingConfig: Optional. Configuration for the network connections
      configuration in the environment.
    privateClusterConfig: Optional. Configuration for the private GKE cluster
      for a Private IP Cloud Composer environment.
    webServerIpv4CidrBlock: Optional. The CIDR block from which IP range for
      web server will be reserved. Needs to be disjoint from
      `private_cluster_config.master_ipv4_cidr_block` and
      `cloud_sql_ipv4_cidr_block`. This field is supported for Cloud Composer
      environments in versions composer-1.*.*-airflow-*.*.*.
    webServerIpv4ReservedRange: Output only. The IP range reserved for the
      tenant project's App Engine VMs. This field is supported for Cloud
      Composer environments in versions composer-1.*.*-airflow-*.*.*.
  """

  cloudComposerConnectionSubnetwork = _messages.StringField(1)
  cloudComposerNetworkIpv4CidrBlock = _messages.StringField(2)
  cloudComposerNetworkIpv4ReservedRange = _messages.StringField(3)
  cloudSqlIpv4CidrBlock = _messages.StringField(4)
  enablePrivateBuildsOnly = _messages.BooleanField(5)
  enablePrivateEnvironment = _messages.BooleanField(6)
  enablePrivatelyUsedPublicIps = _messages.BooleanField(7)
  networkingConfig = _messages.MessageField('NetworkingConfig', 8)
  privateClusterConfig = _messages.MessageField('PrivateClusterConfig', 9)
  webServerIpv4CidrBlock = _messages.StringField(10)
  webServerIpv4ReservedRange = _messages.StringField(11)


class RecoveryConfig(_messages.Message):
  r"""The Recovery settings of an environment.

  Fields:
    scheduledSnapshotsConfig: Optional. The configuration for scheduled
      snapshot creation mechanism.
  """

  scheduledSnapshotsConfig = _messages.MessageField('ScheduledSnapshotsConfig', 1)


class RestartWebServerRequest(_messages.Message):
  r"""Restart Airflow web server."""


class SaveSnapshotRequest(_messages.Message):
  r"""Request to create a snapshot of a Cloud Composer environment.

  Fields:
    snapshotLocation: Location in a Cloud Storage where the snapshot is going
      to be stored, e.g.: "gs://my-bucket/snapshots".
  """

  snapshotLocation = _messages.StringField(1)


class SaveSnapshotResponse(_messages.Message):
  r"""Response to SaveSnapshotRequest.

  Fields:
    snapshotPath: The fully-resolved Cloud Storage path of the created
      snapshot, e.g.: "gs://my-
      bucket/snapshots/project_location_environment_timestamp". This field is
      populated only if the snapshot creation was successful.
  """

  snapshotPath = _messages.StringField(1)


class ScheduledSnapshotsConfig(_messages.Message):
  r"""The configuration for scheduled snapshot creation mechanism.

  Fields:
    enabled: Optional. Whether scheduled snapshots creation is enabled.
    snapshotCreationSchedule: Optional. The cron expression representing the
      time when snapshots creation mechanism runs. This field is subject to
      additional validation around frequency of execution.
    snapshotLocation: Optional. The Cloud Storage location for storing
      automatically created snapshots.
    timeZone: Optional. Time zone that sets the context to interpret
      snapshot_creation_schedule.
  """

  enabled = _messages.BooleanField(1)
  snapshotCreationSchedule = _messages.StringField(2)
  snapshotLocation = _messages.StringField(3)
  timeZone = _messages.StringField(4)


class SchedulerResource(_messages.Message):
  r"""Configuration for resources used by Airflow schedulers.

  Fields:
    count: Optional. The number of schedulers.
    cpu: Optional. CPU request and limit for a single Airflow scheduler
      replica.
    memoryGb: Optional. Memory (GB) request and limit for a single Airflow
      scheduler replica.
    storageGb: Optional. Storage (GB) request and limit for a single Airflow
      scheduler replica.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  cpu = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  memoryGb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  storageGb = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class SoftwareConfig(_messages.Message):
  r"""Specifies the selection and configuration of software inside the
  environment.

  Enums:
    AirflowExecutorTypeValueValuesEnum: The `airflowExecutorType` specifies
      the [executor](https://airflow.apache.org/code.html?highlight=executor#e
      xecutors) by which task instances are run on Airflow. If this field is
      unspecified, the `airflowExecutorType` defaults to `celery`.
    WebServerPluginsModeValueValuesEnum: Optional. Whether or not the web
      server uses custom plugins. If unspecified, the field defaults to
      `PLUGINS_ENABLED`. This field is supported for Cloud Composer
      environments in versions composer-3-airflow-*.*.*-build.* and newer.

  Messages:
    AirflowConfigOverridesValue: Optional. Apache Airflow configuration
      properties to override. Property keys contain the section and property
      names, separated by a hyphen, for example "core-
      dags_are_paused_at_creation". Section names must not contain hyphens
      ("-"), opening square brackets ("["), or closing square brackets ("]").
      The property name must not be empty and must not contain an equals sign
      ("=") or semicolon (";"). Section and property names must not contain a
      period ("."). Apache Airflow configuration property names must be
      written in [snake_case](https://en.wikipedia.org/wiki/Snake_case).
      Property values can contain any character, and can be written in any
      lower/upper case format. Certain Apache Airflow configuration property
      values are [blocked](/composer/docs/concepts/airflow-configurations),
      and cannot be overridden.
    EnvVariablesValue: Optional. Additional environment variables to provide
      to the Apache Airflow scheduler, worker, and webserver processes.
      Environment variable names must match the regular expression `a-zA-Z_*`.
      They cannot specify Apache Airflow software configuration overrides
      (they cannot match the regular expression
      `AIRFLOW__[A-Z0-9_]+__[A-Z0-9_]+`), and they cannot match any of the
      following reserved names: * `AIRFLOW_HOME` * `C_FORCE_ROOT` *
      `CONTAINER_NAME` * `DAGS_FOLDER` * `GCP_PROJECT` * `GCS_BUCKET` *
      `GKE_CLUSTER_NAME` * `SQL_DATABASE` * `SQL_INSTANCE` * `SQL_PASSWORD` *
      `SQL_PROJECT` * `SQL_REGION` * `SQL_USER`
    PypiPackagesValue: Optional. Custom Python Package Index (PyPI) packages
      to be installed in the environment. Keys refer to the lowercase package
      name such as "numpy" and values are the lowercase extras and version
      specifier such as "==1.12.0", "[devel,gcp_api]", or "[devel]>=1.8.2,
      <1.9.2". To specify a package without pinning it to a version specifier,
      use the empty string as the value.

  Fields:
    airflowConfigOverrides: Optional. Apache Airflow configuration properties
      to override. Property keys contain the section and property names,
      separated by a hyphen, for example "core-dags_are_paused_at_creation".
      Section names must not contain hyphens ("-"), opening square brackets
      ("["), or closing square brackets ("]"). The property name must not be
      empty and must not contain an equals sign ("=") or semicolon (";").
      Section and property names must not contain a period ("."). Apache
      Airflow configuration property names must be written in
      [snake_case](https://en.wikipedia.org/wiki/Snake_case). Property values
      can contain any character, and can be written in any lower/upper case
      format. Certain Apache Airflow configuration property values are
      [blocked](/composer/docs/concepts/airflow-configurations), and cannot be
      overridden.
    airflowExecutorType: The `airflowExecutorType` specifies the [executor](ht
      tps://airflow.apache.org/code.html?highlight=executor#executors) by
      which task instances are run on Airflow. If this field is unspecified,
      the `airflowExecutorType` defaults to `celery`.
    cloudDataLineageIntegration: Optional. The configuration for Cloud Data
      Lineage integration.
    envVariables: Optional. Additional environment variables to provide to the
      Apache Airflow scheduler, worker, and webserver processes. Environment
      variable names must match the regular expression `a-zA-Z_*`. They cannot
      specify Apache Airflow software configuration overrides (they cannot
      match the regular expression `AIRFLOW__[A-Z0-9_]+__[A-Z0-9_]+`), and
      they cannot match any of the following reserved names: * `AIRFLOW_HOME`
      * `C_FORCE_ROOT` * `CONTAINER_NAME` * `DAGS_FOLDER` * `GCP_PROJECT` *
      `GCS_BUCKET` * `GKE_CLUSTER_NAME` * `SQL_DATABASE` * `SQL_INSTANCE` *
      `SQL_PASSWORD` * `SQL_PROJECT` * `SQL_REGION` * `SQL_USER`
    imageVersion: Optional. The version of the software running in the
      environment. This encapsulates both the version of Cloud Composer
      functionality and the version of Apache Airflow. It must match the
      regular expression `composer-([0-9]+(\.[0-9]+\.[0-9]+(-preview\.[0-
      9]+)?)?|latest)-airflow-([0-9]+(\.[0-9]+(\.[0-9]+)?)?)`. When used as
      input, the server also checks if the provided version is supported and
      denies the request for an unsupported version. The Cloud Composer
      portion of the image version is a full [semantic
      version](https://semver.org), or an alias in the form of major version
      number or `latest`. When an alias is provided, the server replaces it
      with the current Cloud Composer version that satisfies the alias. The
      Apache Airflow portion of the image version is a full semantic version
      that points to one of the supported Apache Airflow versions, or an alias
      in the form of only major or major.minor versions specified. When an
      alias is provided, the server replaces it with the latest Apache Airflow
      version that satisfies the alias and is supported in the given Cloud
      Composer version. In all cases, the resolved image version is stored in
      the same field. See also [version
      list](/composer/docs/concepts/versioning/composer-versions) and
      [versioning overview](/composer/docs/concepts/versioning/composer-
      versioning-overview).
    pypiPackages: Optional. Custom Python Package Index (PyPI) packages to be
      installed in the environment. Keys refer to the lowercase package name
      such as "numpy" and values are the lowercase extras and version
      specifier such as "==1.12.0", "[devel,gcp_api]", or "[devel]>=1.8.2,
      <1.9.2". To specify a package without pinning it to a version specifier,
      use the empty string as the value.
    pythonVersion: Optional. The major version of Python used to run the
      Apache Airflow scheduler, worker, and webserver processes. Can be set to
      '2' or '3'. If not specified, the default is '3'. Cannot be updated.
      This field is only supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-*.*.*. Environments in newer versions always use
      Python major version 3.
    schedulerCount: Optional. The number of schedulers for Airflow. This field
      is supported for Cloud Composer environments in versions
      composer-1.*.*-airflow-2.*.*.
    webServerPluginsMode: Optional. Whether or not the web server uses custom
      plugins. If unspecified, the field defaults to `PLUGINS_ENABLED`. This
      field is supported for Cloud Composer environments in versions
      composer-3-airflow-*.*.*-build.* and newer.
  """

  class AirflowExecutorTypeValueValuesEnum(_messages.Enum):
    r"""The `airflowExecutorType` specifies the [executor](https://airflow.apa
    che.org/code.html?highlight=executor#executors) by which task instances
    are run on Airflow. If this field is unspecified, the
    `airflowExecutorType` defaults to `celery`.

    Values:
      AIRFLOW_EXECUTOR_TYPE_UNSPECIFIED: The Airflow executor type is
        unspecified.
      CELERY: The Celery executor will be used.
      KUBERNETES: The Kubernetes executor will be used.
    """
    AIRFLOW_EXECUTOR_TYPE_UNSPECIFIED = 0
    CELERY = 1
    KUBERNETES = 2

  class WebServerPluginsModeValueValuesEnum(_messages.Enum):
    r"""Optional. Whether or not the web server uses custom plugins. If
    unspecified, the field defaults to `PLUGINS_ENABLED`. This field is
    supported for Cloud Composer environments in versions
    composer-3-airflow-*.*.*-build.* and newer.

    Values:
      WEB_SERVER_PLUGINS_MODE_UNSPECIFIED: Default mode.
      PLUGINS_DISABLED: Web server plugins are not supported.
      PLUGINS_ENABLED: Web server plugins are supported.
    """
    WEB_SERVER_PLUGINS_MODE_UNSPECIFIED = 0
    PLUGINS_DISABLED = 1
    PLUGINS_ENABLED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AirflowConfigOverridesValue(_messages.Message):
    r"""Optional. Apache Airflow configuration properties to override.
    Property keys contain the section and property names, separated by a
    hyphen, for example "core-dags_are_paused_at_creation". Section names must
    not contain hyphens ("-"), opening square brackets ("["), or closing
    square brackets ("]"). The property name must not be empty and must not
    contain an equals sign ("=") or semicolon (";"). Section and property
    names must not contain a period ("."). Apache Airflow configuration
    property names must be written in
    [snake_case](https://en.wikipedia.org/wiki/Snake_case). Property values
    can contain any character, and can be written in any lower/upper case
    format. Certain Apache Airflow configuration property values are
    [blocked](/composer/docs/concepts/airflow-configurations), and cannot be
    overridden.

    Messages:
      AdditionalProperty: An additional property for a
        AirflowConfigOverridesValue object.

    Fields:
      additionalProperties: Additional properties of type
        AirflowConfigOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AirflowConfigOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvVariablesValue(_messages.Message):
    r"""Optional. Additional environment variables to provide to the Apache
    Airflow scheduler, worker, and webserver processes. Environment variable
    names must match the regular expression `a-zA-Z_*`. They cannot specify
    Apache Airflow software configuration overrides (they cannot match the
    regular expression `AIRFLOW__[A-Z0-9_]+__[A-Z0-9_]+`), and they cannot
    match any of the following reserved names: * `AIRFLOW_HOME` *
    `C_FORCE_ROOT` * `CONTAINER_NAME` * `DAGS_FOLDER` * `GCP_PROJECT` *
    `GCS_BUCKET` * `GKE_CLUSTER_NAME` * `SQL_DATABASE` * `SQL_INSTANCE` *
    `SQL_PASSWORD` * `SQL_PROJECT` * `SQL_REGION` * `SQL_USER`

    Messages:
      AdditionalProperty: An additional property for a EnvVariablesValue
        object.

    Fields:
      additionalProperties: Additional properties of type EnvVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PypiPackagesValue(_messages.Message):
    r"""Optional. Custom Python Package Index (PyPI) packages to be installed
    in the environment. Keys refer to the lowercase package name such as
    "numpy" and values are the lowercase extras and version specifier such as
    "==1.12.0", "[devel,gcp_api]", or "[devel]>=1.8.2, <1.9.2". To specify a
    package without pinning it to a version specifier, use the empty string as
    the value.

    Messages:
      AdditionalProperty: An additional property for a PypiPackagesValue
        object.

    Fields:
      additionalProperties: Additional properties of type PypiPackagesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PypiPackagesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  airflowConfigOverrides = _messages.MessageField('AirflowConfigOverridesValue', 1)
  airflowExecutorType = _messages.EnumField('AirflowExecutorTypeValueValuesEnum', 2)
  cloudDataLineageIntegration = _messages.MessageField('CloudDataLineageIntegration', 3)
  envVariables = _messages.MessageField('EnvVariablesValue', 4)
  imageVersion = _messages.StringField(5)
  pypiPackages = _messages.MessageField('PypiPackagesValue', 6)
  pythonVersion = _messages.StringField(7)
  schedulerCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  webServerPluginsMode = _messages.EnumField('WebServerPluginsModeValueValuesEnum', 9)


class SourceCode(_messages.Message):
  r"""A source code of a DAG.

  Fields:
    code: The source code of the user-defined DAG.
  """

  code = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopAirflowCommandRequest(_messages.Message):
  r"""Stop Airflow Command request.

  Fields:
    executionId: The unique ID of the command execution.
    force: If true, the execution is terminated forcefully (SIGKILL). If
      false, the execution is stopped gracefully, giving it time for cleanup.
    pod: The name of the pod where the command is executed.
    podNamespace: The namespace of the pod where the command is executed.
  """

  executionId = _messages.StringField(1)
  force = _messages.BooleanField(2)
  pod = _messages.StringField(3)
  podNamespace = _messages.StringField(4)


class StopAirflowCommandResponse(_messages.Message):
  r"""Response to StopAirflowCommandRequest.

  Fields:
    isDone: Whether the execution is still running.
    output: Output message from stopping execution request.
  """

  isDone = _messages.BooleanField(1)
  output = _messages.StringField(2, repeated=True)


class StorageConfig(_messages.Message):
  r"""The configuration for data storage in the environment.

  Fields:
    bucket: Optional. The name of the Cloud Storage bucket used by the
      environment. No `gs://` prefix.
    filestoreDirectory: Optional. The path to the Filestore directory used by
      the environment considering the share name as a root
    filestoreInstance: Optional. The Filestore instance uri used by the
      environment.
      projects/{project}/locations/{location}/instances/{instance}
  """

  bucket = _messages.StringField(1)
  filestoreDirectory = _messages.StringField(2)
  filestoreInstance = _messages.StringField(3)


class Task(_messages.Message):
  r"""A single task in a DAG.

  Fields:
    dagId: The DAG ID of the DAG the task belongs to.
    dependsOnPast: When set to true, task instances will run sequentially
      while relying on the previous task's schedule to succeed.
    doXcomPush: if true, an XCom is pushed containing the Operator's result.
    downstreamTasks: IDs of downstream tasks from this task, i.e. those that
      wait on this task.
    emailOnFailure: Whether email alerts should be sent when a task failed.
    emailOnRetry: Whether email alerts should be sent when a task is retried.
    emails: Email addresses used in alerts.
    endDate: If specified, the scheduler won't go beyond this date.
    executionTimeout: Maximum time allowed for the execution of this task
      instance, if it goes beyond it will raise and fail.
    id: The task ID.
    isDynamicallyMapped: Whether this task will be mapped to multiple task
      instances during runtime.
    maxRetryDelay: Maximum delay interval between retries.
    name: Required. The resource name of the task, in the form: "projects/{pro
      jectId}/locations/{locationId}/environments/{environmentId}/dags/{dagId}
      /tasks/{taskId}".
    owner: The owner of the task.
    pool: The slot pool this task should run in.
    poolSlots: The number of pool slots this task should use.
    priorityWeight: Priority weight of this task against other tasks.
    queue: Which queue to target when running this job.
    retries: The number of retries that should be performed before failing the
      task.
    retryDelay: Delay between retries.
    retryExponentialBackoff: Allow progressive longer waits between retries by
      using exponential backoff algorithm on retry delay.
    runAsUser: Unix username to impersonate while running the task.
    sla: Time by which the job is expected to succeed.
    startDate: Determines the execution_date for the first task instance.
    taskConcurrency: When set, a task will be able to limit the concurrent
      runs across execution dates.
    taskId: The task ID.
    taskType: The operator used in this task.
    triggerRule: Defines the rule by which dependencies are applied for the
      task to get triggered.
    upstreamTasks: IDs of upstream tasks from this task, i.e. those that this
      task waits on.
    waitForDownstream: When set to true, an instance of task X will wait for
      tasks immediately downstream of the previous instance of task X to
      finish successfully before it runs.
    weightRule: Weighting method used for the effective total priority weight
      of the task.
  """

  dagId = _messages.StringField(1)
  dependsOnPast = _messages.BooleanField(2)
  doXcomPush = _messages.BooleanField(3)
  downstreamTasks = _messages.StringField(4, repeated=True)
  emailOnFailure = _messages.BooleanField(5)
  emailOnRetry = _messages.BooleanField(6)
  emails = _messages.StringField(7, repeated=True)
  endDate = _messages.StringField(8)
  executionTimeout = _messages.StringField(9)
  id = _messages.StringField(10)
  isDynamicallyMapped = _messages.BooleanField(11)
  maxRetryDelay = _messages.StringField(12)
  name = _messages.StringField(13)
  owner = _messages.StringField(14)
  pool = _messages.StringField(15)
  poolSlots = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  priorityWeight = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  queue = _messages.StringField(18)
  retries = _messages.IntegerField(19, variant=_messages.Variant.INT32)
  retryDelay = _messages.StringField(20)
  retryExponentialBackoff = _messages.BooleanField(21)
  runAsUser = _messages.StringField(22)
  sla = _messages.StringField(23)
  startDate = _messages.StringField(24)
  taskConcurrency = _messages.IntegerField(25, variant=_messages.Variant.INT32)
  taskId = _messages.StringField(26)
  taskType = _messages.StringField(27)
  triggerRule = _messages.StringField(28)
  upstreamTasks = _messages.StringField(29, repeated=True)
  waitForDownstream = _messages.BooleanField(30)
  weightRule = _messages.StringField(31)


class TaskInstance(_messages.Message):
  r"""Task instance in a DAG run.

  Enums:
    StateValueValuesEnum: Task instance state.

  Fields:
    dagId: The DAG ID of the DAG whose execution is described by the DAG run
      the taskInstance belongs to.
    dagRunId: The DAG run ID the task instance belongs to.
    endDate: Timestamp when the task instance finished execution.
    executionDate: Execution date for the task.
    externalExecutorId: ID of the external executor.
    hostname: Hostname of the machine or pod the task runs on.
    id: The task instance ID. It is the same as the task ID of a DAG.
    isDynamicallyMapped: Whether this TaskInstance is dynamically mapped.
    mapIndex: If is_dynamically_mapped is set to true, this field contains
      index of the dynamically-mapped TaskInstance, If is_dynamically_mapped
      is set to false, this field has no meaning.
    maxTries: The number of tries that should be performed before failing the
      task.
    name: Required. The resource name of the task instance, in the form: "proj
      ects/{project_id}/locations/{location_id}/environments/{environment_id}/
      dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_instance_id_with_
      optional_map_index}".
    pool: The slot pool this task runs in.
    priorityWeight: Priority weight of this task against other tasks.
    queue: Which queue to target when running this task.
    queuedDttm: Timestamp when the task was queued.
    renderedMapIndex: Named mapping - a rendered map index template for this
      task instance. If task doesn't have a template specified or
      is_dynamically_mapped is set to false, this field is empty.
    startDate: Timestamp when the task instance started execution.
    state: Task instance state.
    taskId: The task instance ID. It is the same as the task ID in the DAG.
    taskType: The operator used in this task.
    tryNumber: The try number that this task number will be when it is
      actually run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Task instance state.

    Values:
      STATE_UNSPECIFIED: The state of the task instance is unknown.
      SUCCEEDED: Task execution succeeded.
      FAILED: Task execution failed.
      UPSTREAM_FAILED: Upstream task failed.
      SKIPPED: Task skipped.
      UP_FOR_RETRY: Task waiting to be retried.
      UP_FOR_RESCHEDULE: Task waiting to be rescheduled.
      QUEUED: Task queued.
      SCHEDULED: Task scheduled for execution.
      SENSING: Task in sensing mode.
      REMOVED: Task vanished from DAG before it ran.
      RUNNING: Task is executing.
      SHUTDOWN: External request to shut down (e.g. marked failed when
        running).
      RESTARTING: External request to restart (e.g. cleared when running).
      DEFERRED: Deferrable operator waiting on a trigger.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    UPSTREAM_FAILED = 3
    SKIPPED = 4
    UP_FOR_RETRY = 5
    UP_FOR_RESCHEDULE = 6
    QUEUED = 7
    SCHEDULED = 8
    SENSING = 9
    REMOVED = 10
    RUNNING = 11
    SHUTDOWN = 12
    RESTARTING = 13
    DEFERRED = 14

  dagId = _messages.StringField(1)
  dagRunId = _messages.StringField(2)
  endDate = _messages.StringField(3)
  executionDate = _messages.StringField(4)
  externalExecutorId = _messages.StringField(5)
  hostname = _messages.StringField(6)
  id = _messages.StringField(7)
  isDynamicallyMapped = _messages.BooleanField(8)
  mapIndex = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  maxTries = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  name = _messages.StringField(11)
  pool = _messages.StringField(12)
  priorityWeight = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  queue = _messages.StringField(14)
  queuedDttm = _messages.StringField(15)
  renderedMapIndex = _messages.StringField(16)
  startDate = _messages.StringField(17)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  taskId = _messages.StringField(19)
  taskType = _messages.StringField(20)
  tryNumber = _messages.IntegerField(21, variant=_messages.Variant.INT32)


class TaskLogsRetentionConfig(_messages.Message):
  r"""The configuration setting for Task Logs.

  Enums:
    StorageModeValueValuesEnum: Optional. The mode of storage for Airflow
      workers task logs.

  Fields:
    retentionDays: Optional. The number of days to retain task logs in the
      Cloud Logging bucket
    storageMode: Optional. The mode of storage for Airflow workers task logs.
  """

  class StorageModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode of storage for Airflow workers task logs.

    Values:
      TASK_LOGS_STORAGE_MODE_UNSPECIFIED: This configuration is not specified
        by the user.
      CLOUD_LOGGING_AND_CLOUD_STORAGE: Store task logs in Cloud Logging and in
        the environment's Cloud Storage bucket.
      CLOUD_LOGGING_ONLY: Store task logs in Cloud Logging only.
    """
    TASK_LOGS_STORAGE_MODE_UNSPECIFIED = 0
    CLOUD_LOGGING_AND_CLOUD_STORAGE = 1
    CLOUD_LOGGING_ONLY = 2

  retentionDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  storageMode = _messages.EnumField('StorageModeValueValuesEnum', 2)


class TriggerDagRequest(_messages.Message):
  r"""Request to trigger a DAG run.

  Messages:
    ConfValue: The key-value pairs get pickled into the conf attribute in the
      DAG run.

  Fields:
    conf: The key-value pairs get pickled into the conf attribute in the DAG
      run.
    dagRunId: The dag_run_id to be assigned to the triggered DAG run.
    executionDate: The execution date of the DAG run.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfValue(_messages.Message):
    r"""The key-value pairs get pickled into the conf attribute in the DAG
    run.

    Messages:
      AdditionalProperty: An additional property for a ConfValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  conf = _messages.MessageField('ConfValue', 1)
  dagRunId = _messages.StringField(2)
  executionDate = _messages.StringField(3)


class TriggererResource(_messages.Message):
  r"""Configuration for resources used by Airflow triggerers.

  Fields:
    count: Optional. The number of triggerers.
    cpu: Optional. CPU request and limit for a single Airflow triggerer
      replica.
    memoryGb: Optional. Memory (GB) request and limit for a single Airflow
      triggerer replica.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  cpu = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  memoryGb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class UserWorkloadsConfigMap(_messages.Message):
  r"""User workloads ConfigMap used by Airflow tasks that run with Kubernetes
  executor or KubernetesPodOperator.

  Messages:
    DataValue: Optional. The "data" field of Kubernetes ConfigMap, organized
      in key-value pairs. For details see:
      https://kubernetes.io/docs/concepts/configuration/configmap/ Example: {
      "example_key": "example_value", "another_key": "another_value" }

  Fields:
    data: Optional. The "data" field of Kubernetes ConfigMap, organized in
      key-value pairs. For details see:
      https://kubernetes.io/docs/concepts/configuration/configmap/ Example: {
      "example_key": "example_value", "another_key": "another_value" }
    name: Identifier. The resource name of the ConfigMap, in the form: "projec
      ts/{projectId}/locations/{locationId}/environments/{environmentId}/userW
      orkloadsConfigMaps/{userWorkloadsConfigMapId}"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""Optional. The "data" field of Kubernetes ConfigMap, organized in key-
    value pairs. For details see:
    https://kubernetes.io/docs/concepts/configuration/configmap/ Example: {
    "example_key": "example_value", "another_key": "another_value" }

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Additional properties of type DataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  data = _messages.MessageField('DataValue', 1)
  name = _messages.StringField(2)


class UserWorkloadsSecret(_messages.Message):
  r"""User workloads Secret used by Airflow tasks that run with Kubernetes
  executor or KubernetesPodOperator.

  Messages:
    DataValue: Optional. The "data" field of Kubernetes Secret, organized in
      key-value pairs, which can contain sensitive values such as a password,
      a token, or a key. The values for all keys have to be base64-encoded
      strings. For details see:
      https://kubernetes.io/docs/concepts/configuration/secret/ Example: {
      "example": "ZXhhbXBsZV92YWx1ZQ==", "another-example":
      "YW5vdGhlcl9leGFtcGxlX3ZhbHVl" }

  Fields:
    data: Optional. The "data" field of Kubernetes Secret, organized in key-
      value pairs, which can contain sensitive values such as a password, a
      token, or a key. The values for all keys have to be base64-encoded
      strings. For details see:
      https://kubernetes.io/docs/concepts/configuration/secret/ Example: {
      "example": "ZXhhbXBsZV92YWx1ZQ==", "another-example":
      "YW5vdGhlcl9leGFtcGxlX3ZhbHVl" }
    name: Identifier. The resource name of the Secret, in the form: "projects/
      {projectId}/locations/{locationId}/environments/{environmentId}/userWork
      loadsSecrets/{userWorkloadsSecretId}"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""Optional. The "data" field of Kubernetes Secret, organized in key-
    value pairs, which can contain sensitive values such as a password, a
    token, or a key. The values for all keys have to be base64-encoded
    strings. For details see:
    https://kubernetes.io/docs/concepts/configuration/secret/ Example: {
    "example": "ZXhhbXBsZV92YWx1ZQ==", "another-example":
    "YW5vdGhlcl9leGFtcGxlX3ZhbHVl" }

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Additional properties of type DataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  data = _messages.MessageField('DataValue', 1)
  name = _messages.StringField(2)


class WebServerConfig(_messages.Message):
  r"""The configuration settings for the Airflow web server App Engine
  instance. Supported for Cloud Composer environments in versions
  composer-1.*.*-airflow-*.*.*.

  Fields:
    machineType: Optional. Machine type on which Airflow web server is
      running. It has to be one of: composer-n1-webserver-2,
      composer-n1-webserver-4 or composer-n1-webserver-8. If not specified,
      composer-n1-webserver-2 will be used. Value custom is returned only in
      response, if Airflow web server parameters were manually changed to a
      non-standard values.
  """

  machineType = _messages.StringField(1)


class WebServerNetworkAccessControl(_messages.Message):
  r"""Network-level access control policy for the Airflow web server.

  Fields:
    allowedIpRanges: A collection of allowed IP ranges with descriptions.
  """

  allowedIpRanges = _messages.MessageField('AllowedIpRange', 1, repeated=True)


class WebServerResource(_messages.Message):
  r"""Configuration for resources used by Airflow web server.

  Fields:
    count: Optional. The number of web server instances. If not provided or
      set to 0, a single web server instance will be created.
    cpu: Optional. CPU request and limit for Airflow web server.
    memoryGb: Optional. Memory (GB) request and limit for Airflow web server.
    storageGb: Optional. Storage (GB) request and limit for Airflow web
      server.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  cpu = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  memoryGb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  storageGb = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class WorkerResource(_messages.Message):
  r"""Configuration for resources used by Airflow workers.

  Fields:
    cpu: Optional. CPU request and limit for a single Airflow worker replica.
    maxCount: Optional. Maximum number of workers for autoscaling.
    memoryGb: Optional. Memory (GB) request and limit for a single Airflow
      worker replica.
    minCount: Optional. Minimum number of workers for autoscaling.
    storageGb: Optional. Storage (GB) request and limit for a single Airflow
      worker replica.
  """

  cpu = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  maxCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  memoryGb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  minCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  storageGb = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class WorkloadsConfig(_messages.Message):
  r"""The Kubernetes workloads configuration for GKE cluster associated with
  the Cloud Composer environment. Supported for Cloud Composer environments in
  versions composer-2.*.*-airflow-*.*.* and newer.

  Fields:
    dagProcessor: Optional. Resources used by Airflow DAG processors. This
      field is supported for Cloud Composer environments in versions
      composer-3-airflow-*.*.*-build.* and newer.
    scheduler: Optional. Resources used by Airflow scheduler.
    schedulerCpu: Optional. CPU request and limit for Airflow scheduler.
    triggerer: Optional. Resources used by Airflow triggerers.
    webServer: Optional. Resources used by Airflow web server.
    worker: Optional. Resources used by Airflow workers.
    workerCpu: Optional. CPU request and limit for Airflow worker.
    workerMaxCount: Optional. Maximum number of workers for autoscaling.
    workerMinCount: Optional. Minimum number of workers for autoscaling.
  """

  dagProcessor = _messages.MessageField('DagProcessorResource', 1)
  scheduler = _messages.MessageField('SchedulerResource', 2)
  schedulerCpu = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  triggerer = _messages.MessageField('TriggererResource', 4)
  webServer = _messages.MessageField('WebServerResource', 5)
  worker = _messages.MessageField('WorkerResource', 6)
  workerCpu = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  workerMaxCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  workerMinCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ComposerProjectsLocationsEnvironmentsDagsListStatsRequest, 'interval_endTime', 'interval.endTime')
encoding.AddCustomJsonFieldMapping(
    ComposerProjectsLocationsEnvironmentsDagsListStatsRequest, 'interval_startTime', 'interval.startTime')
