"""Generated message classes for authztoolkit version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'authztoolkit'


class AnthosServiceMesh(_messages.Message):
  r"""Message describing AnthosServiceMesh based workload object.

  Fields:
    serviceAccount: Immutable. workload ID = IAM Service account
  """

  serviceAccount = _messages.StringField(1)


class AuthztoolkitProjectsLocationsGetRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class AuthztoolkitProjectsLocationsListRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class AuthztoolkitProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class AuthztoolkitProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class AuthztoolkitProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AuthztoolkitProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AuthztoolkitProjectsLocationsPoliciesCreateRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsPoliciesCreateRequest object.

  Fields:
    parent: Required. Value for parent. Example:
      projects/{project)/locations/{location}
    policy: A Policy resource to be passed as the request body.
    policyId: Required. The unique ID of the policy to be created.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  policy = _messages.MessageField('Policy', 2)
  policyId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class AuthztoolkitProjectsLocationsPoliciesDeleteRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsPoliciesDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class AuthztoolkitProjectsLocationsPoliciesGetRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsPoliciesGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class AuthztoolkitProjectsLocationsPoliciesListRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsPoliciesListRequest object.

  Fields:
    filter: Query filter. Filters must adhere to the following rules: *
      Boolean values must be unquoted "true" or "false" string literals. *
      String values must be double-quoted. * Wildcard character("*") is
      limited to use with the has operator (":"), and can be used only at the
      end of a string literal. * Timestamps must be quoted strings in the
      RFC3339 format. Example : filter=create_time>"2022-05-09T22:28:28Z"
      Filters support logical operators - AND, OR, NOT (Note: OR has higher
      precedence than AND)
    orderBy: Criteria for ordering results. Currently supported fields for
      ordering - name and create_time. Example: order_by="name
      desc,create_time desc".
    pageSize: Requested page size. Server may return fewer items than
      requested. The maximum allowed value is 50, values above this will be
      coerced to 50. Default value: 50
    pageToken: Next page token, received from a previous policies.list call.
      When paginating, all other input parameters (except page_token) provided
      to policies.list call must remain the same.
    parent: Required. Parent value for ListPoliciesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AuthztoolkitProjectsLocationsPoliciesPatchRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsPoliciesPatchRequest object.

  Fields:
    name: Name of resource
    policy: A Policy resource to be passed as the request body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Policy resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request.
      If the user provides the value '*', all fields will be updated. Note:
      Field masks are not supported to update deeper-levels of policy_spec,
      i.e. partial updates for policy_spec are not supported.
  """

  name = _messages.StringField(1, required=True)
  policy = _messages.MessageField('Policy', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class AuthztoolkitProjectsLocationsTargetAssociationsCreateRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsTargetAssociationsCreateRequest object.

  Fields:
    parent: Required. Value for parent. Example:
      projects/{project)/locations/{location}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    targetAssociation: A TargetAssociation resource to be passed as the
      request body.
    targetAssociationId: Required. The unique ID of the TargetAssociation to
      be created.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  targetAssociation = _messages.MessageField('TargetAssociation', 3)
  targetAssociationId = _messages.StringField(4)


class AuthztoolkitProjectsLocationsTargetAssociationsDeleteRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsTargetAssociationsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class AuthztoolkitProjectsLocationsTargetAssociationsGetRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsTargetAssociationsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class AuthztoolkitProjectsLocationsTargetAssociationsListRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsTargetAssociationsListRequest object.

  Fields:
    filter: Optional. Query filter. Filters must adhere to the following
      rules: * Boolean values must be unquoted "true" or "false" string
      literals. * String values must be double-quoted. * Wildcard
      character("*") is limited to use with the has operator (":"), and can be
      used only at the end of a string literal. * Timestamps must be quoted
      strings in the RFC3339 format. Example :
      filter=create_time>"2022-05-09T22:28:28Z" Filters support logical
      operators - AND, OR, NOT (Note: OR has higher precedence than AND)
    orderBy: Optional. Criteria for ordering results. Currently supported
      fields for ordering - name and create_time. Example: order_by="name
      desc,create_time desc".
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. The maximum allowed value is 50, values above this will
      be coerced to 50. Default value: 50
    pageToken: Optional. Next page token, received from a previous
      targetAssociations.list call. When paginating, all other input
      parameters (except page_token) provided to targetAssociations.list call
      must remain the same.
    parent: Required. Parent value for ListTargetAssociationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AuthztoolkitProjectsLocationsTargetAssociationsPatchRequest(_messages.Message):
  r"""A AuthztoolkitProjectsLocationsTargetAssociationsPatchRequest object.

  Fields:
    name: Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    targetAssociation: A TargetAssociation resource to be passed as the
      request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the TargetAssociation resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  targetAssociation = _messages.MessageField('TargetAssociation', 3)
  updateMask = _messages.StringField(4)


class CELPolicy(_messages.Message):
  r"""Authorization policy schema. A policy is composed of a set of Rules that
  specifies the conditions that need to be met in order for an authorization
  decision to be finalized. For example, a policy could specify that all
  requests to a specific IP address or that requests from a specific SPIFFE ID
  must be allowed. Policy can also contain a CEL expression for custom checks.

  Fields:
    ruleBlocks: List of rule blocks.
  """

  ruleBlocks = _messages.MessageField('RuleBlock', 1, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class From(_messages.Message):
  r"""From clause specifies the principals to whom the rule applies.

  Fields:
    principals: List of requesting principal identifiers.
  """

  principals = _messages.StringField(1, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPoliciesResponse(_messages.Message):
  r"""Response returned by policies.list method.

  Fields:
    nextPageToken: Next page token. Provide this to retrieve the subsequent
      page. When paginating, all other parameters (except page_size) provided
      to policies.list must match the call that provided the page token.
    policies: The list of Policies.
    unreachable: Represents missing potential additional resources.
  """

  nextPageToken = _messages.StringField(1)
  policies = _messages.MessageField('Policy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTargetAssociationsResponse(_messages.Message):
  r"""Response returned by targetAssociations.list method.

  Fields:
    nextPageToken: Next page token. Provide this to retrieve the subsequent
      page. When paginating, all other parameters (except page_size) provided
      to targetAssociations.list must match the call that provided the page
      token.
    targetAssociations: The list of TargetAssociation
    unreachable: Represents missing potential additional resources.
  """

  nextPageToken = _messages.StringField(1)
  targetAssociations = _messages.MessageField('TargetAssociation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Name of the resource being targeted by this
      operation.
    verb: Output only. Name of the verb executed by the operation. Example:
      create, update, delete etc.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""Message describing Policy object

  Fields:
    celPolicy: CEL-based authorization policy schema
    createTime: Output only. Create timestamp
    displayName: An arbitrary user-provided name for policy. The display name
      should adhere to the following format. * Must be 6 to 63 characters in
      length. * Can only contain lowercase letters, numbers, and hyphens. *
      Must start with a letter.
    name: Name of resource
    updateTime: Output only. Update timestamp
  """

  celPolicy = _messages.MessageField('CELPolicy', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class PolicyConfig(_messages.Message):
  r"""Message describing PolicyConfig object consisting of policy name and its
  configurations.

  Fields:
    policy: Required. Full policy name. Example:
      projects/{project}/locations/{location}/policies/{policy}
  """

  policy = _messages.StringField(1)


class Rule(_messages.Message):
  r"""Rule is a set of conditions that are evaluated for each incoming
  authorization request.

  Fields:
    displayName: Display name of the rule
    requestFrom: Request origins this rule applies to.
    requestTo: Request destination this rule applies to.
    when: Custom condition for the request.
  """

  displayName = _messages.StringField(1)
  requestFrom = _messages.MessageField('From', 2)
  requestTo = _messages.MessageField('To', 3)
  when = _messages.MessageField('When', 4)


class RuleBlock(_messages.Message):
  r"""RuleBlock holds the action and rule definitions of an authorization
  policy.

  Enums:
    ActionValueValuesEnum: Action type of this policy.

  Fields:
    action: Action type of this policy.
    rules: Rules that must be evaluated for this policy action.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Action type of this policy.

    Values:
      ACTION_UNSPECIFIED: Policy rules with no action type.
      ALLOW: Maps to allow policy rules.
      DENY: Maps to deny policy rules.
    """
    ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  rules = _messages.MessageField('Rule', 2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TargetAssociation(_messages.Message):
  r"""Message describing TargetAssociation object

  Enums:
    EnableAuthorizationDebugLogValueValuesEnum: Optional. Enable the
      generation of authorization debug logs for the target.

  Fields:
    asmWorkload: Immutable. AnthosServiceMesh based workload. Authorization
      Toolkit does not auto configure the authorization settings on the
      workload.
    createTime: Output only. [Output only] Create time stamp
    displayName: Optional. An arbitrary user-provided name for
      TargetAssociation. The display name should adhere to the following
      format. * Must be 6 to 63 characters in length. * Can only contain
      lowercase letters, numbers, and hyphens. * Must start with a letter.
    enableAuthorizationAuditLog: Optional. Enable the generation of
      authorization audit logs for the target.
    enableAuthorizationDebugLog: Optional. Enable the generation of
      authorization debug logs for the target.
    name: Identifier. name of resource
    policies: Optional. List of policies with full policy name and its
      configuration
    updateTime: Output only. [Output only] Update time stamp
  """

  class EnableAuthorizationDebugLogValueValuesEnum(_messages.Enum):
    r"""Optional. Enable the generation of authorization debug logs for the
    target.

    Values:
      LOG_NONE: Disable the authorization debug log.
      LOG_ERROR: Generate authorization debug log only in case the
        authorization result is an error.
      LOG_DENY_AND_ERROR: Generate authorization debug log only in case the
        authorization is denied or the authorization result is an error.
      LOG_ALL: Generate authorization debug log for all the authorization
        results.
    """
    LOG_NONE = 0
    LOG_ERROR = 1
    LOG_DENY_AND_ERROR = 2
    LOG_ALL = 3

  asmWorkload = _messages.MessageField('AnthosServiceMesh', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  enableAuthorizationAuditLog = _messages.BooleanField(4)
  enableAuthorizationDebugLog = _messages.EnumField('EnableAuthorizationDebugLogValueValuesEnum', 5)
  name = _messages.StringField(6)
  policies = _messages.MessageField('PolicyConfig', 7, repeated=True)
  updateTime = _messages.StringField(8)


class To(_messages.Message):
  r"""To clause specifies the host(s), method(s), path(s) and port(s) that the
  rule applies to.

  Fields:
    hosts: List of hosts.
    methods: List of HTTP request methods.
    paths: List of request paths.
    ports: List of host ports.
  """

  hosts = _messages.StringField(1, repeated=True)
  methods = _messages.StringField(2, repeated=True)
  paths = _messages.StringField(3, repeated=True)
  ports = _messages.IntegerField(4, repeated=True, variant=_messages.Variant.INT32)


class When(_messages.Message):
  r"""Custom condition the request must satisfy.

  Fields:
    expr: CEL expression to be evaluated.
  """

  expr = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
