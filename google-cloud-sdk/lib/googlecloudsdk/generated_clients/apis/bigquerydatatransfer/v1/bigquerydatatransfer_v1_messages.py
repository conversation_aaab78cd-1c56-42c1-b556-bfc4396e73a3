"""Generated message classes for bigquerydatatransfer version v1.

Schedule queries or transfer external data from SaaS applications to Google
BigQuery on a regular basis.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'bigquerydatatransfer'


class BigquerydatatransferProjectsDataSourcesCheckValidCredsRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsDataSourcesCheckValidCredsRequest object.

  Fields:
    checkValidCredsRequest: A CheckValidCredsRequest resource to be passed as
      the request body.
    name: Required. The name of the data source. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/dataSources/{data_source_id}` If you are using
      the regionalized method, the name should be in the following form: * `pr
      ojects/{project_id}/locations/{location_id}/dataSources/{data_source_id}
      `
  """

  checkValidCredsRequest = _messages.MessageField('CheckValidCredsRequest', 1)
  name = _messages.StringField(2, required=True)


class BigquerydatatransferProjectsDataSourcesGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsDataSourcesGetRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/dataSources/{data_source_id}` If you are using
      the regionalized method, the name should be in the following form: * `pr
      ojects/{project_id}/locations/{location_id}/dataSources/{data_source_id}
      `
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsDataSourcesListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsDataSourcesListRequest object.

  Fields:
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListDataSourcesRequest` list results. For multiple-page results,
      `ListDataSourcesResponse` outputs a `next_page` token, which can be used
      as the `page_token` value to request the next page of list results.
    parent: Required. The BigQuery project id for which data sources should be
      returned. Must be in the form: `projects/{project_id}` or
      `projects/{project_id}/locations/{location_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BigquerydatatransferProjectsEnrollDataSourcesRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsEnrollDataSourcesRequest object.

  Fields:
    enrollDataSourcesRequest: A EnrollDataSourcesRequest resource to be passed
      as the request body.
    name: Required. The name of the project resource in the form:
      `projects/{project_id}`
  """

  enrollDataSourcesRequest = _messages.MessageField('EnrollDataSourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class BigquerydatatransferProjectsLocationsDataSourcesCheckValidCredsRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsDataSourcesCheckValidCredsRequest
  object.

  Fields:
    checkValidCredsRequest: A CheckValidCredsRequest resource to be passed as
      the request body.
    name: Required. The name of the data source. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/dataSources/{data_source_id}` If you are using
      the regionalized method, the name should be in the following form: * `pr
      ojects/{project_id}/locations/{location_id}/dataSources/{data_source_id}
      `
  """

  checkValidCredsRequest = _messages.MessageField('CheckValidCredsRequest', 1)
  name = _messages.StringField(2, required=True)


class BigquerydatatransferProjectsLocationsDataSourcesGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsDataSourcesGetRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/dataSources/{data_source_id}` If you are using
      the regionalized method, the name should be in the following form: * `pr
      ojects/{project_id}/locations/{location_id}/dataSources/{data_source_id}
      `
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsDataSourcesListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsDataSourcesListRequest object.

  Fields:
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListDataSourcesRequest` list results. For multiple-page results,
      `ListDataSourcesResponse` outputs a `next_page` token, which can be used
      as the `page_token` value to request the next page of list results.
    parent: Required. The BigQuery project id for which data sources should be
      returned. Must be in the form: `projects/{project_id}` or
      `projects/{project_id}/locations/{location_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BigquerydatatransferProjectsLocationsEnrollDataSourcesRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsEnrollDataSourcesRequest object.

  Fields:
    enrollDataSourcesRequest: A EnrollDataSourcesRequest resource to be passed
      as the request body.
    name: Required. The name of the project resource in the form:
      `projects/{project_id}`
  """

  enrollDataSourcesRequest = _messages.MessageField('EnrollDataSourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class BigquerydatatransferProjectsLocationsGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class BigquerydatatransferProjectsLocationsTransferConfigsCreateRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsCreateRequest
  object.

  Fields:
    authorizationCode: Deprecated: Authorization code was required when
      `transferConfig.dataSourceId` is 'youtube_channel' but it is no longer
      used in any data sources. Use `version_info` instead. Optional OAuth2
      authorization code to use with this transfer configuration. This is
      required only if `transferConfig.dataSourceId` is 'youtube_channel' and
      new credentials are needed, as indicated by `CheckValidCreds`. In order
      to obtain authorization_code, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=authorization_code&client_id=client_i
      d&scope=data_source_scopes * The client_id is the OAuth client_id of the
      data source as returned by ListDataSources method. * data_source_scopes
      are the scopes returned by ListDataSources method. Note that this should
      not be set when `service_account_name` is used to create the transfer
      config.
    parent: Required. The BigQuery project id where the transfer configuration
      should be created. Must be in the format
      projects/{project_id}/locations/{location_id} or projects/{project_id}.
      If specified location and location of the destination bigquery dataset
      do not match - the request will fail.
    serviceAccountName: Optional service account email. If this field is set,
      the transfer config will be created with this service account's
      credentials. It requires that the requesting user calling this API has
      permissions to act as this service account. Note that not all data
      sources support service account credentials when creating a transfer
      config. For the latest list of data sources, read about [using service
      accounts](https://cloud.google.com/bigquery-transfer/docs/use-service-
      accounts).
    transferConfig: A TransferConfig resource to be passed as the request
      body.
    versionInfo: Optional version info. This parameter replaces
      `authorization_code` which is no longer used in any data sources. This
      is required only if `transferConfig.dataSourceId` is 'youtube_channel'
      *or* new credentials are needed, as indicated by `CheckValidCreds`. In
      order to obtain version info, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=version_info&client_id=client_id&scop
      e=data_source_scopes * The client_id is the OAuth client_id of the data
      source as returned by ListDataSources method. * data_source_scopes are
      the scopes returned by ListDataSources method. Note that this should not
      be set when `service_account_name` is used to create the transfer
      config.
  """

  authorizationCode = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  serviceAccountName = _messages.StringField(3)
  transferConfig = _messages.MessageField('TransferConfig', 4)
  versionInfo = _messages.StringField(5)


class BigquerydatatransferProjectsLocationsTransferConfigsDeleteRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsDeleteRequest
  object.

  Fields:
    name: Required. The name of the resource to delete. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsGetRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsListRequest
  object.

  Fields:
    dataSourceIds: When specified, only configurations of requested data
      sources are returned.
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransfersRequest` list results. For multiple-page results,
      `ListTransfersResponse` outputs a `next_page` token, which can be used
      as the `page_token` value to request the next page of list results.
    parent: Required. The BigQuery project id for which transfer configs
      should be returned. If you are using the regionless method, the location
      must be `US` and `parent` should be in the following form: *
      `projects/{project_id} If you are using the regionalized method,
      `parent` should be in the following form: *
      `projects/{project_id}/locations/{location_id}`
  """

  dataSourceIds = _messages.StringField(1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsPatchRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsPatchRequest
  object.

  Fields:
    authorizationCode: Deprecated: Authorization code was required when
      `transferConfig.dataSourceId` is 'youtube_channel' but it is no longer
      used in any data sources. Use `version_info` instead. Optional OAuth2
      authorization code to use with this transfer configuration. This is
      required only if `transferConfig.dataSourceId` is 'youtube_channel' and
      new credentials are needed, as indicated by `CheckValidCreds`. In order
      to obtain authorization_code, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=authorization_code&client_id=client_i
      d&scope=data_source_scopes * The client_id is the OAuth client_id of the
      data source as returned by ListDataSources method. * data_source_scopes
      are the scopes returned by ListDataSources method. Note that this should
      not be set when `service_account_name` is used to update the transfer
      config.
    name: Identifier. The resource name of the transfer config. Transfer
      config names have the form either
      `projects/{project_id}/locations/{region}/transferConfigs/{config_id}`
      or `projects/{project_id}/transferConfigs/{config_id}`, where
      `config_id` is usually a UUID, even though it is not guaranteed or
      required. The name is ignored when creating a transfer config.
    serviceAccountName: Optional service account email. If this field is set,
      the transfer config will be created with this service account's
      credentials. It requires that the requesting user calling this API has
      permissions to act as this service account. Note that not all data
      sources support service account credentials when creating a transfer
      config. For the latest list of data sources, read about [using service
      accounts](https://cloud.google.com/bigquery-transfer/docs/use-service-
      accounts).
    transferConfig: A TransferConfig resource to be passed as the request
      body.
    updateMask: Required. Required list of fields to be updated in this
      request.
    versionInfo: Optional version info. This parameter replaces
      `authorization_code` which is no longer used in any data sources. This
      is required only if `transferConfig.dataSourceId` is 'youtube_channel'
      *or* new credentials are needed, as indicated by `CheckValidCreds`. In
      order to obtain version info, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=version_info&client_id=client_id&scop
      e=data_source_scopes * The client_id is the OAuth client_id of the data
      source as returned by ListDataSources method. * data_source_scopes are
      the scopes returned by ListDataSources method. Note that this should not
      be set when `service_account_name` is used to update the transfer
      config.
  """

  authorizationCode = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  serviceAccountName = _messages.StringField(3)
  transferConfig = _messages.MessageField('TransferConfig', 4)
  updateMask = _messages.StringField(5)
  versionInfo = _messages.StringField(6)


class BigquerydatatransferProjectsLocationsTransferConfigsRunsDeleteRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsRunsDeleteRequest
  object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsRunsGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsRunsGetRequest
  object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsRunsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsRunsListRequest
  object.

  Enums:
    RunAttemptValueValuesEnum: Indicates how run attempts are to be pulled.
    StatesValueValuesEnum: When specified, only transfer runs with requested
      states are returned.

  Fields:
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransferRunsRequest` list results. For multiple-page results,
      `ListTransferRunsResponse` outputs a `next_page` token, which can be
      used as the `page_token` value to request the next page of list results.
    parent: Required. Name of transfer configuration for which transfer runs
      should be retrieved. If you are using the regionless method, the
      location must be `US` and the name should be in the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    runAttempt: Indicates how run attempts are to be pulled.
    states: When specified, only transfer runs with requested states are
      returned.
  """

  class RunAttemptValueValuesEnum(_messages.Enum):
    r"""Indicates how run attempts are to be pulled.

    Values:
      RUN_ATTEMPT_UNSPECIFIED: All runs should be returned.
      LATEST: Only latest run per day should be returned.
    """
    RUN_ATTEMPT_UNSPECIFIED = 0
    LATEST = 1

  class StatesValueValuesEnum(_messages.Enum):
    r"""When specified, only transfer runs with requested states are returned.

    Values:
      TRANSFER_STATE_UNSPECIFIED: State placeholder (0).
      PENDING: Data transfer is scheduled and is waiting to be picked up by
        data transfer backend (2).
      RUNNING: Data transfer is in progress (3).
      SUCCEEDED: Data transfer completed successfully (4).
      FAILED: Data transfer failed (5).
      CANCELLED: Data transfer is cancelled (6).
    """
    TRANSFER_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  runAttempt = _messages.EnumField('RunAttemptValueValuesEnum', 4)
  states = _messages.EnumField('StatesValueValuesEnum', 5, repeated=True)


class BigquerydatatransferProjectsLocationsTransferConfigsRunsTransferLogsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsTransferConfigsRunsTransferLogsLi
  stRequest object.

  Enums:
    MessageTypesValueValuesEnum: Message types to return. If not populated -
      INFO, WARNING and ERROR messages are returned.

  Fields:
    messageTypes: Message types to return. If not populated - INFO, WARNING
      and ERROR messages are returned.
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransferLogsRequest` list results. For multiple-page results,
      `ListTransferLogsResponse` outputs a `next_page` token, which can be
      used as the `page_token` value to request the next page of list results.
    parent: Required. Transfer run name. If you are using the regionless
      method, the location must be `US` and the name should be in the
      following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  class MessageTypesValueValuesEnum(_messages.Enum):
    r"""Message types to return. If not populated - INFO, WARNING and ERROR
    messages are returned.

    Values:
      MESSAGE_SEVERITY_UNSPECIFIED: No severity specified.
      INFO: Informational message.
      WARNING: Warning message.
      ERROR: Error message.
    """
    MESSAGE_SEVERITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

  messageTypes = _messages.EnumField('MessageTypesValueValuesEnum', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BigquerydatatransferProjectsLocationsTransferConfigsScheduleRunsRequest(_messages.Message):
  r"""A
  BigquerydatatransferProjectsLocationsTransferConfigsScheduleRunsRequest
  object.

  Fields:
    parent: Required. Transfer configuration name. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    scheduleTransferRunsRequest: A ScheduleTransferRunsRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  scheduleTransferRunsRequest = _messages.MessageField('ScheduleTransferRunsRequest', 2)


class BigquerydatatransferProjectsLocationsTransferConfigsStartManualRunsRequest(_messages.Message):
  r"""A
  BigquerydatatransferProjectsLocationsTransferConfigsStartManualRunsRequest
  object.

  Fields:
    parent: Required. Transfer configuration name. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    startManualTransferRunsRequest: A StartManualTransferRunsRequest resource
      to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  startManualTransferRunsRequest = _messages.MessageField('StartManualTransferRunsRequest', 2)


class BigquerydatatransferProjectsLocationsUnenrollDataSourcesRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsLocationsUnenrollDataSourcesRequest
  object.

  Fields:
    name: Required. The name of the project resource in the form:
      `projects/{project_id}`
    unenrollDataSourcesRequest: A UnenrollDataSourcesRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  unenrollDataSourcesRequest = _messages.MessageField('UnenrollDataSourcesRequest', 2)


class BigquerydatatransferProjectsTransferConfigsCreateRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsCreateRequest object.

  Fields:
    authorizationCode: Deprecated: Authorization code was required when
      `transferConfig.dataSourceId` is 'youtube_channel' but it is no longer
      used in any data sources. Use `version_info` instead. Optional OAuth2
      authorization code to use with this transfer configuration. This is
      required only if `transferConfig.dataSourceId` is 'youtube_channel' and
      new credentials are needed, as indicated by `CheckValidCreds`. In order
      to obtain authorization_code, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=authorization_code&client_id=client_i
      d&scope=data_source_scopes * The client_id is the OAuth client_id of the
      data source as returned by ListDataSources method. * data_source_scopes
      are the scopes returned by ListDataSources method. Note that this should
      not be set when `service_account_name` is used to create the transfer
      config.
    parent: Required. The BigQuery project id where the transfer configuration
      should be created. Must be in the format
      projects/{project_id}/locations/{location_id} or projects/{project_id}.
      If specified location and location of the destination bigquery dataset
      do not match - the request will fail.
    serviceAccountName: Optional service account email. If this field is set,
      the transfer config will be created with this service account's
      credentials. It requires that the requesting user calling this API has
      permissions to act as this service account. Note that not all data
      sources support service account credentials when creating a transfer
      config. For the latest list of data sources, read about [using service
      accounts](https://cloud.google.com/bigquery-transfer/docs/use-service-
      accounts).
    transferConfig: A TransferConfig resource to be passed as the request
      body.
    versionInfo: Optional version info. This parameter replaces
      `authorization_code` which is no longer used in any data sources. This
      is required only if `transferConfig.dataSourceId` is 'youtube_channel'
      *or* new credentials are needed, as indicated by `CheckValidCreds`. In
      order to obtain version info, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=version_info&client_id=client_id&scop
      e=data_source_scopes * The client_id is the OAuth client_id of the data
      source as returned by ListDataSources method. * data_source_scopes are
      the scopes returned by ListDataSources method. Note that this should not
      be set when `service_account_name` is used to create the transfer
      config.
  """

  authorizationCode = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  serviceAccountName = _messages.StringField(3)
  transferConfig = _messages.MessageField('TransferConfig', 4)
  versionInfo = _messages.StringField(5)


class BigquerydatatransferProjectsTransferConfigsDeleteRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsDeleteRequest object.

  Fields:
    name: Required. The name of the resource to delete. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsTransferConfigsGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsGetRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsTransferConfigsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsListRequest object.

  Fields:
    dataSourceIds: When specified, only configurations of requested data
      sources are returned.
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransfersRequest` list results. For multiple-page results,
      `ListTransfersResponse` outputs a `next_page` token, which can be used
      as the `page_token` value to request the next page of list results.
    parent: Required. The BigQuery project id for which transfer configs
      should be returned. If you are using the regionless method, the location
      must be `US` and `parent` should be in the following form: *
      `projects/{project_id} If you are using the regionalized method,
      `parent` should be in the following form: *
      `projects/{project_id}/locations/{location_id}`
  """

  dataSourceIds = _messages.StringField(1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BigquerydatatransferProjectsTransferConfigsPatchRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsPatchRequest object.

  Fields:
    authorizationCode: Deprecated: Authorization code was required when
      `transferConfig.dataSourceId` is 'youtube_channel' but it is no longer
      used in any data sources. Use `version_info` instead. Optional OAuth2
      authorization code to use with this transfer configuration. This is
      required only if `transferConfig.dataSourceId` is 'youtube_channel' and
      new credentials are needed, as indicated by `CheckValidCreds`. In order
      to obtain authorization_code, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=authorization_code&client_id=client_i
      d&scope=data_source_scopes * The client_id is the OAuth client_id of the
      data source as returned by ListDataSources method. * data_source_scopes
      are the scopes returned by ListDataSources method. Note that this should
      not be set when `service_account_name` is used to update the transfer
      config.
    name: Identifier. The resource name of the transfer config. Transfer
      config names have the form either
      `projects/{project_id}/locations/{region}/transferConfigs/{config_id}`
      or `projects/{project_id}/transferConfigs/{config_id}`, where
      `config_id` is usually a UUID, even though it is not guaranteed or
      required. The name is ignored when creating a transfer config.
    serviceAccountName: Optional service account email. If this field is set,
      the transfer config will be created with this service account's
      credentials. It requires that the requesting user calling this API has
      permissions to act as this service account. Note that not all data
      sources support service account credentials when creating a transfer
      config. For the latest list of data sources, read about [using service
      accounts](https://cloud.google.com/bigquery-transfer/docs/use-service-
      accounts).
    transferConfig: A TransferConfig resource to be passed as the request
      body.
    updateMask: Required. Required list of fields to be updated in this
      request.
    versionInfo: Optional version info. This parameter replaces
      `authorization_code` which is no longer used in any data sources. This
      is required only if `transferConfig.dataSourceId` is 'youtube_channel'
      *or* new credentials are needed, as indicated by `CheckValidCreds`. In
      order to obtain version info, make a request to the following URL: https
      ://bigquery.cloud.google.com/datatransfer/oauthz/auth?redirect_uri=urn:i
      etf:wg:oauth:2.0:oob&response_type=version_info&client_id=client_id&scop
      e=data_source_scopes * The client_id is the OAuth client_id of the data
      source as returned by ListDataSources method. * data_source_scopes are
      the scopes returned by ListDataSources method. Note that this should not
      be set when `service_account_name` is used to update the transfer
      config.
  """

  authorizationCode = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  serviceAccountName = _messages.StringField(3)
  transferConfig = _messages.MessageField('TransferConfig', 4)
  updateMask = _messages.StringField(5)
  versionInfo = _messages.StringField(6)


class BigquerydatatransferProjectsTransferConfigsRunsDeleteRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsRunsDeleteRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsTransferConfigsRunsGetRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsRunsGetRequest object.

  Fields:
    name: Required. The name of the resource requested. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  name = _messages.StringField(1, required=True)


class BigquerydatatransferProjectsTransferConfigsRunsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsRunsListRequest object.

  Enums:
    RunAttemptValueValuesEnum: Indicates how run attempts are to be pulled.
    StatesValueValuesEnum: When specified, only transfer runs with requested
      states are returned.

  Fields:
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransferRunsRequest` list results. For multiple-page results,
      `ListTransferRunsResponse` outputs a `next_page` token, which can be
      used as the `page_token` value to request the next page of list results.
    parent: Required. Name of transfer configuration for which transfer runs
      should be retrieved. If you are using the regionless method, the
      location must be `US` and the name should be in the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    runAttempt: Indicates how run attempts are to be pulled.
    states: When specified, only transfer runs with requested states are
      returned.
  """

  class RunAttemptValueValuesEnum(_messages.Enum):
    r"""Indicates how run attempts are to be pulled.

    Values:
      RUN_ATTEMPT_UNSPECIFIED: All runs should be returned.
      LATEST: Only latest run per day should be returned.
    """
    RUN_ATTEMPT_UNSPECIFIED = 0
    LATEST = 1

  class StatesValueValuesEnum(_messages.Enum):
    r"""When specified, only transfer runs with requested states are returned.

    Values:
      TRANSFER_STATE_UNSPECIFIED: State placeholder (0).
      PENDING: Data transfer is scheduled and is waiting to be picked up by
        data transfer backend (2).
      RUNNING: Data transfer is in progress (3).
      SUCCEEDED: Data transfer completed successfully (4).
      FAILED: Data transfer failed (5).
      CANCELLED: Data transfer is cancelled (6).
    """
    TRANSFER_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  runAttempt = _messages.EnumField('RunAttemptValueValuesEnum', 4)
  states = _messages.EnumField('StatesValueValuesEnum', 5, repeated=True)


class BigquerydatatransferProjectsTransferConfigsRunsTransferLogsListRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsRunsTransferLogsListRequest
  object.

  Enums:
    MessageTypesValueValuesEnum: Message types to return. If not populated -
      INFO, WARNING and ERROR messages are returned.

  Fields:
    messageTypes: Message types to return. If not populated - INFO, WARNING
      and ERROR messages are returned.
    pageSize: Page size. The default page size is the maximum value of 1000
      results.
    pageToken: Pagination token, which can be used to request a specific page
      of `ListTransferLogsRequest` list results. For multiple-page results,
      `ListTransferLogsResponse` outputs a `next_page` token, which can be
      used as the `page_token` value to request the next page of list results.
    parent: Required. Transfer run name. If you are using the regionless
      method, the location must be `US` and the name should be in the
      following form: *
      `projects/{project_id}/transferConfigs/{config_id}/runs/{run_id}` If you
      are using the regionalized method, the name should be in the following
      form: * `projects/{project_id}/locations/{location_id}/transferConfigs/{
      config_id}/runs/{run_id}`
  """

  class MessageTypesValueValuesEnum(_messages.Enum):
    r"""Message types to return. If not populated - INFO, WARNING and ERROR
    messages are returned.

    Values:
      MESSAGE_SEVERITY_UNSPECIFIED: No severity specified.
      INFO: Informational message.
      WARNING: Warning message.
      ERROR: Error message.
    """
    MESSAGE_SEVERITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

  messageTypes = _messages.EnumField('MessageTypesValueValuesEnum', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BigquerydatatransferProjectsTransferConfigsScheduleRunsRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsScheduleRunsRequest object.

  Fields:
    parent: Required. Transfer configuration name. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    scheduleTransferRunsRequest: A ScheduleTransferRunsRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  scheduleTransferRunsRequest = _messages.MessageField('ScheduleTransferRunsRequest', 2)


class BigquerydatatransferProjectsTransferConfigsStartManualRunsRequest(_messages.Message):
  r"""A BigquerydatatransferProjectsTransferConfigsStartManualRunsRequest
  object.

  Fields:
    parent: Required. Transfer configuration name. If you are using the
      regionless method, the location must be `US` and the name should be in
      the following form: *
      `projects/{project_id}/transferConfigs/{config_id}` If you are using the
      regionalized method, the name should be in the following form: * `projec
      ts/{project_id}/locations/{location_id}/transferConfigs/{config_id}`
    startManualTransferRunsRequest: A StartManualTransferRunsRequest resource
      to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  startManualTransferRunsRequest = _messages.MessageField('StartManualTransferRunsRequest', 2)


class CheckValidCredsRequest(_messages.Message):
  r"""A request to determine whether the user has valid credentials. This
  method is used to limit the number of OAuth popups in the user interface.
  The user id is inferred from the API call context. If the data source has
  the Google+ authorization type, this method returns false, as it cannot be
  determined whether the credentials are already valid merely based on the
  user id.
  """



class CheckValidCredsResponse(_messages.Message):
  r"""A response indicating whether the credentials exist and are valid.

  Fields:
    hasValidCreds: If set to `true`, the credentials exist and are valid.
  """

  hasValidCreds = _messages.BooleanField(1)


class DataSource(_messages.Message):
  r"""Defines the properties and custom parameters for a data source.

  Enums:
    AuthorizationTypeValueValuesEnum: Indicates the type of authorization.
    DataRefreshTypeValueValuesEnum: Specifies whether the data source supports
      automatic data refresh for the past few days, and how it's supported.
      For some data sources, data might not be complete until a few days
      later, so it's useful to refresh data automatically.
    TransferTypeValueValuesEnum: Deprecated. This field has no effect.

  Fields:
    authorizationType: Indicates the type of authorization.
    clientId: Data source client id which should be used to receive refresh
      token.
    dataRefreshType: Specifies whether the data source supports automatic data
      refresh for the past few days, and how it's supported. For some data
      sources, data might not be complete until a few days later, so it's
      useful to refresh data automatically.
    dataSourceId: Data source id.
    defaultDataRefreshWindowDays: Default data refresh window on days. Only
      meaningful when `data_refresh_type` = `SLIDING_WINDOW`.
    defaultSchedule: Default data transfer schedule. Examples of valid
      schedules include: `1st,3rd monday of month 15:30`, `every wed,fri of
      jan,jun 13:15`, and `first sunday of quarter 00:00`.
    description: User friendly data source description string.
    displayName: User friendly data source name.
    helpUrl: Url for the help document for this data source.
    manualRunsDisabled: Disables backfilling and manual run scheduling for the
      data source.
    minimumScheduleInterval: The minimum interval for scheduler to schedule
      runs.
    name: Output only. Data source resource name.
    parameters: Data source parameters.
    scopes: Api auth scopes for which refresh token needs to be obtained.
      These are scopes needed by a data source to prepare data and ingest them
      into BigQuery, e.g., https://www.googleapis.com/auth/bigquery
    supportsCustomSchedule: Specifies whether the data source supports a user
      defined schedule, or operates on the default schedule. When set to
      `true`, user can override default schedule.
    supportsMultipleTransfers: Deprecated. This field has no effect.
    transferType: Deprecated. This field has no effect.
    updateDeadlineSeconds: The number of seconds to wait for an update from
      the data source before the Data Transfer Service marks the transfer as
      FAILED.
  """

  class AuthorizationTypeValueValuesEnum(_messages.Enum):
    r"""Indicates the type of authorization.

    Values:
      AUTHORIZATION_TYPE_UNSPECIFIED: Type unspecified.
      AUTHORIZATION_CODE: Use OAuth 2 authorization codes that can be
        exchanged for a refresh token on the backend.
      GOOGLE_PLUS_AUTHORIZATION_CODE: Return an authorization code for a given
        Google+ page that can then be exchanged for a refresh token on the
        backend.
      FIRST_PARTY_OAUTH: Use First Party OAuth.
    """
    AUTHORIZATION_TYPE_UNSPECIFIED = 0
    AUTHORIZATION_CODE = 1
    GOOGLE_PLUS_AUTHORIZATION_CODE = 2
    FIRST_PARTY_OAUTH = 3

  class DataRefreshTypeValueValuesEnum(_messages.Enum):
    r"""Specifies whether the data source supports automatic data refresh for
    the past few days, and how it's supported. For some data sources, data
    might not be complete until a few days later, so it's useful to refresh
    data automatically.

    Values:
      DATA_REFRESH_TYPE_UNSPECIFIED: The data source won't support data auto
        refresh, which is default value.
      SLIDING_WINDOW: The data source supports data auto refresh, and runs
        will be scheduled for the past few days. Does not allow custom values
        to be set for each transfer config.
      CUSTOM_SLIDING_WINDOW: The data source supports data auto refresh, and
        runs will be scheduled for the past few days. Allows custom values to
        be set for each transfer config.
    """
    DATA_REFRESH_TYPE_UNSPECIFIED = 0
    SLIDING_WINDOW = 1
    CUSTOM_SLIDING_WINDOW = 2

  class TransferTypeValueValuesEnum(_messages.Enum):
    r"""Deprecated. This field has no effect.

    Values:
      TRANSFER_TYPE_UNSPECIFIED: Invalid or Unknown transfer type placeholder.
      BATCH: Batch data transfer.
      STREAMING: Streaming data transfer. Streaming data source currently
        doesn't support multiple transfer configs per project.
    """
    TRANSFER_TYPE_UNSPECIFIED = 0
    BATCH = 1
    STREAMING = 2

  authorizationType = _messages.EnumField('AuthorizationTypeValueValuesEnum', 1)
  clientId = _messages.StringField(2)
  dataRefreshType = _messages.EnumField('DataRefreshTypeValueValuesEnum', 3)
  dataSourceId = _messages.StringField(4)
  defaultDataRefreshWindowDays = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  defaultSchedule = _messages.StringField(6)
  description = _messages.StringField(7)
  displayName = _messages.StringField(8)
  helpUrl = _messages.StringField(9)
  manualRunsDisabled = _messages.BooleanField(10)
  minimumScheduleInterval = _messages.StringField(11)
  name = _messages.StringField(12)
  parameters = _messages.MessageField('DataSourceParameter', 13, repeated=True)
  scopes = _messages.StringField(14, repeated=True)
  supportsCustomSchedule = _messages.BooleanField(15)
  supportsMultipleTransfers = _messages.BooleanField(16)
  transferType = _messages.EnumField('TransferTypeValueValuesEnum', 17)
  updateDeadlineSeconds = _messages.IntegerField(18, variant=_messages.Variant.INT32)


class DataSourceParameter(_messages.Message):
  r"""A parameter used to define custom fields in a data source definition.

  Enums:
    TypeValueValuesEnum: Parameter type.

  Fields:
    allowedValues: All possible values for the parameter.
    deprecated: If true, it should not be used in new transfers, and it should
      not be visible to users.
    description: Parameter description.
    displayName: Parameter display name in the user interface.
    fields: Deprecated. This field has no effect.
    immutable: Cannot be changed after initial creation.
    maxValue: For integer and double values specifies maximum allowed value.
    minValue: For integer and double values specifies minimum allowed value.
    paramId: Parameter identifier.
    recurse: Deprecated. This field has no effect.
    repeated: Deprecated. This field has no effect.
    required: Is parameter required.
    type: Parameter type.
    validationDescription: Description of the requirements for this field, in
      case the user input does not fulfill the regex pattern or min/max
      values.
    validationHelpUrl: URL to a help document to further explain the naming
      requirements.
    validationRegex: Regular expression which can be used for parameter
      validation.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Parameter type.

    Values:
      TYPE_UNSPECIFIED: Type unspecified.
      STRING: String parameter.
      INTEGER: Integer parameter (64-bits). Will be serialized to json as
        string.
      DOUBLE: Double precision floating point parameter.
      BOOLEAN: Boolean parameter.
      RECORD: Deprecated. This field has no effect.
      PLUS_PAGE: Page ID for a Google+ Page.
      LIST: List of strings parameter.
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    INTEGER = 2
    DOUBLE = 3
    BOOLEAN = 4
    RECORD = 5
    PLUS_PAGE = 6
    LIST = 7

  allowedValues = _messages.StringField(1, repeated=True)
  deprecated = _messages.BooleanField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  fields = _messages.MessageField('DataSourceParameter', 5, repeated=True)
  immutable = _messages.BooleanField(6)
  maxValue = _messages.FloatField(7)
  minValue = _messages.FloatField(8)
  paramId = _messages.StringField(9)
  recurse = _messages.BooleanField(10)
  repeated = _messages.BooleanField(11)
  required = _messages.BooleanField(12)
  type = _messages.EnumField('TypeValueValuesEnum', 13)
  validationDescription = _messages.StringField(14)
  validationHelpUrl = _messages.StringField(15)
  validationRegex = _messages.StringField(16)


class EmailPreferences(_messages.Message):
  r"""Represents preferences for sending email notifications for transfer run
  events.

  Fields:
    enableFailureEmail: If true, email notifications will be sent on transfer
      run failures.
  """

  enableFailureEmail = _messages.BooleanField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfiguration(_messages.Message):
  r"""Represents the encryption configuration for a transfer.

  Fields:
    kmsKeyName: The name of the KMS key used for encrypting BigQuery data.
  """

  kmsKeyName = _messages.StringField(1)


class EnrollDataSourcesRequest(_messages.Message):
  r"""A request to enroll a set of data sources so they are visible in the
  BigQuery UI's `Transfer` tab.

  Fields:
    dataSourceIds: Data sources that are enrolled. It is required to provide
      at least one data source id.
  """

  dataSourceIds = _messages.StringField(1, repeated=True)


class EventDrivenSchedule(_messages.Message):
  r"""Options customizing EventDriven transfers schedule.

  Fields:
    pubsubSubscription: Pub/Sub subscription name used to receive events. Only
      Google Cloud Storage data source support this option. Format:
      projects/{project}/subscriptions/{subscription}
  """

  pubsubSubscription = _messages.StringField(1)


class ListDataSourcesResponse(_messages.Message):
  r"""Returns list of supported data sources and their metadata.

  Fields:
    dataSources: List of supported data sources and their transfer settings.
    nextPageToken: Output only. The next-pagination token. For multiple-page
      list results, this token can be used as the
      `ListDataSourcesRequest.page_token` to request the next page of list
      results.
  """

  dataSources = _messages.MessageField('DataSource', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListTransferConfigsResponse(_messages.Message):
  r"""The returned list of pipelines in the project.

  Fields:
    nextPageToken: Output only. The next-pagination token. For multiple-page
      list results, this token can be used as the
      `ListTransferConfigsRequest.page_token` to request the next page of list
      results.
    transferConfigs: Output only. The stored pipeline transfer configurations.
  """

  nextPageToken = _messages.StringField(1)
  transferConfigs = _messages.MessageField('TransferConfig', 2, repeated=True)


class ListTransferLogsResponse(_messages.Message):
  r"""The returned list transfer run messages.

  Fields:
    nextPageToken: Output only. The next-pagination token. For multiple-page
      list results, this token can be used as the
      `GetTransferRunLogRequest.page_token` to request the next page of list
      results.
    transferMessages: Output only. The stored pipeline transfer messages.
  """

  nextPageToken = _messages.StringField(1)
  transferMessages = _messages.MessageField('TransferMessage', 2, repeated=True)


class ListTransferRunsResponse(_messages.Message):
  r"""The returned list of pipelines in the project.

  Fields:
    nextPageToken: Output only. The next-pagination token. For multiple-page
      list results, this token can be used as the
      `ListTransferRunsRequest.page_token` to request the next page of list
      results.
    transferRuns: Output only. The stored pipeline transfer runs.
  """

  nextPageToken = _messages.StringField(1)
  transferRuns = _messages.MessageField('TransferRun', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManualSchedule(_messages.Message):
  r"""Options customizing manual transfers schedule."""


class ScheduleOptions(_messages.Message):
  r"""Options customizing the data transfer schedule.

  Fields:
    disableAutoScheduling: If true, automatic scheduling of data transfer runs
      for this configuration will be disabled. The runs can be started on ad-
      hoc basis using StartManualTransferRuns API. When automatic scheduling
      is disabled, the TransferConfig.schedule field will be ignored.
    endTime: Defines time to stop scheduling transfer runs. A transfer run
      cannot be scheduled at or after the end time. The end time can be
      changed at any moment. The time when a data transfer can be triggered
      manually is not limited by this option.
    startTime: Specifies time to start scheduling transfer runs. The first run
      will be scheduled at or after the start time according to a recurrence
      pattern defined in the schedule string. The start time can be changed at
      any moment. The time when a data transfer can be triggered manually is
      not limited by this option.
  """

  disableAutoScheduling = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  startTime = _messages.StringField(3)


class ScheduleOptionsV2(_messages.Message):
  r"""V2 options customizing different types of data transfer schedule. This
  field supports existing time-based and manual transfer schedule. Also
  supports Event-Driven transfer schedule. ScheduleOptionsV2 cannot be used
  together with ScheduleOptions/Schedule.

  Fields:
    eventDrivenSchedule: Event driven transfer schedule options. If set, the
      transfer will be scheduled upon events arrial.
    manualSchedule: Manual transfer schedule. If set, the transfer run will
      not be auto-scheduled by the system, unless the client invokes
      StartManualTransferRuns. This is equivalent to disable_auto_scheduling =
      true.
    timeBasedSchedule: Time based transfer schedule options. This is the
      default schedule option.
  """

  eventDrivenSchedule = _messages.MessageField('EventDrivenSchedule', 1)
  manualSchedule = _messages.MessageField('ManualSchedule', 2)
  timeBasedSchedule = _messages.MessageField('TimeBasedSchedule', 3)


class ScheduleTransferRunsRequest(_messages.Message):
  r"""A request to schedule transfer runs for a time range.

  Fields:
    endTime: Required. End time of the range of transfer runs. For example,
      `"2017-05-30T00:00:00+00:00"`.
    startTime: Required. Start time of the range of transfer runs. For
      example, `"2017-05-25T00:00:00+00:00"`.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ScheduleTransferRunsResponse(_messages.Message):
  r"""A response to schedule transfer runs for a time range.

  Fields:
    runs: The transfer runs that were scheduled.
  """

  runs = _messages.MessageField('TransferRun', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartManualTransferRunsRequest(_messages.Message):
  r"""A request to start manual transfer runs.

  Fields:
    requestedRunTime: A run_time timestamp for historical data files or
      reports that are scheduled to be transferred by the scheduled transfer
      run. requested_run_time must be a past time and cannot include future
      time values.
    requestedTimeRange: A time_range start and end timestamp for historical
      data files or reports that are scheduled to be transferred by the
      scheduled transfer run. requested_time_range must be a past time and
      cannot include future time values.
  """

  requestedRunTime = _messages.StringField(1)
  requestedTimeRange = _messages.MessageField('TimeRange', 2)


class StartManualTransferRunsResponse(_messages.Message):
  r"""A response to start manual transfer runs.

  Fields:
    runs: The transfer runs that were created.
  """

  runs = _messages.MessageField('TransferRun', 1, repeated=True)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TimeBasedSchedule(_messages.Message):
  r"""Options customizing the time based transfer schedule. Options are
  migrated from the original ScheduleOptions message.

  Fields:
    endTime: Defines time to stop scheduling transfer runs. A transfer run
      cannot be scheduled at or after the end time. The end time can be
      changed at any moment.
    schedule: Data transfer schedule. If the data source does not support a
      custom schedule, this should be empty. If it is empty, the default value
      for the data source will be used. The specified times are in UTC.
      Examples of valid format: `1st,3rd monday of month 15:30`, `every
      wed,fri of jan,jun 13:15`, and `first sunday of quarter 00:00`. See more
      explanation about the format here:
      https://cloud.google.com/appengine/docs/flexible/python/scheduling-jobs-
      with-cron-yaml#the_schedule_format NOTE: The minimum interval time
      between recurring transfers depends on the data source; refer to the
      documentation for your data source.
    startTime: Specifies time to start scheduling transfer runs. The first run
      will be scheduled at or after the start time according to a recurrence
      pattern defined in the schedule string. The start time can be changed at
      any moment.
  """

  endTime = _messages.StringField(1)
  schedule = _messages.StringField(2)
  startTime = _messages.StringField(3)


class TimeRange(_messages.Message):
  r"""A specification for a time range, this will request transfer runs with
  run_time between start_time (inclusive) and end_time (exclusive).

  Fields:
    endTime: End time of the range of transfer runs. For example,
      `"2017-05-30T00:00:00+00:00"`. The end_time must not be in the future.
      Creates transfer runs where run_time is in the range between start_time
      (inclusive) and end_time (exclusive).
    startTime: Start time of the range of transfer runs. For example,
      `"2017-05-25T00:00:00+00:00"`. The start_time must be strictly less than
      the end_time. Creates transfer runs where run_time is in the range
      between start_time (inclusive) and end_time (exclusive).
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class TransferConfig(_messages.Message):
  r"""Represents a data transfer configuration. A transfer configuration
  contains all metadata needed to perform a data transfer. For example,
  `destination_dataset_id` specifies where data should be stored. When a new
  transfer configuration is created, the specified `destination_dataset_id` is
  created when needed and shared with the appropriate data source service
  account.

  Enums:
    StateValueValuesEnum: Output only. State of the most recently updated
      transfer run.

  Messages:
    ParamsValue: Parameters specific to each data source. For more information
      see the bq tab in the 'Setting up a data transfer' section for each data
      source. For example the parameters for Cloud Storage transfers are
      listed here: https://cloud.google.com/bigquery-transfer/docs/cloud-
      storage-transfer#bq

  Fields:
    dataRefreshWindowDays: The number of days to look back to automatically
      refresh the data. For example, if `data_refresh_window_days = 10`, then
      every day BigQuery reingests data for [today-10, today-1], rather than
      ingesting data for just [today-1]. Only valid if the data source
      supports the feature. Set the value to 0 to use the default value.
    dataSourceId: Data source ID. This cannot be changed once data transfer is
      created. The full list of available data source IDs can be returned
      through an API call: https://cloud.google.com/bigquery-transfer/docs/ref
      erence/datatransfer/rest/v1/projects.locations.dataSources/list
    datasetRegion: Output only. Region in which BigQuery dataset is located.
    destinationDatasetId: The BigQuery target dataset id.
    disabled: Is this config disabled. When set to true, no runs will be
      scheduled for this transfer config.
    displayName: User specified display name for the data transfer.
    emailPreferences: Email notifications will be sent according to these
      preferences to the email address of the user who owns this transfer
      config.
    encryptionConfiguration: The encryption configuration part. Currently, it
      is only used for the optional KMS key name. The BigQuery service account
      of your project must be granted permissions to use the key. Read methods
      will return the key name applied in effect. Write methods will apply the
      key if it is present, or otherwise try to apply project default keys if
      it is absent.
    error: Output only. Error code with detailed information about reason of
      the latest config failure.
    name: Identifier. The resource name of the transfer config. Transfer
      config names have the form either
      `projects/{project_id}/locations/{region}/transferConfigs/{config_id}`
      or `projects/{project_id}/transferConfigs/{config_id}`, where
      `config_id` is usually a UUID, even though it is not guaranteed or
      required. The name is ignored when creating a transfer config.
    nextRunTime: Output only. Next time when data transfer will run.
    notificationPubsubTopic: Pub/Sub topic where notifications will be sent
      after transfer runs associated with this transfer config finish. The
      format for specifying a pubsub topic is:
      `projects/{project_id}/topics/{topic_id}`
    ownerInfo: Output only. Information about the user whose credentials are
      used to transfer data. Populated only for `transferConfigs.get`
      requests. In case the user information is not available, this field will
      not be populated.
    params: Parameters specific to each data source. For more information see
      the bq tab in the 'Setting up a data transfer' section for each data
      source. For example the parameters for Cloud Storage transfers are
      listed here: https://cloud.google.com/bigquery-transfer/docs/cloud-
      storage-transfer#bq
    schedule: Data transfer schedule. If the data source does not support a
      custom schedule, this should be empty. If it is empty, the default value
      for the data source will be used. The specified times are in UTC.
      Examples of valid format: `1st,3rd monday of month 15:30`, `every
      wed,fri of jan,jun 13:15`, and `first sunday of quarter 00:00`. See more
      explanation about the format here:
      https://cloud.google.com/appengine/docs/flexible/python/scheduling-jobs-
      with-cron-yaml#the_schedule_format NOTE: The minimum interval time
      between recurring transfers depends on the data source; refer to the
      documentation for your data source.
    scheduleOptions: Options customizing the data transfer schedule.
    scheduleOptionsV2: Options customizing different types of data transfer
      schedule. This field replaces "schedule" and "schedule_options" fields.
      ScheduleOptionsV2 cannot be used together with ScheduleOptions/Schedule.
    state: Output only. State of the most recently updated transfer run.
    updateTime: Output only. Data transfer modification time. Ignored by
      server on input.
    userId: Deprecated. Unique ID of the user on whose behalf transfer is
      done.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the most recently updated transfer run.

    Values:
      TRANSFER_STATE_UNSPECIFIED: State placeholder (0).
      PENDING: Data transfer is scheduled and is waiting to be picked up by
        data transfer backend (2).
      RUNNING: Data transfer is in progress (3).
      SUCCEEDED: Data transfer completed successfully (4).
      FAILED: Data transfer failed (5).
      CANCELLED: Data transfer is cancelled (6).
    """
    TRANSFER_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Parameters specific to each data source. For more information see the
    bq tab in the 'Setting up a data transfer' section for each data source.
    For example the parameters for Cloud Storage transfers are listed here:
    https://cloud.google.com/bigquery-transfer/docs/cloud-storage-transfer#bq

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dataRefreshWindowDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  dataSourceId = _messages.StringField(2)
  datasetRegion = _messages.StringField(3)
  destinationDatasetId = _messages.StringField(4)
  disabled = _messages.BooleanField(5)
  displayName = _messages.StringField(6)
  emailPreferences = _messages.MessageField('EmailPreferences', 7)
  encryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 8)
  error = _messages.MessageField('Status', 9)
  name = _messages.StringField(10)
  nextRunTime = _messages.StringField(11)
  notificationPubsubTopic = _messages.StringField(12)
  ownerInfo = _messages.MessageField('UserInfo', 13)
  params = _messages.MessageField('ParamsValue', 14)
  schedule = _messages.StringField(15)
  scheduleOptions = _messages.MessageField('ScheduleOptions', 16)
  scheduleOptionsV2 = _messages.MessageField('ScheduleOptionsV2', 17)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  updateTime = _messages.StringField(19)
  userId = _messages.IntegerField(20)


class TransferMessage(_messages.Message):
  r"""Represents a user facing message for a particular data transfer run.

  Enums:
    SeverityValueValuesEnum: Message severity.

  Fields:
    messageText: Message text.
    messageTime: Time when message was logged.
    severity: Message severity.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Message severity.

    Values:
      MESSAGE_SEVERITY_UNSPECIFIED: No severity specified.
      INFO: Informational message.
      WARNING: Warning message.
      ERROR: Error message.
    """
    MESSAGE_SEVERITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

  messageText = _messages.StringField(1)
  messageTime = _messages.StringField(2)
  severity = _messages.EnumField('SeverityValueValuesEnum', 3)


class TransferRun(_messages.Message):
  r"""Represents a data transfer run.

  Enums:
    StateValueValuesEnum: Data transfer run state. Ignored for input requests.

  Messages:
    ParamsValue: Output only. Parameters specific to each data source. For
      more information see the bq tab in the 'Setting up a data transfer'
      section for each data source. For example the parameters for Cloud
      Storage transfers are listed here: https://cloud.google.com/bigquery-
      transfer/docs/cloud-storage-transfer#bq

  Fields:
    dataSourceId: Output only. Data source id.
    destinationDatasetId: Output only. The BigQuery target dataset id.
    emailPreferences: Output only. Email notifications will be sent according
      to these preferences to the email address of the user who owns the
      transfer config this run was derived from.
    endTime: Output only. Time when transfer run ended. Parameter ignored by
      server for input requests.
    errorStatus: Status of the transfer run.
    name: Identifier. The resource name of the transfer run. Transfer run
      names have the form `projects/{project_id}/locations/{location}/transfer
      Configs/{config_id}/runs/{run_id}`. The name is ignored when creating a
      transfer run.
    notificationPubsubTopic: Output only. Pub/Sub topic where a notification
      will be sent after this transfer run finishes. The format for specifying
      a pubsub topic is: `projects/{project_id}/topics/{topic_id}`
    params: Output only. Parameters specific to each data source. For more
      information see the bq tab in the 'Setting up a data transfer' section
      for each data source. For example the parameters for Cloud Storage
      transfers are listed here: https://cloud.google.com/bigquery-
      transfer/docs/cloud-storage-transfer#bq
    runTime: For batch transfer runs, specifies the date and time of the data
      should be ingested.
    schedule: Output only. Describes the schedule of this transfer run if it
      was created as part of a regular schedule. For batch transfer runs that
      are scheduled manually, this is empty. NOTE: the system might choose to
      delay the schedule depending on the current load, so `schedule_time`
      doesn't always match this.
    scheduleTime: Minimum time after which a transfer run can be started.
    startTime: Output only. Time when transfer run was started. Parameter
      ignored by server for input requests.
    state: Data transfer run state. Ignored for input requests.
    updateTime: Output only. Last time the data transfer run state was
      updated.
    userId: Deprecated. Unique ID of the user on whose behalf transfer is
      done.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Data transfer run state. Ignored for input requests.

    Values:
      TRANSFER_STATE_UNSPECIFIED: State placeholder (0).
      PENDING: Data transfer is scheduled and is waiting to be picked up by
        data transfer backend (2).
      RUNNING: Data transfer is in progress (3).
      SUCCEEDED: Data transfer completed successfully (4).
      FAILED: Data transfer failed (5).
      CANCELLED: Data transfer is cancelled (6).
    """
    TRANSFER_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Output only. Parameters specific to each data source. For more
    information see the bq tab in the 'Setting up a data transfer' section for
    each data source. For example the parameters for Cloud Storage transfers
    are listed here: https://cloud.google.com/bigquery-transfer/docs/cloud-
    storage-transfer#bq

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dataSourceId = _messages.StringField(1)
  destinationDatasetId = _messages.StringField(2)
  emailPreferences = _messages.MessageField('EmailPreferences', 3)
  endTime = _messages.StringField(4)
  errorStatus = _messages.MessageField('Status', 5)
  name = _messages.StringField(6)
  notificationPubsubTopic = _messages.StringField(7)
  params = _messages.MessageField('ParamsValue', 8)
  runTime = _messages.StringField(9)
  schedule = _messages.StringField(10)
  scheduleTime = _messages.StringField(11)
  startTime = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  updateTime = _messages.StringField(14)
  userId = _messages.IntegerField(15)


class UnenrollDataSourcesRequest(_messages.Message):
  r"""A request to unenroll a set of data sources so they are no longer
  visible in the BigQuery UI's `Transfer` tab.

  Fields:
    dataSourceIds: Data sources that are unenrolled. It is required to provide
      at least one data source id.
  """

  dataSourceIds = _messages.StringField(1, repeated=True)


class UserInfo(_messages.Message):
  r"""Information about a user.

  Fields:
    email: E-mail address of the user.
  """

  email = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
