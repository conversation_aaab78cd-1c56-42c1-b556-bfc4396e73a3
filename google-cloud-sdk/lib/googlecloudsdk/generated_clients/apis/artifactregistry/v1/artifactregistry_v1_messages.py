"""Generated message classes for artifactregistry version v1.

Store and manage build artifacts in a scalable and integrated service built on
Google infrastructure.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'artifactregistry'


class AptArtifact(_messages.Message):
  r"""A detailed representation of an Apt artifact. Information in the record
  is derived from the archive's control file. See
  https://www.debian.org/doc/debian-policy/ch-controlfields.html

  Enums:
    PackageTypeValueValuesEnum: Output only. An artifact is a binary or source
      package.

  Fields:
    architecture: Output only. Operating system architecture of the artifact.
    component: Output only. Repository component of the artifact.
    controlFile: Output only. Contents of the artifact's control metadata
      file.
    name: Output only. The Artifact Registry resource name of the artifact.
    packageName: Output only. The Apt package name of the artifact.
    packageType: Output only. An artifact is a binary or source package.
  """

  class PackageTypeValueValuesEnum(_messages.Enum):
    r"""Output only. An artifact is a binary or source package.

    Values:
      PACKAGE_TYPE_UNSPECIFIED: Package type is not specified.
      BINARY: Binary package.
      SOURCE: Source package.
    """
    PACKAGE_TYPE_UNSPECIFIED = 0
    BINARY = 1
    SOURCE = 2

  architecture = _messages.StringField(1)
  component = _messages.StringField(2)
  controlFile = _messages.BytesField(3)
  name = _messages.StringField(4)
  packageName = _messages.StringField(5)
  packageType = _messages.EnumField('PackageTypeValueValuesEnum', 6)


class AptRepository(_messages.Message):
  r"""Configuration for an Apt remote repository.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Apt repositories supported
      by Artifact Registry.
  """

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryCustomRepository', 2)
  publicRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryPublicRepository', 3)


class ArtifactregistryProjectsGetProjectSettingsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsGetProjectSettingsRequest object.

  Fields:
    name: Required. The name of the projectSettings resource.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsGetPlatformLogsConfigRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsGetPlatformLogsConfigRequest object.

  Fields:
    name: Required. The name of the platform logs config resource:
      projects/{project}/locations/{location}/platformLogsConfig projects/{pro
      ject}/locations/{location}/repositories/{repository}/platformLogsConfig
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsGetVpcscConfigRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsGetVpcscConfigRequest object.

  Fields:
    name: Required. The name of the VPCSCConfig resource.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ArtifactregistryProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest
  object.

  Fields:
    importAptArtifactsRequest: A ImportAptArtifactsRequest resource to be
      passed as the request body.
    parent: The name of the parent resource where the artifacts will be
      imported.
  """

  importAptArtifactsRequest = _messages.MessageField('ImportAptArtifactsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest
  object.

  Fields:
    parent: The name of the parent resource where the artifacts will be
      uploaded.
    uploadAptArtifactRequest: A UploadAptArtifactRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadAptArtifactRequest = _messages.MessageField('UploadAptArtifactRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesAttachmentsCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAttachmentsCreateRequest
  object.

  Fields:
    attachment: A Attachment resource to be passed as the request body.
    attachmentId: Required. The attachment id to use for this attachment.
    parent: Required. The name of the parent resource where the attachment
      will be created.
  """

  attachment = _messages.MessageField('Attachment', 1)
  attachmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAttachmentsDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAttachmentsDeleteRequest
  object.

  Fields:
    name: Required. The name of the attachment to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAttachmentsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAttachmentsGetRequest
  object.

  Fields:
    name: Required. The name of the attachment to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAttachmentsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAttachmentsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. The fields eligible for filtering
      are: * `target` * `type` * `attachment_namespace`
    pageSize: The maximum number of attachments to return. Maximum page size
      is 1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose attachments will
      be listed.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource where the repository
      will be created.
    repository: A Repository resource to be passed as the request body.
    repositoryId: Required. The repository id to use for this repository.
  """

  parent = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesDeleteRequest object.

  Fields:
    name: Required. The name of the repository to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesDockerImagesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesDockerImagesGetRequest
  object.

  Fields:
    name: Required. The name of the docker images.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesDockerImagesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesDockerImagesListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. The fields eligible for filtering
      are: * `name` Examples of using a filter: *
      `name="projects/p1/locations/us-
      central1/repositories/repo1/dockerImages/nginx@sha:12*"` --> images with
      an package ID equals to "nginx" and version ID starting with "sha:12" .
      * `name="projects/p1/locations/us-
      central1/repositories/repo1/dockerImages/*234"` --> images with an
      Version ID ending with "234". * `name="projects/p1/locations/us-
      central1/repositories/repo1/dockerImages/*inx@sha:12*"` --> images with
      an ID containing "inx@sha:12". * `name="projects/p1/locations/us-
      central1/repositories/repo1/dockerImages/*fff*"` --> images with an ID
      containing "fff", it could either be package ID or version ID that
      contains "fff".
    orderBy: The field to order the results by.
    pageSize: The maximum number of artifacts to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose docker images will
      be listed.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesDeleteRequest
  object.

  Fields:
    name: Required. The name of the file to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest
  object.

  Fields:
    name: Required. The name of the file to download.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest object.

  Fields:
    name: Required. The name of the file to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesListRequest object.

  Fields:
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `name` * `owner` * `annotations` Examples of using a filter: To filter
      the results of your request to files with the name `my_file.txt` in
      project `my-project` in the `us-central` region, in repository `my-
      repo`, append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/files/my-file.txt"` You can also use wildcards to match any number
      of characters before or after the value: * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/files/my-*"` *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/files/*file.txt"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/files/*file*"` To filter the results of
      your request to files owned by the version `1.0` in package `pkg1`,
      append the following filter expression to your request: *
      `owner="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/versions/1.0"` To filter the results of your
      request to files with the annotation key-value pair [`external_link`:
      `external_link_value`], append the following filter expression to your
      request: * `"annotations.external_link:external_link_value"` To filter
      just for a specific annotation key `external_link`, append the following
      filter expression to your request: * `"annotations.external_link"` If
      the annotation key or value contains special characters, you can escape
      them by surrounding the value with backticks. For example, to filter the
      results of your request to files with the annotation key-value pair
      [`external.link`:`https://example.com/my-file`], append the following
      filter expression to your request: * ``
      "annotations.`external.link`:`https://example.com/my-file`" `` You can
      also filter with annotations with a wildcard to match any number of
      characters before or after the value: * ``
      "annotations.*_link:`*example.com*`" ``
    orderBy: The field to order the results by.
    pageSize: The maximum number of files to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the repository whose files will be listed.
      For example: "projects/p1/locations/us-central1/repositories/repo1
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesPatchRequest object.

  Fields:
    googleDevtoolsArtifactregistryV1File: A
      GoogleDevtoolsArtifactregistryV1File resource to be passed as the
      request body.
    name: The name of the file, for example: `projects/p1/locations/us-
      central1/repositories/repo1/files/a%2Fb%2Fc.txt`. If the file ID part
      contains slashes, they are escaped.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  googleDevtoolsArtifactregistryV1File = _messages.MessageField('GoogleDevtoolsArtifactregistryV1File', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesFilesUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesUploadRequest
  object.

  Fields:
    parent: Required. The resource name of the repository where the file will
      be uploaded.
    uploadFileRequest: A UploadFileRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadFileRequest = _messages.MessageField('UploadFileRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesGenericArtifactsUploadRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesGenericArtifactsUploadRequest
  object.

  Fields:
    parent: The resource name of the repository where the generic artifact
      will be uploaded.
    uploadGenericArtifactRequest: A UploadGenericArtifactRequest resource to
      be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadGenericArtifactRequest = _messages.MessageField('UploadGenericArtifactRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGetPlatformLogsConfigRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesGetPlatformLogsConfigRequest
  object.

  Fields:
    name: Required. The name of the platform logs config resource:
      projects/{project}/locations/{location}/platformLogsConfig projects/{pro
      ject}/locations/{location}/repositories/{repository}/platformLogsConfig
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesGetRequest object.

  Fields:
    name: Required. The name of the repository to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGoModulesUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesGoModulesUploadRequest
  object.

  Fields:
    parent: The resource name of the repository where the Go module will be
      uploaded.
    uploadGoModuleRequest: A UploadGoModuleRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadGoModuleRequest = _messages.MessageField('UploadGoModuleRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsImportRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsImportRequest
  object.

  Fields:
    importGoogetArtifactsRequest: A ImportGoogetArtifactsRequest resource to
      be passed as the request body.
    parent: The name of the parent resource where the artifacts will be
      imported.
  """

  importGoogetArtifactsRequest = _messages.MessageField('ImportGoogetArtifactsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsUploadRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsUploadRequest
  object.

  Fields:
    parent: The name of the parent resource where the artifacts will be
      uploaded.
    uploadGoogetArtifactRequest: A UploadGoogetArtifactRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadGoogetArtifactRequest = _messages.MessageField('UploadGoogetArtifactRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesKfpArtifactsUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesKfpArtifactsUploadRequest
  object.

  Fields:
    parent: The resource name of the repository where the KFP artifact will be
      uploaded.
    uploadKfpArtifactRequest: A UploadKfpArtifactRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadKfpArtifactRequest = _messages.MessageField('UploadKfpArtifactRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. The fields eligible for filtering
      are: * `name` Examples of using a filter: To filter the results of your
      request to repositories with the name `my-repo` in project `my-project`
      in the `us-central` region, append the following filter expression to
      your request: * `name="projects/my-project/locations/us-
      central1/repositories/my-repo"` You can also use wildcards to match any
      number of characters before or after the value: * `name="projects/my-
      project/locations/us-central1/repositories/my-*"` * `name="projects/my-
      project/locations/us-central1/repositories/*repo"` * `name="projects/my-
      project/locations/us-central1/repositories/*repo*"`
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of repositories to return. Maximum page size
      is 1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose repositories will
      be listed.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsGetRequest
  object.

  Fields:
    name: Required. The name of the maven artifact.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsListRequest
  object.

  Fields:
    pageSize: The maximum number of artifacts to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose maven artifacts
      will be listed.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ArtifactregistryProjectsLocationsRepositoriesNpmPackagesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesNpmPackagesGetRequest
  object.

  Fields:
    name: Required. The name of the npm package.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesNpmPackagesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesNpmPackagesListRequest
  object.

  Fields:
    pageSize: The maximum number of artifacts to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose npm packages will
      be listed.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest
  object.

  Fields:
    name: Required. The name of the package to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest
  object.

  Fields:
    name: Required. The name of the package to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. The fields eligible for filtering
      are: * `name` * `annotations` Examples of using a filter: To filter the
      results of your request to packages with the name `my-package` in
      project `my-project` in the `us-central` region, in repository `my-
      repo`, append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package"` You can also use wildcards to match any
      number of characters before or after the value: * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/packages/my-*"` *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/*package"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/*pack*"` To filter the results of
      your request to packages with the annotation key-value pair
      [`external_link`: `external_link_value`], append the following filter
      expression to your request": *
      `"annotations.external_link:external_link_value"` To filter the results
      just for a specific annotation key `external_link`, append the following
      filter expression to your request: * `"annotations.external_link"` If
      the annotation key or value contains special characters, you can escape
      them by surrounding the value with backticks. For example, to filter the
      results of your request to packages with the annotation key-value pair
      [`external.link`:`https://example.com/my-package`], append the following
      filter expression to your request: * ``
      "annotations.`external.link`:`https://example.com/my-package`" `` You
      can also filter with annotations with a wildcard to match any number of
      characters before or after the value: * ``
      "annotations.*_link:`*example.com*`" ``
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of packages to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose packages will be
      listed.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest
  object.

  Fields:
    name: The name of the package, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`. If the package ID part
      contains slashes, the slashes are escaped.
    package: A Package resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  package = _messages.MessageField('Package', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest
  object.

  Fields:
    parent: The name of the parent resource where the tag will be created.
    tag: A Tag resource to be passed as the request body.
    tagId: The tag id to use for this repository.
  """

  parent = _messages.StringField(1, required=True)
  tag = _messages.MessageField('Tag', 2)
  tagId = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest
  object.

  Fields:
    name: The name of the tag to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest
  object.

  Fields:
    name: The name of the tag to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest
  object.

  Fields:
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `name` * `version` Examples of using a filter: To filter the results of
      your request to tags with the name `my-tag` in package `my-package` in
      repository `my-repo` in project "`y-project` in the us-central region,
      append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/tags/my-tag"` You can also use wildcards to
      match any number of characters before or after the value: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/tags/my*"` * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/packages/my-
      package/tags/*tag"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/my-package/tags/*tag*"` To filter
      the results of your request to tags applied to the version `1.0` in
      package `my-package`, append the following filter expression to your
      request: * `version="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/my-package/versions/1.0"`
    pageSize: The maximum number of tags to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: The name of the parent package whose tags will be listed. For
      example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest
  object.

  Fields:
    name: The name of the tag, for example: "projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/tags/tag1". If the package
      part contains slashes, the slashes are escaped. The tag part can only
      have characters in [a-zA-Z0-9\-._~:@], anything else must be URL
      encoded.
    tag: A Tag resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  tag = _messages.MessageField('Tag', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsBatchDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsBatchDele
  teRequest object.

  Fields:
    batchDeleteVersionsRequest: A BatchDeleteVersionsRequest resource to be
      passed as the request body.
    parent: The name of the repository holding all requested versions.
  """

  batchDeleteVersionsRequest = _messages.MessageField('BatchDeleteVersionsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest
  object.

  Fields:
    force: By default, a version that is tagged may not be deleted. If
      force=true, the version and any tags pointing to the version are
      deleted.
    name: The name of the version to delete.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: The view that should be returned in the response.

  Fields:
    name: The name of the version to retrieve.
    view: The view that should be returned in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view that should be returned in the response.

    Values:
      VERSION_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Includes basic information about the version, but not any related
        tags.
      FULL: Include everything.
    """
    VERSION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest
  object.

  Enums:
    ViewValueValuesEnum: The view that should be returned in the response.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. The fields eligible for filtering
      are: * `name` * `annotations` Examples of using a filter: To filter the
      results of your request to versions with the name `my-version` in
      project `my-project` in the `us-central` region, in repository `my-
      repo`, append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/versions/my-version"` You can also use
      wildcards to match any number of characters before or after the value: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/versions/*version"` * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/packages/my-
      package/versions/my*"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/my-package/versions/*version*"`
      To filter the results of your request to versions with the annotation
      key-value pair [`external_link`: `external_link_value`], append the
      following filter expression to your request: *
      `"annotations.external_link:external_link_value"` To filter just for a
      specific annotation key `external_link`, append the following filter
      expression to your request: * `"annotations.external_link"` If the
      annotation key or value contains special characters, you can escape them
      by surrounding the value with backticks. For example, to filter the
      results of your request to versions with the annotation key-value pair
      [`external.link`:`https://example.com/my-version`], append the following
      filter expression to your request: * ``
      "annotations.`external.link`:`https://example.com/my-version`" `` You
      can also filter with annotations with a wildcard to match any number of
      characters before or after the value: * ``
      "annotations.*_link:`*example.com*`" ``
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of versions to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: The name of the parent resource whose versions will be listed.
    view: The view that should be returned in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view that should be returned in the response.

    Values:
      VERSION_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Includes basic information about the version, but not any related
        tags.
      FULL: Include everything.
    """
    VERSION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsPatchRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsPatchRequest
  object.

  Fields:
    name: The name of the version, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/versions/art1`. If the package
      or version ID parts contain slashes, the slashes are escaped.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
    version: A Version resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class ArtifactregistryProjectsLocationsRepositoriesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPatchRequest object.

  Fields:
    name: The name of the repository, for example: `projects/p1/locations/us-
      central1/repositories/repo1`. For each location in a project, repository
      names must be unique.
    repository: A Repository resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPythonPackagesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPythonPackagesGetRequest
  object.

  Fields:
    name: Required. The name of the python package.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPythonPackagesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPythonPackagesListRequest
  object.

  Fields:
    pageSize: The maximum number of artifacts to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose python packages
      will be listed.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ArtifactregistryProjectsLocationsRepositoriesReindexRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesReindexRequest object.

  Fields:
    name: Required. The name of the repository to refresh.
    reindexRepositoryRequest: A ReindexRepositoryRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  reindexRepositoryRequest = _messages.MessageField('ReindexRepositoryRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesRulesCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesRulesCreateRequest
  object.

  Fields:
    googleDevtoolsArtifactregistryV1Rule: A
      GoogleDevtoolsArtifactregistryV1Rule resource to be passed as the
      request body.
    parent: Required. The name of the parent resource where the rule will be
      created.
    ruleId: The rule id to use for this repository.
  """

  googleDevtoolsArtifactregistryV1Rule = _messages.MessageField('GoogleDevtoolsArtifactregistryV1Rule', 1)
  parent = _messages.StringField(2, required=True)
  ruleId = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesRulesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesRulesDeleteRequest
  object.

  Fields:
    name: Required. The name of the rule to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesRulesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesRulesGetRequest object.

  Fields:
    name: Required. The name of the rule to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesRulesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesRulesListRequest object.

  Fields:
    pageSize: The maximum number of rules to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent repository whose rules will be
      listed. For example: `projects/p1/locations/us-
      central1/repositories/repo1`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ArtifactregistryProjectsLocationsRepositoriesRulesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesRulesPatchRequest object.

  Fields:
    googleDevtoolsArtifactregistryV1Rule: A
      GoogleDevtoolsArtifactregistryV1Rule resource to be passed as the
      request body.
    name: The name of the rule, for example: `projects/p1/locations/us-
      central1/repositories/repo1/rules/rule1`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  googleDevtoolsArtifactregistryV1Rule = _messages.MessageField('GoogleDevtoolsArtifactregistryV1Rule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesUpdatePlatformLogsConfigRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesUpdatePlatformLogsConfigRequest
  object.

  Fields:
    name: Identifier. The name of the project's or repository's platform logs
      config. Always in one of the forms:
      projects/{projectID}/locations/{location}/platformLogsConfig projects/{p
      rojectID}/locations/{location}/repositories/{repository}/platformLogsCon
      fig
    platformLogsConfig: A PlatformLogsConfig resource to be passed as the
      request body.
    updateMask: Optional. Field mask to support partial updates.
  """

  name = _messages.StringField(1, required=True)
  platformLogsConfig = _messages.MessageField('PlatformLogsConfig', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest
  object.

  Fields:
    importYumArtifactsRequest: A ImportYumArtifactsRequest resource to be
      passed as the request body.
    parent: The name of the parent resource where the artifacts will be
      imported.
  """

  importYumArtifactsRequest = _messages.MessageField('ImportYumArtifactsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest
  object.

  Fields:
    parent: The name of the parent resource where the artifacts will be
      uploaded.
    uploadYumArtifactRequest: A UploadYumArtifactRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadYumArtifactRequest = _messages.MessageField('UploadYumArtifactRequest', 2)


class ArtifactregistryProjectsLocationsUpdatePlatformLogsConfigRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsUpdatePlatformLogsConfigRequest
  object.

  Fields:
    name: Identifier. The name of the project's or repository's platform logs
      config. Always in one of the forms:
      projects/{projectID}/locations/{location}/platformLogsConfig projects/{p
      rojectID}/locations/{location}/repositories/{repository}/platformLogsCon
      fig
    platformLogsConfig: A PlatformLogsConfig resource to be passed as the
      request body.
    updateMask: Optional. Field mask to support partial updates.
  """

  name = _messages.StringField(1, required=True)
  platformLogsConfig = _messages.MessageField('PlatformLogsConfig', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsUpdateVpcscConfigRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsUpdateVpcscConfigRequest object.

  Fields:
    name: The name of the project's VPC SC Config. Always of the form:
      projects/{projectID}/locations/{location}/vpcscConfig In update request:
      never set In response: always set
    updateMask: Field mask to support partial updates.
    vPCSCConfig: A VPCSCConfig resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  vPCSCConfig = _messages.MessageField('VPCSCConfig', 3)


class ArtifactregistryProjectsUpdateProjectSettingsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsUpdateProjectSettingsRequest object.

  Fields:
    name: The name of the project's settings. Always of the form:
      projects/{project-id}/projectSettings In update request: never set In
      response: always set
    projectSettings: A ProjectSettings resource to be passed as the request
      body.
    updateMask: Field mask to support partial updates.
  """

  name = _messages.StringField(1, required=True)
  projectSettings = _messages.MessageField('ProjectSettings', 2)
  updateMask = _messages.StringField(3)


class Attachment(_messages.Message):
  r"""An Attachment refers to additional metadata that can be attached to
  artifacts in Artifact Registry. An attachment consists of one or more files.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Artifact Registry. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Artifact Registry. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    attachmentNamespace: The namespace this attachment belongs to. E.g. If an
      attachment is created by artifact analysis, namespace is set to
      `artifactanalysis.googleapis.com`.
    createTime: Output only. The time when the attachment was created.
    files: Required. The files that belong to this attachment. If the file ID
      part contains slashes, they are escaped. E.g. `projects/p1/locations/us-
      central1/repositories/repo1/files/sha:`.
    name: The name of the attachment. E.g.
      `projects/p1/locations/us/repositories/repo/attachments/sbom`.
    ociVersionName: Output only. The name of the OCI version that this
      attachment created. Only populated for Docker attachments. E.g.
      `projects/p1/locations/us-
      central1/repositories/repo1/packages/p1/versions/v1`.
    target: Required. The target the attachment is for, can be a Version,
      Package or Repository. E.g. `projects/p1/locations/us-
      central1/repositories/repo1/packages/p1/versions/v1`.
    type: Type of attachment. E.g. `application/vnd.spdx+json`
    updateTime: Output only. The time when the attachment was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Artifact Registry. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  attachmentNamespace = _messages.StringField(2)
  createTime = _messages.StringField(3)
  files = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5)
  ociVersionName = _messages.StringField(6)
  target = _messages.StringField(7)
  type = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class BatchDeleteVersionsMetadata(_messages.Message):
  r"""The metadata of an LRO from deleting multiple versions.

  Fields:
    failedVersions: The versions the operation failed to delete.
  """

  failedVersions = _messages.StringField(1, repeated=True)


class BatchDeleteVersionsRequest(_messages.Message):
  r"""The request to delete multiple versions across a repository.

  Fields:
    names: Required. The names of the versions to delete. The maximum number
      of versions deleted per batch is determined by the service and is
      dependent on the available resources in the region.
    validateOnly: If true, the request is performed without deleting data,
      following AIP-163.
  """

  names = _messages.StringField(1, repeated=True)
  validateOnly = _messages.BooleanField(2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CleanupPolicy(_messages.Message):
  r"""Artifact policy configuration for repository cleanup policies.

  Enums:
    ActionValueValuesEnum: Policy action.

  Fields:
    action: Policy action.
    condition: Policy condition for matching versions.
    id: The user-provided ID of the cleanup policy.
    mostRecentVersions: Policy condition for retaining a minimum number of
      versions. May only be specified with a Keep action.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Policy action.

    Values:
      ACTION_UNSPECIFIED: Action not specified.
      DELETE: Delete action.
      KEEP: Keep action.
    """
    ACTION_UNSPECIFIED = 0
    DELETE = 1
    KEEP = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  condition = _messages.MessageField('CleanupPolicyCondition', 2)
  id = _messages.StringField(3)
  mostRecentVersions = _messages.MessageField('CleanupPolicyMostRecentVersions', 4)


class CleanupPolicyCondition(_messages.Message):
  r"""CleanupPolicyCondition is a set of conditions attached to a
  CleanupPolicy. If multiple entries are set, all must be satisfied for the
  condition to be satisfied.

  Enums:
    TagStateValueValuesEnum: Match versions by tag status.

  Fields:
    moreStaleThan: Match versions that have not been pulled in the duration.
    newerThan: Match versions newer than a duration.
    olderThan: Match versions older than a duration.
    packageNamePrefixes: Match versions by package prefix. Applied on any
      prefix match.
    tagPrefixes: Match versions by tag prefix. Applied on any prefix match.
    tagState: Match versions by tag status.
    versionNamePrefixes: Match versions by version name prefix. Applied on any
      prefix match.
  """

  class TagStateValueValuesEnum(_messages.Enum):
    r"""Match versions by tag status.

    Values:
      TAG_STATE_UNSPECIFIED: Tag status not specified.
      TAGGED: Applies to tagged versions only.
      UNTAGGED: Applies to untagged versions only.
      ANY: Applies to all versions.
    """
    TAG_STATE_UNSPECIFIED = 0
    TAGGED = 1
    UNTAGGED = 2
    ANY = 3

  moreStaleThan = _messages.StringField(1)
  newerThan = _messages.StringField(2)
  olderThan = _messages.StringField(3)
  packageNamePrefixes = _messages.StringField(4, repeated=True)
  tagPrefixes = _messages.StringField(5, repeated=True)
  tagState = _messages.EnumField('TagStateValueValuesEnum', 6)
  versionNamePrefixes = _messages.StringField(7, repeated=True)


class CleanupPolicyMostRecentVersions(_messages.Message):
  r"""CleanupPolicyMostRecentVersions is an alternate condition of a
  CleanupPolicy for retaining a minimum number of versions.

  Fields:
    keepCount: Minimum number of versions to keep.
    packageNamePrefixes: List of package name prefixes that will apply this
      rule.
  """

  keepCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  packageNamePrefixes = _messages.StringField(2, repeated=True)


class CommonRemoteRepository(_messages.Message):
  r"""Common remote repository settings type.

  Fields:
    uri: Required. A common public repository base for remote repository.
  """

  uri = _messages.StringField(1)


class DockerImage(_messages.Message):
  r"""DockerImage represents a docker artifact. The following fields are
  returned as untyped metadata in the Version resource, using camelcase keys
  (i.e. metadata.imageSizeBytes): * imageSizeBytes * mediaType * buildTime

  Fields:
    artifactType: ArtifactType type of this image, e.g.
      application/vnd.example+type". If the `subject` is set and no
      `artifactType `is given, the `mediaType` will be considered as the
      `artifactType`. This field is returned as the `metadata.artifactType`
      field in the Version resource.
    buildTime: The time this image was built. This field is returned as the
      'metadata.buildTime' field in the Version resource. The build time is
      returned to the client as an RFC 3339 string, which can be easily used
      with the JavaScript Date constructor.
    imageSizeBytes: Calculated size of the image. This field is returned as
      the 'metadata.imageSizeBytes' field in the Version resource.
    mediaType: Media type of this image, e.g.
      "application/vnd.docker.distribution.manifest.v2+json". This field is
      returned as the 'metadata.mediaType' field in the Version resource.
    name: Required. registry_location, project_id, repository_name and image
      id forms a unique image
      name:`projects//locations//repository//dockerImages/`. For example,
      "projects/test-project/locations/us-west4/repositories/test-
      repo/dockerImages/ nginx@sha256:e9954c1fc875017be1c3e36eca16be2d9e9bccc4
      bf072163515467d6a823c7cf", where "us-west4" is the registry_location,
      "test-project" is the project_id, "test-repo" is the repository_name and
      "nginx@sha256:e9954c1fc875017be1c3e36eca16be2d9e9bccc4bf072163515467d6a8
      23c7cf" is the image's digest.
    subjectDigest: Digest of the subject if provided. This field is returned
      as the `metadata.subjectDigest` field in the Version resource.
    tags: Tags attached to this image.
    updateTime: Output only. The time when the docker image was last updated.
    uploadTime: Time the image was uploaded.
    uri: Required. URL to access the image. Example: us-
      west4-docker.pkg.dev/test-project/test-repo/nginx@sha256:e9954c1fc875017
      be1c3e36eca16be2d9e9bccc4bf072163515467d6a823c7cf
  """

  artifactType = _messages.StringField(1)
  buildTime = _messages.StringField(2)
  imageSizeBytes = _messages.IntegerField(3)
  mediaType = _messages.StringField(4)
  name = _messages.StringField(5)
  subjectDigest = _messages.StringField(6)
  tags = _messages.StringField(7, repeated=True)
  updateTime = _messages.StringField(8)
  uploadTime = _messages.StringField(9)
  uri = _messages.StringField(10)


class DockerRepository(_messages.Message):
  r"""Configuration for a Docker remote repository.

  Enums:
    PublicRepositoryValueValuesEnum: One of the publicly available Docker
      repositories supported by Artifact Registry.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Docker repositories
      supported by Artifact Registry.
  """

  class PublicRepositoryValueValuesEnum(_messages.Enum):
    r"""One of the publicly available Docker repositories supported by
    Artifact Registry.

    Values:
      PUBLIC_REPOSITORY_UNSPECIFIED: Unspecified repository.
      DOCKER_HUB: Docker Hub.
    """
    PUBLIC_REPOSITORY_UNSPECIFIED = 0
    DOCKER_HUB = 1

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigDockerRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigDockerRepositoryCustomRepository', 2)
  publicRepository = _messages.EnumField('PublicRepositoryValueValuesEnum', 3)


class DockerRepositoryConfig(_messages.Message):
  r"""DockerRepositoryConfig is docker related repository details. Provides
  additional configuration details for repositories of the docker format type.

  Fields:
    immutableTags: The repository which enabled this flag prevents all tags
      from being modified, moved or deleted. This does not prevent tags from
      being created.
  """

  immutableTags = _messages.BooleanField(1)


class DownloadFileResponse(_messages.Message):
  r"""The response to download a file."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GenericArtifact(_messages.Message):
  r"""GenericArtifact represents a generic artifact

  Fields:
    createTime: Output only. The time when the Generic module is created.
    name: Resource name of the generic artifact. project, location,
      repository, package_id and version_id create a unique generic artifact.
      i.e. "projects/test-project/locations/us-west4/repositories/test-repo/
      genericArtifacts/package_id:version_id"
    updateTime: Output only. The time when the Generic module is updated.
    version: The version of the generic artifact.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  updateTime = _messages.StringField(3)
  version = _messages.StringField(4)


class GoModule(_messages.Message):
  r"""GoModule represents a Go module.

  Fields:
    createTime: Output only. The time when the Go module is created.
    name: The resource name of a Go module.
    updateTime: Output only. The time when the Go module is updated.
    version: The version of the Go module. Must be a valid canonical version
      as defined in https://go.dev/ref/mod#glos-canonical-version.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  updateTime = _messages.StringField(3)
  version = _messages.StringField(4)


class GoRepository(_messages.Message):
  r"""Configuration for a Go remote repository.

  Fields:
    customRepository: One of the publicly available Go repositories.
  """

  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigGoRepositoryCustomRepository', 1)


class GoogetArtifact(_messages.Message):
  r"""A detailed representation of a GooGet artifact.

  Fields:
    architecture: Output only. Operating system architecture of the artifact.
    name: Output only. The Artifact Registry resource name of the artifact.
    packageName: Output only. The GooGet package name of the artifact.
  """

  architecture = _messages.StringField(1)
  name = _messages.StringField(2)
  packageName = _messages.StringField(3)


class GoogleDevtoolsArtifactregistryV1File(_messages.Message):
  r"""Files store content that is potentially associated with Packages or
  Versions.

  Messages:
    AnnotationsValue: Optional. Client specified annotations.

  Fields:
    annotations: Optional. Client specified annotations.
    createTime: Output only. The time when the File was created.
    fetchTime: Output only. The time when the last attempt to refresh the
      file's data was made. Only set when the repository is remote.
    hashes: The hashes of the file content.
    name: The name of the file, for example: `projects/p1/locations/us-
      central1/repositories/repo1/files/a%2Fb%2Fc.txt`. If the file ID part
      contains slashes, they are escaped.
    owner: The name of the Package or Version that owns this file, if any.
    sizeBytes: The size of the File in bytes.
    updateTime: Output only. The time when the File was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  fetchTime = _messages.StringField(3)
  hashes = _messages.MessageField('Hash', 4, repeated=True)
  name = _messages.StringField(5)
  owner = _messages.StringField(6)
  sizeBytes = _messages.IntegerField(7)
  updateTime = _messages.StringField(8)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryCustomRepository(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, for
      ex: "https://my.apt.registry/".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigAptRepositoryPublicRepository(_messages.Message):
  r"""Publicly available Apt repositories constructed from a common repository
  base and a custom repository path.

  Enums:
    RepositoryBaseValueValuesEnum: A common public repository base for Apt.

  Fields:
    repositoryBase: A common public repository base for Apt.
    repositoryPath: A custom field to define a path to a specific repository
      from the base.
  """

  class RepositoryBaseValueValuesEnum(_messages.Enum):
    r"""A common public repository base for Apt.

    Values:
      REPOSITORY_BASE_UNSPECIFIED: Unspecified repository base.
      DEBIAN: Debian.
      UBUNTU: Ubuntu LTS/Pro.
      DEBIAN_SNAPSHOT: Archived Debian.
    """
    REPOSITORY_BASE_UNSPECIFIED = 0
    DEBIAN = 1
    UBUNTU = 2
    DEBIAN_SNAPSHOT = 3

  repositoryBase = _messages.EnumField('RepositoryBaseValueValuesEnum', 1)
  repositoryPath = _messages.StringField(2)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigDockerRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigDockerRepositoryCustomRepository(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the custom remote repository, for ex:
      "https://registry-1.docker.io".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigGoRepositoryCustomRepository(_messages.Message):
  r"""Custom Go remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, Must
      be the URI of a version control system. For example: https://github.com.
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigMavenRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigMavenRepositoryCustomRepository(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, for
      ex: "https://my.maven.registry/".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigNpmRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV****************************************************(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, for
      ex: "https://my.npm.registry/".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigPythonRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigPythonRepositoryCustomRepository(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, for
      ex: "https://my.python.registry/".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryArtifactRegistryRepository(_messages.Message):
  r"""A representation of an Artifact Registry repository.

  Fields:
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  repository = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryCustomRepository(_messages.Message):
  r"""Customer-specified publicly available remote repository.

  Fields:
    uri: An http/https uri reference to the upstream remote repository, for
      ex: "https://my.yum.registry/".
  """

  uri = _messages.StringField(1)


class GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryPublicRepository(_messages.Message):
  r"""Publicly available Yum repositories constructed from a common repository
  base and a custom repository path.

  Enums:
    RepositoryBaseValueValuesEnum: A common public repository base for Yum.

  Fields:
    repositoryBase: A common public repository base for Yum.
    repositoryPath: A custom field to define a path to a specific repository
      from the base.
  """

  class RepositoryBaseValueValuesEnum(_messages.Enum):
    r"""A common public repository base for Yum.

    Values:
      REPOSITORY_BASE_UNSPECIFIED: Unspecified repository base.
      CENTOS: CentOS.
      CENTOS_DEBUG: CentOS Debug.
      CENTOS_VAULT: CentOS Vault.
      CENTOS_STREAM: CentOS Stream.
      ROCKY: Rocky.
      EPEL: Fedora Extra Packages for Enterprise Linux (EPEL).
    """
    REPOSITORY_BASE_UNSPECIFIED = 0
    CENTOS = 1
    CENTOS_DEBUG = 2
    CENTOS_VAULT = 3
    CENTOS_STREAM = 4
    ROCKY = 5
    EPEL = 6

  repositoryBase = _messages.EnumField('RepositoryBaseValueValuesEnum', 1)
  repositoryPath = _messages.StringField(2)


class GoogleDevtoolsArtifactregistryV1Rule(_messages.Message):
  r"""A rule defines the deny or allow action of the operation it applies to
  and the conditions required for the rule to apply. You can set one rule for
  an entire repository and one rule for each package within.

  Enums:
    ActionValueValuesEnum: The action this rule takes.
    OperationValueValuesEnum:

  Fields:
    action: The action this rule takes.
    condition: Optional. A CEL expression for conditions that must be met in
      order for the rule to apply. If not provided, the rule matches all
      objects.
    name: The name of the rule, for example: `projects/p1/locations/us-
      central1/repositories/repo1/rules/rule1`.
    operation: A OperationValueValuesEnum attribute.
    packageId: The package ID the rule applies to. If empty, this rule applies
      to all packages inside the repository.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action this rule takes.

    Values:
      ACTION_UNSPECIFIED: Action not specified.
      ALLOW: Allow the operation.
      DENY: Deny the operation.
    """
    ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class OperationValueValuesEnum(_messages.Enum):
    r"""OperationValueValuesEnum enum type.

    Values:
      OPERATION_UNSPECIFIED: Operation not specified.
      DOWNLOAD: Download operation.
      TAG_MUTATION: Tag Mutation operation.
    """
    OPERATION_UNSPECIFIED = 0
    DOWNLOAD = 1
    TAG_MUTATION = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  condition = _messages.MessageField('Expr', 2)
  name = _messages.StringField(3)
  operation = _messages.EnumField('OperationValueValuesEnum', 4)
  packageId = _messages.StringField(5)


class Hash(_messages.Message):
  r"""A hash of file content.

  Enums:
    TypeValueValuesEnum: The algorithm used to compute the hash value.

  Fields:
    type: The algorithm used to compute the hash value.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The algorithm used to compute the hash value.

    Values:
      HASH_TYPE_UNSPECIFIED: Unspecified.
      SHA256: SHA256 hash.
      MD5: MD5 hash.
    """
    HASH_TYPE_UNSPECIFIED = 0
    SHA256 = 1
    MD5 = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class ImportAptArtifactsErrorInfo(_messages.Message):
  r"""Error information explaining why a package was not imported.

  Fields:
    error: The detailed error status.
    gcsSource: Google Cloud Storage location requested.
  """

  error = _messages.MessageField('Status', 1)
  gcsSource = _messages.MessageField('ImportAptArtifactsGcsSource', 2)


class ImportAptArtifactsGcsSource(_messages.Message):
  r"""Google Cloud Storage location where the artifacts currently reside.

  Fields:
    uris: Cloud Storage paths URI (e.g., gs://my_bucket//my_object).
    useWildcards: Supports URI wildcards for matching multiple objects from a
      single URI.
  """

  uris = _messages.StringField(1, repeated=True)
  useWildcards = _messages.BooleanField(2)


class ImportAptArtifactsMetadata(_messages.Message):
  r"""The operation metadata for importing artifacts."""


class ImportAptArtifactsRequest(_messages.Message):
  r"""The request to import new apt artifacts.

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('ImportAptArtifactsGcsSource', 1)


class ImportAptArtifactsResponse(_messages.Message):
  r"""The response message from importing APT artifacts.

  Fields:
    aptArtifacts: The Apt artifacts imported.
    errors: Detailed error info for packages that were not imported.
  """

  aptArtifacts = _messages.MessageField('AptArtifact', 1, repeated=True)
  errors = _messages.MessageField('ImportAptArtifactsErrorInfo', 2, repeated=True)


class ImportGoogetArtifactsErrorInfo(_messages.Message):
  r"""Error information explaining why a package was not imported.

  Fields:
    error: The detailed error status.
    gcsSource: Google Cloud Storage location requested.
  """

  error = _messages.MessageField('Status', 1)
  gcsSource = _messages.MessageField('ImportGoogetArtifactsGcsSource', 2)


class ImportGoogetArtifactsGcsSource(_messages.Message):
  r"""Google Cloud Storage location where the artifacts currently reside.

  Fields:
    uris: Cloud Storage paths URI (e.g., `gs://my_bucket/my_object`).
    useWildcards: Supports URI wildcards for matching multiple objects from a
      single URI.
  """

  uris = _messages.StringField(1, repeated=True)
  useWildcards = _messages.BooleanField(2)


class ImportGoogetArtifactsMetadata(_messages.Message):
  r"""The operation metadata for importing artifacts."""


class ImportGoogetArtifactsRequest(_messages.Message):
  r"""The request to import new googet artifacts.

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('ImportGoogetArtifactsGcsSource', 1)


class ImportGoogetArtifactsResponse(_messages.Message):
  r"""The response message from importing artifacts.

  Fields:
    errors: Detailed error info for packages that were not imported.
    googetArtifacts: The GooGet artifacts updated.
  """

  errors = _messages.MessageField('ImportGoogetArtifactsErrorInfo', 1, repeated=True)
  googetArtifacts = _messages.MessageField('GoogetArtifact', 2, repeated=True)


class ImportYumArtifactsErrorInfo(_messages.Message):
  r"""Error information explaining why a package was not imported.

  Fields:
    error: The detailed error status.
    gcsSource: Google Cloud Storage location requested.
  """

  error = _messages.MessageField('Status', 1)
  gcsSource = _messages.MessageField('ImportYumArtifactsGcsSource', 2)


class ImportYumArtifactsGcsSource(_messages.Message):
  r"""Google Cloud Storage location where the artifacts currently reside.

  Fields:
    uris: Cloud Storage paths URI (e.g., gs://my_bucket//my_object).
    useWildcards: Supports URI wildcards for matching multiple objects from a
      single URI.
  """

  uris = _messages.StringField(1, repeated=True)
  useWildcards = _messages.BooleanField(2)


class ImportYumArtifactsMetadata(_messages.Message):
  r"""The operation metadata for importing artifacts."""


class ImportYumArtifactsRequest(_messages.Message):
  r"""The request to import new yum artifacts.

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('ImportYumArtifactsGcsSource', 1)


class ImportYumArtifactsResponse(_messages.Message):
  r"""The response message from importing YUM artifacts.

  Fields:
    errors: Detailed error info for packages that were not imported.
    yumArtifacts: The yum artifacts imported.
  """

  errors = _messages.MessageField('ImportYumArtifactsErrorInfo', 1, repeated=True)
  yumArtifacts = _messages.MessageField('YumArtifact', 2, repeated=True)


class KfpArtifact(_messages.Message):
  r"""A detailed representation of a KFP artifact.

  Fields:
    name: Output only. Resource name of the KFP artifact. Since users don't
      directly interact with this resource, the name will be derived from the
      associated version. For example, when version =
      ".../versions/sha256:abcdef...", the name will be
      ".../kfpArtifacts/sha256:abcdef...".
    version: The version associated with the KFP artifact. Must follow the
      Semantic Versioning standard.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class ListAttachmentsResponse(_messages.Message):
  r"""The response from listing attachments.

  Fields:
    attachments: The attachments returned.
    nextPageToken: The token to retrieve the next page of attachments, or
      empty if there are no more attachments to return.
  """

  attachments = _messages.MessageField('Attachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDockerImagesResponse(_messages.Message):
  r"""The response from listing docker images.

  Fields:
    dockerImages: The docker images returned.
    nextPageToken: The token to retrieve the next page of artifacts, or empty
      if there are no more artifacts to return.
  """

  dockerImages = _messages.MessageField('DockerImage', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFilesResponse(_messages.Message):
  r"""The response from listing files.

  Fields:
    files: The files returned.
    nextPageToken: The token to retrieve the next page of files, or empty if
      there are no more files to return.
  """

  files = _messages.MessageField('GoogleDevtoolsArtifactregistryV1File', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMavenArtifactsResponse(_messages.Message):
  r"""The response from listing maven artifacts.

  Fields:
    mavenArtifacts: The maven artifacts returned.
    nextPageToken: The token to retrieve the next page of artifacts, or empty
      if there are no more artifacts to return.
  """

  mavenArtifacts = _messages.MessageField('MavenArtifact', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNpmPackagesResponse(_messages.Message):
  r"""The response from listing npm packages.

  Fields:
    nextPageToken: The token to retrieve the next page of artifacts, or empty
      if there are no more artifacts to return.
    npmPackages: The npm packages returned.
  """

  nextPageToken = _messages.StringField(1)
  npmPackages = _messages.MessageField('NpmPackage', 2, repeated=True)


class ListPackagesResponse(_messages.Message):
  r"""The response from listing packages.

  Fields:
    nextPageToken: The token to retrieve the next page of packages, or empty
      if there are no more packages to return.
    packages: The packages returned.
  """

  nextPageToken = _messages.StringField(1)
  packages = _messages.MessageField('Package', 2, repeated=True)


class ListPythonPackagesResponse(_messages.Message):
  r"""The response from listing python packages.

  Fields:
    nextPageToken: The token to retrieve the next page of artifacts, or empty
      if there are no more artifacts to return.
    pythonPackages: The python packages returned.
  """

  nextPageToken = _messages.StringField(1)
  pythonPackages = _messages.MessageField('PythonPackage', 2, repeated=True)


class ListRepositoriesResponse(_messages.Message):
  r"""The response from listing repositories.

  Fields:
    nextPageToken: The token to retrieve the next page of repositories, or
      empty if there are no more repositories to return.
    repositories: The repositories returned.
  """

  nextPageToken = _messages.StringField(1)
  repositories = _messages.MessageField('Repository', 2, repeated=True)


class ListRulesResponse(_messages.Message):
  r"""The response from listing rules.

  Fields:
    nextPageToken: The token to retrieve the next page of rules, or empty if
      there are no more rules to return.
    rules: The rules returned.
  """

  nextPageToken = _messages.StringField(1)
  rules = _messages.MessageField('GoogleDevtoolsArtifactregistryV1Rule', 2, repeated=True)


class ListTagsResponse(_messages.Message):
  r"""The response from listing tags.

  Fields:
    nextPageToken: The token to retrieve the next page of tags, or empty if
      there are no more tags to return.
    tags: The tags returned.
  """

  nextPageToken = _messages.StringField(1)
  tags = _messages.MessageField('Tag', 2, repeated=True)


class ListVersionsResponse(_messages.Message):
  r"""The response from listing versions.

  Fields:
    nextPageToken: The token to retrieve the next page of versions, or empty
      if there are no more versions to return.
    versions: The versions returned.
  """

  nextPageToken = _messages.StringField(1)
  versions = _messages.MessageField('Version', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MavenArtifact(_messages.Message):
  r"""MavenArtifact represents a maven artifact.

  Fields:
    artifactId: Artifact ID for the artifact.
    createTime: Output only. Time the artifact was created.
    groupId: Group ID for the artifact. Example: com.google.guava
    name: Required. registry_location, project_id, repository_name and
      maven_artifact forms a unique artifact For example, "projects/test-
      project/locations/us-west4/repositories/test-repo/mavenArtifacts/
      com.google.guava:guava:31.0-jre", where "us-west4" is the
      registry_location, "test-project" is the project_id, "test-repo" is the
      repository_name and "com.google.guava:guava:31.0-jre" is the maven
      artifact.
    pomUri: Required. URL to access the pom file of the artifact. Example: us-
      west4-maven.pkg.dev/test-project/test-
      repo/com/google/guava/guava/31.0/guava-31.0.pom
    updateTime: Output only. Time the artifact was updated.
    version: Version of this artifact.
  """

  artifactId = _messages.StringField(1)
  createTime = _messages.StringField(2)
  groupId = _messages.StringField(3)
  name = _messages.StringField(4)
  pomUri = _messages.StringField(5)
  updateTime = _messages.StringField(6)
  version = _messages.StringField(7)


class MavenRepository(_messages.Message):
  r"""Configuration for a Maven remote repository.

  Enums:
    PublicRepositoryValueValuesEnum: One of the publicly available Maven
      repositories supported by Artifact Registry.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Maven repositories
      supported by Artifact Registry.
  """

  class PublicRepositoryValueValuesEnum(_messages.Enum):
    r"""One of the publicly available Maven repositories supported by Artifact
    Registry.

    Values:
      PUBLIC_REPOSITORY_UNSPECIFIED: Unspecified repository.
      MAVEN_CENTRAL: Maven Central.
    """
    PUBLIC_REPOSITORY_UNSPECIFIED = 0
    MAVEN_CENTRAL = 1

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigMavenRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigMavenRepositoryCustomRepository', 2)
  publicRepository = _messages.EnumField('PublicRepositoryValueValuesEnum', 3)


class MavenRepositoryConfig(_messages.Message):
  r"""MavenRepositoryConfig is maven related repository details. Provides
  additional configuration details for repositories of the maven format type.

  Enums:
    VersionPolicyValueValuesEnum: Version policy defines the versions that the
      registry will accept.

  Fields:
    allowSnapshotOverwrites: The repository with this flag will allow
      publishing the same snapshot versions.
    versionPolicy: Version policy defines the versions that the registry will
      accept.
  """

  class VersionPolicyValueValuesEnum(_messages.Enum):
    r"""Version policy defines the versions that the registry will accept.

    Values:
      VERSION_POLICY_UNSPECIFIED: VERSION_POLICY_UNSPECIFIED - the version
        policy is not defined. When the version policy is not defined, no
        validation is performed for the versions.
      RELEASE: RELEASE - repository will accept only Release versions.
      SNAPSHOT: SNAPSHOT - repository will accept only Snapshot versions.
    """
    VERSION_POLICY_UNSPECIFIED = 0
    RELEASE = 1
    SNAPSHOT = 2

  allowSnapshotOverwrites = _messages.BooleanField(1)
  versionPolicy = _messages.EnumField('VersionPolicyValueValuesEnum', 2)


class NetworkConfig(_messages.Message):
  r"""Config for the routing/network configuration of the repository.

  Fields:
    alternativeHostname: Optional. An alternative hostname that a repository
      can be accessed through. Routing a host to AR needs to be handled
      externally via PSC NEGs. Only 1 project per region may use a given
      alternative hostname.
    isDefault: Optional. Whether this is the default repository for the
      alternative hostname. Only 1 repository per hostname may be marked as
      the default. This repository will only be defaulted to if no path prefix
      is matched, allowing the customer to pull images from a hostname without
      a project or path prefix in the request path.
    prefix: Optional. A path prefix the repo can be accessed through, to
      differentiate multiple repositories using the same alternative hostname.
      If the customer does not set this value, it will default to the repo
      name.
  """

  alternativeHostname = _messages.StringField(1)
  isDefault = _messages.BooleanField(2)
  prefix = _messages.StringField(3)


class NpmPackage(_messages.Message):
  r"""NpmPackage represents an npm artifact.

  Fields:
    createTime: Output only. Time the package was created.
    name: Required. registry_location, project_id, repository_name and
      npm_package forms a unique package For example, "projects/test-
      project/locations/us-west4/repositories/test-repo/npmPackages/
      npm_test:1.0.0", where "us-west4" is the registry_location, "test-
      project" is the project_id, "test-repo" is the repository_name and
      npm_test:1.0.0" is the npm package.
    packageName: Package for the artifact.
    tags: Tags attached to this package.
    updateTime: Output only. Time the package was updated.
    version: Version of this package.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  packageName = _messages.StringField(3)
  tags = _messages.StringField(4, repeated=True)
  updateTime = _messages.StringField(5)
  version = _messages.StringField(6)


class NpmRepository(_messages.Message):
  r"""Configuration for a Npm remote repository.

  Enums:
    PublicRepositoryValueValuesEnum: One of the publicly available Npm
      repositories supported by Artifact Registry.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Npm repositories supported
      by Artifact Registry.
  """

  class PublicRepositoryValueValuesEnum(_messages.Enum):
    r"""One of the publicly available Npm repositories supported by Artifact
    Registry.

    Values:
      PUBLIC_REPOSITORY_UNSPECIFIED: Unspecified repository.
      NPMJS: npmjs.
    """
    PUBLIC_REPOSITORY_UNSPECIFIED = 0
    NPMJS = 1

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigNpmRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV****************************************************', 2)
  publicRepository = _messages.EnumField('PublicRepositoryValueValuesEnum', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata type for longrunning-operations, currently empty."""


class Package(_messages.Message):
  r"""Packages are named collections of versions.

  Messages:
    AnnotationsValue: Optional. Client specified annotations.

  Fields:
    annotations: Optional. Client specified annotations.
    createTime: The time when the package was created.
    displayName: The display name of the package.
    name: The name of the package, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`. If the package ID part
      contains slashes, the slashes are escaped.
    updateTime: The time when the package was last updated. This includes
      publishing a new version of the package.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class PlatformLogsConfig(_messages.Message):
  r"""The platform logs config for a project or a repository.

  Enums:
    LoggingStateValueValuesEnum: Optional. The state of the platform logs:
      enabled or disabled.
    SeverityLevelValueValuesEnum: Optional. The severity level for the logs.
      Logs will be generated if their severity level is >= than the value of
      the severity level mentioned here.

  Fields:
    loggingState: Optional. The state of the platform logs: enabled or
      disabled.
    name: Identifier. The name of the project's or repository's platform logs
      config. Always in one of the forms:
      projects/{projectID}/locations/{location}/platformLogsConfig projects/{p
      rojectID}/locations/{location}/repositories/{repository}/platformLogsCon
      fig
    severityLevel: Optional. The severity level for the logs. Logs will be
      generated if their severity level is >= than the value of the severity
      level mentioned here.
  """

  class LoggingStateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of the platform logs: enabled or disabled.

    Values:
      LOGGING_STATE_UNSPECIFIED: Platform logs settings for the parent
        resource haven't been set. This is the default state or when the user
        clears the settings for the parent.
      ENABLED: Platform logs are enabled.
      DISABLED: Platform logs are disabled.
    """
    LOGGING_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class SeverityLevelValueValuesEnum(_messages.Enum):
    r"""Optional. The severity level for the logs. Logs will be generated if
    their severity level is >= than the value of the severity level mentioned
    here.

    Values:
      SEVERITY_LEVEL_UNSPECIFIED: No severity level specified, meaning
        everything is being logged.
      DEBUG: Debug or trace information.
      INFO: Routine information, such as ongoing status or performance.
      NOTICE: Normal but significant events, such as start up, shut down, or a
        configuration change.
      WARNING: Warning events that might cause problems.
      ERROR: Error events that are likely to cause problems.
      CRITICAL: Critical events that cause more severe problems or outages.
      ALERT: Alert events that require a person must take an action
        immediately.
      EMERGENCY: One or more systems are unusable.
    """
    SEVERITY_LEVEL_UNSPECIFIED = 0
    DEBUG = 1
    INFO = 2
    NOTICE = 3
    WARNING = 4
    ERROR = 5
    CRITICAL = 6
    ALERT = 7
    EMERGENCY = 8

  loggingState = _messages.EnumField('LoggingStateValueValuesEnum', 1)
  name = _messages.StringField(2)
  severityLevel = _messages.EnumField('SeverityLevelValueValuesEnum', 3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ProjectSettings(_messages.Message):
  r"""The Artifact Registry settings that apply to a Project.

  Enums:
    LegacyRedirectionStateValueValuesEnum: The redirection state of the legacy
      repositories in this project.

  Fields:
    legacyRedirectionState: The redirection state of the legacy repositories
      in this project.
    name: The name of the project's settings. Always of the form:
      projects/{project-id}/projectSettings In update request: never set In
      response: always set
    pullPercent: The percentage of pull traffic to redirect from GCR to AR
      when using partial redirection.
  """

  class LegacyRedirectionStateValueValuesEnum(_messages.Enum):
    r"""The redirection state of the legacy repositories in this project.

    Values:
      REDIRECTION_STATE_UNSPECIFIED: No redirection status has been set.
      REDIRECTION_FROM_GCR_IO_DISABLED: Redirection is disabled.
      REDIRECTION_FROM_GCR_IO_ENABLED: Redirection is enabled.
      REDIRECTION_FROM_GCR_IO_FINALIZED: Redirection is enabled, and has been
        finalized so cannot be reverted.
      REDIRECTION_FROM_GCR_IO_PARTIAL: Redirection is partially enabled.
      REDIRECTION_FROM_GCR_IO_ENABLED_AND_COPYING: Redirection is enabled and
        missing images are copied from GCR
      REDIRECTION_FROM_GCR_IO_PARTIAL_AND_COPYING: Redirection is partially
        enabled and missing images are copied from GCR
    """
    REDIRECTION_STATE_UNSPECIFIED = 0
    REDIRECTION_FROM_GCR_IO_DISABLED = 1
    REDIRECTION_FROM_GCR_IO_ENABLED = 2
    REDIRECTION_FROM_GCR_IO_FINALIZED = 3
    REDIRECTION_FROM_GCR_IO_PARTIAL = 4
    REDIRECTION_FROM_GCR_IO_ENABLED_AND_COPYING = 5
    REDIRECTION_FROM_GCR_IO_PARTIAL_AND_COPYING = 6

  legacyRedirectionState = _messages.EnumField('LegacyRedirectionStateValueValuesEnum', 1)
  name = _messages.StringField(2)
  pullPercent = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class PythonPackage(_messages.Message):
  r"""PythonPackage represents a python artifact.

  Fields:
    createTime: Output only. Time the package was created.
    name: Required. registry_location, project_id, repository_name and
      python_package forms a unique package
      name:`projects//locations//repository//pythonPackages/`. For example,
      "projects/test-project/locations/us-west4/repositories/test-
      repo/pythonPackages/ python_package:1.0.0", where "us-west4" is the
      registry_location, "test-project" is the project_id, "test-repo" is the
      repository_name and python_package:1.0.0" is the python package.
    packageName: Package for the artifact.
    updateTime: Output only. Time the package was updated.
    uri: Required. URL to access the package. Example: us-
      west4-python.pkg.dev/test-project/test-repo/python_package/file-
      name-1.0.0.tar.gz
    version: Version of this package.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  packageName = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  uri = _messages.StringField(5)
  version = _messages.StringField(6)


class PythonRepository(_messages.Message):
  r"""Configuration for a Python remote repository.

  Enums:
    PublicRepositoryValueValuesEnum: One of the publicly available Python
      repositories supported by Artifact Registry.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Python repositories
      supported by Artifact Registry.
  """

  class PublicRepositoryValueValuesEnum(_messages.Enum):
    r"""One of the publicly available Python repositories supported by
    Artifact Registry.

    Values:
      PUBLIC_REPOSITORY_UNSPECIFIED: Unspecified repository.
      PYPI: PyPI.
    """
    PUBLIC_REPOSITORY_UNSPECIFIED = 0
    PYPI = 1

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigPythonRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigPythonRepositoryCustomRepository', 2)
  publicRepository = _messages.EnumField('PublicRepositoryValueValuesEnum', 3)


class ReindexRepositoryRequest(_messages.Message):
  r"""The request to update the index files in an OS repository."""


class RemoteRepositoryConfig(_messages.Message):
  r"""Remote repository configuration.

  Enums:
    RemoteTypeValueValuesEnum:

  Fields:
    aptRepository: Specific settings for an Apt remote repository.
    commonRepository: Common remote repository settings. Used as the remote
      repository upstream URL.
    deleteNotFoundCacheFiles: Optional. If files are removed from the remote
      host, should they also be removed from the Artifact Registry repository
      when requested? Only supported for docker, maven, and python
    description: The description of the remote source.
    disableUpstreamValidation: Input only. A create/update remote repo option
      to avoid making a HEAD/GET request to validate a remote repo and any
      supplied upstream credentials.
    dockerRepository: Specific settings for a Docker remote repository.
    goRepository: Specific settings for a Go remote repository.
    mavenRepository: Specific settings for a Maven remote repository.
    npmRepository: Specific settings for an Npm remote repository.
    pythonRepository: Specific settings for a Python remote repository.
    remoteType: A RemoteTypeValueValuesEnum attribute.
    serviceDirectoryConfig: A ServiceDirectoryConfig attribute.
    upstreamCredentials: Optional. The credentials used to access the remote
      repository.
    yumRepository: Specific settings for a Yum remote repository.
  """

  class RemoteTypeValueValuesEnum(_messages.Enum):
    r"""RemoteTypeValueValuesEnum enum type.

    Values:
      REMOTE_TYPE_UNSPECIFIED: <no description>
      MIRROR: <no description>
      CACHE_LAYER: <no description>
    """
    REMOTE_TYPE_UNSPECIFIED = 0
    MIRROR = 1
    CACHE_LAYER = 2

  aptRepository = _messages.MessageField('AptRepository', 1)
  commonRepository = _messages.MessageField('CommonRemoteRepository', 2)
  deleteNotFoundCacheFiles = _messages.BooleanField(3)
  description = _messages.StringField(4)
  disableUpstreamValidation = _messages.BooleanField(5)
  dockerRepository = _messages.MessageField('DockerRepository', 6)
  goRepository = _messages.MessageField('GoRepository', 7)
  mavenRepository = _messages.MessageField('MavenRepository', 8)
  npmRepository = _messages.MessageField('NpmRepository', 9)
  pythonRepository = _messages.MessageField('PythonRepository', 10)
  remoteType = _messages.EnumField('RemoteTypeValueValuesEnum', 11)
  serviceDirectoryConfig = _messages.MessageField('ServiceDirectoryConfig', 12)
  upstreamCredentials = _messages.MessageField('UpstreamCredentials', 13)
  yumRepository = _messages.MessageField('YumRepository', 14)


class Repository(_messages.Message):
  r"""A Repository for storing artifacts with a specific format.

  Enums:
    FormatValueValuesEnum: Optional. The format of packages that are stored in
      the repository.
    ModeValueValuesEnum: Optional. The mode of the repository.

  Messages:
    CleanupPoliciesValue: Optional. Cleanup policies for this repository.
      Cleanup policies indicate when certain package versions can be
      automatically deleted. Map keys are policy IDs supplied by users during
      policy creation. They must unique within a repository and be under 128
      characters in length.
    LabelsValue: Labels with user-defined metadata. This field may contain up
      to 64 entries. Label keys and values may be no longer than 63
      characters. Label keys must begin with a lowercase letter and may only
      contain lowercase letters, numeric characters, underscores, and dashes.

  Fields:
    cleanupPolicies: Optional. Cleanup policies for this repository. Cleanup
      policies indicate when certain package versions can be automatically
      deleted. Map keys are policy IDs supplied by users during policy
      creation. They must unique within a repository and be under 128
      characters in length.
    cleanupPolicyDryRun: Optional. If true, the cleanup pipeline is prevented
      from deleting versions in this repository.
    createTime: Output only. The time when the repository was created.
    description: The user-provided description of the repository.
    disallowUnspecifiedMode: Optional. If this is true, an unspecified repo
      type will be treated as error rather than defaulting to standard.
    dockerConfig: Docker repository config contains repository level
      configuration for the repositories of docker type.
    format: Optional. The format of packages that are stored in the
      repository.
    kmsKeyName: The Cloud KMS resource name of the customer managed encryption
      key that's used to encrypt the contents of the Repository. Has the form:
      `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-
      key`. This value may not be changed after the Repository has been
      created.
    labels: Labels with user-defined metadata. This field may contain up to 64
      entries. Label keys and values may be no longer than 63 characters.
      Label keys must begin with a lowercase letter and may only contain
      lowercase letters, numeric characters, underscores, and dashes.
    mavenConfig: Maven repository config contains repository level
      configuration for the repositories of maven type.
    mode: Optional. The mode of the repository.
    name: The name of the repository, for example: `projects/p1/locations/us-
      central1/repositories/repo1`. For each location in a project, repository
      names must be unique.
    networkConfig: Optional. Config for the routing/network configuration of
      the repository.
    registryUri: Output only. The repository endpoint, for example: `us-
      docker.pkg.dev/my-proj/my-repo`.
    remoteRepositoryConfig: Configuration specific for a Remote Repository.
    satisfiesPzi: Output only. Whether or not this repository satisfies PZI.
    satisfiesPzs: Output only. Whether or not this repository satisfies PZS.
    sbomConfig: Optional. Config and state for sbom generation for resources
      within this Repository.
    sizeBytes: Output only. The size, in bytes, of all artifact storage in
      this repository. Repositories that are generally available or in public
      preview use this to calculate storage costs.
    updateTime: Output only. The time when the repository was last updated.
    virtualRepositoryConfig: Configuration specific for a Virtual Repository.
    vulnerabilityScanningConfig: Optional. Config and state for vulnerability
      scanning of resources within this Repository.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Optional. The format of packages that are stored in the repository.

    Values:
      FORMAT_UNSPECIFIED: Unspecified package format.
      DOCKER: Docker package format.
      MAVEN: Maven package format.
      NPM: NPM package format.
      APT: APT package format.
      YUM: YUM package format.
      GOOGET: GooGet package format.
      PYTHON: Python package format.
      KFP: Kubeflow Pipelines package format.
      GO: Go package format.
      GENERIC: Generic package format.
      RUBY: Ruby package format.
    """
    FORMAT_UNSPECIFIED = 0
    DOCKER = 1
    MAVEN = 2
    NPM = 3
    APT = 4
    YUM = 5
    GOOGET = 6
    PYTHON = 7
    KFP = 8
    GO = 9
    GENERIC = 10
    RUBY = 11

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode of the repository.

    Values:
      MODE_UNSPECIFIED: Unspecified mode.
      STANDARD_REPOSITORY: A standard repository storing artifacts.
      VIRTUAL_REPOSITORY: A virtual repository to serve artifacts from one or
        more sources.
      REMOTE_REPOSITORY: A remote repository to serve artifacts from a remote
        source.
      AOSS_REPOSITORY: An AOSS repository provides artifacts from AOSS
        upstreams.
      ASSURED_OSS_REPOSITORY: Replacement of AOSS_REPOSITORY.
    """
    MODE_UNSPECIFIED = 0
    STANDARD_REPOSITORY = 1
    VIRTUAL_REPOSITORY = 2
    REMOTE_REPOSITORY = 3
    AOSS_REPOSITORY = 4
    ASSURED_OSS_REPOSITORY = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CleanupPoliciesValue(_messages.Message):
    r"""Optional. Cleanup policies for this repository. Cleanup policies
    indicate when certain package versions can be automatically deleted. Map
    keys are policy IDs supplied by users during policy creation. They must
    unique within a repository and be under 128 characters in length.

    Messages:
      AdditionalProperty: An additional property for a CleanupPoliciesValue
        object.

    Fields:
      additionalProperties: Additional properties of type CleanupPoliciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CleanupPoliciesValue object.

      Fields:
        key: Name of the additional property.
        value: A CleanupPolicy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('CleanupPolicy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user-defined metadata. This field may contain up to 64
    entries. Label keys and values may be no longer than 63 characters. Label
    keys must begin with a lowercase letter and may only contain lowercase
    letters, numeric characters, underscores, and dashes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cleanupPolicies = _messages.MessageField('CleanupPoliciesValue', 1)
  cleanupPolicyDryRun = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  disallowUnspecifiedMode = _messages.BooleanField(5)
  dockerConfig = _messages.MessageField('DockerRepositoryConfig', 6)
  format = _messages.EnumField('FormatValueValuesEnum', 7)
  kmsKeyName = _messages.StringField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  mavenConfig = _messages.MessageField('MavenRepositoryConfig', 10)
  mode = _messages.EnumField('ModeValueValuesEnum', 11)
  name = _messages.StringField(12)
  networkConfig = _messages.MessageField('NetworkConfig', 13)
  registryUri = _messages.StringField(14)
  remoteRepositoryConfig = _messages.MessageField('RemoteRepositoryConfig', 15)
  satisfiesPzi = _messages.BooleanField(16)
  satisfiesPzs = _messages.BooleanField(17)
  sbomConfig = _messages.MessageField('SbomConfig', 18)
  sizeBytes = _messages.IntegerField(19)
  updateTime = _messages.StringField(20)
  virtualRepositoryConfig = _messages.MessageField('VirtualRepositoryConfig', 21)
  vulnerabilityScanningConfig = _messages.MessageField('VulnerabilityScanningConfig', 22)


class SbomConfig(_messages.Message):
  r"""Config for whether to generate SBOMs for resources in this repository,
  as well as output fields describing current state.

  Enums:
    EnablementConfigValueValuesEnum: Optional. Config for whether this
      repository has sbom generation disabled.
    EnablementStateValueValuesEnum: Output only. State of feature enablement,
      combining repository enablement config and API enablement state.

  Fields:
    enablementConfig: Optional. Config for whether this repository has sbom
      generation disabled.
    enablementState: Output only. State of feature enablement, combining
      repository enablement config and API enablement state.
    enablementStateReason: Output only. Reason for the repository state and
      potential actions to activate it.
    gcsBucket: Optional. The GCS bucket to put the generated SBOMs into.
    lastEnableTime: Output only. The last time this repository config was set
      to INHERITED.
  """

  class EnablementConfigValueValuesEnum(_messages.Enum):
    r"""Optional. Config for whether this repository has sbom generation
    disabled.

    Values:
      ENABLEMENT_CONFIG_UNSPECIFIED: Unspecified config was not set. This will
        be interpreted as DISABLED.
      INHERITED: Inherited indicates the repository is allowed for SBOM
        generation, however the actual state will be inherited from the API
        enablement state.
      DISABLED: Disabled indicates the repository will not generate SBOMs.
    """
    ENABLEMENT_CONFIG_UNSPECIFIED = 0
    INHERITED = 1
    DISABLED = 2

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of feature enablement, combining repository
    enablement config and API enablement state.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Enablement state is unclear.
      SBOM_UNSUPPORTED: Repository does not support SBOM generation.
      SBOM_DISABLED: SBOM generation is disabled for this repository.
      SBOM_ACTIVE: SBOM generation is active for this feature.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    SBOM_UNSUPPORTED = 1
    SBOM_DISABLED = 2
    SBOM_ACTIVE = 3

  enablementConfig = _messages.EnumField('EnablementConfigValueValuesEnum', 1)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 2)
  enablementStateReason = _messages.StringField(3)
  gcsBucket = _messages.StringField(4)
  lastEnableTime = _messages.StringField(5)


class ServiceDirectoryConfig(_messages.Message):
  r"""Config for Service Directory Integration.

  Fields:
    service: Required. Service Directory resource name (versionless), for ex:
      projects/my-proj/locations/us/namespaces/network/services/my-service
  """

  service = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tag(_messages.Message):
  r"""Tags point to a version and represent an alternative name that can be
  used to access the version.

  Fields:
    name: The name of the tag, for example: "projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/tags/tag1". If the package
      part contains slashes, the slashes are escaped. The tag part can only
      have characters in [a-zA-Z0-9\-._~:@], anything else must be URL
      encoded.
    version: The name of the version the tag refers to, for example:
      `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/versions/sha256:5243811` If
      the package or version ID parts contain slashes, the slashes are
      escaped.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UploadAptArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadAptArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading artifacts."""


class UploadAptArtifactRequest(_messages.Message):
  r"""The request to upload an artifact."""


class UploadAptArtifactResponse(_messages.Message):
  r"""The response of the completed artifact upload operation. This response
  is contained in the Operation and available to users.

  Fields:
    aptArtifacts: The Apt artifacts updated.
  """

  aptArtifacts = _messages.MessageField('AptArtifact', 1, repeated=True)


class UploadFileMediaResponse(_messages.Message):
  r"""The response to upload a generic artifact.

  Fields:
    operation: Operation that will be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadFileRequest(_messages.Message):
  r"""The request to upload a file.

  Fields:
    fileId: Optional. The ID of the file. If left empty will default to sha256
      digest of the content uploaded.
  """

  fileId = _messages.StringField(1)


class UploadGenericArtifactMediaResponse(_messages.Message):
  r"""The response to upload a generic artifact.

  Fields:
    operation: Operation that will be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadGenericArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading generic artifacts."""


class UploadGenericArtifactRequest(_messages.Message):
  r"""The request to upload a generic artifact. The created GenericArtifact
  will have the resource name {parent}/genericArtifacts/package_id:version_id.
  The created file will have the resource name
  {parent}/files/package_id:version_id:filename.

  Fields:
    filename: The name of the file of the generic artifact to be uploaded.
      E.g. `example-file.zip` The filename is limited to letters, numbers, and
      url safe characters, i.e. [a-zA-Z0-9-_.~@].
    packageId: The ID of the package of the generic artifact. If the package
      does not exist, a new package will be created. The `package_id` should
      start and end with a letter or number, only contain letters, numbers,
      hyphens, underscores, and periods, and not exceed 256 characters.
    versionId: The ID of the version of the generic artifact. If the version
      does not exist, a new version will be created. The version_id must start
      and end with a letter or number, can only contain lowercase letters,
      numbers, the following characters [-.+~:], i.e.[a-z0-9-.+~:] and cannot
      exceed a total of 128 characters. Creating a version called `latest` is
      not allowed.
  """

  filename = _messages.StringField(1)
  packageId = _messages.StringField(2)
  versionId = _messages.StringField(3)


class UploadGoModuleMediaResponse(_messages.Message):
  r"""The response to upload a Go module.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadGoModuleMetadata(_messages.Message):
  r"""The operation metadata for uploading go modules."""


class UploadGoModuleRequest(_messages.Message):
  r"""The request to upload a Go module."""


class UploadGoogetArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadGoogetArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading artifacts."""


class UploadGoogetArtifactRequest(_messages.Message):
  r"""The request to upload an artifact."""


class UploadGoogetArtifactResponse(_messages.Message):
  r"""The response of the completed artifact upload operation. This response
  is contained in the Operation and available to users.

  Fields:
    googetArtifacts: The GooGet artifacts updated.
  """

  googetArtifacts = _messages.MessageField('GoogetArtifact', 1, repeated=True)


class UploadKfpArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation that will be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadKfpArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading KFP artifacts."""


class UploadKfpArtifactRequest(_messages.Message):
  r"""The request to upload an artifact.

  Fields:
    description: Description of the package version.
    tags: Tags to be created with the version.
  """

  description = _messages.StringField(1)
  tags = _messages.StringField(2, repeated=True)


class UploadYumArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadYumArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading artifacts."""


class UploadYumArtifactRequest(_messages.Message):
  r"""The request to upload an artifact."""


class UploadYumArtifactResponse(_messages.Message):
  r"""The response of the completed artifact upload operation. This response
  is contained in the Operation and available to users.

  Fields:
    yumArtifacts: The Yum artifacts updated.
  """

  yumArtifacts = _messages.MessageField('YumArtifact', 1, repeated=True)


class UpstreamCredentials(_messages.Message):
  r"""The credentials to access the remote repository.

  Fields:
    usernamePasswordCredentials: Use username and password to access the
      remote repository.
  """

  usernamePasswordCredentials = _messages.MessageField('UsernamePasswordCredentials', 1)


class UpstreamPolicy(_messages.Message):
  r"""Artifact policy configuration for the repository contents.

  Fields:
    id: The user-provided ID of the upstream policy.
    priority: Entries with a greater priority value take precedence in the
      pull order.
    repository: A reference to the repository resource, for example:
      `projects/p1/locations/us-central1/repositories/repo1`.
  """

  id = _messages.StringField(1)
  priority = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  repository = _messages.StringField(3)


class UsernamePasswordCredentials(_messages.Message):
  r"""Username and password credentials.

  Fields:
    passwordSecretVersion: The Secret Manager key version that holds the
      password to access the remote repository. Must be in the format of
      `projects/{project}/secrets/{secret}/versions/{version}`.
    username: The username to access the remote repository.
  """

  passwordSecretVersion = _messages.StringField(1)
  username = _messages.StringField(2)


class VPCSCConfig(_messages.Message):
  r"""The Artifact Registry VPC SC config that apply to a Project.

  Enums:
    VpcscPolicyValueValuesEnum: The project per location VPC SC policy that
      defines the VPC SC behavior for the Remote Repository (Allow/Deny).

  Fields:
    name: The name of the project's VPC SC Config. Always of the form:
      projects/{projectID}/locations/{location}/vpcscConfig In update request:
      never set In response: always set
    vpcscPolicy: The project per location VPC SC policy that defines the VPC
      SC behavior for the Remote Repository (Allow/Deny).
  """

  class VpcscPolicyValueValuesEnum(_messages.Enum):
    r"""The project per location VPC SC policy that defines the VPC SC
    behavior for the Remote Repository (Allow/Deny).

    Values:
      VPCSC_POLICY_UNSPECIFIED: VPCSC_POLICY_UNSPECIFIED - the VPS SC policy
        is not defined. When VPS SC policy is not defined - the Service will
        use the default behavior (VPCSC_DENY).
      DENY: VPCSC_DENY - repository will block the requests to the Upstreams
        for the Remote Repositories if the resource is in the perimeter.
      ALLOW: VPCSC_ALLOW - repository will allow the requests to the Upstreams
        for the Remote Repositories if the resource is in the perimeter.
    """
    VPCSC_POLICY_UNSPECIFIED = 0
    DENY = 1
    ALLOW = 2

  name = _messages.StringField(1)
  vpcscPolicy = _messages.EnumField('VpcscPolicyValueValuesEnum', 2)


class Version(_messages.Message):
  r"""The body of a version resource. A version resource represents a
  collection of components, such as files and other data. This may correspond
  to a version in many package management schemes.

  Messages:
    AnnotationsValue: Optional. Client specified annotations.
    MetadataValue: Output only. Repository-specific Metadata stored against
      this version. The fields returned are defined by the underlying
      repository-specific resource. Currently, the resources could be:
      DockerImage MavenArtifact

  Fields:
    annotations: Optional. Client specified annotations.
    createTime: The time when the version was created.
    description: Optional. Description of the version, as specified in its
      metadata.
    metadata: Output only. Repository-specific Metadata stored against this
      version. The fields returned are defined by the underlying repository-
      specific resource. Currently, the resources could be: DockerImage
      MavenArtifact
    name: The name of the version, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/versions/art1`. If the package
      or version ID parts contain slashes, the slashes are escaped.
    relatedTags: Output only. A list of related tags. Will contain up to 100
      tags that reference this version.
    updateTime: The time when the version was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Output only. Repository-specific Metadata stored against this version.
    The fields returned are defined by the underlying repository-specific
    resource. Currently, the resources could be: DockerImage MavenArtifact

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)
  relatedTags = _messages.MessageField('Tag', 6, repeated=True)
  updateTime = _messages.StringField(7)


class VirtualRepositoryConfig(_messages.Message):
  r"""Virtual repository configuration.

  Fields:
    upstreamPolicies: Policies that configure the upstream artifacts
      distributed by the Virtual Repository. Upstream policies cannot be set
      on a standard repository.
  """

  upstreamPolicies = _messages.MessageField('UpstreamPolicy', 1, repeated=True)


class VulnerabilityScanningConfig(_messages.Message):
  r"""Config on whether to perform vulnerability scanning for resources in
  this repository, as well as output fields describing current state.

  Enums:
    EnablementConfigValueValuesEnum: Optional. Config for whether this
      repository has vulnerability scanning disabled.
    EnablementStateValueValuesEnum: Output only. State of feature enablement,
      combining repository enablement config and API enablement state.

  Fields:
    enablementConfig: Optional. Config for whether this repository has
      vulnerability scanning disabled.
    enablementState: Output only. State of feature enablement, combining
      repository enablement config and API enablement state.
    enablementStateReason: Output only. Reason for the repository state.
    lastEnableTime: Output only. The last time this repository config was
      enabled.
  """

  class EnablementConfigValueValuesEnum(_messages.Enum):
    r"""Optional. Config for whether this repository has vulnerability
    scanning disabled.

    Values:
      ENABLEMENT_CONFIG_UNSPECIFIED: Not set. This will be treated as
        INHERITED.
      INHERITED: Scanning is Enabled, but dependent on API enablement.
      DISABLED: No automatic vulnerability scanning will be performed for this
        repository.
    """
    ENABLEMENT_CONFIG_UNSPECIFIED = 0
    INHERITED = 1
    DISABLED = 2

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of feature enablement, combining repository
    enablement config and API enablement state.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Enablement state is unclear.
      SCANNING_UNSUPPORTED: Repository does not support vulnerability
        scanning.
      SCANNING_DISABLED: Vulnerability scanning is disabled for this
        repository.
      SCANNING_ACTIVE: Vulnerability scanning is active for this repository.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    SCANNING_UNSUPPORTED = 1
    SCANNING_DISABLED = 2
    SCANNING_ACTIVE = 3

  enablementConfig = _messages.EnumField('EnablementConfigValueValuesEnum', 1)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 2)
  enablementStateReason = _messages.StringField(3)
  lastEnableTime = _messages.StringField(4)


class YumArtifact(_messages.Message):
  r"""A detailed representation of a Yum artifact.

  Enums:
    PackageTypeValueValuesEnum: Output only. An artifact is a binary or source
      package.

  Fields:
    architecture: Output only. Operating system architecture of the artifact.
    name: Output only. The Artifact Registry resource name of the artifact.
    packageName: Output only. The yum package name of the artifact.
    packageType: Output only. An artifact is a binary or source package.
  """

  class PackageTypeValueValuesEnum(_messages.Enum):
    r"""Output only. An artifact is a binary or source package.

    Values:
      PACKAGE_TYPE_UNSPECIFIED: Package type is not specified.
      BINARY: Binary package (.rpm).
      SOURCE: Source package (.srpm).
    """
    PACKAGE_TYPE_UNSPECIFIED = 0
    BINARY = 1
    SOURCE = 2

  architecture = _messages.StringField(1)
  name = _messages.StringField(2)
  packageName = _messages.StringField(3)
  packageType = _messages.EnumField('PackageTypeValueValuesEnum', 4)


class YumRepository(_messages.Message):
  r"""Configuration for a Yum remote repository.

  Fields:
    artifactRegistryRepository: An Artifact Registry Repository.
    customRepository: Customer-specified remote repository.
    publicRepository: One of the publicly available Yum repositories supported
      by Artifact Registry.
  """

  artifactRegistryRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryArtifactRegistryRepository', 1)
  customRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryCustomRepository', 2)
  publicRepository = _messages.MessageField('GoogleDevtoolsArtifactregistryV1RemoteRepositoryConfigYumRepositoryPublicRepository', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
