"""Generated client library for artifactregistry version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.artifactregistry.v1 import artifactregistry_v1_messages as messages


class ArtifactregistryV1(base_api.BaseApiClient):
  """Generated client library for service artifactregistry version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://artifactregistry.googleapis.com/'
  MTLS_BASE_URL = 'https://artifactregistry.mtls.googleapis.com/'

  _PACKAGE = 'artifactregistry'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ArtifactregistryV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new artifactregistry handle."""
    url = url or self.BASE_URL
    super(ArtifactregistryV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_repositories_aptArtifacts = self.ProjectsLocationsRepositoriesAptArtifactsService(self)
    self.projects_locations_repositories_attachments = self.ProjectsLocationsRepositoriesAttachmentsService(self)
    self.projects_locations_repositories_dockerImages = self.ProjectsLocationsRepositoriesDockerImagesService(self)
    self.projects_locations_repositories_files = self.ProjectsLocationsRepositoriesFilesService(self)
    self.projects_locations_repositories_genericArtifacts = self.ProjectsLocationsRepositoriesGenericArtifactsService(self)
    self.projects_locations_repositories_goModules = self.ProjectsLocationsRepositoriesGoModulesService(self)
    self.projects_locations_repositories_googetArtifacts = self.ProjectsLocationsRepositoriesGoogetArtifactsService(self)
    self.projects_locations_repositories_kfpArtifacts = self.ProjectsLocationsRepositoriesKfpArtifactsService(self)
    self.projects_locations_repositories_mavenArtifacts = self.ProjectsLocationsRepositoriesMavenArtifactsService(self)
    self.projects_locations_repositories_npmPackages = self.ProjectsLocationsRepositoriesNpmPackagesService(self)
    self.projects_locations_repositories_packages_tags = self.ProjectsLocationsRepositoriesPackagesTagsService(self)
    self.projects_locations_repositories_packages_versions = self.ProjectsLocationsRepositoriesPackagesVersionsService(self)
    self.projects_locations_repositories_packages = self.ProjectsLocationsRepositoriesPackagesService(self)
    self.projects_locations_repositories_pythonPackages = self.ProjectsLocationsRepositoriesPythonPackagesService(self)
    self.projects_locations_repositories_rules = self.ProjectsLocationsRepositoriesRulesService(self)
    self.projects_locations_repositories_yumArtifacts = self.ProjectsLocationsRepositoriesYumArtifactsService(self)
    self.projects_locations_repositories = self.ProjectsLocationsRepositoriesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ArtifactregistryProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesAptArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_aptArtifacts resource."""

    _NAME = 'projects_locations_repositories_aptArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesAptArtifactsService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/aptArtifacts:create',
          ),
          }

    def Import(self, request, global_params=None):
      r"""Imports Apt artifacts. The returned Operation will complete once the resources are imported. Package, Version, and File resources are created based on the imported artifacts. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/aptArtifacts:import',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.aptArtifacts.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/aptArtifacts:import',
        request_field='importAptArtifactsRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads an Apt artifact. The returned Operation will complete once the resources are uploaded. Package, Version, and File resources are created based on the imported artifact. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadAptArtifactMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/aptArtifacts:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.aptArtifacts.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/aptArtifacts:create',
        request_field='uploadAptArtifactRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest',
        response_type_name='UploadAptArtifactMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_attachments resource."""

    _NAME = 'projects_locations_repositories_attachments'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an attachment. The returned Operation will finish once the attachment has been created. Its response will be the created attachment.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/attachments',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.attachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attachmentId'],
        relative_path='v1/{+parent}/attachments',
        request_field='attachment',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an attachment. The returned Operation will finish once the attachments has been deleted. It will not have any Operation metadata and will return a `google.protobuf.Empty` response.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/attachments/{attachmentsId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.attachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an attachment.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Attachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/attachments/{attachmentsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.attachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAttachmentsGetRequest',
        response_type_name='Attachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists attachments.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/attachments',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.attachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attachments',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesAttachmentsListRequest',
        response_type_name='ListAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesDockerImagesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_dockerImages resource."""

    _NAME = 'projects_locations_repositories_dockerImages'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesDockerImagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a docker image.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesDockerImagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DockerImage) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/dockerImages/{dockerImagesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.dockerImages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesDockerImagesGetRequest',
        response_type_name='DockerImage',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists docker images.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesDockerImagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDockerImagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/dockerImages',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.dockerImages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/dockerImages',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesDockerImagesListRequest',
        response_type_name='ListDockerImagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesFilesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_files resource."""

    _NAME = 'projects_locations_repositories_files'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesFilesService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=True,
              resumable_path='/resumable/upload/v1/{+parent}/files:upload',
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/files:upload',
          ),
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a file and all of its content. It is only allowed on generic repositories. The returned operation will complete once the file has been deleted.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files/{filesId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.files.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Download(self, request, global_params=None, download=None):
      r"""Download a file.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        download: (Download, default: None) If present, download
            data from the request via this stream.
      Returns:
        (DownloadFileResponse) The response message.
      """
      config = self.GetMethodConfig('Download')
      return self._RunMethod(
          config, request, global_params=global_params,
          download=download)

    Download.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files/{filesId}:download',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.files.download',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:download',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest',
        response_type_name='DownloadFileResponse',
        supports_download=True,
    )

    def Get(self, request, global_params=None):
      r"""Gets a file.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleDevtoolsArtifactregistryV1File) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files/{filesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.files.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest',
        response_type_name='GoogleDevtoolsArtifactregistryV1File',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists files.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.files.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/files',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesListRequest',
        response_type_name='ListFilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a file.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleDevtoolsArtifactregistryV1File) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files/{filesId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.files.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleDevtoolsArtifactregistryV1File',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesPatchRequest',
        response_type_name='GoogleDevtoolsArtifactregistryV1File',
        supports_download=False,
    )

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a file to a repository. The returned Operation will complete once the resources are uploaded.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesFilesUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadFileMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/files:upload',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.files.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/files:upload',
        request_field='uploadFileRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesFilesUploadRequest',
        response_type_name='UploadFileMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesGenericArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_genericArtifacts resource."""

    _NAME = 'projects_locations_repositories_genericArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesGenericArtifactsService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=True,
              resumable_path='/resumable/upload/v1/{+parent}/genericArtifacts:create',
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/genericArtifacts:create',
          ),
          }

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a Generic artifact. The returned operation will complete once the resources are uploaded. Package, version, and file resources are created based on the uploaded artifact. Uploaded artifacts that conflict with existing resources will raise an `ALREADY_EXISTS` error.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGenericArtifactsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadGenericArtifactMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/genericArtifacts:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.genericArtifacts.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/genericArtifacts:create',
        request_field='uploadGenericArtifactRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGenericArtifactsUploadRequest',
        response_type_name='UploadGenericArtifactMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesGoModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_goModules resource."""

    _NAME = 'projects_locations_repositories_goModules'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesGoModulesService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/goModules:create',
          ),
          }

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a Go module. The returned Operation will complete once the Go module is uploaded. Package, Version, and File resources are created based on the uploaded Go module.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGoModulesUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadGoModuleMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/goModules:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.goModules.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/goModules:create',
        request_field='uploadGoModuleRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGoModulesUploadRequest',
        response_type_name='UploadGoModuleMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesGoogetArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_googetArtifacts resource."""

    _NAME = 'projects_locations_repositories_googetArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesGoogetArtifactsService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/googetArtifacts:create',
          ),
          }

    def Import(self, request, global_params=None):
      r"""Imports GooGet artifacts. The returned Operation will complete once the resources are imported. Package, Version, and File resources are created based on the imported artifacts. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/googetArtifacts:import',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.googetArtifacts.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/googetArtifacts:import',
        request_field='importGoogetArtifactsRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a GooGet artifact. The returned Operation will complete once the resources are uploaded. Package, Version, and File resources are created based on the imported artifact. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadGoogetArtifactMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/googetArtifacts:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.googetArtifacts.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/googetArtifacts:create',
        request_field='uploadGoogetArtifactRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGoogetArtifactsUploadRequest',
        response_type_name='UploadGoogetArtifactMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesKfpArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_kfpArtifacts resource."""

    _NAME = 'projects_locations_repositories_kfpArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesKfpArtifactsService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/kfpArtifacts:create',
          ),
          }

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a KFP artifact. The returned Operation will complete once the resource is uploaded. Package, Version, and File resources will be created based on the uploaded artifact. Uploaded artifacts that conflict with existing resources will be overwritten.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesKfpArtifactsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadKfpArtifactMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/kfpArtifacts:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.kfpArtifacts.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/kfpArtifacts:create',
        request_field='uploadKfpArtifactRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesKfpArtifactsUploadRequest',
        response_type_name='UploadKfpArtifactMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesMavenArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_mavenArtifacts resource."""

    _NAME = 'projects_locations_repositories_mavenArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesMavenArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a maven artifact.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MavenArtifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/mavenArtifacts/{mavenArtifactsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.mavenArtifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsGetRequest',
        response_type_name='MavenArtifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists maven artifacts.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMavenArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/mavenArtifacts',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.mavenArtifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/mavenArtifacts',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesMavenArtifactsListRequest',
        response_type_name='ListMavenArtifactsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesNpmPackagesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_npmPackages resource."""

    _NAME = 'projects_locations_repositories_npmPackages'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesNpmPackagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a npm package.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesNpmPackagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NpmPackage) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/npmPackages/{npmPackagesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.npmPackages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesNpmPackagesGetRequest',
        response_type_name='NpmPackage',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists npm packages.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesNpmPackagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNpmPackagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/npmPackages',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.npmPackages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/npmPackages',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesNpmPackagesListRequest',
        response_type_name='ListNpmPackagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPackagesTagsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_packages_tags resource."""

    _NAME = 'projects_locations_repositories_packages_tags'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesPackagesTagsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a tag.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tag) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/tags',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.packages.tags.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tagId'],
        relative_path='v1/{+parent}/tags',
        request_field='tag',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest',
        response_type_name='Tag',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a tag.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/tags/{tagsId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.packages.tags.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a tag.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tag) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/tags/{tagsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.tags.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest',
        response_type_name='Tag',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists tags.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTagsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/tags',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.tags.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/tags',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest',
        response_type_name='ListTagsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a tag.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tag) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/tags/{tagsId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.packages.tags.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='tag',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest',
        response_type_name='Tag',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPackagesVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_packages_versions resource."""

    _NAME = 'projects_locations_repositories_packages_versions'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesPackagesVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes multiple versions across a repository. The returned operation will complete once the versions have been deleted.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/versions:batchDelete',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.packages.versions.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/versions:batchDelete',
        request_field='batchDeleteVersionsRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsBatchDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a version and all of its content. The returned operation will complete once the version has been deleted.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.packages.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a version.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Version) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/versions/{versionsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest',
        response_type_name='Version',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists versions.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/versions',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest',
        response_type_name='ListVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a version.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Version) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}/versions/{versionsId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.packages.versions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='version',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsPatchRequest',
        response_type_name='Version',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPackagesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_packages resource."""

    _NAME = 'projects_locations_repositories_packages'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesPackagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a package and all of its versions and tags. The returned operation will complete once the package has been deleted.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.packages.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a package.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Package) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest',
        response_type_name='Package',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists packages.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPackagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.packages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/packages',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest',
        response_type_name='ListPackagesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a package.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Package) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/packages/{packagesId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.packages.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='package',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest',
        response_type_name='Package',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPythonPackagesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_pythonPackages resource."""

    _NAME = 'projects_locations_repositories_pythonPackages'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesPythonPackagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a python package.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPythonPackagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PythonPackage) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pythonPackages/{pythonPackagesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.pythonPackages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPythonPackagesGetRequest',
        response_type_name='PythonPackage',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists python packages.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPythonPackagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPythonPackagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pythonPackages',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.pythonPackages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/pythonPackages',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPythonPackagesListRequest',
        response_type_name='ListPythonPackagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_rules resource."""

    _NAME = 'projects_locations_repositories_rules'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a rule.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleDevtoolsArtifactregistryV1Rule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/rules',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.rules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['ruleId'],
        relative_path='v1/{+parent}/rules',
        request_field='googleDevtoolsArtifactregistryV1Rule',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesRulesCreateRequest',
        response_type_name='GoogleDevtoolsArtifactregistryV1Rule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a rule.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/rules/{rulesId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.rules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesRulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a rule.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleDevtoolsArtifactregistryV1Rule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/rules/{rulesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.rules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesRulesGetRequest',
        response_type_name='GoogleDevtoolsArtifactregistryV1Rule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists rules.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/rules',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.rules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/rules',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesRulesListRequest',
        response_type_name='ListRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a rule.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleDevtoolsArtifactregistryV1Rule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/rules/{rulesId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.rules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleDevtoolsArtifactregistryV1Rule',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesRulesPatchRequest',
        response_type_name='GoogleDevtoolsArtifactregistryV1Rule',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesYumArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_yumArtifacts resource."""

    _NAME = 'projects_locations_repositories_yumArtifacts'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesYumArtifactsService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/yumArtifacts:create',
          ),
          }

    def Import(self, request, global_params=None):
      r"""Imports Yum (RPM) artifacts. The returned Operation will complete once the resources are imported. Package, Version, and File resources are created based on the imported artifacts. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/yumArtifacts:import',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.yumArtifacts.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/yumArtifacts:import',
        request_field='importYumArtifactsRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Upload(self, request, global_params=None, upload=None):
      r"""Directly uploads a Yum artifact. The returned Operation will complete once the resources are uploaded. Package, Version, and File resources are created based on the imported artifact. Imported artifacts that conflict with existing resources are ignored.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (UploadYumArtifactMediaResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/yumArtifacts:create',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.yumArtifacts.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/yumArtifacts:create',
        request_field='uploadYumArtifactRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest',
        response_type_name='UploadYumArtifactMediaResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories resource."""

    _NAME = 'projects_locations_repositories'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsRepositoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a repository. The returned Operation will finish once the repository has been created. Its response will be the created Repository.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['repositoryId'],
        relative_path='v1/{+parent}/repositories',
        request_field='repository',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a repository and all of its contents. The returned Operation will finish once the repository has been deleted. It will not have any Operation metadata and will return a google.protobuf.Empty response.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='DELETE',
        method_id='artifactregistry.projects.locations.repositories.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a repository.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Repository) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGetRequest',
        response_type_name='Repository',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy for a given resource.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:getIamPolicy',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def GetPlatformLogsConfig(self, request, global_params=None):
      r"""Retrieves the platform logs config for the project or repository.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesGetPlatformLogsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PlatformLogsConfig) The response message.
      """
      config = self.GetMethodConfig('GetPlatformLogsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetPlatformLogsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/platformLogsConfig',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.getPlatformLogsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesGetPlatformLogsConfigRequest',
        response_type_name='PlatformLogsConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists repositories.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories',
        http_method='GET',
        method_id='artifactregistry.projects.locations.repositories.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/repositories',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesListRequest',
        response_type_name='ListRepositoriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a repository.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Repository) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='repository',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesPatchRequest',
        response_type_name='Repository',
        supports_download=False,
    )

    def Reindex(self, request, global_params=None):
      r"""Updates the index files for an OS repository. Intended for use on remote repositories to check if the upstream has been updated, and if so pull the new index files.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesReindexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reindex')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reindex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:reindex',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.reindex',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reindex',
        request_field='reindexRepositoryRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesReindexRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Updates the IAM policy for a given resource.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:setIamPolicy',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests if the caller has a list of permissions on a resource.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:testIamPermissions',
        http_method='POST',
        method_id='artifactregistry.projects.locations.repositories.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def UpdatePlatformLogsConfig(self, request, global_params=None):
      r"""Updates the platform logs config for the project or repository.

      Args:
        request: (ArtifactregistryProjectsLocationsRepositoriesUpdatePlatformLogsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PlatformLogsConfig) The response message.
      """
      config = self.GetMethodConfig('UpdatePlatformLogsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdatePlatformLogsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/platformLogsConfig',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.repositories.updatePlatformLogsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='platformLogsConfig',
        request_type_name='ArtifactregistryProjectsLocationsRepositoriesUpdatePlatformLogsConfigRequest',
        response_type_name='PlatformLogsConfig',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ArtifactregistryProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='artifactregistry.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetPlatformLogsConfig(self, request, global_params=None):
      r"""Retrieves the platform logs config for the project or repository.

      Args:
        request: (ArtifactregistryProjectsLocationsGetPlatformLogsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PlatformLogsConfig) The response message.
      """
      config = self.GetMethodConfig('GetPlatformLogsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetPlatformLogsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/platformLogsConfig',
        http_method='GET',
        method_id='artifactregistry.projects.locations.getPlatformLogsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsGetPlatformLogsConfigRequest',
        response_type_name='PlatformLogsConfig',
        supports_download=False,
    )

    def GetVpcscConfig(self, request, global_params=None):
      r"""Retrieves the VPCSC Config for the Project.

      Args:
        request: (ArtifactregistryProjectsLocationsGetVpcscConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VPCSCConfig) The response message.
      """
      config = self.GetMethodConfig('GetVpcscConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVpcscConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vpcscConfig',
        http_method='GET',
        method_id='artifactregistry.projects.locations.getVpcscConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsGetVpcscConfigRequest',
        response_type_name='VPCSCConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ArtifactregistryProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='artifactregistry.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='ArtifactregistryProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdatePlatformLogsConfig(self, request, global_params=None):
      r"""Updates the platform logs config for the project or repository.

      Args:
        request: (ArtifactregistryProjectsLocationsUpdatePlatformLogsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PlatformLogsConfig) The response message.
      """
      config = self.GetMethodConfig('UpdatePlatformLogsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdatePlatformLogsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/platformLogsConfig',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.updatePlatformLogsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='platformLogsConfig',
        request_type_name='ArtifactregistryProjectsLocationsUpdatePlatformLogsConfigRequest',
        response_type_name='PlatformLogsConfig',
        supports_download=False,
    )

    def UpdateVpcscConfig(self, request, global_params=None):
      r"""Updates the VPCSC Config for the Project.

      Args:
        request: (ArtifactregistryProjectsLocationsUpdateVpcscConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VPCSCConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateVpcscConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateVpcscConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vpcscConfig',
        http_method='PATCH',
        method_id='artifactregistry.projects.locations.updateVpcscConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='vPCSCConfig',
        request_type_name='ArtifactregistryProjectsLocationsUpdateVpcscConfigRequest',
        response_type_name='VPCSCConfig',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ArtifactregistryV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetProjectSettings(self, request, global_params=None):
      r"""Retrieves the Settings for the Project.

      Args:
        request: (ArtifactregistryProjectsGetProjectSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectSettings) The response message.
      """
      config = self.GetMethodConfig('GetProjectSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetProjectSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/projectSettings',
        http_method='GET',
        method_id='artifactregistry.projects.getProjectSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ArtifactregistryProjectsGetProjectSettingsRequest',
        response_type_name='ProjectSettings',
        supports_download=False,
    )

    def UpdateProjectSettings(self, request, global_params=None):
      r"""Updates the Settings for the Project.

      Args:
        request: (ArtifactregistryProjectsUpdateProjectSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateProjectSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateProjectSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/projectSettings',
        http_method='PATCH',
        method_id='artifactregistry.projects.updateProjectSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='projectSettings',
        request_type_name='ArtifactregistryProjectsUpdateProjectSettingsRequest',
        response_type_name='ProjectSettings',
        supports_download=False,
    )
