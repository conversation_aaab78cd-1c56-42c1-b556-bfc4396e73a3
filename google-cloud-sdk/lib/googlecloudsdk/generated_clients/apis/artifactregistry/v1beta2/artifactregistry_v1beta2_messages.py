"""Generated message classes for artifactregistry version v1beta2.

Store and manage build artifacts in a scalable and integrated service built on
Google infrastructure.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'artifactregistry'


class AptArtifact(_messages.Message):
  r"""A detailed representation of an Apt artifact. Information in the record
  is derived from the archive's control file. See
  https://www.debian.org/doc/debian-policy/ch-controlfields.html

  Enums:
    PackageTypeValueValuesEnum: Output only. An artifact is a binary or source
      package.

  Fields:
    architecture: Output only. Operating system architecture of the artifact.
    component: Output only. Repository component of the artifact.
    controlFile: Output only. Contents of the artifact's control metadata
      file.
    name: Output only. The Artifact Registry resource name of the artifact.
    packageName: Output only. The Apt package name of the artifact.
    packageType: Output only. An artifact is a binary or source package.
  """

  class PackageTypeValueValuesEnum(_messages.Enum):
    r"""Output only. An artifact is a binary or source package.

    Values:
      PACKAGE_TYPE_UNSPECIFIED: Package type is not specified.
      BINARY: Binary package.
      SOURCE: Source package.
    """
    PACKAGE_TYPE_UNSPECIFIED = 0
    BINARY = 1
    SOURCE = 2

  architecture = _messages.StringField(1)
  component = _messages.StringField(2)
  controlFile = _messages.BytesField(3)
  name = _messages.StringField(4)
  packageName = _messages.StringField(5)
  packageType = _messages.EnumField('PackageTypeValueValuesEnum', 6)


class ArtifactregistryProjectsGetProjectSettingsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsGetProjectSettingsRequest object.

  Fields:
    name: Required. The name of the projectSettings resource.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ArtifactregistryProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAptArtifactsImportRequest
  object.

  Fields:
    importAptArtifactsRequest: A ImportAptArtifactsRequest resource to be
      passed as the request body.
    parent: The name of the parent resource where the artifacts will be
      imported.
  """

  importAptArtifactsRequest = _messages.MessageField('ImportAptArtifactsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesAptArtifactsUploadRequest
  object.

  Fields:
    parent: The name of the parent resource where the artifacts will be
      uploaded.
    uploadAptArtifactRequest: A UploadAptArtifactRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadAptArtifactRequest = _messages.MessageField('UploadAptArtifactRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource where the repository
      will be created.
    repository: A Repository resource to be passed as the request body.
    repositoryId: Required. The repository id to use for this repository.
  """

  parent = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesDeleteRequest object.

  Fields:
    name: Required. The name of the repository to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesDownloadRequest
  object.

  Fields:
    name: Required. The name of the file to download.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesGetRequest object.

  Fields:
    name: Required. The name of the file to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesFilesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesFilesListRequest object.

  Fields:
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `name` * `owner` * `annotations` Examples of using a filter: To filter
      the results of your request to files with the name `my_file.txt` in
      project `my-project` in the `us-central` region, in repository `my-
      repo`, append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/files/my-file.txt"` You can also use wildcards to match any number
      of characters before or after the value: * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/files/my-*"` *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/files/*file.txt"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/files/*file*"` To filter the results of
      your request to files owned by the version `1.0` in package `pkg1`,
      append the following filter expression to your request: *
      `owner="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/versions/1.0"` To filter the results of your
      request to files with the annotation key-value pair [`external_link`:
      `external_link_value`], append the following filter expression to your
      request: * `"annotations.external_link:external_link_value"` To filter
      just for a specific annotation key `external_link`, append the following
      filter expression to your request: * `"annotations.external_link"` If
      the annotation key or value contains special characters, you can escape
      them by surrounding the value with backticks. For example, to filter the
      results of your request to files with the annotation key-value pair
      [`external.link`:`https://example.com/my-file`], append the following
      filter expression to your request: * ``
      "annotations.`external.link`:`https://example.com/my-file`" `` You can
      also filter with annotations with a wildcard to match any number of
      characters before or after the value: * ``
      "annotations.*_link:`*example.com*`" ``
    pageSize: The maximum number of files to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the repository whose files will be listed.
      For example: "projects/p1/locations/us-central1/repositories/repo1
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesGetRequest object.

  Fields:
    name: Required. The name of the repository to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesListRequest object.

  Fields:
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of repositories to return. Maximum page size
      is 1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose repositories will
      be listed.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesDeleteRequest
  object.

  Fields:
    name: Required. The name of the package to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesGetRequest
  object.

  Fields:
    name: Required. The name of the package to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesListRequest
  object.

  Fields:
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of packages to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: Required. The name of the parent resource whose packages will be
      listed.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesPatchRequest
  object.

  Fields:
    name: The name of the package, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`. If the package ID part
      contains slashes, the slashes are escaped.
    package: A Package resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  package = _messages.MessageField('Package', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsCreateRequest
  object.

  Fields:
    parent: The name of the parent resource where the tag will be created.
    tag: A Tag resource to be passed as the request body.
    tagId: The tag id to use for this repository.
  """

  parent = _messages.StringField(1, required=True)
  tag = _messages.MessageField('Tag', 2)
  tagId = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsDeleteRequest
  object.

  Fields:
    name: The name of the tag to delete.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsGetRequest
  object.

  Fields:
    name: The name of the tag to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsListRequest
  object.

  Fields:
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `name` * `version` Examples of using a filter: To filter the results of
      your request to tags with the name `my-tag` in package `my-package` in
      repository `my-repo` in project "`y-project` in the us-central region,
      append the following filter expression to your request: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/tags/my-tag"` You can also use wildcards to
      match any number of characters before or after the value: *
      `name="projects/my-project/locations/us-central1/repositories/my-
      repo/packages/my-package/tags/my*"` * `name="projects/my-
      project/locations/us-central1/repositories/my-repo/packages/my-
      package/tags/*tag"` * `name="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/my-package/tags/*tag*"` To filter
      the results of your request to tags applied to the version `1.0` in
      package `my-package`, append the following filter expression to your
      request: * `version="projects/my-project/locations/us-
      central1/repositories/my-repo/packages/my-package/versions/1.0"`
    pageSize: The maximum number of tags to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: The name of the parent package whose tags will be listed. For
      example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPackagesTagsPatchRequest
  object.

  Fields:
    name: The name of the tag, for example: "projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/tags/tag1". If the package
      part contains slashes, the slashes are escaped. The tag part can only
      have characters in [a-zA-Z0-9\-._~:@], anything else must be URL
      encoded.
    tag: A Tag resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  tag = _messages.MessageField('Tag', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsDeleteRequest
  object.

  Fields:
    force: By default, a version that is tagged may not be deleted. If
      force=true, the version and any tags pointing to the version are
      deleted.
    name: The name of the version to delete.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: The view that should be returned in the response.

  Fields:
    name: The name of the version to retrieve.
    view: The view that should be returned in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view that should be returned in the response.

    Values:
      VERSION_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Includes basic information about the version, but not any related
        tags.
      FULL: Include everything.
    """
    VERSION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest(_messages.Message):
  r"""A
  ArtifactregistryProjectsLocationsRepositoriesPackagesVersionsListRequest
  object.

  Enums:
    ViewValueValuesEnum: The view that should be returned in the response.

  Fields:
    orderBy: Optional. The field to order the results by.
    pageSize: The maximum number of versions to return. Maximum page size is
      1,000.
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: The name of the parent resource whose versions will be listed.
    view: The view that should be returned in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view that should be returned in the response.

    Values:
      VERSION_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Includes basic information about the version, but not any related
        tags.
      FULL: Include everything.
    """
    VERSION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class ArtifactregistryProjectsLocationsRepositoriesPatchRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesPatchRequest object.

  Fields:
    name: The name of the repository, for example: `projects/p1/locations/us-
      central1/repositories/repo1`. For each location in a project, repository
      names must be unique.
    repository: A Repository resource to be passed as the request body.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  updateMask = _messages.StringField(3)


class ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesYumArtifactsImportRequest
  object.

  Fields:
    importYumArtifactsRequest: A ImportYumArtifactsRequest resource to be
      passed as the request body.
    parent: The name of the parent resource where the artifacts will be
      imported.
  """

  importYumArtifactsRequest = _messages.MessageField('ImportYumArtifactsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest(_messages.Message):
  r"""A ArtifactregistryProjectsLocationsRepositoriesYumArtifactsUploadRequest
  object.

  Fields:
    parent: The name of the parent resource where the artifacts will be
      uploaded.
    uploadYumArtifactRequest: A UploadYumArtifactRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  uploadYumArtifactRequest = _messages.MessageField('UploadYumArtifactRequest', 2)


class ArtifactregistryProjectsUpdateProjectSettingsRequest(_messages.Message):
  r"""A ArtifactregistryProjectsUpdateProjectSettingsRequest object.

  Fields:
    name: The name of the project's settings. Always of the form:
      projects/{project-id}/projectSettings In update request: never set In
      response: always set
    projectSettings: A ProjectSettings resource to be passed as the request
      body.
    updateMask: Field mask to support partial updates.
  """

  name = _messages.StringField(1, required=True)
  projectSettings = _messages.MessageField('ProjectSettings', 2)
  updateMask = _messages.StringField(3)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class DownloadFileResponse(_messages.Message):
  r"""The response to download a file."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GoogleDevtoolsArtifactregistryV1beta2File(_messages.Message):
  r"""Files store content that is potentially associated with Packages or
  Versions.

  Fields:
    createTime: Output only. The time when the File was created.
    hashes: The hashes of the file content.
    name: The name of the file, for example: `projects/p1/locations/us-
      central1/repositories/repo1/files/a%2Fb%2Fc.txt`. If the file ID part
      contains slashes, they are escaped.
    owner: The name of the Package or Version that owns this file, if any.
    sizeBytes: The size of the File in bytes.
    updateTime: Output only. The time when the File was last updated.
  """

  createTime = _messages.StringField(1)
  hashes = _messages.MessageField('Hash', 2, repeated=True)
  name = _messages.StringField(3)
  owner = _messages.StringField(4)
  sizeBytes = _messages.IntegerField(5)
  updateTime = _messages.StringField(6)


class Hash(_messages.Message):
  r"""A hash of file content.

  Enums:
    TypeValueValuesEnum: The algorithm used to compute the hash value.

  Fields:
    type: The algorithm used to compute the hash value.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The algorithm used to compute the hash value.

    Values:
      HASH_TYPE_UNSPECIFIED: Unspecified.
      SHA256: SHA256 hash.
      MD5: MD5 hash.
    """
    HASH_TYPE_UNSPECIFIED = 0
    SHA256 = 1
    MD5 = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class ImportAptArtifactsErrorInfo(_messages.Message):
  r"""Error information explaining why a package was not imported.

  Fields:
    error: The detailed error status.
    gcsSource: Google Cloud Storage location requested.
  """

  error = _messages.MessageField('Status', 1)
  gcsSource = _messages.MessageField('ImportAptArtifactsGcsSource', 2)


class ImportAptArtifactsGcsSource(_messages.Message):
  r"""Google Cloud Storage location where the artifacts currently reside.

  Fields:
    uris: Cloud Storage paths URI (e.g., gs://my_bucket//my_object).
    useWildcards: Supports URI wildcards for matching multiple objects from a
      single URI.
  """

  uris = _messages.StringField(1, repeated=True)
  useWildcards = _messages.BooleanField(2)


class ImportAptArtifactsMetadata(_messages.Message):
  r"""The operation metadata for importing artifacts."""


class ImportAptArtifactsRequest(_messages.Message):
  r"""The request to import new apt artifacts.

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('ImportAptArtifactsGcsSource', 1)


class ImportAptArtifactsResponse(_messages.Message):
  r"""The response message from importing APT artifacts.

  Fields:
    aptArtifacts: The Apt artifacts imported.
    errors: Detailed error info for packages that were not imported.
  """

  aptArtifacts = _messages.MessageField('AptArtifact', 1, repeated=True)
  errors = _messages.MessageField('ImportAptArtifactsErrorInfo', 2, repeated=True)


class ImportYumArtifactsErrorInfo(_messages.Message):
  r"""Error information explaining why a package was not imported.

  Fields:
    error: The detailed error status.
    gcsSource: Google Cloud Storage location requested.
  """

  error = _messages.MessageField('Status', 1)
  gcsSource = _messages.MessageField('ImportYumArtifactsGcsSource', 2)


class ImportYumArtifactsGcsSource(_messages.Message):
  r"""Google Cloud Storage location where the artifacts currently reside.

  Fields:
    uris: Cloud Storage paths URI (e.g., gs://my_bucket//my_object).
    useWildcards: Supports URI wildcards for matching multiple objects from a
      single URI.
  """

  uris = _messages.StringField(1, repeated=True)
  useWildcards = _messages.BooleanField(2)


class ImportYumArtifactsMetadata(_messages.Message):
  r"""The operation metadata for importing artifacts."""


class ImportYumArtifactsRequest(_messages.Message):
  r"""The request to import new yum artifacts.

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('ImportYumArtifactsGcsSource', 1)


class ImportYumArtifactsResponse(_messages.Message):
  r"""The response message from importing YUM artifacts.

  Fields:
    errors: Detailed error info for packages that were not imported.
    yumArtifacts: The yum artifacts imported.
  """

  errors = _messages.MessageField('ImportYumArtifactsErrorInfo', 1, repeated=True)
  yumArtifacts = _messages.MessageField('YumArtifact', 2, repeated=True)


class ListFilesResponse(_messages.Message):
  r"""The response from listing files.

  Fields:
    files: The files returned.
    nextPageToken: The token to retrieve the next page of files, or empty if
      there are no more files to return.
  """

  files = _messages.MessageField('GoogleDevtoolsArtifactregistryV1beta2File', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPackagesResponse(_messages.Message):
  r"""The response from listing packages.

  Fields:
    nextPageToken: The token to retrieve the next page of packages, or empty
      if there are no more packages to return.
    packages: The packages returned.
  """

  nextPageToken = _messages.StringField(1)
  packages = _messages.MessageField('Package', 2, repeated=True)


class ListRepositoriesResponse(_messages.Message):
  r"""The response from listing repositories.

  Fields:
    nextPageToken: The token to retrieve the next page of repositories, or
      empty if there are no more repositories to return.
    repositories: The repositories returned.
  """

  nextPageToken = _messages.StringField(1)
  repositories = _messages.MessageField('Repository', 2, repeated=True)


class ListTagsResponse(_messages.Message):
  r"""The response from listing tags.

  Fields:
    nextPageToken: The token to retrieve the next page of tags, or empty if
      there are no more tags to return.
    tags: The tags returned.
  """

  nextPageToken = _messages.StringField(1)
  tags = _messages.MessageField('Tag', 2, repeated=True)


class ListVersionsResponse(_messages.Message):
  r"""The response from listing versions.

  Fields:
    nextPageToken: The token to retrieve the next page of versions, or empty
      if there are no more versions to return.
    versions: The versions returned.
  """

  nextPageToken = _messages.StringField(1)
  versions = _messages.MessageField('Version', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MavenRepositoryConfig(_messages.Message):
  r"""MavenRepositoryConfig is maven related repository details. Provides
  additional configuration details for repositories of the maven format type.

  Enums:
    VersionPolicyValueValuesEnum: Version policy defines the versions that the
      registry will accept.

  Fields:
    allowSnapshotOverwrites: The repository with this flag will allow
      publishing the same snapshot versions.
    versionPolicy: Version policy defines the versions that the registry will
      accept.
  """

  class VersionPolicyValueValuesEnum(_messages.Enum):
    r"""Version policy defines the versions that the registry will accept.

    Values:
      VERSION_POLICY_UNSPECIFIED: VERSION_POLICY_UNSPECIFIED - the version
        policy is not defined. When the version policy is not defined, no
        validation is performed for the versions.
      RELEASE: RELEASE - repository will accept only Release versions.
      SNAPSHOT: SNAPSHOT - repository will accept only Snapshot versions.
    """
    VERSION_POLICY_UNSPECIFIED = 0
    RELEASE = 1
    SNAPSHOT = 2

  allowSnapshotOverwrites = _messages.BooleanField(1)
  versionPolicy = _messages.EnumField('VersionPolicyValueValuesEnum', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata type for longrunning-operations, currently empty."""


class Package(_messages.Message):
  r"""Packages are named collections of versions.

  Messages:
    AnnotationsValue: Optional. Client specified annotations.

  Fields:
    annotations: Optional. Client specified annotations.
    createTime: The time when the package was created.
    displayName: The display name of the package.
    name: The name of the package, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1`. If the package ID part
      contains slashes, the slashes are escaped.
    updateTime: The time when the package was last updated. This includes
      publishing a new version of the package.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ProjectSettings(_messages.Message):
  r"""The Artifact Registry settings that apply to a Project.

  Enums:
    LegacyRedirectionStateValueValuesEnum: The redirection state of the legacy
      repositories in this project.

  Fields:
    legacyRedirectionState: The redirection state of the legacy repositories
      in this project.
    name: The name of the project's settings. Always of the form:
      projects/{project-id}/projectSettings In update request: never set In
      response: always set
    pullPercent: The percentage of pull traffic to redirect from GCR to AR
      when using partial redirection.
  """

  class LegacyRedirectionStateValueValuesEnum(_messages.Enum):
    r"""The redirection state of the legacy repositories in this project.

    Values:
      REDIRECTION_STATE_UNSPECIFIED: No redirection status has been set.
      REDIRECTION_FROM_GCR_IO_DISABLED: Redirection is disabled.
      REDIRECTION_FROM_GCR_IO_ENABLED: Redirection is enabled.
      REDIRECTION_FROM_GCR_IO_FINALIZED: Redirection is enabled, and has been
        finalized so cannot be reverted.
      REDIRECTION_FROM_GCR_IO_PARTIAL: Redirection is partially enabled.
      REDIRECTION_FROM_GCR_IO_ENABLED_AND_COPYING: Redirection is enabled and
        missing images are copied from GCR
      REDIRECTION_FROM_GCR_IO_PARTIAL_AND_COPYING: Redirection is partially
        enabled and missing images are copied from GCR
    """
    REDIRECTION_STATE_UNSPECIFIED = 0
    REDIRECTION_FROM_GCR_IO_DISABLED = 1
    REDIRECTION_FROM_GCR_IO_ENABLED = 2
    REDIRECTION_FROM_GCR_IO_FINALIZED = 3
    REDIRECTION_FROM_GCR_IO_PARTIAL = 4
    REDIRECTION_FROM_GCR_IO_ENABLED_AND_COPYING = 5
    REDIRECTION_FROM_GCR_IO_PARTIAL_AND_COPYING = 6

  legacyRedirectionState = _messages.EnumField('LegacyRedirectionStateValueValuesEnum', 1)
  name = _messages.StringField(2)
  pullPercent = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Repository(_messages.Message):
  r"""A Repository for storing artifacts with a specific format.

  Enums:
    FormatValueValuesEnum: Optional. The format of packages that are stored in
      the repository.

  Messages:
    LabelsValue: Labels with user-defined metadata. This field may contain up
      to 64 entries. Label keys and values may be no longer than 63
      characters. Label keys must begin with a lowercase letter and may only
      contain lowercase letters, numeric characters, underscores, and dashes.

  Fields:
    createTime: Output only. The time when the repository was created.
    description: The user-provided description of the repository.
    format: Optional. The format of packages that are stored in the
      repository.
    kmsKeyName: The Cloud KMS resource name of the customer managed encryption
      key that's used to encrypt the contents of the Repository. Has the form:
      `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-
      key`. This value may not be changed after the Repository has been
      created.
    labels: Labels with user-defined metadata. This field may contain up to 64
      entries. Label keys and values may be no longer than 63 characters.
      Label keys must begin with a lowercase letter and may only contain
      lowercase letters, numeric characters, underscores, and dashes.
    mavenConfig: Maven repository config contains repository level
      configuration for the repositories of maven type.
    name: The name of the repository, for example: `projects/p1/locations/us-
      central1/repositories/repo1`. For each location in a project, repository
      names must be unique.
    satisfiesPzi: Output only. Whether or not this repository satisfies PZI.
    satisfiesPzs: Output only. Whether or not this repository satisfies PZS.
    sizeBytes: Output only. The size, in bytes, of all artifact storage in
      this repository. Repositories that are generally available or in public
      preview use this to calculate storage costs.
    updateTime: Output only. The time when the repository was last updated.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Optional. The format of packages that are stored in the repository.

    Values:
      FORMAT_UNSPECIFIED: Unspecified package format.
      DOCKER: Docker package format.
      MAVEN: Maven package format.
      NPM: NPM package format.
      APT: APT package format.
      YUM: YUM package format.
      GOOGET: GooGet package format.
      PYTHON: Python package format.
    """
    FORMAT_UNSPECIFIED = 0
    DOCKER = 1
    MAVEN = 2
    NPM = 3
    APT = 4
    YUM = 5
    GOOGET = 6
    PYTHON = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user-defined metadata. This field may contain up to 64
    entries. Label keys and values may be no longer than 63 characters. Label
    keys must begin with a lowercase letter and may only contain lowercase
    letters, numeric characters, underscores, and dashes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  format = _messages.EnumField('FormatValueValuesEnum', 3)
  kmsKeyName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  mavenConfig = _messages.MessageField('MavenRepositoryConfig', 6)
  name = _messages.StringField(7)
  satisfiesPzi = _messages.BooleanField(8)
  satisfiesPzs = _messages.BooleanField(9)
  sizeBytes = _messages.IntegerField(10)
  updateTime = _messages.StringField(11)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tag(_messages.Message):
  r"""Tags point to a version and represent an alternative name that can be
  used to access the version.

  Fields:
    name: The name of the tag, for example: "projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/tags/tag1". If the package
      part contains slashes, the slashes are escaped. The tag part can only
      have characters in [a-zA-Z0-9\-._~:@], anything else must be URL
      encoded.
    version: The name of the version the tag refers to, for example:
      `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/versions/sha256:5243811` If
      the package or version ID parts contain slashes, the slashes are
      escaped.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UploadAptArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadAptArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading artifacts."""


class UploadAptArtifactRequest(_messages.Message):
  r"""The request to upload an artifact."""


class UploadAptArtifactResponse(_messages.Message):
  r"""The response of the completed artifact upload operation. This response
  is contained in the Operation and available to users.

  Fields:
    aptArtifacts: The Apt artifacts updated.
  """

  aptArtifacts = _messages.MessageField('AptArtifact', 1, repeated=True)


class UploadYumArtifactMediaResponse(_messages.Message):
  r"""The response to upload an artifact.

  Fields:
    operation: Operation to be returned to the user.
  """

  operation = _messages.MessageField('Operation', 1)


class UploadYumArtifactMetadata(_messages.Message):
  r"""The operation metadata for uploading artifacts."""


class UploadYumArtifactRequest(_messages.Message):
  r"""The request to upload an artifact."""


class UploadYumArtifactResponse(_messages.Message):
  r"""The response of the completed artifact upload operation. This response
  is contained in the Operation and available to users.

  Fields:
    yumArtifacts: The Yum artifacts updated.
  """

  yumArtifacts = _messages.MessageField('YumArtifact', 1, repeated=True)


class Version(_messages.Message):
  r"""The body of a version resource. A version resource represents a
  collection of components, such as files and other data. This may correspond
  to a version in many package management schemes.

  Messages:
    MetadataValue: Output only. Repository-specific Metadata stored against
      this version. The fields returned are defined by the underlying
      repository-specific resource. Currently, the resources could be:
      DockerImage MavenArtifact

  Fields:
    createTime: The time when the version was created.
    description: Optional. Description of the version, as specified in its
      metadata.
    metadata: Output only. Repository-specific Metadata stored against this
      version. The fields returned are defined by the underlying repository-
      specific resource. Currently, the resources could be: DockerImage
      MavenArtifact
    name: The name of the version, for example: `projects/p1/locations/us-
      central1/repositories/repo1/packages/pkg1/versions/art1`. If the package
      or version ID parts contain slashes, the slashes are escaped.
    relatedTags: Output only. A list of related tags. Will contain up to 100
      tags that reference this version.
    updateTime: The time when the version was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Output only. Repository-specific Metadata stored against this version.
    The fields returned are defined by the underlying repository-specific
    resource. Currently, the resources could be: DockerImage MavenArtifact

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  relatedTags = _messages.MessageField('Tag', 5, repeated=True)
  updateTime = _messages.StringField(6)


class YumArtifact(_messages.Message):
  r"""A detailed representation of a Yum artifact.

  Enums:
    PackageTypeValueValuesEnum: Output only. An artifact is a binary or source
      package.

  Fields:
    architecture: Output only. Operating system architecture of the artifact.
    name: Output only. The Artifact Registry resource name of the artifact.
    packageName: Output only. The yum package name of the artifact.
    packageType: Output only. An artifact is a binary or source package.
  """

  class PackageTypeValueValuesEnum(_messages.Enum):
    r"""Output only. An artifact is a binary or source package.

    Values:
      PACKAGE_TYPE_UNSPECIFIED: Package type is not specified.
      BINARY: Binary package (.rpm).
      SOURCE: Source package (.srpm).
    """
    PACKAGE_TYPE_UNSPECIFIED = 0
    BINARY = 1
    SOURCE = 2

  architecture = _messages.StringField(1)
  name = _messages.StringField(2)
  packageName = _messages.StringField(3)
  packageType = _messages.EnumField('PackageTypeValueValuesEnum', 4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ArtifactregistryProjectsLocationsRepositoriesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
