"""Generated message classes for anthosevents version v1beta1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'anthosevents'


class APIVersionKind(_messages.Message):
  r"""APIVersionKind is an APIVersion and Kind tuple.

  Fields:
    apiVersion: APIVersion - the API version of the resource to watch.
    kind: Kind of the resource to watch. More info:
      https://git.k8s.io/community/contributors/devel/sig-architecture/api-
      conventions.md#types-kinds
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)


class APIVersionKindSelector(_messages.Message):
  r"""APIVersionKindSelector is an APIVersion Kind tuple with a LabelSelector.

  Fields:
    apiVersion: APIVersion - the API version of the resource to watch.
    kind: Kind of the resource to watch. More info:
      https://git.k8s.io/community/contributors/devel/sig-architecture/api-
      conventions.md#types-kinds
    selector: LabelSelector filters this source to objects to those resources
      pass the label selector. More info:
      http://kubernetes.io/docs/concepts/overview/working-with-
      objects/labels/#label-selectors
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  selector = _messages.MessageField('LabelSelector', 3)


class Addressable(_messages.Message):
  r"""From
  https://github.com/knative/pkg/blob/master/apis/duck/v1/addressable_types.go
  Addressable provides a generic mechanism for a custom resource definition to
  indicate a destination for message delivery. Addressable is the schema for
  the destination information. This is typically stored in the object's
  `status`, as this information may be generated by the controller.

  Fields:
    url: A string attribute.
  """

  url = _messages.StringField(1)


class AnthoseventsBrokersListRequest(_messages.Message):
  r"""A AnthoseventsBrokersListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: A integer attribute.
    parent: The namespace name.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsCustomresourcedefinitionsListRequest(_messages.Message):
  r"""A AnthoseventsCustomresourcedefinitionsListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: A integer attribute.
    parent: The project ID or project number from which the storages should be
      listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesApiserversourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesApiserversourcesCreateRequest object.

  Fields:
    apiServerSource: A ApiServerSource resource to be passed as the request
      body.
    parent: The project ID or project number in which this apiserversource
      should be created.
  """

  apiServerSource = _messages.MessageField('ApiServerSource', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesApiserversourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesApiserversourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: The name of the apiserversource being deleted. If needed, replace
      {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesApiserversourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesApiserversourcesGetRequest object.

  Fields:
    name: The name of the apiserversource being retrieved. If needed, replace
      {namespace_id} with the project ID.
    region: The region in which this resource exists.
  """

  name = _messages.StringField(1, required=True)
  region = _messages.StringField(2)


class AnthoseventsNamespacesApiserversourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesApiserversourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the apiserversources
      should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesApiserversourcesReplaceApiServerSourceRequest(_messages.Message):
  r"""A AnthoseventsNamespacesApiserversourcesReplaceApiServerSourceRequest
  object.

  Fields:
    apiServerSource: A ApiServerSource resource to be passed as the request
      body.
    name: The name of the apiserversource being retrieved. If needed, replace
      {namespace_id} with the project ID.
    region: The region in which this resource exists.
  """

  apiServerSource = _messages.MessageField('ApiServerSource', 1)
  name = _messages.StringField(2, required=True)
  region = _messages.StringField(3)


class AnthoseventsNamespacesBrokersCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesBrokersCreateRequest object.

  Fields:
    broker: A Broker resource to be passed as the request body.
    parent: The namespace name.
  """

  broker = _messages.MessageField('Broker', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesBrokersDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesBrokersDeleteRequest object.

  Fields:
    name: The relative name of the broker being deleted, including the
      namespace
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesBrokersGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesBrokersGetRequest object.

  Fields:
    name: The name of the Broker being retrieved.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesBrokersListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesBrokersListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: A integer attribute.
    parent: The namespace name.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesCloudauditlogssourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudauditlogssourcesCreateRequest object.

  Fields:
    cloudAuditLogsSource: A CloudAuditLogsSource resource to be passed as the
      request body.
    parent: The project ID or project number in which this
      cloudauditlogssource should be created.
  """

  cloudAuditLogsSource = _messages.MessageField('CloudAuditLogsSource', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudauditlogssourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudauditlogssourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: The name of the cloudauditlogssource being deleted. If needed,
      replace {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesCloudauditlogssourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudauditlogssourcesGetRequest object.

  Fields:
    name: The name of the cloudauditlogssource being retrieved. If needed,
      replace {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudauditlogssourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudauditlogssourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the
      cloudauditlogssources should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesCloudauditlogssourcesReplaceCloudAuditLogsSourceRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudauditlogssourcesReplaceCloudAuditLogsSource
  Request object.

  Fields:
    cloudAuditLogsSource: A CloudAuditLogsSource resource to be passed as the
      request body.
    name: The name of the cloudauditlogssource being retrieved. If needed,
      replace {namespace_id} with the project ID.
  """

  cloudAuditLogsSource = _messages.MessageField('CloudAuditLogsSource', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudpubsubsourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudpubsubsourcesCreateRequest object.

  Fields:
    cloudPubSubSource: A CloudPubSubSource resource to be passed as the
      request body.
    parent: The project ID or project number in which this cloudpubsubsource
      should be created.
  """

  cloudPubSubSource = _messages.MessageField('CloudPubSubSource', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudpubsubsourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudpubsubsourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: The name of the cloudpubsubsource being deleted. If needed, replace
      {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesCloudpubsubsourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudpubsubsourcesGetRequest object.

  Fields:
    name: The name of the cloudpubsubsource being retrieved. If needed,
      replace {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudpubsubsourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudpubsubsourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the cloudpubsubsources
      should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesCloudpubsubsourcesReplaceCloudPubSubSourceRequest(_messages.Message):
  r"""A
  AnthoseventsNamespacesCloudpubsubsourcesReplaceCloudPubSubSourceRequest
  object.

  Fields:
    cloudPubSubSource: A CloudPubSubSource resource to be passed as the
      request body.
    name: The name of the cloudpubsubsource being retrieved. If needed,
      replace {namespace_id} with the project ID.
  """

  cloudPubSubSource = _messages.MessageField('CloudPubSubSource', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudschedulersourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudschedulersourcesCreateRequest object.

  Fields:
    cloudSchedulerSource: A CloudSchedulerSource resource to be passed as the
      request body.
    parent: Required. The project ID or project number in which this
      cloudschedulersource should be created.
  """

  cloudSchedulerSource = _messages.MessageField('CloudSchedulerSource', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudschedulersourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudschedulersourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: Required. The name of the cloudschedulersource being deleted. If
      needed, replace {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesCloudschedulersourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudschedulersourcesGetRequest object.

  Fields:
    name: Required. The name of the cloudschedulersource being retrieved. If
      needed, replace {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudschedulersourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudschedulersourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: Required. The project ID or project number from which the
      cloudschedulersources should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesCloudschedulersourcesReplaceCloudSchedulerSourceRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudschedulersourcesReplaceCloudSchedulerSource
  Request object.

  Fields:
    cloudSchedulerSource: A CloudSchedulerSource resource to be passed as the
      request body.
    name: Required. The name of the cloudschedulersource being retrieved. If
      needed, replace {namespace_id} with the project ID.
  """

  cloudSchedulerSource = _messages.MessageField('CloudSchedulerSource', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudstoragesourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudstoragesourcesCreateRequest object.

  Fields:
    cloudStorageSource: A CloudStorageSource resource to be passed as the
      request body.
    parent: Required. The project ID or project number in which this
      cloudstoragesource should be created.
  """

  cloudStorageSource = _messages.MessageField('CloudStorageSource', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudstoragesourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudstoragesourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: Required. The name of the cloudstoragesource being deleted. If
      needed, replace {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesCloudstoragesourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudstoragesourcesGetRequest object.

  Fields:
    name: Required. The name of the cloudstoragesource being retrieved. If
      needed, replace {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudstoragesourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudstoragesourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: Required. The project ID or project number from which the
      cloudstoragesources should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesCloudstoragesourcesReplaceCloudStorageSourceRequest(_messages.Message):
  r"""A
  AnthoseventsNamespacesCloudstoragesourcesReplaceCloudStorageSourceRequest
  object.

  Fields:
    cloudStorageSource: A CloudStorageSource resource to be passed as the
      request body.
    name: Required. The name of the cloudstoragesource being retrieved. If
      needed, replace {namespace_id} with the project ID.
  """

  cloudStorageSource = _messages.MessageField('CloudStorageSource', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCustomresourcedefinitionsGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCustomresourcedefinitionsGetRequest object.

  Fields:
    name: The name of the CustomResourceDefinition being retrieved. If needed,
      replace {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesPingsourcesCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesPingsourcesCreateRequest object.

  Fields:
    parent: The namespace name.
    pingSource: A PingSource resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  pingSource = _messages.MessageField('PingSource', 2)


class AnthoseventsNamespacesPingsourcesDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesPingsourcesDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    kind: Cloud Run currently ignores this parameter.
    name: The name of the pingsource being deleted. If needed, replace
      {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesPingsourcesGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesPingsourcesGetRequest object.

  Fields:
    name: The name of the pingsource being retrieved. If needed, replace
      {namespace_id} with the project ID.
    region: The region in which this resource exists.
  """

  name = _messages.StringField(1, required=True)
  region = _messages.StringField(2)


class AnthoseventsNamespacesPingsourcesListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesPingsourcesListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the pingsources should
      be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesPingsourcesReplacePingSourceRequest(_messages.Message):
  r"""A AnthoseventsNamespacesPingsourcesReplacePingSourceRequest object.

  Fields:
    name: The name of the pingsource being retrieved. If needed, replace
      {namespace_id} with the project ID.
    pingSource: A PingSource resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  pingSource = _messages.MessageField('PingSource', 2)


class AnthoseventsNamespacesTriggersCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesTriggersCreateRequest object.

  Fields:
    parent: The project ID or project number in which this trigger should be
      created.
    trigger: A Trigger resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  trigger = _messages.MessageField('Trigger', 2)


class AnthoseventsNamespacesTriggersDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesTriggersDeleteRequest object.

  Fields:
    apiVersion: Specifies the target version for the cascading deletion
      policy. Events for Cloud Run currently ignores this parameter.
    kind: Specifies the cascading deletion policy, in conjunction of
      `progation_policy` field. Events for Cloud Run currently ignores this
      parameter.
    name: The name of the trigger being deleted. If needed, replace
      {namespace_id} with the project ID.
    propagationPolicy: Specifies the propagation policy of delete. Events for
      Cloud Run currently ignores this setting, and deletes in the background.
      Please see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class AnthoseventsNamespacesTriggersGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesTriggersGetRequest object.

  Fields:
    name: The name of the trigger being retrieved. If needed, replace
      {namespace_id} with the project ID.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesTriggersListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesTriggersListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Events for Cloud Run.
    includeUninitialized: If true, partially initialized resources are
      included in the response. Not currently used by Events for Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the triggers should be
      listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Events for Cloud
      Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Events for Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class AnthoseventsNamespacesTriggersReplaceTriggerRequest(_messages.Message):
  r"""A AnthoseventsNamespacesTriggersReplaceTriggerRequest object.

  Fields:
    name: The name of the trigger being retrieved. If needed, replace
      {namespace_id} with the project ID.
    trigger: A Trigger resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  trigger = _messages.MessageField('Trigger', 2)


class AnthoseventsTriggersListRequest(_messages.Message):
  r"""A AnthoseventsTriggersListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Events for Cloud Run.
    includeUninitialized: If true, partially initialized resources are
      included in the response. Not currently used by Events for Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The project ID or project number from which the triggers should be
      listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Events for Cloud
      Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Events for Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class ApiServerSource(_messages.Message):
  r"""A ApiServerSource object.

  Fields:
    apiVersion: The API version for this call such as
      "sources.knative.dev/v1beta1".
    kind: The kind of resource, in this case "ApiServerSource".
    metadata: Metadata associated with this ApiServerSource.
    spec: Spec defines the desired state of the ApiServerSource.
    status: Status represents the current state of the ApiServerSource. This
      data may be out of date. +optional
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('ApiServerSourceSpec', 4)
  status = _messages.MessageField('ApiServerSourceStatus', 5)


class ApiServerSourceSpec(_messages.Message):
  r"""The desired state of the ApiServerSource.

  Fields:
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink.
    mode: EventMode controls the format of the event. `Reference` sends a
      dataref event type for the resource under watch. `Resource` send the
      full resource lifecycle event. Defaults to `Reference`
    owner: ResourceOwner is an additional filter to only track resources that
      are owned by a specific resource type. If ResourceOwner matches
      Resources[n] then Resources[n] is allowed to pass the ResourceOwner
      filter.
    resources: Resource are the resources this source will track and send
      related lifecycle events from the Kubernetes ApiServer, with an optional
      label selector to help filter.
    serviceAccountName: ServiceAccountName is the k8s service account which
      binds to a google service account. This google service account has
      required permissions to poll from a Cloud Pub/Sub subscription. If not
      specified, defaults to use secret.
    sink: Sink is a reference to an object that will resolve to a uri to use
      as the sink.
  """

  ceOverrides = _messages.MessageField('CloudEventOverrides', 1)
  mode = _messages.StringField(2)
  owner = _messages.MessageField('APIVersionKind', 3)
  resources = _messages.MessageField('APIVersionKindSelector', 4, repeated=True)
  serviceAccountName = _messages.StringField(5)
  sink = _messages.MessageField('Destination', 6)


class ApiServerSourceStatus(_messages.Message):
  r"""ApiServerSourceStatus represents the current state of a ApiServerSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    conditions: Conditions the latest available observations of a resource's
      current state.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudPubSubSource that was last processed by the controller.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  observedGeneration = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Broker(_messages.Message):
  r"""Broker collects a pool of events that are consumable using Triggers.
  Brokers provide a well-known endpoint for event delivery that senders can
  use with minimal knowledge of the event routing strategy. Subscribers use
  Triggers to request delivery of events from a Broker's pool to a specific
  URL or Addressable endpoint.

  Fields:
    apiVersion: The API version for this call.
    kind: The kind of resource, in this case always "Broker".
    metadata: Metadata associated with this Broker.
    spec: A BrokerSpec attribute.
    status: A BrokerStatus attribute.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('BrokerSpec', 4)
  status = _messages.MessageField('BrokerStatus', 5)


class BrokerSpec(_messages.Message):
  r"""A BrokerSpec object.

  Fields:
    config: Config is a KReference to the configuration that specifies
      configuration options for this Broker. For example, this could be a
      pointer to a ConfigMap.
    delivery: Delivery is the delivery specification for Events within the
      Broker mesh. This includes things like retries, DLQ, etc.
  """

  config = _messages.MessageField('KReference', 1)
  delivery = _messages.MessageField('DeliverySpec', 2)


class BrokerStatus(_messages.Message):
  r"""A BrokerStatus object.

  Fields:
    address: Broker is Addressable. It exposes the endpoint as an URI to get
      events delivered into the Broker mesh.
    conditions: Conditions the latest available observations of a resource's
      current state.
    observedGeneration: ObservedGeneration is the 'Generation' of the Broker
      that was last processed by the controller.
  """

  address = _messages.MessageField('Addressable', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  observedGeneration = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class CloudAuditLogsSource(_messages.Message):
  r"""A CloudAuditLogsSource object.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "CloudAuditLogsSource".
    metadata: Metadata associated with this CloudAuditLogsSource.
    spec: Spec defines the desired state of the CloudAuditLogsSource.
    status: Status represents the current state of the CloudAuditLogsSource.
      This data may be out of date. +optional
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudAuditLogsSourceSpec', 4)
  status = _messages.MessageField('CloudAuditLogsSourceStatus', 5)


class CloudAuditLogsSourceSpec(_messages.Message):
  r"""The desired state of the CloudAuditLogsSource.

  Fields:
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink.
    methodName: Required. The name of the service method or operation. For API
      calls, this should be the name of the API method. Required.
    project: Optional. Project is the ID of the Google Cloud Project that the
      CloudPubSubSource Topic exists in. If omitted, defaults to same as the
      cluster.
    resourceName: Optional. The resource or collection that is the target of
      the operation. The name is a scheme-less URI, not including the API
      service name.
    secret: Optional. Secret is the credential to use to create the Scheduler
      Job. If not specified, defaults to: Name: google-cloud-key Key: key.json
    serviceAccountName: Optional. ServiceAccountName is the k8s service
      account which binds to a google service account. This google service
      account has required permissions to poll from a Cloud Pub/Sub
      subscription. If not specified, defaults to use secret.
    serviceName: Required. The GCP service providing audit logs. Required.
    sink: Sink is a reference to an object that will resolve to a uri to use
      as the sink.
  """

  ceOverrides = _messages.MessageField('CloudEventOverrides', 1)
  methodName = _messages.StringField(2)
  project = _messages.StringField(3)
  resourceName = _messages.StringField(4)
  secret = _messages.MessageField('SecretKeySelector', 5)
  serviceAccountName = _messages.StringField(6)
  serviceName = _messages.StringField(7)
  sink = _messages.MessageField('Destination', 8)


class CloudAuditLogsSourceStatus(_messages.Message):
  r"""CloudAuditLogsSourceStatus represents the current state of a
  CloudAuditLogsSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    ceAttributes: CloudEventAttributes are the specific attributes that the
      Source uses as part of its CloudEvents.
    conditions: Conditions the latest available observations of a resource's
      current state.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudPubSubSource that was last processed by the controller.
    projectId: ProjectID is the project ID of the Topic, might have been
      resolved.
    serviceAccountName: ServiceAccountName is the k8s service account
      associated with Google service account.
    sinkUri: SinkURI is the current active sink URI that has been configured
      for the Source.
    stackDriverSink: ID of the Stackdriver sink used to publish audit log
      messages.
    subscriptionId: SubscriptionID is the created subscription ID.
    topicId: TopicID where the notifications are sent to.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  ceAttributes = _messages.MessageField('CloudEventAttributes', 2, repeated=True)
  conditions = _messages.MessageField('Condition', 3, repeated=True)
  observedGeneration = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(5)
  serviceAccountName = _messages.StringField(6)
  sinkUri = _messages.StringField(7)
  stackDriverSink = _messages.StringField(8)
  subscriptionId = _messages.StringField(9)
  topicId = _messages.StringField(10)


class CloudEventAttributes(_messages.Message):
  r"""CloudEventAttributes are the specific attributes that the Source uses as
  part of its CloudEvents.

  Fields:
    source: Source is the CloudEvents source attribute.
    type: Type refers to the CloudEvent type attribute.
  """

  source = _messages.StringField(1)
  type = _messages.StringField(2)


class CloudEventOverrides(_messages.Message):
  r"""CloudEventOverrides defines arguments for a Source that control the
  output format of the CloudEvents produced by the Source.

  Messages:
    ExtensionsValue: Extensions specify what attribute are added or overridden
      on the outbound event. Each `Extensions` key-value pair are set on the
      event as an attribute extension independently. +optional

  Fields:
    extensions: Extensions specify what attribute are added or overridden on
      the outbound event. Each `Extensions` key-value pair are set on the
      event as an attribute extension independently. +optional
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValue(_messages.Message):
    r"""Extensions specify what attribute are added or overridden on the
    outbound event. Each `Extensions` key-value pair are set on the event as
    an attribute extension independently. +optional

    Messages:
      AdditionalProperty: An additional property for a ExtensionsValue object.

    Fields:
      additionalProperties: Additional properties of type ExtensionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  extensions = _messages.MessageField('ExtensionsValue', 1)


class CloudPubSubSource(_messages.Message):
  r"""A CloudPubSubSource object.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "CloudPubSubSource".
    metadata: Metadata associated with this CloudPubSubSource.
    spec: Spec defines the desired state of the CloudPubSubSource.
    status: Status represents the current state of the CloudPubSubSource. This
      data may be out of date. +optional
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudPubSubSourceSpec', 4)
  status = _messages.MessageField('CloudPubSubSourceStatus', 5)


class CloudPubSubSourceSpec(_messages.Message):
  r"""The desired state of the CloudPubSubSource.

  Fields:
    ackDeadline: AckDeadline is the default maximum time after a subscriber
      receives a message before the subscriber should acknowledge the message.
      Defaults to 30 seconds ('30s'). +optional
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink. +optional
    project: Project is the ID of the Google Cloud Project that the
      CloudPubSubSource Topic exists in. If omitted, defaults to same as the
      cluster.
    retainAckedMessages: RetainAckedMessages defines whether to retain
      acknowledged messages. If true, acknowledged messages will not be
      expunged until they fall out of the RetentionDuration window.
    retentionDuration: RetentionDuration defines how long to retain messages
      in backlog, from the time of publish. If RetainAckedMessages is true,
      this duration affects the retention of acknowledged messages, otherwise
      only unacknowledged messages are retained. Cannot be longer than 7 days
      or shorter than 10 minutes. Defaults to 7 days ('7d'). +optional
    secret: Secret is the credential to use to create the Scheduler Job. If
      not specified, defaults to: Name: google-cloud-key Key: key.json
    serviceAccountName: ServiceAccountName is the k8s service account which
      binds to a google service account. This google service account has
      required permissions to poll from a Cloud Pub/Sub subscription. If not
      specified, defaults to use secret.
    sink: Sink is a reference to an object that will resolve to a domain name
      or a URI directly to use as the sink.
    topic: Topic is the ID of the CloudPubSubSource Topic to Subscribe to. It
      must be in the form of the unique identifier within the project, not the
      entire name. E.g. it must be 'laconia', not 'projects/my-
      proj/topics/laconia'.
  """

  ackDeadline = _messages.StringField(1)
  ceOverrides = _messages.MessageField('CloudEventOverrides', 2)
  project = _messages.StringField(3)
  retainAckedMessages = _messages.BooleanField(4)
  retentionDuration = _messages.StringField(5)
  secret = _messages.MessageField('SecretKeySelector', 6)
  serviceAccountName = _messages.StringField(7)
  sink = _messages.MessageField('Destination', 8)
  topic = _messages.StringField(9)


class CloudPubSubSourceStatus(_messages.Message):
  r"""CloudPubSubSourceStatus represents the current state of a
  CloudPubSubSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    ceAttributes: CloudEventAttributes are the specific attributes that the
      Source uses as part of its CloudEvents.
    conditions: Conditions the latest available observations of a resource's
      current state.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudPubSubSource that was last processed by the controller.
    projectId: ProjectID is the project ID of the Topic, might have been
      resolved.
    serviceAccountName: ServiceAccountName is the k8s service account
      associated with Google service account.
    sinkUri: SinkURI is the current active sink URI that has been configured
      for the Source. +optional
    subscriptionId: SubscriptionID is the created subscription ID.
    topicId: TopicID where the notifications are sent to.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  ceAttributes = _messages.MessageField('CloudEventAttributes', 2, repeated=True)
  conditions = _messages.MessageField('Condition', 3, repeated=True)
  observedGeneration = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(5)
  serviceAccountName = _messages.StringField(6)
  sinkUri = _messages.StringField(7)
  subscriptionId = _messages.StringField(8)
  topicId = _messages.StringField(9)


class CloudSchedulerSource(_messages.Message):
  r"""The CloudSchedulerSource resource.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "CloudSchedulerSource".
    metadata: Metadata associated with this CloudSchedulerSource.
    spec: Spec defines the desired state of the CloudSchedulerSource.
    status: Status represents the current state of the CloudSchedulerSource.
      This data may be out of date.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudSchedulerSourceSpec', 4)
  status = _messages.MessageField('CloudSchedulerSourceStatus', 5)


class CloudSchedulerSourceSpec(_messages.Message):
  r"""The desired state of the CloudSchedulerSource.

  Fields:
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink.
    data: Data to send in the payload of the Event.
    location: Location to create the Scheduler job in.
    project: Project is the ID of the Google Cloud Project that the
      CloudPubSubSource Topic exists in. If omitted, defaults to same as the
      cluster.
    schedule: Schedule in cron format, for example: "* * * * *" would be run
      every minute.
    secret: Secret is the credential to use to create the Scheduler Job. If
      not specified, defaults to: Name: google-cloud-key Key: key.json
    serviceAccountName: Optional. ServiceAccountName is the k8s service
      account which binds to a google service account. This google service
      account has required permissions to poll from a Cloud Pub/Sub
      subscription. If not specified, defaults to use secret.
    sink: Sink is a reference to an object that will resolve to a domain name
      or a URI directly to use as the sink.
  """

  ceOverrides = _messages.MessageField('CloudEventOverrides', 1)
  data = _messages.StringField(2)
  location = _messages.StringField(3)
  project = _messages.StringField(4)
  schedule = _messages.StringField(5)
  secret = _messages.MessageField('SecretKeySelector', 6)
  serviceAccountName = _messages.StringField(7)
  sink = _messages.MessageField('Destination', 8)


class CloudSchedulerSourceStatus(_messages.Message):
  r"""CloudSchedulerSourceStatus represents the current state of a
  CloudSchedulerSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    ceAttributes: CloudEventAttributes are the specific attributes that the
      Source uses as part of its CloudEvents.
    conditions: Array of observed CloudSchedulerSourceConditions, indicating
      the current state of the CloudSchedulerSource.
    jobName: JobName is the name of the created scheduler Job on success.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudSchedulerSource that was last processed by the controller.
    projectId: ProjectID is the project ID of the Topic, might have been
      resolved.
    serviceAccountName: ServiceAccountName is the k8s service account
      associated with Google service account.
    sinkUri: SinkURI is the current active sink URI that has been configured
      for the Source.
    subscriptionId: SubscriptionID is the created subscription ID.
    topicId: TopicID where the notifications are sent to.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  ceAttributes = _messages.MessageField('CloudEventAttributes', 2, repeated=True)
  conditions = _messages.MessageField('Condition', 3, repeated=True)
  jobName = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(6)
  serviceAccountName = _messages.StringField(7)
  sinkUri = _messages.StringField(8)
  subscriptionId = _messages.StringField(9)
  topicId = _messages.StringField(10)


class CloudStorageSource(_messages.Message):
  r"""The CloudStorageSource resource.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "CloudStorageSource".
    metadata: Metadata associated with this CloudStorageSource.
    spec: Spec defines the desired state of the CloudStorageSource.
    status: Status represents the current state of the CloudStorageSource.
      This data may be out of date.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudStorageSourceSpec', 4)
  status = _messages.MessageField('CloudStorageSourceStatus', 5)


class CloudStorageSourceSpec(_messages.Message):
  r"""The desired state of the CloudStorageSource.

  Fields:
    bucket: Bucket to subscribe to.
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink.
    eventTypes: EventTypes to subscribe to. If unspecified, then subscribe to
      all events.
    objectNamePrefix: ObjectNamePrefix limits the notifications to objects
      with this prefix.
    payloadFormat: PayloadFormat specifies the contents of the message
      payload. See https://cloud.google.com/storage/docs/pubsub-
      notifications#payload.
    project: Project is the ID of the Google Cloud Project that the PubSub
      Topic exists in. If omitted, defaults to same as the cluster.
    secret: Secret is the credential to use to create the Scheduler Job. If
      not specified, defaults to: Name: google-cloud-key Key: key.json
    serviceAccountName: ServiceAccountName is the k8s service account which
      binds to a google service account. This google service account has
      required permissions to poll from a Cloud Pub/Sub subscription. If not
      specified, defaults to use secret.
    sink: Sink is a reference to an object that will resolve to a domain name
      or a URI directly to use as the sink.
  """

  bucket = _messages.StringField(1)
  ceOverrides = _messages.MessageField('CloudEventOverrides', 2)
  eventTypes = _messages.StringField(3, repeated=True)
  objectNamePrefix = _messages.StringField(4)
  payloadFormat = _messages.StringField(5)
  project = _messages.StringField(6)
  secret = _messages.MessageField('SecretKeySelector', 7)
  serviceAccountName = _messages.StringField(8)
  sink = _messages.MessageField('Destination', 9)


class CloudStorageSourceStatus(_messages.Message):
  r"""CloudStorageSourceStatus represents the current state of a
  CloudStorageSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    ceAttributes: CloudEventAttributes are the specific attributes that the
      Source uses as part of its CloudEvents.
    conditions: Conditions the latest available observations of a resource's
      current state.
    notificationId: NotificationID is the ID that GCS identifies this
      notification as.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudPubSubSource that was last processed by the controller.
    projectId: Project is the ID of the Google Cloud Project that the PubSub
      Topic exists in. If omitted, defaults to same as the cluster.
    serviceAccountName: ServiceAccountName is the k8s service account
      associated with Google service account.
    sinkUri: SinkURI is the current active sink URI that has been configured
      for the Source.
    subscriptionId: SubscriptionID is the created subscription ID.
    topicId: TopicID where the notifications are sent to.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  ceAttributes = _messages.MessageField('CloudEventAttributes', 2, repeated=True)
  conditions = _messages.MessageField('Condition', 3, repeated=True)
  notificationId = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(6)
  serviceAccountName = _messages.StringField(7)
  sinkUri = _messages.StringField(8)
  subscriptionId = _messages.StringField(9)
  topicId = _messages.StringField(10)


class Condition(_messages.Message):
  r"""Condition defines a generic condition for a Resource

  Fields:
    lastTransitionTime: Optional. Last time the condition transitioned from
      one status to another.
    message: Optional. Human readable message indicating details about the
      current status.
    reason: Optional. One-word CamelCase reason for the condition's last
      transition.
    severity: Optional. How to interpret failures of this condition, one of
      Error, Warning, Info
    status: Status of the condition, one of True, False, Unknown.
    type: type is used to communicate the status of the reconciliation
      process. See also: https://github.com/knative/serving/blob/master/docs/s
      pec/errors.md#error-conditions-and-reporting Types common to all
      resources include: * "Ready": True when the Resource is ready.
  """

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  severity = _messages.StringField(4)
  status = _messages.StringField(5)
  type = _messages.StringField(6)


class CustomResourceColumnDefinition(_messages.Message):
  r"""CustomResourceColumnDefinition specifies a column for server side
  printing.

  Fields:
    description: description is a human readable description of this column.
      +optional
    format: format is an optional OpenAPI type definition for this column. The
      'name' format is applied to the primary identifier column to assist in
      clients identifying column is the resource name. See
      https://github.com/OAI/OpenAPI-
      Specification/blob/master/versions/2.0.md#data-types for more. +optional
    jsonPath: JSONPath is a simple JSON path, i.e. with array notation.
    name: name is a human readable name for the column.
    priority: priority is an integer defining the relative importance of this
      column compared to others. Lower numbers are considered higher priority.
      Columns that may be omitted in limited space scenarios should be given a
      higher priority. +optional
    type: type is an OpenAPI type definition for this column. See
      https://github.com/OAI/OpenAPI-
      Specification/blob/master/versions/2.0.md#data-types for more.
  """

  description = _messages.StringField(1)
  format = _messages.StringField(2)
  jsonPath = _messages.StringField(3)
  name = _messages.StringField(4)
  priority = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  type = _messages.StringField(6)


class CustomResourceDefinition(_messages.Message):
  r"""CustomResourceDefinition represents a resource that should be exposed on
  the API server. Its name MUST be in the format <.spec.name>.<.spec.group>.

  Fields:
    apiVersion: The API version for this call such as
      "k8s.apiextensions.io/v1beta1".
    kind: The kind of resource, in this case always
      "CustomResourceDefinition".
    metadata: Metadata associated with this CustomResourceDefinition.
    spec: Spec describes how the user wants the resources to appear
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CustomResourceDefinitionSpec', 4)


class CustomResourceDefinitionNames(_messages.Message):
  r"""CustomResourceDefinitionNames indicates the names to serve this
  CustomResourceDefinition

  Fields:
    categories: Categories is a list of grouped resources custom resources
      belong to (e.g. 'all') +optional
    kind: Kind is the serialized kind of the resource. It is normally
      CamelCase and singular.
    listKind: ListKind is the serialized kind of the list for this resource.
      Defaults to List. +optional
    plural: Plural is the plural name of the resource to serve. It must match
      the name of the CustomResourceDefinition-registration too: plural.group
      and it must be all lowercase.
    shortNames: ShortNames are short names for the resource. It must be all
      lowercase. +optional
    singular: Singular is the singular name of the resource. It must be all
      lowercase Defaults to lowercased +optional
  """

  categories = _messages.StringField(1, repeated=True)
  kind = _messages.StringField(2)
  listKind = _messages.StringField(3)
  plural = _messages.StringField(4)
  shortNames = _messages.StringField(5, repeated=True)
  singular = _messages.StringField(6)


class CustomResourceDefinitionSpec(_messages.Message):
  r"""CustomResourceDefinitionSpec describes how a user wants their resource
  to appear

  Fields:
    additionalPrinterColumns: AdditionalPrinterColumns are additional columns
      shown e.g. in kubectl next to the name. Defaults to a created-at column.
      +optional
    group: Group is the group this resource belongs in
    names: Names are the names used to describe this custom resource
    scope: Scope indicates whether this resource is cluster or namespace
      scoped. Default is namespaced
    subresources: Subresources describes the subresources for CustomResources
      +optional
    validation: Validation describes the validation methods for
      CustomResources +optional
    version: Version is the version this resource belongs in Should be always
      first item in Versions field if provided. Optional, but at least one of
      Version or Versions must be set. Deprecated: Please use `Versions`.
      +optional
    versions: Versions is the list of all supported versions for this
      resource. If Version field is provided, this field is optional.
      Validation: All versions must use the same validation schema for now.
      i.e., top level Validation field is applied to all of these versions.
      Order: The version name will be used to compute the order. If the
      version string is "kube-like", it will sort above non "kube-like"
      version strings, which are ordered lexicographically. "Kube-like"
      versions start with a "v", then are followed by a number (the major
      version), then optionally the string "alpha" or "beta" and another
      number (the minor version). These are sorted first by GA > beta > alpha
      (where GA is a version with no suffix such as beta or alpha), and then
      by comparing major version, then minor version. An example sorted list
      of versions: v10, v2, v1, v11beta2, v10beta3, v3beta1, v12alpha1,
      v11alpha2, foo1, foo10. +optional
  """

  additionalPrinterColumns = _messages.MessageField('CustomResourceColumnDefinition', 1, repeated=True)
  group = _messages.StringField(2)
  names = _messages.MessageField('CustomResourceDefinitionNames', 3)
  scope = _messages.StringField(4)
  subresources = _messages.MessageField('CustomResourceSubresources', 5)
  validation = _messages.MessageField('CustomResourceValidation', 6)
  version = _messages.StringField(7)
  versions = _messages.MessageField('CustomResourceDefinitionVersion', 8, repeated=True)


class CustomResourceDefinitionVersion(_messages.Message):
  r"""A CustomResourceDefinitionVersion object.

  Fields:
    name: Name is the version name, e.g. "v1", "v2beta1", etc.
    served: Served is a flag enabling/disabling this version from being served
      via REST APIs
    storage: Storage flags the version as storage version. There must be
      exactly one flagged as storage version.
  """

  name = _messages.StringField(1)
  served = _messages.BooleanField(2)
  storage = _messages.BooleanField(3)


class CustomResourceSubresourceScale(_messages.Message):
  r"""CustomResourceSubresourceScale defines how to serve the scale
  subresource for CustomResources.

  Fields:
    labelSelectorPath: LabelSelectorPath defines the JSON path inside of a
      CustomResource that corresponds to Scale.Status.Selector. Only JSON
      paths without the array notation are allowed. Must be a JSON Path under
      .status. Must be set to work with HPA. If there is no value under the
      given path in the CustomResource, the status label selector value in the
      /scale subresource will default to the empty string. +optional
    specReplicasPath: SpecReplicasPath defines the JSON path inside of a
      CustomResource that corresponds to Scale.Spec.Replicas. Only JSON paths
      without the array notation are allowed. Must be a JSON Path under .spec.
      If there is no value under the given path in the CustomResource, the
      /scale subresource will return an error on GET.
    statusReplicasPath: StatusReplicasPath defines the JSON path inside of a
      CustomResource that corresponds to Scale.Status.Replicas. Only JSON
      paths without the array notation are allowed. Must be a JSON Path under
      .status. If there is no value under the given path in the
      CustomResource, the status replica value in the /scale subresource will
      default to 0.
  """

  labelSelectorPath = _messages.StringField(1)
  specReplicasPath = _messages.StringField(2)
  statusReplicasPath = _messages.StringField(3)


class CustomResourceSubresourceStatus(_messages.Message):
  r"""CustomResourceSubresourceStatus defines how to serve the status
  subresource for CustomResources. Status is represented by the `.status` JSON
  path inside of a CustomResource. When set, * exposes a /status subresource
  for the custom resource * PUT requests to the /status subresource take a
  custom resource object, and ignore changes to anything except the status
  stanza * PUT/POST/PATCH requests to the custom resource ignore changes to
  the status stanza
  """



class CustomResourceSubresources(_messages.Message):
  r"""CustomResourceSubresources defines the status and scale subresources for
  CustomResources.

  Fields:
    scale: Scale denotes the scale subresource for CustomResources +optional
    status: Status denotes the status subresource for CustomResources
      +optional
  """

  scale = _messages.MessageField('CustomResourceSubresourceScale', 1)
  status = _messages.MessageField('CustomResourceSubresourceStatus', 2)


class CustomResourceValidation(_messages.Message):
  r"""CustomResourceValidation is a list of validation methods for
  CustomResources.

  Fields:
    openAPIV3Schema: OpenAPIV3Schema is the OpenAPI v3 schema to be validated
      against. +optional
  """

  openAPIV3Schema = _messages.MessageField('JSONSchemaProps', 1)


class DeliverySpec(_messages.Message):
  r"""From https://github.com/knative/eventing/blob/master/pkg/apis/duck/v1bet
  a1/delivery_types.go

  Fields:
    backoffDelay: BackoffDelay is the delay before retrying. More information
      on Duration format: - https://www.iso.org/iso-8601-date-and-time-
      format.html - https://en.wikipedia.org/wiki/ISO_8601 For linear policy,
      backoff delay is the time interval between retries. For exponential
      policy , backoff delay is backoffDelay*2^. +optional BackoffDelay
      *string `json:"backoffDelay,omitempty"
    backoffPolicy: BackoffPolicy is the retry backoff policy (linear,
      exponential).
    deadLetterSink: DeadLetterSink is the sink receiving event that could not
      be sent to a destination.
    retry: Retry is the minimum number of retries the sender should attempt
      when sending an event before moving it to the dead letter sink.
  """

  backoffDelay = _messages.StringField(1)
  backoffPolicy = _messages.StringField(2)
  deadLetterSink = _messages.MessageField('Destination', 3)
  retry = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Destination(_messages.Message):
  r"""A Destination object.

  Fields:
    ref: ObjectReference points to an Addressable. + optional
    uri: URI is for direct URI Designations or used with the resulting URL
      from Addressable ObjectReference. If used with an ObjectReference, will
      be appended to the path of the resulting URL from the Addressable. +
      optional
  """

  ref = _messages.MessageField('ObjectReference', 1)
  uri = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON
  representation for `Empty` is empty JSON object `{}`.
  """



class ExternalDocumentation(_messages.Message):
  r"""ExternalDocumentation allows referencing an external resource for
  extended documentation.

  Fields:
    description: A string attribute.
    url: A string attribute.
  """

  description = _messages.StringField(1)
  url = _messages.StringField(2)


class JSON(_messages.Message):
  r"""JSON represents any valid JSON value. These types are supported: bool,
  int64, float64, string, []interface{}, map[string]interface{} and nil.

  Fields:
    raw: A byte attribute.
  """

  raw = _messages.BytesField(1)


class JSONSchemaProps(_messages.Message):
  r"""JSONSchemaProps is a JSON-Schema following Specification Draft 4
  (http://json-schema.org/).

  Messages:
    DefinitionsValue: A DefinitionsValue object.
    DependenciesValue: A DependenciesValue object.
    PatternPropertiesValue: A PatternPropertiesValue object.
    PropertiesValue: A PropertiesValue object.

  Fields:
    additionalItems: A JSONSchemaPropsOrBool attribute.
    additionalProperties: A JSONSchemaPropsOrBool attribute.
    allOf: A JSONSchemaProps attribute.
    anyOf: A JSONSchemaProps attribute.
    default: A JSON attribute.
    definitions: A DefinitionsValue attribute.
    dependencies: A DependenciesValue attribute.
    description: A string attribute.
    enum: A string attribute.
    example: A JSON attribute.
    exclusiveMaximum: A boolean attribute.
    exclusiveMinimum: A boolean attribute.
    externalDocs: A ExternalDocumentation attribute.
    format: A string attribute.
    id: A string attribute.
    items: A JSONSchemaPropsOrArray attribute.
    maxItems: A string attribute.
    maxLength: A string attribute.
    maxProperties: A string attribute.
    maximum: A number attribute.
    minItems: A string attribute.
    minLength: A string attribute.
    minProperties: A string attribute.
    minimum: A number attribute.
    multipleOf: A number attribute.
    not_: A JSONSchemaProps attribute.
    oneOf: A JSONSchemaProps attribute.
    pattern: A string attribute.
    patternProperties: A PatternPropertiesValue attribute.
    properties: A PropertiesValue attribute.
    ref: A string attribute.
    required: A string attribute.
    schema: A string attribute.
    title: A string attribute.
    type: A string attribute.
    uniqueItems: A boolean attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DefinitionsValue(_messages.Message):
    r"""A DefinitionsValue object.

    Messages:
      AdditionalProperty: An additional property for a DefinitionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DefinitionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DefinitionsValue object.

      Fields:
        key: Name of the additional property.
        value: A JSONSchemaProps attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JSONSchemaProps', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DependenciesValue(_messages.Message):
    r"""A DependenciesValue object.

    Messages:
      AdditionalProperty: An additional property for a DependenciesValue
        object.

    Fields:
      additionalProperties: Additional properties of type DependenciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DependenciesValue object.

      Fields:
        key: Name of the additional property.
        value: A JSONSchemaPropsOrStringArray attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JSONSchemaPropsOrStringArray', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PatternPropertiesValue(_messages.Message):
    r"""A PatternPropertiesValue object.

    Messages:
      AdditionalProperty: An additional property for a PatternPropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        PatternPropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PatternPropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A JSONSchemaProps attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JSONSchemaProps', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""A PropertiesValue object.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A JSONSchemaProps attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JSONSchemaProps', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalItems = _messages.MessageField('JSONSchemaPropsOrBool', 1)
  additionalProperties = _messages.MessageField('JSONSchemaPropsOrBool', 2)
  allOf = _messages.MessageField('JSONSchemaProps', 3, repeated=True)
  anyOf = _messages.MessageField('JSONSchemaProps', 4, repeated=True)
  default = _messages.MessageField('JSON', 5)
  definitions = _messages.MessageField('DefinitionsValue', 6)
  dependencies = _messages.MessageField('DependenciesValue', 7)
  description = _messages.StringField(8)
  enum = _messages.StringField(9, repeated=True)
  example = _messages.MessageField('JSON', 10)
  exclusiveMaximum = _messages.BooleanField(11)
  exclusiveMinimum = _messages.BooleanField(12)
  externalDocs = _messages.MessageField('ExternalDocumentation', 13)
  format = _messages.StringField(14)
  id = _messages.StringField(15)
  items = _messages.MessageField('JSONSchemaPropsOrArray', 16)
  maxItems = _messages.IntegerField(17)
  maxLength = _messages.IntegerField(18)
  maxProperties = _messages.IntegerField(19)
  maximum = _messages.FloatField(20)
  minItems = _messages.IntegerField(21)
  minLength = _messages.IntegerField(22)
  minProperties = _messages.IntegerField(23)
  minimum = _messages.FloatField(24)
  multipleOf = _messages.FloatField(25)
  not_ = _messages.MessageField('JSONSchemaProps', 26)
  oneOf = _messages.MessageField('JSONSchemaProps', 27, repeated=True)
  pattern = _messages.StringField(28)
  patternProperties = _messages.MessageField('PatternPropertiesValue', 29)
  properties = _messages.MessageField('PropertiesValue', 30)
  ref = _messages.StringField(31)
  required = _messages.StringField(32, repeated=True)
  schema = _messages.StringField(33)
  title = _messages.StringField(34)
  type = _messages.StringField(35)
  uniqueItems = _messages.BooleanField(36)


class JSONSchemaPropsOrArray(_messages.Message):
  r"""JSONSchemaPropsOrArray represents a value that can either be a
  JSONSchemaProps or an array of JSONSchemaProps. Mainly here for
  serialization purposes.

  Fields:
    jsonSchemas: A JSONSchemaProps attribute.
    schema: A JSONSchemaProps attribute.
  """

  jsonSchemas = _messages.MessageField('JSONSchemaProps', 1, repeated=True)
  schema = _messages.MessageField('JSONSchemaProps', 2)


class JSONSchemaPropsOrBool(_messages.Message):
  r"""JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value.
  Defaults to true for the boolean property.

  Fields:
    allows: A boolean attribute.
    schema: A JSONSchemaProps attribute.
  """

  allows = _messages.BooleanField(1)
  schema = _messages.MessageField('JSONSchemaProps', 2)


class JSONSchemaPropsOrStringArray(_messages.Message):
  r"""JSONSchemaPropsOrStringArray represents a JSONSchemaProps or a string
  array.

  Fields:
    property: A string attribute.
    schema: A JSONSchemaProps attribute.
  """

  property = _messages.StringField(1, repeated=True)
  schema = _messages.MessageField('JSONSchemaProps', 2)


class KReference(_messages.Message):
  r"""from
  https://github.com/knative/pkg/blob/master/apis/duck/v1/knative_reference.go
  KReference contains enough information to refer to another object. It's a
  trimmed down version of corev1.ObjectReference.

  Fields:
    apiVersion: API version of the referent.
    kind: Kind of the referent. More info:
      https://git.k8s.io/community/contributors/devel/sig-architecture/api-
      conventions.md#types-kinds
    name: Name of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/#names
    namespace: Namespace of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/namespaces/ This is optional field, it gets defaulted to the
      object holding it if left out.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3)
  namespace = _messages.StringField(4)


class LabelSelector(_messages.Message):
  r"""A label selector is a label query over a set of resources. The result of
  matchLabels and matchExpressions are ANDed. An empty label selector matches
  all objects. A null label selector matches no objects.

  Messages:
    MatchLabelsValue: match_labels is a map of {key,value} pairs. A single
      {key,value} in the matchLabels map is equivalent to an element of
      matchExpressions, whose key field is "key", the operator is "In", and
      the values array contains only "value". The requirements are ANDed.

  Fields:
    matchExpressions: match_expressions is a list of label selector
      requirements. The requirements are ANDed.
    matchLabels: match_labels is a map of {key,value} pairs. A single
      {key,value} in the matchLabels map is equivalent to an element of
      matchExpressions, whose key field is "key", the operator is "In", and
      the values array contains only "value". The requirements are ANDed.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MatchLabelsValue(_messages.Message):
    r"""match_labels is a map of {key,value} pairs. A single {key,value} in
    the matchLabels map is equivalent to an element of matchExpressions, whose
    key field is "key", the operator is "In", and the values array contains
    only "value". The requirements are ANDed.

    Messages:
      AdditionalProperty: An additional property for a MatchLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MatchLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MatchLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  matchExpressions = _messages.MessageField('LabelSelectorRequirement', 1, repeated=True)
  matchLabels = _messages.MessageField('MatchLabelsValue', 2)


class LabelSelectorRequirement(_messages.Message):
  r"""A label selector requirement is a selector that contains values, a key,
  and an operator that relates the key and values.

  Fields:
    key: key is the label key that the selector applies to. +patchMergeKey=key
      +patchStrategy=merge
    operator: operator represents a key's relationship to a set of values.
      Valid operators are In, NotIn, Exists and DoesNotExist.
    values: values is an array of string values. If the operator is In or
      NotIn, the values array must be non-empty. If the operator is Exists or
      DoesNotExist, the values array must be empty. This array is replaced
      during a strategic merge patch. +optional
  """

  key = _messages.StringField(1)
  operator = _messages.StringField(2)
  values = _messages.StringField(3)


class ListApiServerSourcesResponse(_messages.Message):
  r"""ListApiServerSourcesResponse is a list of ApiServerSource resources.

  Fields:
    apiVersion: The API version for this call such as
      "sources.knative.dev/v1beta1".
    items: List of ApiServerSources.
    kind: The kind of this resource, in this case "ApiServerSourceList".
    metadata: Metadata associated with this ApiServerSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('ApiServerSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListBrokersResponse(_messages.Message):
  r"""A ListBrokersResponse object.

  Fields:
    apiVersion: The API version for this call such as
      "eventing.knative.dev/v1".
    items: List of Brokers.
    kind: The kind of this resource, in this case "BrokerList".
    metadata: Metadata associated with this Broker list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Broker', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListCloudAuditLogsSourcesResponse(_messages.Message):
  r"""ListCloudAuditLogsSourcesResponse is a list of CloudAuditLogsSource
  resources.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    items: List of CloudAuditLogsSources.
    kind: The kind of this resource, in this case "CloudAuditLogsSourceList".
    metadata: Metadata associated with this CloudAuditLogsSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CloudAuditLogsSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListCloudPubSubSourcesResponse(_messages.Message):
  r"""ListCloudPubSubSourcesResponse is a list of CloudPubSubSource resources.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    items: List of CloudPubSubSources.
    kind: The kind of this resource, in this case "CloudPubSubSourceList".
    metadata: Metadata associated with this CloudPubSubSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CloudPubSubSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListCloudSchedulerSourcesResponse(_messages.Message):
  r"""ListCloudSchedulerSourcesResponse is a list of CloudSchedulerSource
  resources.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    items: List of CloudSchedulerSources.
    kind: The kind of this resource, in this case "CloudSchedulerSourceList".
    metadata: Metadata associated with this CloudSchedulerSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CloudSchedulerSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListCloudStorageSourcesResponse(_messages.Message):
  r"""ListCloudStorageSourcesResponse is a list of CloudStorageSource
  resources.

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    items: List of CloudStorageSources.
    kind: The kind of this resource, in this case "CloudStorageSourceList".
    metadata: Metadata associated with this CloudStorageSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CloudStorageSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListCustomResourceDefinitionsResponse(_messages.Message):
  r"""A ListCustomResourceDefinitionsResponse object.

  Fields:
    apiVersion: The API version for this call such as
      "k8s.apiextensions.io/v1beta1".
    items: List of CustomResourceDefinitions.
    kind: The kind of this resource, in this case
      "CustomResourceDefinitionList".
    metadata: Metadata associated with this CustomResourceDefinition list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CustomResourceDefinition', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListMeta(_messages.Message):
  r"""ListMeta describes metadata that synthetic resources must have,
  including lists and various status objects. A resource may have only one of
  {ObjectMeta, ListMeta}.

  Fields:
    continue_: continue may be set if the user set a limit on the number of
      items returned, and indicates that the server has more data available.
      The value is opaque and may be used to issue another request to the
      endpoint that served this list to retrieve the next set of available
      objects. Continuing a list may not be possible if the server
      configuration has changed or more than a few minutes have passed. The
      resourceVersion field returned when using this continue value will be
      identical to the value in the first response.
    resourceVersion: String that identifies the server's internal version of
      this object that can be used by clients to determine when objects have
      changed. Value must be treated as opaque by clients and passed
      unmodified back to the server. Populated by the system. Read-only. More
      info: https://git.k8s.io/community/contributors/devel/api-
      conventions.md#concurrency-control-and-consistency +optional
    selfLink: SelfLink is a URL representing this object. Populated by the
      system. Read-only. +optional
  """

  continue_ = _messages.StringField(1)
  resourceVersion = _messages.StringField(2)
  selfLink = _messages.StringField(3)


class ListPingSourcesResponse(_messages.Message):
  r"""ListPingSourcesResponse is a list of PingSource resources.

  Fields:
    apiVersion: The API version for this call such as
      "sources.knative.dev/v1beta1".
    items: List of PingSources.
    kind: The kind of this resource, in this case "PingSourceList".
    metadata: Metadata associated with this PingSource list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('PingSource', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListTriggersResponse(_messages.Message):
  r"""ListTriggersResponse is a list of Trigger resources.

  Fields:
    apiVersion: The API version for this call such as
      "eventing.knative.dev/v1beta1".
    items: List of Triggers.
    kind: The kind of this resource, in this case "TriggerList".
    metadata: Optional. Metadata associated with this Trigger list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Trigger', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class LocalObjectReference(_messages.Message):
  r"""Cloud Run fully managed: not supported Cloud Run for Anthos: supported
  LocalObjectReference contains enough information to let you locate the
  referenced object inside the same namespace.

  Fields:
    name: (Optional) Cloud Run fully managed: not supported Cloud Run for
      Anthos: supported Name of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/#names
  """

  name = _messages.StringField(1)


class ObjectMeta(_messages.Message):
  r"""k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta is metadata that all
  persisted resources must have, which includes all objects users must create.

  Messages:
    AnnotationsValue: (Optional) Annotations is an unstructured key value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      http://kubernetes.io/docs/user-guide/annotations
    LabelsValue: (Optional) Map of string keys and values that can be used to
      organize and categorize (scope and select) objects. May match selectors
      of replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels

  Fields:
    annotations: (Optional) Annotations is an unstructured key value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      http://kubernetes.io/docs/user-guide/annotations
    clusterName: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported The name of the cluster which the object belongs
      to. This is used to distinguish resources with same name and namespace
      in different clusters. This field is not set anywhere right now and
      apiserver is going to ignore it if set in create or update request.
    creationTimestamp: (Optional) CreationTimestamp is a timestamp
      representing the server time when this object was created. It is not
      guaranteed to be set in happens-before order across separate operations.
      Clients may not set this value. It is represented in RFC3339 form and is
      in UTC. Populated by the system. Read-only. Null for lists. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#metadata
    deletionGracePeriodSeconds: (Optional) Cloud Run fully managed: not
      supported Cloud Run for Anthos: supported Number of seconds allowed for
      this object to gracefully terminate before it will be removed from the
      system. Only set when deletionTimestamp is also set. May only be
      shortened. Read-only.
    deletionTimestamp: (Optional) Cloud Run fully managed: not supported Cloud
      Run for Anthos: supported DeletionTimestamp is RFC 3339 date and time at
      which this resource will be deleted. This field is set by the server
      when a graceful deletion is requested by the user, and is not directly
      settable by a client. The resource is expected to be deleted (no longer
      visible from resource lists, and not reachable by name) after the time
      in this field, once the finalizers list is empty. As long as the
      finalizers list contains items, deletion is blocked. Once the
      deletionTimestamp is set, this value may not be unset or be set further
      into the future, although it may be shortened or the resource may be
      deleted prior to this time. For example, a user may request that a pod
      is deleted in 30 seconds. The Kubelet will react by sending a graceful
      termination signal to the containers in the pod. After that 30 seconds,
      the Kubelet will send a hard termination signal (SIGKILL) to the
      container and after cleanup, remove the pod from the API. In the
      presence of network partitions, this object may still exist after this
      timestamp, until an administrator or automated process can determine the
      resource is fully terminated. If not set, graceful deletion of the
      object has not been requested. Populated by the system when a graceful
      deletion is requested. Read-only. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#metadata
    finalizers: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported Must be empty before the object is deleted from
      the registry. Each entry is an identifier for the responsible component
      that will remove the entry from the list. If the deletionTimestamp of
      the object is non-nil, entries in this list can only be removed.
      +patchStrategy=merge
    generateName: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported GenerateName is an optional prefix, used by the
      server, to generate a unique name ONLY IF the Name field has not been
      provided. If this field is used, the name returned to the client will be
      different than the name passed. This value will also be combined with a
      unique suffix. The provided value has the same validation rules as the
      Name field, and may be truncated by the length of the suffix required to
      make the value unique on the server. If this field is specified and the
      generated name exists, the server will NOT return a 409 - instead, it
      will either return 201 Created or 500 with Reason ServerTimeout
      indicating a unique name could not be found in the time allotted, and
      the client should retry (optionally after the time indicated in the
      Retry-After header). Applied only if Name is not specified. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#idempotency string generateName = 2;
    generation: (Optional) A sequence number representing a specific
      generation of the desired state. Populated by the system. Read-only.
    labels: (Optional) Map of string keys and values that can be used to
      organize and categorize (scope and select) objects. May match selectors
      of replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels
    name: Name must be unique within a namespace, within a Cloud Run region.
      Is required when creating resources, although some resources may allow a
      client to request the generation of an appropriate name automatically.
      Name is primarily intended for creation idempotence and configuration
      definition. Cannot be updated. More info:
      http://kubernetes.io/docs/user-guide/identifiers#names +optional
    namespace: Namespace defines the space within each name must be unique,
      within a Cloud Run region. In Cloud Run the namespace must be equal to
      either the project ID or project number.
    ownerReferences: (Optional) Cloud Run fully managed: not supported Cloud
      Run for Anthos: supported List of objects that own this object. If ALL
      objects in the list have been deleted, this object will be garbage
      collected.
    resourceVersion: (Optional) An opaque value that represents the internal
      version of this object that can be used by clients to determine when
      objects have changed. May be used for optimistic concurrency, change
      detection, and the watch operation on a resource or set of resources.
      Clients must treat these values as opaque and passed unmodified back to
      the server. They may only be valid for a particular resource or set of
      resources. Populated by the system. Read-only. Value must be treated as
      opaque by clients and . More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#concurrency-control-and-consistency
    selfLink: (Optional) SelfLink is a URL representing this object. Populated
      by the system. Read-only. string selfLink = 4;
    uid: (Optional) UID is the unique in time and space value for this object.
      It is typically generated by the server on successful creation of a
      resource and is not allowed to change on PUT operations. Populated by
      the system. Read-only. More info: http://kubernetes.io/docs/user-
      guide/identifiers#uids
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""(Optional) Annotations is an unstructured key value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: http://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""(Optional) Map of string keys and values that can be used to organize
    and categorize (scope and select) objects. May match selectors of
    replication controllers and routes. More info:
    http://kubernetes.io/docs/user-guide/labels

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  clusterName = _messages.StringField(2)
  creationTimestamp = _messages.StringField(3)
  deletionGracePeriodSeconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  deletionTimestamp = _messages.StringField(5)
  finalizers = _messages.StringField(6, repeated=True)
  generateName = _messages.StringField(7)
  generation = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  namespace = _messages.StringField(11)
  ownerReferences = _messages.MessageField('OwnerReference', 12, repeated=True)
  resourceVersion = _messages.StringField(13)
  selfLink = _messages.StringField(14)
  uid = _messages.StringField(15)


class ObjectReference(_messages.Message):
  r"""ObjectReference contains enough information to let you inspect or modify
  the referred object.

  Fields:
    apiVersion: API version of the referent. +optional
    fieldPath: If referring to a piece of an object instead of an entire
      object, this string should contain a valid JSON/Go field access
      statement, such as desiredState.manifest.containers[2]. For example, if
      the object reference is to a container within a pod, this would take on
      a value like: "spec.containers{name}" (where "name" refers to the name
      of the container that triggered the event) or if no container name is
      specified "spec.containers[2]" (container with index 2 in this pod).
      This syntax is chosen only to have some well-defined way of referencing
      a part of an object.
    kind: Kind of the referent. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#types-kinds +optional
    name: Name of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/#names +optional
    namespace: Namespace of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/namespaces/ +optional
    resourceVersion: Specific resourceVersion to which this reference is made,
      if any. More info: https://git.k8s.io/community/contributors/devel/api-
      conventions.md#concurrency-control-and-consistency +optional
    uid: UID of the referent. More info:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/#uids +optional
  """

  apiVersion = _messages.StringField(1)
  fieldPath = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4)
  namespace = _messages.StringField(5)
  resourceVersion = _messages.StringField(6)
  uid = _messages.StringField(7)


class OwnerReference(_messages.Message):
  r"""OwnerReference contains enough information to let you identify an owning
  object. Currently, an owning object must be in the same namespace, so there
  is no namespace field.

  Fields:
    apiVersion: API version of the referent.
    blockOwnerDeletion: If true, AND if the owner has the "foregroundDeletion"
      finalizer, then the owner cannot be deleted from the key-value store
      until this reference is removed. Defaults to false. To set this field, a
      user needs "delete" permission of the owner, otherwise 422
      (Unprocessable Entity) will be returned. +optional
    controller: If true, this reference points to the managing controller.
      +optional
    kind: Kind of the referent. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#types-kinds
    name: Name of the referent. More info: http://kubernetes.io/docs/user-
      guide/identifiers#names
    uid: UID of the referent. More info: http://kubernetes.io/docs/user-
      guide/identifiers#uids
  """

  apiVersion = _messages.StringField(1)
  blockOwnerDeletion = _messages.BooleanField(2)
  controller = _messages.BooleanField(3)
  kind = _messages.StringField(4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)


class PingSource(_messages.Message):
  r"""A PingSource object.

  Fields:
    apiVersion: The API version for this call such as
      "sources.knative.dev/v1beta1".
    kind: The kind of resource, in this case "PingSource".
    metadata: Metadata associated with this PingSource.
    spec: Spec defines the desired state of the PingSource.
    status: Status represents the current state of the PingSource. This data
      may be out of date.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('PingSourceSpec', 4)
  status = _messages.MessageField('PingSourceStatus', 5)


class PingSourceSpec(_messages.Message):
  r"""The desired state of the PingSource.

  Fields:
    ceOverrides: CloudEventOverrides defines overrides to control the output
      format and modifications of the event sent to the sink.
    jsonData: JsonData is json encoded data used as the body of the event
      posted to the sink. Default is empty. If set, datacontenttype will also
      be set to "application/json".
    schedule: Schedule is the cronjob schedule. Defaults to `* * * * *`.
    sink: Sink is a reference to an object that will resolve to a uri to use
      as the sink.
    timezone: Timezone modifies the actual time relative to the specified
      timezone. Defaults to the system time zone. More general information
      about time zones: https://www.iana.org/time-zones List of valid timezone
      values: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
  """

  ceOverrides = _messages.MessageField('CloudEventOverrides', 1)
  jsonData = _messages.StringField(2)
  schedule = _messages.StringField(3)
  sink = _messages.MessageField('Destination', 4)
  timezone = _messages.StringField(5)


class PingSourceStatus(_messages.Message):
  r"""PingSourceStatus represents the current state of a PingSource.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    ceAttributes: CloudEventAttributes are the specific attributes that the
      Source uses as part of its CloudEvents.
    conditions: Conditions the latest available observations of a resource's
      current state.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      CloudPubSubSource that was last processed by the controller.
    projectId: ProjectID is the project ID of the Topic, might have been
      resolved.
    serviceAccountName: ServiceAccountName is the k8s service account
      associated with Google service account.
    sinkUri: SinkURI is the current active sink URI that has been configured
      for the Source.
    stackDriverSink: ID of the Stackdriver sink used to publish audit log
      messages.
    subscriptionId: SubscriptionID is the created subscription ID.
    topicId: TopicID where the notifications are sent to.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  ceAttributes = _messages.MessageField('CloudEventAttributes', 2, repeated=True)
  conditions = _messages.MessageField('Condition', 3, repeated=True)
  observedGeneration = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(5)
  serviceAccountName = _messages.StringField(6)
  sinkUri = _messages.StringField(7)
  stackDriverSink = _messages.StringField(8)
  subscriptionId = _messages.StringField(9)
  topicId = _messages.StringField(10)


class SecretKeySelector(_messages.Message):
  r"""Cloud Run fully managed: not supported Cloud Run for Anthos: supported
  SecretKeySelector selects a key of a Secret.

  Fields:
    key: Cloud Run fully managed: not supported Cloud Run for Anthos:
      supported The key of the secret to select from. Must be a valid secret
      key.
    localObjectReference: This field should not be used directly as it is
      meant to be inlined directly into the message. Use the "name" field
      instead.
    name: Cloud Run fully managed: not supported Cloud Run for Anthos:
      supported The name of the secret in the pod's namespace to select from.
    optional: (Optional) Cloud Run fully managed: not supported Cloud Run for
      Anthos: supported Specify whether the Secret or its key must be defined
  """

  key = _messages.StringField(1)
  localObjectReference = _messages.MessageField('LocalObjectReference', 2)
  name = _messages.StringField(3)
  optional = _messages.BooleanField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Trigger(_messages.Message):
  r"""Represents a request to have events delivered to a consumer from a
  Broker's event pool.

  Fields:
    apiVersion: The API version for this call such as
      "eventing.knative.dev/v1beta1".
    kind: The kind of resource, in this case "Trigger".
    metadata: Optional. Metadata associated with this Trigger, including name,
      namespace, and annotations. .
    spec: Spec defines the desired state of the Trigger.
    status: Optional. Status represents the current state of the Trigger. This
      data may be out of date.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('TriggerSpec', 4)
  status = _messages.MessageField('TriggerStatus', 5)


class TriggerFilter(_messages.Message):
  r"""Filters events based on exact matches on the attributes.

  Messages:
    AttributesValue: Optional. Attributes filters events by exact match on
      event context attributes. Each key in the map is compared with the
      equivalent key in the event context. An event passes the filter if all
      values are equal to the specified values. Nested context attributes are
      not supported as keys. Only string values are supported.

  Fields:
    attributes: Optional. Attributes filters events by exact match on event
      context attributes. Each key in the map is compared with the equivalent
      key in the event context. An event passes the filter if all values are
      equal to the specified values. Nested context attributes are not
      supported as keys. Only string values are supported.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Attributes filters events by exact match on event context
    attributes. Each key in the map is compared with the equivalent key in the
    event context. An event passes the filter if all values are equal to the
    specified values. Nested context attributes are not supported as keys.
    Only string values are supported.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)


class TriggerSpec(_messages.Message):
  r"""The desired state of the Trigger.

  Fields:
    broker: Optional. Broker is the broker that this trigger receives events
      from. If not provided, fully-managed Events for Cloud Run uses the
      `google` broker by default
    filter: Optional. Filter is the filter to apply against all events from
      the Broker. Only events that pass this filter will be sent to the
      Subscriber.
    serviceAccountEmail: Optional. Email address of the IAM service account
      associated with the trigger. The service account represents the identity
      of the trigger and used to generate identity tokens when invoking Cloud
      Run service specified in the "subscriber" field. See also
      https://cloud.google.com/run/docs/triggering/pubsub-push#create-service-
      account for information on how to invoke authenticated Cloud Run
      services.
    subscriber: A Destination attribute.
  """

  broker = _messages.StringField(1)
  filter = _messages.MessageField('TriggerFilter', 2)
  serviceAccountEmail = _messages.StringField(3)
  subscriber = _messages.MessageField('Destination', 4)


class TriggerStatus(_messages.Message):
  r"""The current state of a Trigger.

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    conditions: As a Trigger is being prepared, it will incrementally update
      conditions. Trigger-specific conditions include: * "Subscriber": True
      when the target Cloud Run service is resolved. * "Subscribed": True when
      the Pub/Sub topic and subscription associated with the trigger is
      successfully set up.
    observedGeneration: ObservedGeneration is the 'Generation' of the Trigger
      that was last processed by the controller.
    subscriberUri: SubscriberURI is the resolved URI of the receiver for this
      Trigger.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  observedGeneration = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  subscriberUri = _messages.StringField(4)


encoding.AddCustomJsonFieldMapping(
    JSONSchemaProps, 'not_', 'not')
encoding.AddCustomJsonFieldMapping(
    ListMeta, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsBrokersListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsCustomresourcedefinitionsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesApiserversourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesBrokersListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesCloudauditlogssourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesCloudpubsubsourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesCloudschedulersourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesCloudstoragesourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesPingsourcesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesTriggersListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsTriggersListRequest, 'continue_', 'continue')
