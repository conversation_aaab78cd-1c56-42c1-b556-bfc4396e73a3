"""Generated message classes for anthosevents version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'anthosevents'


class AnthoseventsKuberunsCreateRequest(_messages.Message):
  r"""A AnthoseventsKuberunsCreateRequest object.

  Fields:
    kubeRun: A KubeRun resource to be passed as the request body.
    parent: The namespace in which this KubeRun resource should be created.
  """

  kubeRun = _messages.MessageField('KubeRun', 1)
  parent = _messages.StringField(2)


class AnthoseventsKuberunsDeleteRequest(_messages.Message):
  r"""A AnthoseventsKuberunsDeleteRequest object.

  Fields:
    name: The name of the KubeRun resource being deleted.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsKuberunsGetRequest(_messages.Message):
  r"""A AnthoseventsKuberunsGetRequest object.

  Fields:
    name: The name of the KubeRun resource being retrieved.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsKuberunsListRequest(_messages.Message):
  r"""A AnthoseventsKuberunsListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The namespace from which the KubeRun resources should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start.
    watch: Flag that indicates that the client expects to watch this resource
      as well.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  labelSelector = _messages.StringField(3)
  limit = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  parent = _messages.StringField(5)
  resourceVersion = _messages.StringField(6)
  watch = _messages.BooleanField(7)


class AnthoseventsKuberunsPatchRequest(_messages.Message):
  r"""A AnthoseventsKuberunsPatchRequest object.

  Fields:
    kubeRun: A KubeRun resource to be passed as the request body.
    name: The name of the KubeRun resource being updated.
  """

  kubeRun = _messages.MessageField('KubeRun', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsKuberunsReplaceKubeRunRequest(_messages.Message):
  r"""A AnthoseventsKuberunsReplaceKubeRunRequest object.

  Fields:
    kubeRun: A KubeRun resource to be passed as the request body.
    name: The name of the KubeRun resource being replaced.
  """

  kubeRun = _messages.MessageField('KubeRun', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudrunsCreateRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsCreateRequest object.

  Fields:
    cloudRun: A CloudRun resource to be passed as the request body.
    parent: The namespace in which this CloudRun resource should be created.
  """

  cloudRun = _messages.MessageField('CloudRun', 1)
  parent = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudrunsDeleteRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsDeleteRequest object.

  Fields:
    name: The name of the CloudRun resource being deleted.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudrunsGetRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsGetRequest object.

  Fields:
    name: The name of the CloudRun resource being retrieved.
  """

  name = _messages.StringField(1, required=True)


class AnthoseventsNamespacesCloudrunsListRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsListRequest object.

  Fields:
    continue_: Optional encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: The namespace from which the CloudRun resources should be listed.
    resourceVersion: The baseline resource version from which the list or
      watch operation should start.
    watch: Flag that indicates that the client expects to watch this resource
      as well.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  labelSelector = _messages.StringField(3)
  limit = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  parent = _messages.StringField(5, required=True)
  resourceVersion = _messages.StringField(6)
  watch = _messages.BooleanField(7)


class AnthoseventsNamespacesCloudrunsPatchRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsPatchRequest object.

  Fields:
    cloudRun: A CloudRun resource to be passed as the request body.
    name: The name of the CloudRun resource being updated.
  """

  cloudRun = _messages.MessageField('CloudRun', 1)
  name = _messages.StringField(2, required=True)


class AnthoseventsNamespacesCloudrunsReplaceCloudRunRequest(_messages.Message):
  r"""A AnthoseventsNamespacesCloudrunsReplaceCloudRunRequest object.

  Fields:
    cloudRun: A CloudRun resource to be passed as the request body.
    name: The name of the CloudRun resource being replaced.
  """

  cloudRun = _messages.MessageField('CloudRun', 1)
  name = _messages.StringField(2, required=True)


class CloudRun(_messages.Message):
  r"""CloudRun is the Schema for the CloudRuns API

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "CloudRun".
    metadata: Metadata associated with this CloudRun resource
    spec: The CloudRunSpec reflects the state of CloudRun
      `json:"spec,omitempty"`
    status: The CloudRunStatus defines the observed status
      `json:"status,omitempty"`
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudRunSpec', 4)
  status = _messages.MessageField('CloudRunStatus', 5)


class CloudRunSpec(_messages.Message):
  r"""CloudRunSpec defines the desired state of CloudRun

  Fields:
    eventing: `json:"eventing,omitempty"`
    networking: `json:"networking,omitempty"`
    serving: `json:"serving,omitempty"`
  """

  eventing = _messages.MessageField('EventingSpec', 1)
  networking = _messages.MessageField('NetworkingSpec', 2)
  serving = _messages.MessageField('ServingSpec', 3)


class CloudRunStatus(_messages.Message):
  r"""CloudRunStatus defines the observed state of CloudRun

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    conditions: Conditions are the latest available observations of a
      resource's current state.
    eventingversion: The version of the installed release.
    istioversion: The version of the installed release.
    observedGeneration: ObservedGeneration is the 'Generation' of the Service
      that was last processed by the controller.
    servingversion: The version of the installed release.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  eventingversion = _messages.StringField(3)
  istioversion = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  servingversion = _messages.StringField(6)


class Condition(_messages.Message):
  r"""Condition defines a generic condition for a Resource

  Fields:
    lastTransitionTime: Optional. Last time the condition transitioned from
      one status to another.
    message: Optional. Human readable message indicating details about the
      current status.
    reason: Optional. One-word CamelCase reason for the condition's last
      transition.
    severity: Optional. How to interpret failures of this condition, one of
      Error, Warning, Info
    status: Status of the condition, one of True, False, Unknown.
    type: type is used to communicate the status of the reconciliation
      process. See also: https://github.com/knative/serving/blob/master/docs/s
      pec/errors.md#error-conditions-and-reporting Types common to all
      resources include: * "Ready": True when the Resource is ready.
  """

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  severity = _messages.StringField(4)
  status = _messages.StringField(5)
  type = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON
  representation for `Empty` is empty JSON object `{}`.
  """



class EventingSpec(_messages.Message):
  r"""EventingSpec defines the desired state of Eventing

  Fields:
    enabled: A boolean attribute.
  """

  enabled = _messages.BooleanField(1)


class KubeRun(_messages.Message):
  r"""KubeRun is the Schema for the KubeRuns API

  Fields:
    apiVersion: The API version for this call such as
      "events.cloud.google.com/v1beta1".
    kind: The kind of resource, in this case "KubeRun".
    metadata: Metadata associated with this KubeRun resource
    spec: The KubeRunSpec reflects the state of KubeRun
    status: The KubeRunStatus defines the observed status
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('CloudRunSpec', 4)
  status = _messages.MessageField('KubeRunStatus', 5)


class KubeRunStatus(_messages.Message):
  r"""KubeRunStatus defines the observed state of KubeRun

  Messages:
    AnnotationsValue: Annotations is additional Status fields for the Resource
      to save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.

  Fields:
    annotations: Annotations is additional Status fields for the Resource to
      save some additional State as well as convey more information to the
      user. This is roughly akin to Annotations on any k8s resource, just the
      reconciler conveying richer information outwards.
    conditions: Conditions are the latest available observations of a
      resource's current state.
    eventingversion: The version of the installed release.
    istioversion: The version of the installed release.
    observedGeneration: ObservedGeneration is the 'Generation' of the Service
      that was last processed by the controller.
    servingversion: The version of the installed release.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is additional Status fields for the Resource to save some
    additional State as well as convey more information to the user. This is
    roughly akin to Annotations on any k8s resource, just the reconciler
    conveying richer information outwards.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  eventingversion = _messages.StringField(3)
  istioversion = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  servingversion = _messages.StringField(6)


class ListCloudRunsResponse(_messages.Message):
  r"""ListCloudRunsResponse is a list of CloudRun resources. The next page
  token is specified as the "continue" field in ListMeta.

  Fields:
    apiVersion: The API version for this call such as "core/v1".
    items: A CloudRun attribute.
    kind: The kind of this resource, in this case "CloudRunList".
    metadata: Metadata associated with this CloudRun list.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('CloudRun', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)


class ListKubeRunsResponse(_messages.Message):
  r"""ListKubeRunsResponse is a list of KubeRun resources. The next page token
  is specified as the "continue" field in ListMeta.

  Fields:
    apiVersion: The API version for this call such as "core/v1".
    items: A KubeRun attribute.
    kind: The kind of this resource, in this case "KubeRunList".
    metadata: Metadata associated with this KubeRun list.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('KubeRun', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)


class ListMeta(_messages.Message):
  r"""ListMeta describes metadata that synthetic resources must have,
  including lists and various status objects. A resource may have only one of
  {ObjectMeta, ListMeta}.

  Fields:
    continue_: continue may be set if the user set a limit on the number of
      items returned, and indicates that the server has more data available.
      The value is opaque and may be used to issue another request to the
      endpoint that served this list to retrieve the next set of available
      objects. Continuing a list may not be possible if the server
      configuration has changed or more than a few minutes have passed. The
      resourceVersion field returned when using this continue value will be
      identical to the value in the first response.
    resourceVersion: String that identifies the server's internal version of
      this object that can be used by clients to determine when objects have
      changed. Value must be treated as opaque by clients and passed
      unmodified back to the server. Populated by the system. Read-only. More
      info: https://git.k8s.io/community/contributors/devel/api-
      conventions.md#concurrency-control-and-consistency +optional
    selfLink: SelfLink is a URL representing this object. Populated by the
      system. Read-only. +optional
  """

  continue_ = _messages.StringField(1)
  resourceVersion = _messages.StringField(2)
  selfLink = _messages.StringField(3)


class NetworkingSpec(_messages.Message):
  r"""NetworkingSpec defines the desired state of Networking

  Fields:
    enabled: A boolean attribute.
    loadbalancertype: LoadBalancerType is whether the istio ingress is
      internal or external possible values are internal | external(implicit).
  """

  enabled = _messages.BooleanField(1)
  loadbalancertype = _messages.StringField(2)


class ObjectMeta(_messages.Message):
  r"""k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta is metadata that all
  persisted resources must have, which includes all objects users must create.

  Messages:
    AnnotationsValue: (Optional) Annotations is an unstructured key value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      http://kubernetes.io/docs/user-guide/annotations
    LabelsValue: (Optional) Map of string keys and values that can be used to
      organize and categorize (scope and select) objects. May match selectors
      of replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels

  Fields:
    annotations: (Optional) Annotations is an unstructured key value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      http://kubernetes.io/docs/user-guide/annotations
    clusterName: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported The name of the cluster which the object belongs
      to. This is used to distinguish resources with same name and namespace
      in different clusters. This field is not set anywhere right now and
      apiserver is going to ignore it if set in create or update request.
    creationTimestamp: (Optional) CreationTimestamp is a timestamp
      representing the server time when this object was created. It is not
      guaranteed to be set in happens-before order across separate operations.
      Clients may not set this value. It is represented in RFC3339 form and is
      in UTC. Populated by the system. Read-only. Null for lists. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#metadata
    deletionGracePeriodSeconds: (Optional) Cloud Run fully managed: not
      supported Cloud Run for Anthos: supported Number of seconds allowed for
      this object to gracefully terminate before it will be removed from the
      system. Only set when deletionTimestamp is also set. May only be
      shortened. Read-only.
    deletionTimestamp: (Optional) Cloud Run fully managed: not supported Cloud
      Run for Anthos: supported DeletionTimestamp is RFC 3339 date and time at
      which this resource will be deleted. This field is set by the server
      when a graceful deletion is requested by the user, and is not directly
      settable by a client. The resource is expected to be deleted (no longer
      visible from resource lists, and not reachable by name) after the time
      in this field, once the finalizers list is empty. As long as the
      finalizers list contains items, deletion is blocked. Once the
      deletionTimestamp is set, this value may not be unset or be set further
      into the future, although it may be shortened or the resource may be
      deleted prior to this time. For example, a user may request that a pod
      is deleted in 30 seconds. The Kubelet will react by sending a graceful
      termination signal to the containers in the pod. After that 30 seconds,
      the Kubelet will send a hard termination signal (SIGKILL) to the
      container and after cleanup, remove the pod from the API. In the
      presence of network partitions, this object may still exist after this
      timestamp, until an administrator or automated process can determine the
      resource is fully terminated. If not set, graceful deletion of the
      object has not been requested. Populated by the system when a graceful
      deletion is requested. Read-only. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#metadata
    finalizers: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported Must be empty before the object is deleted from
      the registry. Each entry is an identifier for the responsible component
      that will remove the entry from the list. If the deletionTimestamp of
      the object is non-nil, entries in this list can only be removed.
      +patchStrategy=merge
    generateName: (Optional) Cloud Run fully managed: not supported Cloud Run
      for Anthos: supported GenerateName is an optional prefix, used by the
      server, to generate a unique name ONLY IF the Name field has not been
      provided. If this field is used, the name returned to the client will be
      different than the name passed. This value will also be combined with a
      unique suffix. The provided value has the same validation rules as the
      Name field, and may be truncated by the length of the suffix required to
      make the value unique on the server. If this field is specified and the
      generated name exists, the server will NOT return a 409 - instead, it
      will either return 201 Created or 500 with Reason ServerTimeout
      indicating a unique name could not be found in the time allotted, and
      the client should retry (optionally after the time indicated in the
      Retry-After header). Applied only if Name is not specified. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#idempotency string generateName = 2;
    generation: (Optional) A sequence number representing a specific
      generation of the desired state. Populated by the system. Read-only.
    labels: (Optional) Map of string keys and values that can be used to
      organize and categorize (scope and select) objects. May match selectors
      of replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels
    name: Name must be unique within a namespace, within a Cloud Run region.
      Is required when creating resources, although some resources may allow a
      client to request the generation of an appropriate name automatically.
      Name is primarily intended for creation idempotence and configuration
      definition. Cannot be updated. More info:
      http://kubernetes.io/docs/user-guide/identifiers#names +optional
    namespace: Namespace defines the space within each name must be unique,
      within a Cloud Run region. In Cloud Run the namespace must be equal to
      either the project ID or project number.
    ownerReferences: (Optional) Cloud Run fully managed: not supported Cloud
      Run for Anthos: supported List of objects that own this object. If ALL
      objects in the list have been deleted, this object will be garbage
      collected.
    resourceVersion: (Optional) An opaque value that represents the internal
      version of this object that can be used by clients to determine when
      objects have changed. May be used for optimistic concurrency, change
      detection, and the watch operation on a resource or set of resources.
      Clients must treat these values as opaque and passed unmodified back to
      the server. They may only be valid for a particular resource or set of
      resources. Populated by the system. Read-only. Value must be treated as
      opaque by clients and . More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#concurrency-control-and-consistency
    selfLink: (Optional) SelfLink is a URL representing this object. Populated
      by the system. Read-only. string selfLink = 4;
    uid: (Optional) UID is the unique in time and space value for this object.
      It is typically generated by the server on successful creation of a
      resource and is not allowed to change on PUT operations. Populated by
      the system. Read-only. More info: http://kubernetes.io/docs/user-
      guide/identifiers#uids
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""(Optional) Annotations is an unstructured key value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: http://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""(Optional) Map of string keys and values that can be used to organize
    and categorize (scope and select) objects. May match selectors of
    replication controllers and routes. More info:
    http://kubernetes.io/docs/user-guide/labels

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  clusterName = _messages.StringField(2)
  creationTimestamp = _messages.StringField(3)
  deletionGracePeriodSeconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  deletionTimestamp = _messages.StringField(5)
  finalizers = _messages.StringField(6, repeated=True)
  generateName = _messages.StringField(7)
  generation = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  namespace = _messages.StringField(11)
  ownerReferences = _messages.MessageField('OwnerReference', 12, repeated=True)
  resourceVersion = _messages.StringField(13)
  selfLink = _messages.StringField(14)
  uid = _messages.StringField(15)


class OwnerReference(_messages.Message):
  r"""OwnerReference contains enough information to let you identify an owning
  object. Currently, an owning object must be in the same namespace, so there
  is no namespace field.

  Fields:
    apiVersion: API version of the referent.
    blockOwnerDeletion: If true, AND if the owner has the "foregroundDeletion"
      finalizer, then the owner cannot be deleted from the key-value store
      until this reference is removed. Defaults to false. To set this field, a
      user needs "delete" permission of the owner, otherwise 422
      (Unprocessable Entity) will be returned. +optional
    controller: If true, this reference points to the managing controller.
      +optional
    kind: Kind of the referent. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#types-kinds
    name: Name of the referent. More info: http://kubernetes.io/docs/user-
      guide/identifiers#names
    uid: UID of the referent. More info: http://kubernetes.io/docs/user-
      guide/identifiers#uids
  """

  apiVersion = _messages.StringField(1)
  blockOwnerDeletion = _messages.BooleanField(2)
  controller = _messages.BooleanField(3)
  kind = _messages.StringField(4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)


class ServingSpec(_messages.Message):
  r"""ServingSpec defines the desired state of Serving

  Fields:
    enabled: A boolean attribute.
  """

  enabled = _messages.BooleanField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    ListMeta, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsKuberunsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    AnthoseventsNamespacesCloudrunsListRequest, 'continue_', 'continue')
