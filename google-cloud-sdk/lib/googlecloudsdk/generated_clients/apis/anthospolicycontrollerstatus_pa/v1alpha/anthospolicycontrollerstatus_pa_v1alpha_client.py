"""Generated client library for anthospolicycontrollerstatus_pa version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.anthospolicycontrollerstatus_pa.v1alpha import anthospolicycontrollerstatus_pa_v1alpha_messages as messages


class AnthospolicycontrollerstatusPaV1alpha(base_api.BaseApiClient):
  """Generated client library for service anthospolicycontrollerstatus_pa version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://anthospolicycontrollerstatus-pa.googleapis.com/'
  MTLS_BASE_URL = 'https://anthospolicycontrollerstatus-pa.mtls.googleapis.com/'

  _PACKAGE = 'anthospolicycontrollerstatus_pa'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AnthospolicycontrollerstatusPaV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new anthospolicycontrollerstatus_pa handle."""
    url = url or self.BASE_URL
    super(AnthospolicycontrollerstatusPaV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_membershipConstraintAuditViolations = self.ProjectsMembershipConstraintAuditViolationsService(self)
    self.projects_membershipConstraintAuditViolationsProducer = self.ProjectsMembershipConstraintAuditViolationsProducerService(self)
    self.projects_membershipConstraintTemplates = self.ProjectsMembershipConstraintTemplatesService(self)
    self.projects_membershipConstraints = self.ProjectsMembershipConstraintsService(self)
    self.projects_membershipConstraintsProducer = self.ProjectsMembershipConstraintsProducerService(self)
    self.projects_memberships = self.ProjectsMembershipsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsMembershipConstraintAuditViolationsService(base_api.BaseApiService):
    """Service class for the projects_membershipConstraintAuditViolations resource."""

    _NAME = 'projects_membershipConstraintAuditViolations'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipConstraintAuditViolationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""ListMembershipConstraintAuditViolations returns membership specific constraint audit violation info.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintAuditViolationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipConstraintAuditViolationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraintAuditViolations',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraintAuditViolations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/membershipConstraintAuditViolations',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintAuditViolationsListRequest',
        response_type_name='ListMembershipConstraintAuditViolationsResponse',
        supports_download=False,
    )

  class ProjectsMembershipConstraintAuditViolationsProducerService(base_api.BaseApiService):
    """Service class for the projects_membershipConstraintAuditViolationsProducer resource."""

    _NAME = 'projects_membershipConstraintAuditViolationsProducer'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipConstraintAuditViolationsProducerService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""ListMembershipConstraintAuditViolationsProducer returns membership specific constraint audit violation info. This endpoint is meant for calls originating from Google internal services.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintAuditViolationsProducerListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipConstraintAuditViolationsProducerResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraintAuditViolationsProducer',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraintAuditViolationsProducer.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/membershipConstraintAuditViolationsProducer',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintAuditViolationsProducerListRequest',
        response_type_name='ListMembershipConstraintAuditViolationsProducerResponse',
        supports_download=False,
    )

  class ProjectsMembershipConstraintTemplatesService(base_api.BaseApiService):
    """Service class for the projects_membershipConstraintTemplates resource."""

    _NAME = 'projects_membershipConstraintTemplates'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipConstraintTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves status for a single membership constraint template on a single member cluster.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MembershipConstraintTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraintTemplates/{membershipConstraintTemplatesId}/{membershipConstraintTemplatesId1}',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraintTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintTemplatesGetRequest',
        response_type_name='MembershipConstraintTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists status for constraint templates. Each entry in the response has a ConstraintTemplateRef and MembershipRef, corresponding to status aggregated across all resources within a single member cluster, in pseudocode the response's shape is: [StatusForConstraintTemplate1OnMemberClusterA, StatusForConstraintTemplate2OnMemberClusterA, StatusForConstraintTemplate1OnMemberClusterB, StatusForConstraintTemplate3OnMemberClusterC, ...].

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipConstraintTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraintTemplates',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraintTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/membershipConstraintTemplates',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintTemplatesListRequest',
        response_type_name='ListMembershipConstraintTemplatesResponse',
        supports_download=False,
    )

  class ProjectsMembershipConstraintsService(base_api.BaseApiService):
    """Service class for the projects_membershipConstraints resource."""

    _NAME = 'projects_membershipConstraints'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipConstraintsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves status for a single constraint on a single member cluster.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MembershipConstraint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraints/{membershipConstraintsId}/{membershipConstraintsId1}/{membershipConstraintsId2}',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintsGetRequest',
        response_type_name='MembershipConstraint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListMembershipConstraints returns per-membership runtime status for constraints. The response contains a list of MembershipConstraints. Each MembershipConstraint contains a MembershipRef indicating which member cluster the constraint status corresponds to. Note that if the request is ListMembershipConstraints(parent=project1) and clusterA is registered to project2 via a Membership in project1, then clusterA's info will appear in the response.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipConstraintsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraints',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/membershipConstraints',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintsListRequest',
        response_type_name='ListMembershipConstraintsResponse',
        supports_download=False,
    )

  class ProjectsMembershipConstraintsProducerService(base_api.BaseApiService):
    """Service class for the projects_membershipConstraintsProducer resource."""

    _NAME = 'projects_membershipConstraintsProducer'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipConstraintsProducerService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""ListMembershipConstraintsProducer returns per-membership runtime status for constraints. This endpoint is meant for calls originating from Google internal services. The response contains a list of MembershipConstraints. Each MembershipConstraint contains a MembershipRef indicating which member cluster the constraint status corresponds to. Note that if the request is ListMembershipConstraintsProducer(parent=project1) and clusterA is registered to project2 via a Membership in project1, then clusterA's info will appear in the response.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipConstraintsProducerListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipConstraintsProducerResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/membershipConstraintsProducer',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.membershipConstraintsProducer.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/membershipConstraintsProducer',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipConstraintsProducerListRequest',
        response_type_name='ListMembershipConstraintsProducerResponse',
        supports_download=False,
    )

  class ProjectsMembershipsService(base_api.BaseApiService):
    """Service class for the projects_memberships resource."""

    _NAME = 'projects_memberships'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsMembershipsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""ListMembershipsProducer returns runtime status from memberships of a fleet.

      Args:
        request: (AnthospolicycontrollerstatusPaProjectsMembershipsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipsProducerResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/memberships',
        http_method='GET',
        method_id='anthospolicycontrollerstatus_pa.projects.memberships.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/memberships',
        request_field='',
        request_type_name='AnthospolicycontrollerstatusPaProjectsMembershipsListRequest',
        response_type_name='ListMembershipsProducerResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AnthospolicycontrollerstatusPaV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
