"""Generated client library for aiplatform version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.aiplatform.v1 import aiplatform_v1_messages as messages


class AiplatformV1(base_api.BaseApiClient):
  """Generated client library for service aiplatform version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://aiplatform.googleapis.com/'
  MTLS_BASE_URL = 'https://aiplatform.mtls.googleapis.com/'

  _PACKAGE = 'aiplatform'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AiplatformV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new aiplatform handle."""
    url = url or self.BASE_URL
    super(AiplatformV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.media = self.MediaService(self)
    self.projects_locations_batchPredictionJobs = self.ProjectsLocationsBatchPredictionJobsService(self)
    self.projects_locations_cachedContents = self.ProjectsLocationsCachedContentsService(self)
    self.projects_locations_customJobs = self.ProjectsLocationsCustomJobsService(self)
    self.projects_locations_datasets_annotationSpecs = self.ProjectsLocationsDatasetsAnnotationSpecsService(self)
    self.projects_locations_datasets_dataItems_annotations = self.ProjectsLocationsDatasetsDataItemsAnnotationsService(self)
    self.projects_locations_datasets_dataItems = self.ProjectsLocationsDatasetsDataItemsService(self)
    self.projects_locations_datasets_datasetVersions = self.ProjectsLocationsDatasetsDatasetVersionsService(self)
    self.projects_locations_datasets_savedQueries = self.ProjectsLocationsDatasetsSavedQueriesService(self)
    self.projects_locations_datasets = self.ProjectsLocationsDatasetsService(self)
    self.projects_locations_deploymentResourcePools = self.ProjectsLocationsDeploymentResourcePoolsService(self)
    self.projects_locations_endpoints = self.ProjectsLocationsEndpointsService(self)
    self.projects_locations_featureGroups_features = self.ProjectsLocationsFeatureGroupsFeaturesService(self)
    self.projects_locations_featureGroups = self.ProjectsLocationsFeatureGroupsService(self)
    self.projects_locations_featureOnlineStores_featureViews_featureViewSyncs = self.ProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsService(self)
    self.projects_locations_featureOnlineStores_featureViews = self.ProjectsLocationsFeatureOnlineStoresFeatureViewsService(self)
    self.projects_locations_featureOnlineStores = self.ProjectsLocationsFeatureOnlineStoresService(self)
    self.projects_locations_featurestores_entityTypes_features = self.ProjectsLocationsFeaturestoresEntityTypesFeaturesService(self)
    self.projects_locations_featurestores_entityTypes = self.ProjectsLocationsFeaturestoresEntityTypesService(self)
    self.projects_locations_featurestores = self.ProjectsLocationsFeaturestoresService(self)
    self.projects_locations_hyperparameterTuningJobs = self.ProjectsLocationsHyperparameterTuningJobsService(self)
    self.projects_locations_indexEndpoints = self.ProjectsLocationsIndexEndpointsService(self)
    self.projects_locations_indexes = self.ProjectsLocationsIndexesService(self)
    self.projects_locations_metadataStores_artifacts = self.ProjectsLocationsMetadataStoresArtifactsService(self)
    self.projects_locations_metadataStores_contexts = self.ProjectsLocationsMetadataStoresContextsService(self)
    self.projects_locations_metadataStores_executions = self.ProjectsLocationsMetadataStoresExecutionsService(self)
    self.projects_locations_metadataStores_metadataSchemas = self.ProjectsLocationsMetadataStoresMetadataSchemasService(self)
    self.projects_locations_metadataStores = self.ProjectsLocationsMetadataStoresService(self)
    self.projects_locations_migratableResources = self.ProjectsLocationsMigratableResourcesService(self)
    self.projects_locations_modelDeploymentMonitoringJobs = self.ProjectsLocationsModelDeploymentMonitoringJobsService(self)
    self.projects_locations_models_evaluations_slices = self.ProjectsLocationsModelsEvaluationsSlicesService(self)
    self.projects_locations_models_evaluations = self.ProjectsLocationsModelsEvaluationsService(self)
    self.projects_locations_models = self.ProjectsLocationsModelsService(self)
    self.projects_locations_nasJobs_nasTrialDetails = self.ProjectsLocationsNasJobsNasTrialDetailsService(self)
    self.projects_locations_nasJobs = self.ProjectsLocationsNasJobsService(self)
    self.projects_locations_notebookExecutionJobs = self.ProjectsLocationsNotebookExecutionJobsService(self)
    self.projects_locations_notebookRuntimeTemplates = self.ProjectsLocationsNotebookRuntimeTemplatesService(self)
    self.projects_locations_notebookRuntimes = self.ProjectsLocationsNotebookRuntimesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_persistentResources = self.ProjectsLocationsPersistentResourcesService(self)
    self.projects_locations_pipelineJobs = self.ProjectsLocationsPipelineJobsService(self)
    self.projects_locations_publishers_models = self.ProjectsLocationsPublishersModelsService(self)
    self.projects_locations_publishers = self.ProjectsLocationsPublishersService(self)
    self.projects_locations_ragCorpora_ragFiles = self.ProjectsLocationsRagCorporaRagFilesService(self)
    self.projects_locations_ragCorpora = self.ProjectsLocationsRagCorporaService(self)
    self.projects_locations_reasoningEngines = self.ProjectsLocationsReasoningEnginesService(self)
    self.projects_locations_schedules = self.ProjectsLocationsSchedulesService(self)
    self.projects_locations_specialistPools = self.ProjectsLocationsSpecialistPoolsService(self)
    self.projects_locations_studies_trials = self.ProjectsLocationsStudiesTrialsService(self)
    self.projects_locations_studies = self.ProjectsLocationsStudiesService(self)
    self.projects_locations_tensorboards_experiments_runs_timeSeries = self.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(self)
    self.projects_locations_tensorboards_experiments_runs = self.ProjectsLocationsTensorboardsExperimentsRunsService(self)
    self.projects_locations_tensorboards_experiments = self.ProjectsLocationsTensorboardsExperimentsService(self)
    self.projects_locations_tensorboards = self.ProjectsLocationsTensorboardsService(self)
    self.projects_locations_trainingPipelines = self.ProjectsLocationsTrainingPipelinesService(self)
    self.projects_locations_tuningJobs = self.ProjectsLocationsTuningJobsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)
    self.publishers_models = self.PublishersModelsService(self)
    self.publishers = self.PublishersService(self)

  class MediaService(base_api.BaseApiService):
    """Service class for the media resource."""

    _NAME = 'media'

    def __init__(self, client):
      super(AiplatformV1.MediaService, self).__init__(client)
      self._upload_configs = {
          'Upload': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=None,
              resumable_path=None,
              simple_multipart=True,
              simple_path='/upload/v1/{+parent}/ragFiles:upload',
          ),
          }

    def Upload(self, request, global_params=None, upload=None):
      r"""Upload a file into a RagCorpus.

      Args:
        request: (AiplatformMediaUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (GoogleCloudAiplatformV1UploadRagFileResponse) The response message.
      """
      config = self.GetMethodConfig('Upload')
      upload_config = self.GetUploadConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}/ragFiles:upload',
        http_method='POST',
        method_id='aiplatform.media.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/ragFiles:upload',
        request_field='googleCloudAiplatformV1UploadRagFileRequest',
        request_type_name='AiplatformMediaUploadRequest',
        response_type_name='GoogleCloudAiplatformV1UploadRagFileResponse',
        supports_download=False,
    )

  class ProjectsLocationsBatchPredictionJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_batchPredictionJobs resource."""

    _NAME = 'projects_locations_batchPredictionJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsBatchPredictionJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a BatchPredictionJob. Starts asynchronous cancellation on the BatchPredictionJob. The server makes the best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetBatchPredictionJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On a successful cancellation, the BatchPredictionJob is not deleted;instead its BatchPredictionJob.state is set to `CANCELLED`. Any files already outputted by the job are not deleted.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelBatchPredictionJobRequest',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a BatchPredictionJob. A BatchPredictionJob once created will right away be attempted to start.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/batchPredictionJobs',
        request_field='googleCloudAiplatformV1BatchPredictionJob',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchPredictionJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a BatchPredictionJob. Can only be called on jobs that already finished.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.batchPredictionJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BatchPredictionJob.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1BatchPredictionJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BatchPredictionJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListBatchPredictionJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/batchPredictionJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListBatchPredictionJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCachedContentsService(base_api.BaseApiService):
    """Service class for the projects_locations_cachedContents resource."""

    _NAME = 'projects_locations_cachedContents'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsCachedContentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates cached content, this call will initialize the cached content in the data storage, and users need to pay for the cache data storage.

      Args:
        request: (AiplatformProjectsLocationsCachedContentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CachedContent) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cachedContents',
        http_method='POST',
        method_id='aiplatform.projects.locations.cachedContents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/cachedContents',
        request_field='googleCloudAiplatformV1CachedContent',
        request_type_name='AiplatformProjectsLocationsCachedContentsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1CachedContent',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes cached content.

      Args:
        request: (AiplatformProjectsLocationsCachedContentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cachedContents/{cachedContentsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.cachedContents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCachedContentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets cached content configurations.

      Args:
        request: (AiplatformProjectsLocationsCachedContentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CachedContent) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cachedContents/{cachedContentsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.cachedContents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCachedContentsGetRequest',
        response_type_name='GoogleCloudAiplatformV1CachedContent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists cached contents in a project.

      Args:
        request: (AiplatformProjectsLocationsCachedContentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListCachedContentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cachedContents',
        http_method='GET',
        method_id='aiplatform.projects.locations.cachedContents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/cachedContents',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCachedContentsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListCachedContentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates cached content configurations.

      Args:
        request: (AiplatformProjectsLocationsCachedContentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CachedContent) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cachedContents/{cachedContentsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.cachedContents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1CachedContent',
        request_type_name='AiplatformProjectsLocationsCachedContentsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1CachedContent',
        supports_download=False,
    )

  class ProjectsLocationsCustomJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_customJobs resource."""

    _NAME = 'projects_locations_customJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsCustomJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a CustomJob. Starts asynchronous cancellation on the CustomJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetCustomJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the CustomJob is not deleted; instead it becomes a job with a CustomJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and CustomJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelCustomJobRequest',
        request_type_name='AiplatformProjectsLocationsCustomJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a CustomJob. A created CustomJob right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customJobs',
        request_field='googleCloudAiplatformV1CustomJob',
        request_type_name='AiplatformProjectsLocationsCustomJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1CustomJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.customJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1CustomJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CustomJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListCustomJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/customJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListCustomJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsAnnotationSpecsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationSpecs resource."""

    _NAME = 'projects_locations_datasets_annotationSpecs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsAnnotationSpecsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an AnnotationSpec.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AnnotationSpec) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest',
        response_type_name='GoogleCloudAiplatformV1AnnotationSpec',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsAnnotationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems_annotations resource."""

    _NAME = 'projects_locations_datasets_dataItems_annotations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsDataItemsAnnotationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists Annotations belongs to a dataitem.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/annotations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListAnnotationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems resource."""

    _NAME = 'projects_locations_datasets_dataItems'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsDataItemsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists DataItems in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/dataItems',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDataItemsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDatasetVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_datasetVersions resource."""

    _NAME = 'projects_locations_datasets_datasetVersions'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsDatasetVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a version from a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/datasetVersions',
        request_field='googleCloudAiplatformV1DatasetVersion',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Dataset version.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions/{datasetVersionsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Dataset version.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DatasetVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions/{datasetVersionsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsGetRequest',
        response_type_name='GoogleCloudAiplatformV1DatasetVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DatasetVersions in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDatasetVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/datasetVersions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDatasetVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a DatasetVersion.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DatasetVersion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions/{datasetVersionsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1DatasetVersion',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1DatasetVersion',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a dataset version.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDatasetVersionsRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/datasetVersions/{datasetVersionsId}:restore',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.datasetVersions.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restore',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDatasetVersionsRestoreRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsSavedQueriesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_savedQueries resource."""

    _NAME = 'projects_locations_datasets_savedQueries'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a SavedQuery.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.savedQueries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SavedQueries in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/savedQueries',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListSavedQueriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets resource."""

    _NAME = 'projects_locations_datasets'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/datasets',
        request_field='googleCloudAiplatformV1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data from a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:export',
        request_field='googleCloudAiplatformV1ExportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Dataset',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:import',
        request_field='googleCloudAiplatformV1ImportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Datasets in a Location.

      Args:
        request: (AiplatformProjectsLocationsDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/datasets',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDatasetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.datasets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Dataset',
        supports_download=False,
    )

    def SearchDataItems(self, request, global_params=None):
      r"""Searches DataItems in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSearchDataItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchDataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchDataItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchDataItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:searchDataItems',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.searchDataItems',
        ordered_params=['dataset'],
        path_params=['dataset'],
        query_params=['annotationFilters', 'annotationsFilter', 'annotationsLimit', 'dataItemFilter', 'dataLabelingJob', 'fieldMask', 'orderBy', 'orderByAnnotation_orderBy', 'orderByAnnotation_savedQuery', 'orderByDataItem', 'pageSize', 'pageToken', 'savedQuery'],
        relative_path='v1/{+dataset}:searchDataItems',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSearchDataItemsRequest',
        response_type_name='GoogleCloudAiplatformV1SearchDataItemsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeploymentResourcePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_deploymentResourcePools resource."""

    _NAME = 'projects_locations_deploymentResourcePools'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDeploymentResourcePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a DeploymentResourcePool.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools',
        http_method='POST',
        method_id='aiplatform.projects.locations.deploymentResourcePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deploymentResourcePools',
        request_field='googleCloudAiplatformV1CreateDeploymentResourcePoolRequest',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a DeploymentResourcePool.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools/{deploymentResourcePoolsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.deploymentResourcePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a DeploymentResourcePool.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DeploymentResourcePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools/{deploymentResourcePoolsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.deploymentResourcePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsGetRequest',
        response_type_name='GoogleCloudAiplatformV1DeploymentResourcePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List DeploymentResourcePools in a location.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDeploymentResourcePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools',
        http_method='GET',
        method_id='aiplatform.projects.locations.deploymentResourcePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/deploymentResourcePools',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDeploymentResourcePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a DeploymentResourcePool.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools/{deploymentResourcePoolsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.deploymentResourcePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1DeploymentResourcePool',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryDeployedModels(self, request, global_params=None):
      r"""List DeployedModels that have been deployed on this DeploymentResourcePool.

      Args:
        request: (AiplatformProjectsLocationsDeploymentResourcePoolsQueryDeployedModelsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1QueryDeployedModelsResponse) The response message.
      """
      config = self.GetMethodConfig('QueryDeployedModels')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryDeployedModels.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deploymentResourcePools/{deploymentResourcePoolsId}:queryDeployedModels',
        http_method='GET',
        method_id='aiplatform.projects.locations.deploymentResourcePools.queryDeployedModels',
        ordered_params=['deploymentResourcePool'],
        path_params=['deploymentResourcePool'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+deploymentResourcePool}:queryDeployedModels',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDeploymentResourcePoolsQueryDeployedModelsRequest',
        response_type_name='GoogleCloudAiplatformV1QueryDeployedModelsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_endpoints resource."""

    _NAME = 'projects_locations_endpoints'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def ComputeTokens(self, request, global_params=None):
      r"""Return a list of tokens based on the input text.

      Args:
        request: (AiplatformProjectsLocationsEndpointsComputeTokensRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ComputeTokensResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeTokens')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeTokens.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:computeTokens',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.computeTokens',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:computeTokens',
        request_field='googleCloudAiplatformV1ComputeTokensRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsComputeTokensRequest',
        response_type_name='GoogleCloudAiplatformV1ComputeTokensResponse',
        supports_download=False,
    )

    def CountTokens(self, request, global_params=None):
      r"""Perform a token counting.

      Args:
        request: (AiplatformProjectsLocationsEndpointsCountTokensRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CountTokensResponse) The response message.
      """
      config = self.GetMethodConfig('CountTokens')
      return self._RunMethod(
          config, request, global_params=global_params)

    CountTokens.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:countTokens',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.countTokens',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:countTokens',
        request_field='googleCloudAiplatformV1CountTokensRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsCountTokensRequest',
        response_type_name='GoogleCloudAiplatformV1CountTokensResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endpointId'],
        relative_path='v1/{+parent}/endpoints',
        request_field='googleCloudAiplatformV1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.endpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployModel(self, request, global_params=None):
      r"""Deploys a Model into this Endpoint, creating a DeployedModel within it.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:deployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.deployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:deployModel',
        request_field='googleCloudAiplatformV1DeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsDeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DirectPredict(self, request, global_params=None):
      r"""Perform an unary online prediction request to a gRPC model server for Vertex first-party products and frameworks.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDirectPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DirectPredictResponse) The response message.
      """
      config = self.GetMethodConfig('DirectPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    DirectPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:directPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.directPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:directPredict',
        request_field='googleCloudAiplatformV1DirectPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsDirectPredictRequest',
        response_type_name='GoogleCloudAiplatformV1DirectPredictResponse',
        supports_download=False,
    )

    def DirectRawPredict(self, request, global_params=None):
      r"""Perform an unary online prediction request to a gRPC model server for custom containers.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDirectRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DirectRawPredictResponse) The response message.
      """
      config = self.GetMethodConfig('DirectRawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    DirectRawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:directRawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.directRawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:directRawPredict',
        request_field='googleCloudAiplatformV1DirectRawPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsDirectRawPredictRequest',
        response_type_name='GoogleCloudAiplatformV1DirectRawPredictResponse',
        supports_download=False,
    )

    def Explain(self, request, global_params=None):
      r"""Perform an online explanation. If deployed_model_id is specified, the corresponding DeployModel must have explanation_spec populated. If deployed_model_id is not specified, all DeployedModels must have explanation_spec populated.

      Args:
        request: (AiplatformProjectsLocationsEndpointsExplainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ExplainResponse) The response message.
      """
      config = self.GetMethodConfig('Explain')
      return self._RunMethod(
          config, request, global_params=global_params)

    Explain.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:explain',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.explain',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:explain',
        request_field='googleCloudAiplatformV1ExplainRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsExplainRequest',
        response_type_name='GoogleCloudAiplatformV1ExplainResponse',
        supports_download=False,
    )

    def GenerateContent(self, request, global_params=None):
      r"""Generate content with multimodal inputs.

      Args:
        request: (AiplatformProjectsLocationsEndpointsGenerateContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1GenerateContentResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:generateContent',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.generateContent',
        ordered_params=['model'],
        path_params=['model'],
        query_params=[],
        relative_path='v1/{+model}:generateContent',
        request_field='googleCloudAiplatformV1GenerateContentRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsGenerateContentRequest',
        response_type_name='GoogleCloudAiplatformV1GenerateContentResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Endpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Endpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'gdcZone', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/endpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListEndpointsResponse',
        supports_download=False,
    )

    def MutateDeployedModel(self, request, global_params=None):
      r"""Updates an existing deployed model. Updatable fields include `min_replica_count`, `max_replica_count`, `autoscaling_metric_specs`, `disable_container_logging` (v1 only), and `enable_container_logging` (v1beta1 only).

      Args:
        request: (AiplatformProjectsLocationsEndpointsMutateDeployedModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('MutateDeployedModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    MutateDeployedModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:mutateDeployedModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.mutateDeployedModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:mutateDeployedModel',
        request_field='googleCloudAiplatformV1MutateDeployedModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsMutateDeployedModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.endpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Endpoint',
        supports_download=False,
    )

    def Predict(self, request, global_params=None):
      r"""Perform an online prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PredictResponse) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:predict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.predict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:predict',
        request_field='googleCloudAiplatformV1PredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsPredictRequest',
        response_type_name='GoogleCloudAiplatformV1PredictResponse',
        supports_download=False,
    )

    def PredictLongRunning(self, request, global_params=None):
      r"""PredictLongRunning method for the projects_locations_endpoints service.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPredictLongRunningRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('PredictLongRunning')
      return self._RunMethod(
          config, request, global_params=global_params)

    PredictLongRunning.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:predictLongRunning',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.predictLongRunning',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:predictLongRunning',
        request_field='googleCloudAiplatformV1PredictLongRunningRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsPredictLongRunningRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RawPredict(self, request, global_params=None):
      r"""Perform an online prediction with an arbitrary HTTP payload. The response includes the following HTTP headers: * `X-Vertex-AI-Endpoint-Id`: ID of the Endpoint that served this prediction. * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's DeployedModel that served this prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('RawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:rawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.rawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:rawPredict',
        request_field='googleCloudAiplatformV1RawPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def ServerStreamingPredict(self, request, global_params=None):
      r"""Perform a server-side streaming online prediction request for Vertex LLM streaming.

      Args:
        request: (AiplatformProjectsLocationsEndpointsServerStreamingPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1StreamingPredictResponse) The response message.
      """
      config = self.GetMethodConfig('ServerStreamingPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    ServerStreamingPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:serverStreamingPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.serverStreamingPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:serverStreamingPredict',
        request_field='googleCloudAiplatformV1StreamingPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsServerStreamingPredictRequest',
        response_type_name='GoogleCloudAiplatformV1StreamingPredictResponse',
        supports_download=False,
    )

    def StreamGenerateContent(self, request, global_params=None):
      r"""Generate content with multimodal inputs with streaming support.

      Args:
        request: (AiplatformProjectsLocationsEndpointsStreamGenerateContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1GenerateContentResponse) The response message.
      """
      config = self.GetMethodConfig('StreamGenerateContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamGenerateContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:streamGenerateContent',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.streamGenerateContent',
        ordered_params=['model'],
        path_params=['model'],
        query_params=[],
        relative_path='v1/{+model}:streamGenerateContent',
        request_field='googleCloudAiplatformV1GenerateContentRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsStreamGenerateContentRequest',
        response_type_name='GoogleCloudAiplatformV1GenerateContentResponse',
        supports_download=False,
    )

    def StreamRawPredict(self, request, global_params=None):
      r"""Perform a streaming online prediction with an arbitrary HTTP payload.

      Args:
        request: (AiplatformProjectsLocationsEndpointsStreamRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('StreamRawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamRawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:streamRawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.streamRawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:streamRawPredict',
        request_field='googleCloudAiplatformV1StreamRawPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsStreamRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def UndeployModel(self, request, global_params=None):
      r"""Undeploys a Model from an Endpoint, removing a DeployedModel from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsEndpointsUndeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:undeployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.undeployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:undeployModel',
        request_field='googleCloudAiplatformV1UndeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsUndeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an Endpoint with a long running operation.

      Args:
        request: (AiplatformProjectsLocationsEndpointsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:update',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:update',
        request_field='googleCloudAiplatformV1UpdateEndpointLongRunningRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeatureGroupsFeaturesService(base_api.BaseApiService):
    """Service class for the projects_locations_featureGroups_features resource."""

    _NAME = 'projects_locations_featureGroups_features'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeatureGroupsFeaturesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a batch of Features in a given FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.features.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/features:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateFeaturesRequest',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesBatchCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Feature in a given FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.features.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureId'],
        relative_path='v1/{+parent}/features',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features/{featuresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featureGroups.features.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Feature) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features/{featuresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureGroups.features.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Feature',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Features in a given FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureGroups.features.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'latestStatsCount', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/features',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeaturesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsFeaturesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}/features/{featuresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featureGroups.features.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsFeaturesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeatureGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_featureGroups resource."""

    _NAME = 'projects_locations_featureGroups'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeatureGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FeatureGroup in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureGroupId'],
        relative_path='v1/{+parent}/featureGroups',
        request_field='googleCloudAiplatformV1FeatureGroup',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featureGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1FeatureGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsGetRequest',
        response_type_name='GoogleCloudAiplatformV1FeatureGroup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FeatureGroups in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeatureGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/featureGroups',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeatureGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single FeatureGroup.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featureGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1FeatureGroup',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeatureGroupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureGroups/{featureGroupsId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureGroups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureGroupsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsService(base_api.BaseApiService):
    """Service class for the projects_locations_featureOnlineStores_featureViews_featureViewSyncs resource."""

    _NAME = 'projects_locations_featureOnlineStores_featureViews_featureViewSyncs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single FeatureViewSync.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1FeatureViewSync) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}/featureViewSyncs/{featureViewSyncsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.featureViewSyncs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsGetRequest',
        response_type_name='GoogleCloudAiplatformV1FeatureViewSync',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FeatureViewSyncs in a given FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeatureViewSyncsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}/featureViewSyncs',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.featureViewSyncs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/featureViewSyncs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFeatureViewSyncsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeatureViewSyncsResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeatureOnlineStoresFeatureViewsService(base_api.BaseApiService):
    """Service class for the projects_locations_featureOnlineStores_featureViews resource."""

    _NAME = 'projects_locations_featureOnlineStores_featureViews'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeatureOnlineStoresFeatureViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FeatureView in a given FeatureOnlineStore.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureViewId', 'runSyncImmediately'],
        relative_path='v1/{+parent}/featureViews',
        request_field='googleCloudAiplatformV1FeatureView',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def FetchFeatureValues(self, request, global_params=None):
      r"""Fetch feature values under a FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFetchFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1FetchFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('FetchFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:fetchFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.fetchFeatureValues',
        ordered_params=['featureView'],
        path_params=['featureView'],
        query_params=[],
        relative_path='v1/{+featureView}:fetchFeatureValues',
        request_field='googleCloudAiplatformV1FetchFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsFetchFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1FetchFeatureValuesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1FeatureView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsGetRequest',
        response_type_name='GoogleCloudAiplatformV1FeatureView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FeatureViews in a given FeatureOnlineStore.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeatureViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/featureViews',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeatureViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1FeatureView',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchNearestEntities(self, request, global_params=None):
      r"""Search the nearest entities under a FeatureView. Search only works for indexable feature view; if a feature view isn't indexable, returns Invalid argument response.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSearchNearestEntitiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchNearestEntitiesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchNearestEntities')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchNearestEntities.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:searchNearestEntities',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.searchNearestEntities',
        ordered_params=['featureView'],
        path_params=['featureView'],
        query_params=[],
        relative_path='v1/{+featureView}:searchNearestEntities',
        request_field='googleCloudAiplatformV1SearchNearestEntitiesRequest',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSearchNearestEntitiesRequest',
        response_type_name='GoogleCloudAiplatformV1SearchNearestEntitiesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def Sync(self, request, global_params=None):
      r"""Triggers on-demand sync for the FeatureView.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SyncFeatureViewResponse) The response message.
      """
      config = self.GetMethodConfig('Sync')
      return self._RunMethod(
          config, request, global_params=global_params)

    Sync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:sync',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.sync',
        ordered_params=['featureView'],
        path_params=['featureView'],
        query_params=[],
        relative_path='v1/{+featureView}:sync',
        request_field='googleCloudAiplatformV1SyncFeatureViewRequest',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsSyncRequest',
        response_type_name='GoogleCloudAiplatformV1SyncFeatureViewResponse',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}/featureViews/{featureViewsId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.featureViews.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresFeatureViewsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeatureOnlineStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_featureOnlineStores resource."""

    _NAME = 'projects_locations_featureOnlineStores'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeatureOnlineStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FeatureOnlineStore in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureOnlineStoreId'],
        relative_path='v1/{+parent}/featureOnlineStores',
        request_field='googleCloudAiplatformV1FeatureOnlineStore',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single FeatureOnlineStore. The FeatureOnlineStore must not contain any FeatureViews.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featureOnlineStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single FeatureOnlineStore.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1FeatureOnlineStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1FeatureOnlineStore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FeatureOnlineStores in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeatureOnlineStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores',
        http_method='GET',
        method_id='aiplatform.projects.locations.featureOnlineStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/featureOnlineStores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeatureOnlineStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single FeatureOnlineStore.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featureOnlineStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1FeatureOnlineStore',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeatureOnlineStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featureOnlineStores/{featureOnlineStoresId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featureOnlineStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeatureOnlineStoresTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesFeaturesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes_features resource."""

    _NAME = 'projects_locations_featurestores_entityTypes_features'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresEntityTypesFeaturesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a batch of Features in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/features:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateFeaturesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesBatchCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Feature in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureId'],
        relative_path='v1/{+parent}/features',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Feature) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Feature',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Features in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'latestStatsCount', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/features',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeaturesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Feature) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Feature',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes resource."""

    _NAME = 'projects_locations_featurestores_entityTypes'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EntityType in a given Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['entityTypeId'],
        relative_path='v1/{+parent}/entityTypes',
        request_field='googleCloudAiplatformV1EntityType',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EntityType. The EntityType must not have any Features or `force` must be set to true for the request to succeed.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeleteFeatureValues(self, request, global_params=None):
      r"""Delete Feature values from Featurestore. The progress of the deletion is tracked by the returned operation. The deleted feature values are guaranteed to be invisible to subsequent read operations after the operation is marked as successfully done. If a delete feature values operation fails, the feature values returned from reads and exports may be inconsistent. If consistency is required, the caller must retry the same delete request again and wait till the new operation returned is marked as successfully done.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeleteFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:deleteFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.deleteFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:deleteFeatureValues',
        request_field='googleCloudAiplatformV1DeleteFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportFeatureValues(self, request, global_params=None):
      r"""Exports Feature values from all the entities of a target EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesExportFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ExportFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:exportFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.exportFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:exportFeatureValues',
        request_field='googleCloudAiplatformV1ExportFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesExportFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1EntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesGetRequest',
        response_type_name='GoogleCloudAiplatformV1EntityType',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def ImportFeatureValues(self, request, global_params=None):
      r"""Imports Feature values into the Featurestore from a source storage. The progress of the import is tracked by the returned operation. The imported features are guaranteed to be visible to subsequent read operations after the operation is marked as successfully done. If an import operation fails, the Feature values returned from reads and exports may be inconsistent. If consistency is required, the caller must retry the same import request again and wait till the new operation returned is marked as successfully done. There are also scenarios where the caller can cause inconsistency. - Source data for import contains multiple distinct Feature values for the same entity ID and timestamp. - Source is modified during an import. This includes adding, updating, or removing source data and/or metadata. Examples of updating metadata include but are not limited to changing storage location, storage class, or retention policy. - Online serving cluster is under-provisioned.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesImportFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ImportFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:importFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.importFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:importFeatureValues',
        request_field='googleCloudAiplatformV1ImportFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesImportFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EntityTypes in a given Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/entityTypes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1EntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1EntityType',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1EntityType',
        supports_download=False,
    )

    def ReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values of a specific entity of an EntityType. For reading feature values of multiple entities of an EntityType, please use StreamingReadFeatureValues.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('ReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:readFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.readFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:readFeatureValues',
        request_field='googleCloudAiplatformV1ReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1ReadFeatureValuesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def StreamingReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values for multiple entities. Depending on their size, data for different entities may be broken up across multiple responses.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('StreamingReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamingReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:streamingReadFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.streamingReadFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:streamingReadFeatureValues',
        request_field='googleCloudAiplatformV1StreamingReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1ReadFeatureValuesResponse',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def WriteFeatureValues(self, request, global_params=None):
      r"""Writes Feature values of one or more entities of an EntityType. The Feature values are merged into existing entities if any. The Feature values to be written must have timestamp within the online storage retention.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesWriteFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('WriteFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    WriteFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:writeFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.writeFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:writeFeatureValues',
        request_field='googleCloudAiplatformV1WriteFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesWriteFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1WriteFeatureValuesResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores resource."""

    _NAME = 'projects_locations_featurestores'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchReadFeatureValues(self, request, global_params=None):
      r"""Batch reads Feature values from a Featurestore. This API enables batch reading Feature values, where each read instance in the batch may read Feature values of entities from one or more EntityTypes. Point-in-time correctness is guaranteed for Feature values of each read instance as of each instance's read timestamp.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresBatchReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:batchReadFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.batchReadFeatureValues',
        ordered_params=['featurestore'],
        path_params=['featurestore'],
        query_params=[],
        relative_path='v1/{+featurestore}:batchReadFeatureValues',
        request_field='googleCloudAiplatformV1BatchReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresBatchReadFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Featurestore in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featurestoreId'],
        relative_path='v1/{+parent}/featurestores',
        request_field='googleCloudAiplatformV1Featurestore',
        request_type_name='AiplatformProjectsLocationsFeaturestoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Featurestore. The Featurestore must not contain any EntityTypes or `force` must be set to true for the request to succeed.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Featurestore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1Featurestore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Featurestores in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeaturestoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/featurestores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeaturestoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Featurestore',
        request_type_name='AiplatformProjectsLocationsFeaturestoresPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchFeatures(self, request, global_params=None):
      r"""Searches Features matching a query in a given project.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresSearchFeaturesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchFeatures')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchFeatures.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores:searchFeatures',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.searchFeatures',
        ordered_params=['location'],
        path_params=['location'],
        query_params=['pageSize', 'pageToken', 'query'],
        relative_path='v1/{+location}/featurestores:searchFeatures',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresSearchFeaturesRequest',
        response_type_name='GoogleCloudAiplatformV1SearchFeaturesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsHyperparameterTuningJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_hyperparameterTuningJobs resource."""

    _NAME = 'projects_locations_hyperparameterTuningJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsHyperparameterTuningJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a HyperparameterTuningJob. Starts asynchronous cancellation on the HyperparameterTuningJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetHyperparameterTuningJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the HyperparameterTuningJob is not deleted; instead it becomes a job with a HyperparameterTuningJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and HyperparameterTuningJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelHyperparameterTuningJobRequest',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/hyperparameterTuningJobs',
        request_field='googleCloudAiplatformV1HyperparameterTuningJob',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1HyperparameterTuningJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1HyperparameterTuningJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HyperparameterTuningJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListHyperparameterTuningJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/hyperparameterTuningJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListHyperparameterTuningJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsIndexEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_indexEndpoints resource."""

    _NAME = 'projects_locations_indexEndpoints'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsIndexEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/indexEndpoints',
        request_field='googleCloudAiplatformV1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployIndex(self, request, global_params=None):
      r"""Deploys an Index into this IndexEndpoint, creating a DeployedIndex within it.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:deployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.deployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:deployIndex',
        request_field='googleCloudAiplatformV1DeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1IndexEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists IndexEndpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListIndexEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/indexEndpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListIndexEndpointsResponse',
        supports_download=False,
    )

    def MutateDeployedIndex(self, request, global_params=None):
      r"""Update an existing DeployedIndex under an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('MutateDeployedIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    MutateDeployedIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:mutateDeployedIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.mutateDeployedIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:mutateDeployedIndex',
        request_field='googleCloudAiplatformV1DeployedIndex',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1IndexEndpoint',
        supports_download=False,
    )

    def UndeployIndex(self, request, global_params=None):
      r"""Undeploys an Index from an IndexEndpoint, removing a DeployedIndex from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:undeployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.undeployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:undeployIndex',
        request_field='googleCloudAiplatformV1UndeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsIndexesService(base_api.BaseApiService):
    """Service class for the projects_locations_indexes resource."""

    _NAME = 'projects_locations_indexes'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsIndexesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/indexes',
        request_field='googleCloudAiplatformV1Index',
        request_type_name='AiplatformProjectsLocationsIndexesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Index. An Index can only be deleted when all its DeployedIndexes had been undeployed.

      Args:
        request: (AiplatformProjectsLocationsIndexesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Index) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Index',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Indexes in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListIndexesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/indexes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListIndexesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Index',
        request_type_name='AiplatformProjectsLocationsIndexesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RemoveDatapoints(self, request, global_params=None):
      r"""Remove Datapoints from an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesRemoveDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RemoveDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:removeDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.removeDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1/{+index}:removeDatapoints',
        request_field='googleCloudAiplatformV1RemoveDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesRemoveDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1RemoveDatapointsResponse',
        supports_download=False,
    )

    def UpsertDatapoints(self, request, global_params=None):
      r"""Add/update Datapoints into an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesUpsertDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1UpsertDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('UpsertDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpsertDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:upsertDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.upsertDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1/{+index}:upsertDatapoints',
        request_field='googleCloudAiplatformV1UpsertDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesUpsertDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1UpsertDatapointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_artifacts resource."""

    _NAME = 'projects_locations_metadataStores_artifacts'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Artifact associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['artifactId'],
        relative_path='v1/{+parent}/artifacts',
        request_field='googleCloudAiplatformV1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Artifacts in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/artifacts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListArtifactsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Artifacts.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/artifacts:purge',
        request_field='googleCloudAiplatformV1PurgeArtifactsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryArtifactLineageSubgraph(self, request, global_params=None):
      r"""Retrieves lineage of an Artifact represented through Artifacts and Executions connected by Event edges and returned as a LineageSubgraph.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsQueryArtifactLineageSubgraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryArtifactLineageSubgraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryArtifactLineageSubgraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}:queryArtifactLineageSubgraph',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.queryArtifactLineageSubgraph',
        ordered_params=['artifact'],
        path_params=['artifact'],
        query_params=['filter', 'maxHops'],
        relative_path='v1/{+artifact}:queryArtifactLineageSubgraph',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsQueryArtifactLineageSubgraphRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_contexts resource."""

    _NAME = 'projects_locations_metadataStores_contexts'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddContextArtifactsAndExecutions(self, request, global_params=None):
      r"""Adds a set of Artifacts and Executions to a Context. If any of the Artifacts or Executions have already been added to a Context, they are simply skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextArtifactsAndExecutions')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextArtifactsAndExecutions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextArtifactsAndExecutions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextArtifactsAndExecutions',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:addContextArtifactsAndExecutions',
        request_field='googleCloudAiplatformV1AddContextArtifactsAndExecutionsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest',
        response_type_name='GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse',
        supports_download=False,
    )

    def AddContextChildren(self, request, global_params=None):
      r"""Adds a set of Contexts as children to a parent Context. If any of the child Contexts have already been added to the parent Context, they are simply skipped. If this call would create a cycle or cause any Context to have more than 10 parents, the request will fail with an INVALID_ARGUMENT error.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddContextChildrenResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextChildren')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextChildren.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextChildren',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextChildren',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:addContextChildren',
        request_field='googleCloudAiplatformV1AddContextChildrenRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest',
        response_type_name='GoogleCloudAiplatformV1AddContextChildrenResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Context associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['contextId'],
        relative_path='v1/{+parent}/contexts',
        request_field='googleCloudAiplatformV1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Contexts on the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/contexts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Contexts.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/contexts:purge',
        request_field='googleCloudAiplatformV1PurgeContextsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryContextLineageSubgraph(self, request, global_params=None):
      r"""Retrieves Artifacts and Executions within the specified Context, connected by Event edges and returned as a LineageSubgraph.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryContextLineageSubgraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryContextLineageSubgraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:queryContextLineageSubgraph',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.queryContextLineageSubgraph',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:queryContextLineageSubgraph',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

    def RemoveContextChildren(self, request, global_params=None):
      r"""Remove a set of children contexts from a parent Context. If any of the child Contexts were NOT added to the parent Context, they are simply skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsRemoveContextChildrenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RemoveContextChildrenResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveContextChildren')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveContextChildren.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:removeContextChildren',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.removeContextChildren',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:removeContextChildren',
        request_field='googleCloudAiplatformV1RemoveContextChildrenRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsRemoveContextChildrenRequest',
        response_type_name='GoogleCloudAiplatformV1RemoveContextChildrenResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_executions resource."""

    _NAME = 'projects_locations_metadataStores_executions'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddExecutionEvents(self, request, global_params=None):
      r"""Adds Events to the specified Execution. An Event indicates whether an Artifact was used as an input or output for an Execution. If an Event already exists between the Execution and the Artifact, the Event is skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddExecutionEventsResponse) The response message.
      """
      config = self.GetMethodConfig('AddExecutionEvents')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddExecutionEvents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:addExecutionEvents',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.addExecutionEvents',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1/{+execution}:addExecutionEvents',
        request_field='googleCloudAiplatformV1AddExecutionEventsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest',
        response_type_name='GoogleCloudAiplatformV1AddExecutionEventsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an Execution associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['executionId'],
        relative_path='v1/{+parent}/executions',
        request_field='googleCloudAiplatformV1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.executions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Executions in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/executions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListExecutionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.executions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Executions.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/executions:purge',
        request_field='googleCloudAiplatformV1PurgeExecutionsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryExecutionInputsAndOutputs(self, request, global_params=None):
      r"""Obtains the set of input and output Artifacts for this Execution, in the form of LineageSubgraph that also contains the Execution and connecting Events.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryExecutionInputsAndOutputs')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryExecutionInputsAndOutputs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:queryExecutionInputsAndOutputs',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.queryExecutionInputsAndOutputs',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1/{+execution}:queryExecutionInputsAndOutputs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresMetadataSchemasService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_metadataSchemas resource."""

    _NAME = 'projects_locations_metadataStores_metadataSchemas'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresMetadataSchemasService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a MetadataSchema.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataSchema) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataSchemaId'],
        relative_path='v1/{+parent}/metadataSchemas',
        request_field='googleCloudAiplatformV1MetadataSchema',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasCreateRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataSchema',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific MetadataSchema.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataSchema) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas/{metadataSchemasId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasGetRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataSchema',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MetadataSchemas.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListMetadataSchemasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/metadataSchemas',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasListRequest',
        response_type_name='GoogleCloudAiplatformV1ListMetadataSchemasResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores resource."""

    _NAME = 'projects_locations_metadataStores'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Initializes a MetadataStore, including allocation of resources.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataStoreId'],
        relative_path='v1/{+parent}/metadataStores',
        request_field='googleCloudAiplatformV1MetadataStore',
        request_type_name='AiplatformProjectsLocationsMetadataStoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MetadataStore and all its child resources (Artifacts, Executions, and Contexts).

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataStore',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MetadataStores for a Location.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListMetadataStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/metadataStores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresListRequest',
        response_type_name='GoogleCloudAiplatformV1ListMetadataStoresResponse',
        supports_download=False,
    )

  class ProjectsLocationsMigratableResourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_migratableResources resource."""

    _NAME = 'projects_locations_migratableResources'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMigratableResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchMigrate(self, request, global_params=None):
      r"""Batch migrates resources from ml.googleapis.com, automl.googleapis.com, and datalabeling.googleapis.com to Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchMigrate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchMigrate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/migratableResources:batchMigrate',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.batchMigrate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/migratableResources:batchMigrate',
        request_field='googleCloudAiplatformV1BatchMigrateResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches all of the resources in automl.googleapis.com, datalabeling.googleapis.com and ml.googleapis.com that can be migrated to Vertex AI's given location.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchMigratableResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/migratableResources:search',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/migratableResources:search',
        request_field='googleCloudAiplatformV1SearchMigratableResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesSearchRequest',
        response_type_name='GoogleCloudAiplatformV1SearchMigratableResourcesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelDeploymentMonitoringJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_modelDeploymentMonitoringJobs resource."""

    _NAME = 'projects_locations_modelDeploymentMonitoringJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelDeploymentMonitoringJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a ModelDeploymentMonitoringJob. It will run periodically on a configured interval.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='googleCloudAiplatformV1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelDeploymentMonitoringJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelDeploymentMonitoringJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelDeploymentMonitoringJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses a ModelDeploymentMonitoringJob. If the job is running, the server makes a best effort to cancel the job. Will mark ModelDeploymentMonitoringJob.state to 'PAUSED'.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:pause',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.pause',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:pause',
        request_field='googleCloudAiplatformV1PauseModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes a paused ModelDeploymentMonitoringJob. It will start to run from next scheduled time. A deleted ModelDeploymentMonitoringJob can't be resumed.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:resume',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resume',
        request_field='googleCloudAiplatformV1ResumeModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def SearchModelDeploymentMonitoringStatsAnomalies(self, request, global_params=None):
      r"""Searches Model Monitoring Statistics generated within a given time window.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchModelDeploymentMonitoringStatsAnomalies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchModelDeploymentMonitoringStatsAnomalies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:searchModelDeploymentMonitoringStatsAnomalies',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.searchModelDeploymentMonitoringStatsAnomalies',
        ordered_params=['modelDeploymentMonitoringJob'],
        path_params=['modelDeploymentMonitoringJob'],
        query_params=[],
        relative_path='v1/{+modelDeploymentMonitoringJob}:searchModelDeploymentMonitoringStatsAnomalies',
        request_field='googleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest',
        response_type_name='GoogleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsSlicesService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations_slices resource."""

    _NAME = 'projects_locations_models_evaluations_slices'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsEvaluationsSlicesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchImport(self, request, global_params=None):
      r"""Imports a list of externally generated EvaluatedAnnotations.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesBatchImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchImportEvaluatedAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchImport')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchImport.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices/{slicesId}:batchImport',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.slices.batchImport',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:batchImport',
        request_field='googleCloudAiplatformV1BatchImportEvaluatedAnnotationsRequest',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesBatchImportRequest',
        response_type_name='GoogleCloudAiplatformV1BatchImportEvaluatedAnnotationsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluationSlice.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluationSlice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices/{slicesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluationSlice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluationSlices in a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelEvaluationSlicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/slices',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelEvaluationSlicesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations resource."""

    _NAME = 'projects_locations_models_evaluations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluation',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports an externally generated ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/evaluations:import',
        request_field='googleCloudAiplatformV1ImportModelEvaluationRequest',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsImportRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluations in a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/evaluations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_models resource."""

    _NAME = 'projects_locations_models'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Copy(self, request, global_params=None):
      r"""Copies an already existing Vertex AI Model into the specified Location. The source Model must exist in the same Project. When copying custom Models, the users themselves are responsible for Model.metadata content to be region-agnostic, as well as making sure that any resources (e.g. files) it depends on remain accessible.

      Args:
        request: (AiplatformProjectsLocationsModelsCopyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Copy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Copy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models:copy',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.copy',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/models:copy',
        request_field='googleCloudAiplatformV1CopyModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsCopyRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Model. A model cannot be deleted if any Endpoint resource has a DeployedModel based on the model in its deployed_models field.

      Args:
        request: (AiplatformProjectsLocationsModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeleteVersion(self, request, global_params=None):
      r"""Deletes a Model version. Model version can only be deleted if there are no DeployedModels created from it. Deleting the only version in the Model is not allowed. Use DeleteModel for deleting the Model instead.

      Args:
        request: (AiplatformProjectsLocationsModelsDeleteVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeleteVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:deleteVersion',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.deleteVersion',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:deleteVersion',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsDeleteVersionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports a trained, exportable Model to a location specified by the user. A Model is considered to be exportable if it has at least one supported export format.

      Args:
        request: (AiplatformProjectsLocationsModelsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:export',
        request_field='googleCloudAiplatformV1ExportModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsModelsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Models in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/models',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelsResponse',
        supports_download=False,
    )

    def ListCheckpoints(self, request, global_params=None):
      r"""Lists checkpoints of the specified model version.

      Args:
        request: (AiplatformProjectsLocationsModelsListCheckpointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelVersionCheckpointsResponse) The response message.
      """
      config = self.GetMethodConfig('ListCheckpoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListCheckpoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:listCheckpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.listCheckpoints',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+name}:listCheckpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListCheckpointsRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelVersionCheckpointsResponse',
        supports_download=False,
    )

    def ListVersions(self, request, global_params=None):
      r"""Lists versions of the specified model.

      Args:
        request: (AiplatformProjectsLocationsModelsListVersionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListVersions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListVersions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:listVersions',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.listVersions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+name}:listVersions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListVersionsRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelVersionsResponse',
        supports_download=False,
    )

    def MergeVersionAliases(self, request, global_params=None):
      r"""Merges a set of aliases for a Model version.

      Args:
        request: (AiplatformProjectsLocationsModelsMergeVersionAliasesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('MergeVersionAliases')
      return self._RunMethod(
          config, request, global_params=global_params)

    MergeVersionAliases.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:mergeVersionAliases',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.mergeVersionAliases',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:mergeVersionAliases',
        request_field='googleCloudAiplatformV1MergeVersionAliasesRequest',
        request_type_name='AiplatformProjectsLocationsModelsMergeVersionAliasesRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.models.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Model',
        request_type_name='AiplatformProjectsLocationsModelsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsModelsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsModelsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsModelsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def UpdateExplanationDataset(self, request, global_params=None):
      r"""Incrementally update the dataset used for an examples model.

      Args:
        request: (AiplatformProjectsLocationsModelsUpdateExplanationDatasetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UpdateExplanationDataset')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateExplanationDataset.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:updateExplanationDataset',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.updateExplanationDataset',
        ordered_params=['model'],
        path_params=['model'],
        query_params=[],
        relative_path='v1/{+model}:updateExplanationDataset',
        request_field='googleCloudAiplatformV1UpdateExplanationDatasetRequest',
        request_type_name='AiplatformProjectsLocationsModelsUpdateExplanationDatasetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Upload(self, request, global_params=None):
      r"""Uploads a Model artifact into Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsModelsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models:upload',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/models:upload',
        request_field='googleCloudAiplatformV1UploadModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsUploadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsNasJobsNasTrialDetailsService(base_api.BaseApiService):
    """Service class for the projects_locations_nasJobs_nasTrialDetails resource."""

    _NAME = 'projects_locations_nasJobs_nasTrialDetails'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsNasJobsNasTrialDetailsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a NasTrialDetail.

      Args:
        request: (AiplatformProjectsLocationsNasJobsNasTrialDetailsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NasTrialDetail) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs/{nasJobsId}/nasTrialDetails/{nasTrialDetailsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.nasJobs.nasTrialDetails.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNasJobsNasTrialDetailsGetRequest',
        response_type_name='GoogleCloudAiplatformV1NasTrialDetail',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List top NasTrialDetails of a NasJob.

      Args:
        request: (AiplatformProjectsLocationsNasJobsNasTrialDetailsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListNasTrialDetailsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs/{nasJobsId}/nasTrialDetails',
        http_method='GET',
        method_id='aiplatform.projects.locations.nasJobs.nasTrialDetails.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/nasTrialDetails',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNasJobsNasTrialDetailsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListNasTrialDetailsResponse',
        supports_download=False,
    )

  class ProjectsLocationsNasJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_nasJobs resource."""

    _NAME = 'projects_locations_nasJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsNasJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a NasJob. Starts asynchronous cancellation on the NasJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetNasJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the NasJob is not deleted; instead it becomes a job with a NasJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and NasJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsNasJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs/{nasJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.nasJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelNasJobRequest',
        request_type_name='AiplatformProjectsLocationsNasJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a NasJob.

      Args:
        request: (AiplatformProjectsLocationsNasJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NasJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.nasJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/nasJobs',
        request_field='googleCloudAiplatformV1NasJob',
        request_type_name='AiplatformProjectsLocationsNasJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1NasJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a NasJob.

      Args:
        request: (AiplatformProjectsLocationsNasJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs/{nasJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.nasJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNasJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a NasJob.

      Args:
        request: (AiplatformProjectsLocationsNasJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NasJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs/{nasJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.nasJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNasJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1NasJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists NasJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsNasJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListNasJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nasJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.nasJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/nasJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNasJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListNasJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsNotebookExecutionJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_notebookExecutionJobs resource."""

    _NAME = 'projects_locations_notebookExecutionJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsNotebookExecutionJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a NotebookExecutionJob.

      Args:
        request: (AiplatformProjectsLocationsNotebookExecutionJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookExecutionJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookExecutionJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['notebookExecutionJobId'],
        relative_path='v1/{+parent}/notebookExecutionJobs',
        request_field='googleCloudAiplatformV1NotebookExecutionJob',
        request_type_name='AiplatformProjectsLocationsNotebookExecutionJobsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a NotebookExecutionJob.

      Args:
        request: (AiplatformProjectsLocationsNotebookExecutionJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookExecutionJobs/{notebookExecutionJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.notebookExecutionJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookExecutionJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a NotebookExecutionJob.

      Args:
        request: (AiplatformProjectsLocationsNotebookExecutionJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NotebookExecutionJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookExecutionJobs/{notebookExecutionJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookExecutionJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookExecutionJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1NotebookExecutionJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists NotebookExecutionJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsNotebookExecutionJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListNotebookExecutionJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookExecutionJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookExecutionJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/notebookExecutionJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookExecutionJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListNotebookExecutionJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsNotebookRuntimeTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_notebookRuntimeTemplates resource."""

    _NAME = 'projects_locations_notebookRuntimeTemplates'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsNotebookRuntimeTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a NotebookRuntimeTemplate.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['notebookRuntimeTemplateId'],
        relative_path='v1/{+parent}/notebookRuntimeTemplates',
        request_field='googleCloudAiplatformV1NotebookRuntimeTemplate',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a NotebookRuntimeTemplate.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a NotebookRuntimeTemplate.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NotebookRuntimeTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesGetRequest',
        response_type_name='GoogleCloudAiplatformV1NotebookRuntimeTemplate',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists NotebookRuntimeTemplates in a Location.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListNotebookRuntimeTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/notebookRuntimeTemplates',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListNotebookRuntimeTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a NotebookRuntimeTemplate.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NotebookRuntimeTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1NotebookRuntimeTemplate',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1NotebookRuntimeTemplate',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimeTemplatesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimeTemplates/{notebookRuntimeTemplatesId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimeTemplates.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimeTemplatesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsNotebookRuntimesService(base_api.BaseApiService):
    """Service class for the projects_locations_notebookRuntimes resource."""

    _NAME = 'projects_locations_notebookRuntimes'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsNotebookRuntimesService, self).__init__(client)
      self._upload_configs = {
          }

    def Assign(self, request, global_params=None):
      r"""Assigns a NotebookRuntime to a user for a particular Notebook file. This method will either returns an existing assignment or generates a new one.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesAssignRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Assign')
      return self._RunMethod(
          config, request, global_params=global_params)

    Assign.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes:assign',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimes.assign',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/notebookRuntimes:assign',
        request_field='googleCloudAiplatformV1AssignNotebookRuntimeRequest',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesAssignRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a NotebookRuntime.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes/{notebookRuntimesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.notebookRuntimes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a NotebookRuntime.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1NotebookRuntime) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes/{notebookRuntimesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookRuntimes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesGetRequest',
        response_type_name='GoogleCloudAiplatformV1NotebookRuntime',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists NotebookRuntimes in a Location.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListNotebookRuntimesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes',
        http_method='GET',
        method_id='aiplatform.projects.locations.notebookRuntimes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/notebookRuntimes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListNotebookRuntimesResponse',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts a NotebookRuntime.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes/{notebookRuntimesId}:start',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimes.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:start',
        request_field='googleCloudAiplatformV1StartNotebookRuntimeRequest',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesStartRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a NotebookRuntime.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes/{notebookRuntimesId}:stop',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimes.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='googleCloudAiplatformV1StopNotebookRuntimeRequest',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesStopRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Upgrade(self, request, global_params=None):
      r"""Upgrades a NotebookRuntime.

      Args:
        request: (AiplatformProjectsLocationsNotebookRuntimesUpgradeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Upgrade')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upgrade.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/notebookRuntimes/{notebookRuntimesId}:upgrade',
        http_method='POST',
        method_id='aiplatform.projects.locations.notebookRuntimes.upgrade',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:upgrade',
        request_field='googleCloudAiplatformV1UpgradeNotebookRuntimeRequest',
        request_type_name='AiplatformProjectsLocationsNotebookRuntimesUpgradeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPersistentResourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_persistentResources resource."""

    _NAME = 'projects_locations_persistentResources'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsPersistentResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a PersistentResource.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources',
        http_method='POST',
        method_id='aiplatform.projects.locations.persistentResources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['persistentResourceId'],
        relative_path='v1/{+parent}/persistentResources',
        request_field='googleCloudAiplatformV1PersistentResource',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a PersistentResource.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources/{persistentResourcesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.persistentResources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a PersistentResource.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PersistentResource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources/{persistentResourcesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.persistentResources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesGetRequest',
        response_type_name='GoogleCloudAiplatformV1PersistentResource',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PersistentResources in a Location.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListPersistentResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources',
        http_method='GET',
        method_id='aiplatform.projects.locations.persistentResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/persistentResources',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListPersistentResourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a PersistentResource.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources/{persistentResourcesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.persistentResources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1PersistentResource',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Reboot(self, request, global_params=None):
      r"""Reboots a PersistentResource.

      Args:
        request: (AiplatformProjectsLocationsPersistentResourcesRebootRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Reboot')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reboot.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/persistentResources/{persistentResourcesId}:reboot',
        http_method='POST',
        method_id='aiplatform.projects.locations.persistentResources.reboot',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reboot',
        request_field='googleCloudAiplatformV1RebootPersistentResourceRequest',
        request_type_name='AiplatformProjectsLocationsPersistentResourcesRebootRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPipelineJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelineJobs resource."""

    _NAME = 'projects_locations_pipelineJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsPipelineJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCancel(self, request, global_params=None):
      r"""Batch cancel PipelineJobs. Firstly the server will check if all the jobs are in non-terminal states, and skip the jobs that are already terminated. If the operation failed, none of the pipeline jobs are cancelled. The server will poll the states of all the pipeline jobs periodically to check the cancellation status. This operation will return an LRO.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsBatchCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs:batchCancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.batchCancel',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pipelineJobs:batchCancel',
        request_field='googleCloudAiplatformV1BatchCancelPipelineJobsRequest',
        request_type_name='AiplatformProjectsLocationsPipelineJobsBatchCancelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchDelete(self, request, global_params=None):
      r"""Batch deletes PipelineJobs The Operation is atomic. If it fails, none of the PipelineJobs are deleted. If it succeeds, all of the PipelineJobs are deleted.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs:batchDelete',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pipelineJobs:batchDelete',
        request_field='googleCloudAiplatformV1BatchDeletePipelineJobsRequest',
        request_type_name='AiplatformProjectsLocationsPipelineJobsBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Cancels a PipelineJob. Starts asynchronous cancellation on the PipelineJob. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetPipelineJob or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the PipelineJob is not deleted; instead it becomes a pipeline with a PipelineJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and PipelineJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelPipelineJobRequest',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a PipelineJob. A PipelineJob will run immediately when created.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pipelineJobId'],
        relative_path='v1/{+parent}/pipelineJobs',
        request_field='googleCloudAiplatformV1PipelineJob',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1PipelineJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.pipelineJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1PipelineJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PipelineJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListPipelineJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/pipelineJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListPipelineJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPublishersModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_publishers_models resource."""

    _NAME = 'projects_locations_publishers_models'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsPublishersModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def ComputeTokens(self, request, global_params=None):
      r"""Return a list of tokens based on the input text.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsComputeTokensRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ComputeTokensResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeTokens')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeTokens.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:computeTokens',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.computeTokens',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:computeTokens',
        request_field='googleCloudAiplatformV1ComputeTokensRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsComputeTokensRequest',
        response_type_name='GoogleCloudAiplatformV1ComputeTokensResponse',
        supports_download=False,
    )

    def CountTokens(self, request, global_params=None):
      r"""Perform a token counting.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsCountTokensRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CountTokensResponse) The response message.
      """
      config = self.GetMethodConfig('CountTokens')
      return self._RunMethod(
          config, request, global_params=global_params)

    CountTokens.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:countTokens',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.countTokens',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:countTokens',
        request_field='googleCloudAiplatformV1CountTokensRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsCountTokensRequest',
        response_type_name='GoogleCloudAiplatformV1CountTokensResponse',
        supports_download=False,
    )

    def GenerateContent(self, request, global_params=None):
      r"""Generate content with multimodal inputs.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsGenerateContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1GenerateContentResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:generateContent',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.generateContent',
        ordered_params=['model'],
        path_params=['model'],
        query_params=[],
        relative_path='v1/{+model}:generateContent',
        request_field='googleCloudAiplatformV1GenerateContentRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsGenerateContentRequest',
        response_type_name='GoogleCloudAiplatformV1GenerateContentResponse',
        supports_download=False,
    )

    def Predict(self, request, global_params=None):
      r"""Perform an online prediction.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PredictResponse) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:predict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.predict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:predict',
        request_field='googleCloudAiplatformV1PredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsPredictRequest',
        response_type_name='GoogleCloudAiplatformV1PredictResponse',
        supports_download=False,
    )

    def PredictLongRunning(self, request, global_params=None):
      r"""PredictLongRunning method for the projects_locations_publishers_models service.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsPredictLongRunningRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('PredictLongRunning')
      return self._RunMethod(
          config, request, global_params=global_params)

    PredictLongRunning.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:predictLongRunning',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.predictLongRunning',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:predictLongRunning',
        request_field='googleCloudAiplatformV1PredictLongRunningRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsPredictLongRunningRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RawPredict(self, request, global_params=None):
      r"""Perform an online prediction with an arbitrary HTTP payload. The response includes the following HTTP headers: * `X-Vertex-AI-Endpoint-Id`: ID of the Endpoint that served this prediction. * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's DeployedModel that served this prediction.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('RawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:rawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.rawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:rawPredict',
        request_field='googleCloudAiplatformV1RawPredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def ServerStreamingPredict(self, request, global_params=None):
      r"""Perform a server-side streaming online prediction request for Vertex LLM streaming.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsServerStreamingPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1StreamingPredictResponse) The response message.
      """
      config = self.GetMethodConfig('ServerStreamingPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    ServerStreamingPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:serverStreamingPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.serverStreamingPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:serverStreamingPredict',
        request_field='googleCloudAiplatformV1StreamingPredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsServerStreamingPredictRequest',
        response_type_name='GoogleCloudAiplatformV1StreamingPredictResponse',
        supports_download=False,
    )

    def StreamGenerateContent(self, request, global_params=None):
      r"""Generate content with multimodal inputs with streaming support.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsStreamGenerateContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1GenerateContentResponse) The response message.
      """
      config = self.GetMethodConfig('StreamGenerateContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamGenerateContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:streamGenerateContent',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.streamGenerateContent',
        ordered_params=['model'],
        path_params=['model'],
        query_params=[],
        relative_path='v1/{+model}:streamGenerateContent',
        request_field='googleCloudAiplatformV1GenerateContentRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsStreamGenerateContentRequest',
        response_type_name='GoogleCloudAiplatformV1GenerateContentResponse',
        supports_download=False,
    )

    def StreamRawPredict(self, request, global_params=None):
      r"""Perform a streaming online prediction with an arbitrary HTTP payload.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsStreamRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('StreamRawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamRawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:streamRawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.streamRawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:streamRawPredict',
        request_field='googleCloudAiplatformV1StreamRawPredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsStreamRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

  class ProjectsLocationsPublishersService(base_api.BaseApiService):
    """Service class for the projects_locations_publishers resource."""

    _NAME = 'projects_locations_publishers'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsPublishersService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsRagCorporaRagFilesService(base_api.BaseApiService):
    """Service class for the projects_locations_ragCorpora_ragFiles resource."""

    _NAME = 'projects_locations_ragCorpora_ragFiles'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsRagCorporaRagFilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a RagFile.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaRagFilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}/ragFiles/{ragFilesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.ragCorpora.ragFiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaRagFilesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a RagFile.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaRagFilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RagFile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}/ragFiles/{ragFilesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.ragCorpora.ragFiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaRagFilesGetRequest',
        response_type_name='GoogleCloudAiplatformV1RagFile',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import files from Google Cloud Storage or Google Drive into a RagCorpus.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaRagFilesImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}/ragFiles:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.ragCorpora.ragFiles.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/ragFiles:import',
        request_field='googleCloudAiplatformV1ImportRagFilesRequest',
        request_type_name='AiplatformProjectsLocationsRagCorporaRagFilesImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RagFiles in a RagCorpus.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaRagFilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListRagFilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}/ragFiles',
        http_method='GET',
        method_id='aiplatform.projects.locations.ragCorpora.ragFiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/ragFiles',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaRagFilesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListRagFilesResponse',
        supports_download=False,
    )

  class ProjectsLocationsRagCorporaService(base_api.BaseApiService):
    """Service class for the projects_locations_ragCorpora resource."""

    _NAME = 'projects_locations_ragCorpora'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsRagCorporaService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a RagCorpus.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora',
        http_method='POST',
        method_id='aiplatform.projects.locations.ragCorpora.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/ragCorpora',
        request_field='googleCloudAiplatformV1RagCorpus',
        request_type_name='AiplatformProjectsLocationsRagCorporaCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a RagCorpus.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.ragCorpora.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a RagCorpus.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RagCorpus) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.ragCorpora.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaGetRequest',
        response_type_name='GoogleCloudAiplatformV1RagCorpus',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RagCorpora in a Location.

      Args:
        request: (AiplatformProjectsLocationsRagCorporaListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListRagCorporaResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora',
        http_method='GET',
        method_id='aiplatform.projects.locations.ragCorpora.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/ragCorpora',
        request_field='',
        request_type_name='AiplatformProjectsLocationsRagCorporaListRequest',
        response_type_name='GoogleCloudAiplatformV1ListRagCorporaResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a RagCorpus.

      Args:
        request: (GoogleCloudAiplatformV1RagCorpus) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ragCorpora/{ragCorporaId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.ragCorpora.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudAiplatformV1RagCorpus',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsReasoningEnginesService(base_api.BaseApiService):
    """Service class for the projects_locations_reasoningEngines resource."""

    _NAME = 'projects_locations_reasoningEngines'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsReasoningEnginesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines',
        http_method='POST',
        method_id='aiplatform.projects.locations.reasoningEngines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/reasoningEngines',
        request_field='googleCloudAiplatformV1ReasoningEngine',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines/{reasoningEnginesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.reasoningEngines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReasoningEngine) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines/{reasoningEnginesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.reasoningEngines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesGetRequest',
        response_type_name='GoogleCloudAiplatformV1ReasoningEngine',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists reasoning engines in a location.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListReasoningEnginesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines',
        http_method='GET',
        method_id='aiplatform.projects.locations.reasoningEngines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/reasoningEngines',
        request_field='',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListReasoningEnginesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines/{reasoningEnginesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.reasoningEngines.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1ReasoningEngine',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Query(self, request, global_params=None):
      r"""Queries using a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1QueryReasoningEngineResponse) The response message.
      """
      config = self.GetMethodConfig('Query')
      return self._RunMethod(
          config, request, global_params=global_params)

    Query.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines/{reasoningEnginesId}:query',
        http_method='POST',
        method_id='aiplatform.projects.locations.reasoningEngines.query',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:query',
        request_field='googleCloudAiplatformV1QueryReasoningEngineRequest',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesQueryRequest',
        response_type_name='GoogleCloudAiplatformV1QueryReasoningEngineResponse',
        supports_download=False,
    )

    def StreamQuery(self, request, global_params=None):
      r"""Streams queries using a reasoning engine.

      Args:
        request: (AiplatformProjectsLocationsReasoningEnginesStreamQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('StreamQuery')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamQuery.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/reasoningEngines/{reasoningEnginesId}:streamQuery',
        http_method='POST',
        method_id='aiplatform.projects.locations.reasoningEngines.streamQuery',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:streamQuery',
        request_field='googleCloudAiplatformV1StreamQueryReasoningEngineRequest',
        request_type_name='AiplatformProjectsLocationsReasoningEnginesStreamQueryRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchedulesService(base_api.BaseApiService):
    """Service class for the projects_locations_schedules resource."""

    _NAME = 'projects_locations_schedules'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsSchedulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Schedule.

      Args:
        request: (AiplatformProjectsLocationsSchedulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Schedule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules',
        http_method='POST',
        method_id='aiplatform.projects.locations.schedules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schedules',
        request_field='googleCloudAiplatformV1Schedule',
        request_type_name='AiplatformProjectsLocationsSchedulesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Schedule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Schedule.

      Args:
        request: (AiplatformProjectsLocationsSchedulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.schedules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSchedulesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Schedule.

      Args:
        request: (AiplatformProjectsLocationsSchedulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Schedule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.schedules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSchedulesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Schedule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Schedules in a Location.

      Args:
        request: (AiplatformProjectsLocationsSchedulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListSchedulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules',
        http_method='GET',
        method_id='aiplatform.projects.locations.schedules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/schedules',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSchedulesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListSchedulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an active or paused Schedule. When the Schedule is updated, new runs will be scheduled starting from the updated next execution time after the update time based on the time_specification in the updated Schedule. All unstarted runs before the update time will be skipped while already created runs will NOT be paused or canceled.

      Args:
        request: (AiplatformProjectsLocationsSchedulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Schedule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.schedules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Schedule',
        request_type_name='AiplatformProjectsLocationsSchedulesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Schedule',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses a Schedule. Will mark Schedule.state to 'PAUSED'. If the schedule is paused, no new runs will be created. Already created runs will NOT be paused or canceled.

      Args:
        request: (AiplatformProjectsLocationsSchedulesPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}:pause',
        http_method='POST',
        method_id='aiplatform.projects.locations.schedules.pause',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:pause',
        request_field='googleCloudAiplatformV1PauseScheduleRequest',
        request_type_name='AiplatformProjectsLocationsSchedulesPauseRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes a paused Schedule to start scheduling new runs. Will mark Schedule.state to 'ACTIVE'. Only paused Schedule can be resumed. When the Schedule is resumed, new runs will be scheduled starting from the next execution time after the current time based on the time_specification in the Schedule. If Schedule.catch_up is set up true, all missed runs will be scheduled for backfill first.

      Args:
        request: (AiplatformProjectsLocationsSchedulesResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}:resume',
        http_method='POST',
        method_id='aiplatform.projects.locations.schedules.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resume',
        request_field='googleCloudAiplatformV1ResumeScheduleRequest',
        request_type_name='AiplatformProjectsLocationsSchedulesResumeRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class ProjectsLocationsSpecialistPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_specialistPools resource."""

    _NAME = 'projects_locations_specialistPools'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsSpecialistPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='POST',
        method_id='aiplatform.projects.locations.specialistPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/specialistPools',
        request_field='googleCloudAiplatformV1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a SpecialistPool as well as all Specialists in the pool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.specialistPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SpecialistPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsGetRequest',
        response_type_name='GoogleCloudAiplatformV1SpecialistPool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SpecialistPools in a Location.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListSpecialistPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/specialistPools',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListSpecialistPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.specialistPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesTrialsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_trials resource."""

    _NAME = 'projects_locations_studies_trials'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsStudiesTrialsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddTrialMeasurement(self, request, global_params=None):
      r"""Adds a measurement of the objective metrics to a Trial. This measurement is assumed to have been taken before the Trial is complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('AddTrialMeasurement')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddTrialMeasurement.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:addTrialMeasurement',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.addTrialMeasurement',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1/{+trialName}:addTrialMeasurement',
        request_field='googleCloudAiplatformV1AddTrialMeasurementRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def CheckTrialEarlyStoppingState(self, request, global_params=None):
      r"""Checks whether a Trial should stop or not. Returns a long-running operation. When the operation is successful, it will contain a CheckTrialEarlyStoppingStateResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('CheckTrialEarlyStoppingState')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckTrialEarlyStoppingState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:checkTrialEarlyStoppingState',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.checkTrialEarlyStoppingState',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1/{+trialName}:checkTrialEarlyStoppingState',
        request_field='googleCloudAiplatformV1CheckTrialEarlyStoppingStateRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Complete(self, request, global_params=None):
      r"""Marks a Trial as complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:complete',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:complete',
        request_field='googleCloudAiplatformV1CompleteTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCompleteRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Adds a user provided Trial to a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials',
        request_field='googleCloudAiplatformV1Trial',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.trials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Trials associated with a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/trials',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTrialsResponse',
        supports_download=False,
    )

    def ListOptimalTrials(self, request, global_params=None):
      r"""Lists the pareto-optimal Trials for multi-objective Study or the optimal Trials for single-objective Study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListOptimalTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('ListOptimalTrials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListOptimalTrials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:listOptimalTrials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.listOptimalTrials',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:listOptimalTrials',
        request_field='googleCloudAiplatformV1ListOptimalTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest',
        response_type_name='GoogleCloudAiplatformV1ListOptimalTrialsResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:stop',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='googleCloudAiplatformV1StopTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsStopRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Suggest(self, request, global_params=None):
      r"""Adds one or more Trials to a Study, with parameter values suggested by Vertex AI Vizier. Returns a long-running operation associated with the generation of Trial suggestions. When this long-running operation succeeds, it will contain a SuggestTrialsResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsSuggestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Suggest')
      return self._RunMethod(
          config, request, global_params=global_params)

    Suggest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:suggest',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.suggest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:suggest',
        request_field='googleCloudAiplatformV1SuggestTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsSuggestRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesService(base_api.BaseApiService):
    """Service class for the projects_locations_studies resource."""

    _NAME = 'projects_locations_studies'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsStudiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Study. A resource name will be generated after creation of the Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/studies',
        request_field='googleCloudAiplatformV1Study',
        request_type_name='AiplatformProjectsLocationsStudiesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Study by name.

      Args:
        request: (AiplatformProjectsLocationsStudiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the studies in a region for an associated project.

      Args:
        request: (AiplatformProjectsLocationsStudiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListStudiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/studies',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListStudiesResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks a study up using the user-defined display_name field instead of the fully qualified resource name.

      Args:
        request: (AiplatformProjectsLocationsStudiesLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies:lookup',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/studies:lookup',
        request_field='googleCloudAiplatformV1LookupStudyRequest',
        request_type_name='AiplatformProjectsLocationsStudiesLookupRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs_timeSeries resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs_timeSeries'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardTimeSeriesId'],
        relative_path='v1/{+parent}/timeSeries',
        request_field='googleCloudAiplatformV1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportTensorboardTimeSeries(self, request, global_params=None):
      r"""Exports a TensorboardTimeSeries' data. Data is returned in paginated responses.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ExportTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('ExportTensorboardTimeSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportTensorboardTimeSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:exportTensorboardTimeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.exportTensorboardTimeSeries',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=[],
        relative_path='v1/{+tensorboardTimeSeries}:exportTensorboardTimeSeries',
        request_field='googleCloudAiplatformV1ExportTensorboardTimeSeriesDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest',
        response_type_name='GoogleCloudAiplatformV1ExportTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardTimeSeries in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/timeSeries',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def Read(self, request, global_params=None):
      r"""Reads a TensorboardTimeSeries' data. By default, if the number of data points stored is less than 1000, all data is returned. Otherwise, 1000 data points is randomly selected from this time series and returned. This value can be changed by changing max_data_points, which can't be greater than 10k.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('Read')
      return self._RunMethod(
          config, request, global_params=global_params)

    Read.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:read',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.read',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=['filter', 'maxDataPoints'],
        relative_path='v1/{+tensorboardTimeSeries}:read',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def ReadBlobData(self, request, global_params=None):
      r"""Gets bytes of TensorboardBlobs. This is to allow reading blob data stored in consumer project's Cloud Storage bucket without users having to obtain Cloud Storage access permission.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardBlobDataResponse) The response message.
      """
      config = self.GetMethodConfig('ReadBlobData')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadBlobData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:readBlobData',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.readBlobData',
        ordered_params=['timeSeries'],
        path_params=['timeSeries'],
        query_params=['blobIds'],
        relative_path='v1/{+timeSeries}:readBlobData',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardBlobDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardRuns.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchCreateTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/runs:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateTensorboardRunsRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchCreateTensorboardRunsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardRunId'],
        relative_path='v1/{+parent}/runs',
        request_field='googleCloudAiplatformV1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardRuns in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/runs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardRunsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points into multiple TensorboardTimeSeries under a TensorboardRun. If any data fail to be ingested, an error is returned.

      Args:
        request: (GoogleCloudAiplatformV1WriteTensorboardRunDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteTensorboardRunDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.write',
        ordered_params=['tensorboardRun'],
        path_params=['tensorboardRun'],
        query_params=[],
        relative_path='v1/{+tensorboardRun}:write',
        request_field='<request>',
        request_type_name='GoogleCloudAiplatformV1WriteTensorboardRunDataRequest',
        response_type_name='GoogleCloudAiplatformV1WriteTensorboardRunDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments resource."""

    _NAME = 'projects_locations_tensorboards_experiments'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardTimeSeries that belong to a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchCreateTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateTensorboardTimeSeriesRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchCreateTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardExperimentId'],
        relative_path='v1/{+parent}/experiments',
        request_field='googleCloudAiplatformV1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardExperiments in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardExperimentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/experiments',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardExperimentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points of multiple TensorboardTimeSeries in multiple TensorboardRun's. If any data fail to be ingested, an error is returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteTensorboardExperimentDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.write',
        ordered_params=['tensorboardExperiment'],
        path_params=['tensorboardExperiment'],
        query_params=[],
        relative_path='v1/{+tensorboardExperiment}:write',
        request_field='googleCloudAiplatformV1WriteTensorboardExperimentDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest',
        response_type_name='GoogleCloudAiplatformV1WriteTensorboardExperimentDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards resource."""

    _NAME = 'projects_locations_tensorboards'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchRead(self, request, global_params=None):
      r"""Reads multiple TensorboardTimeSeries' data. The data point number limit is 1000 for scalars, 100 for tensors and blob references. If the number of data points stored is less than the limit, all data is returned. Otherwise, the number limit of data points is randomly selected from this time series and returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsBatchReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('BatchRead')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchRead.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}:batchRead',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.batchRead',
        ordered_params=['tensorboard'],
        path_params=['tensorboard'],
        query_params=['timeSeries'],
        relative_path='v1/{+tensorboard}:batchRead',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsBatchReadRequest',
        response_type_name='GoogleCloudAiplatformV1BatchReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/tensorboards',
        request_field='googleCloudAiplatformV1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Tensorboard) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Tensorboard',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Tensorboards in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/tensorboards',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ReadSize(self, request, global_params=None):
      r"""Returns the storage size for a given TensorBoard instance.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsReadSizeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardSizeResponse) The response message.
      """
      config = self.GetMethodConfig('ReadSize')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadSize.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}:readSize',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.readSize',
        ordered_params=['tensorboard'],
        path_params=['tensorboard'],
        query_params=[],
        relative_path='v1/{+tensorboard}:readSize',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsReadSizeRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardSizeResponse',
        supports_download=False,
    )

    def ReadUsage(self, request, global_params=None):
      r"""Returns a list of monthly active users for a given TensorBoard instance.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsReadUsageRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardUsageResponse) The response message.
      """
      config = self.GetMethodConfig('ReadUsage')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadUsage.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}:readUsage',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.readUsage',
        ordered_params=['tensorboard'],
        path_params=['tensorboard'],
        query_params=[],
        relative_path='v1/{+tensorboard}:readUsage',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsReadUsageRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardUsageResponse',
        supports_download=False,
    )

  class ProjectsLocationsTrainingPipelinesService(base_api.BaseApiService):
    """Service class for the projects_locations_trainingPipelines resource."""

    _NAME = 'projects_locations_trainingPipelines'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTrainingPipelinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a TrainingPipeline. Starts asynchronous cancellation on the TrainingPipeline. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetTrainingPipeline or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the TrainingPipeline is not deleted; instead it becomes a pipeline with a TrainingPipeline.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and TrainingPipeline.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelTrainingPipelineRequest',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TrainingPipeline. A created TrainingPipeline right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trainingPipelines',
        request_field='googleCloudAiplatformV1TrainingPipeline',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TrainingPipeline',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.trainingPipelines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesGetRequest',
        response_type_name='GoogleCloudAiplatformV1TrainingPipeline',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TrainingPipelines in a Location.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTrainingPipelinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/trainingPipelines',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTrainingPipelinesResponse',
        supports_download=False,
    )

  class ProjectsLocationsTuningJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_tuningJobs resource."""

    _NAME = 'projects_locations_tuningJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTuningJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a TuningJob. Starts asynchronous cancellation on the TuningJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use GenAiTuningService.GetTuningJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the TuningJob is not deleted; instead it becomes a job with a TuningJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and TuningJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTuningJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tuningJobs/{tuningJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tuningJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelTuningJobRequest',
        request_type_name='AiplatformProjectsLocationsTuningJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TuningJob. A created TuningJob right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsTuningJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TuningJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tuningJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.tuningJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/tuningJobs',
        request_field='googleCloudAiplatformV1TuningJob',
        request_type_name='AiplatformProjectsLocationsTuningJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TuningJob',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TuningJob.

      Args:
        request: (AiplatformProjectsLocationsTuningJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TuningJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tuningJobs/{tuningJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tuningJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTuningJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1TuningJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TuningJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsTuningJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTuningJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tuningJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.tuningJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/tuningJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTuningJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTuningJobsResponse',
        supports_download=False,
    )

    def RebaseTunedModel(self, request, global_params=None):
      r"""Rebase a TunedModel.

      Args:
        request: (AiplatformProjectsLocationsTuningJobsRebaseTunedModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('RebaseTunedModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    RebaseTunedModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tuningJobs:rebaseTunedModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tuningJobs.rebaseTunedModel',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/tuningJobs:rebaseTunedModel',
        request_field='googleCloudAiplatformV1RebaseTunedModelRequest',
        request_type_name='AiplatformProjectsLocationsTuningJobsRebaseTunedModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def AugmentPrompt(self, request, global_params=None):
      r"""Given an input prompt, it returns augmented prompt from vertex rag store to guide LLM towards generating grounded responses.

      Args:
        request: (AiplatformProjectsLocationsAugmentPromptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AugmentPromptResponse) The response message.
      """
      config = self.GetMethodConfig('AugmentPrompt')
      return self._RunMethod(
          config, request, global_params=global_params)

    AugmentPrompt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:augmentPrompt',
        http_method='POST',
        method_id='aiplatform.projects.locations.augmentPrompt',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:augmentPrompt',
        request_field='googleCloudAiplatformV1AugmentPromptRequest',
        request_type_name='AiplatformProjectsLocationsAugmentPromptRequest',
        response_type_name='GoogleCloudAiplatformV1AugmentPromptResponse',
        supports_download=False,
    )

    def CorroborateContent(self, request, global_params=None):
      r"""Given an input text, it returns a score that evaluates the factuality of the text. It also extracts and returns claims from the text and provides supporting facts.

      Args:
        request: (AiplatformProjectsLocationsCorroborateContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CorroborateContentResponse) The response message.
      """
      config = self.GetMethodConfig('CorroborateContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    CorroborateContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:corroborateContent',
        http_method='POST',
        method_id='aiplatform.projects.locations.corroborateContent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:corroborateContent',
        request_field='googleCloudAiplatformV1CorroborateContentRequest',
        request_type_name='AiplatformProjectsLocationsCorroborateContentRequest',
        response_type_name='GoogleCloudAiplatformV1CorroborateContentResponse',
        supports_download=False,
    )

    def EvaluateInstances(self, request, global_params=None):
      r"""Evaluates instances based on a given metric.

      Args:
        request: (AiplatformProjectsLocationsEvaluateInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1EvaluateInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('EvaluateInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    EvaluateInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:evaluateInstances',
        http_method='POST',
        method_id='aiplatform.projects.locations.evaluateInstances',
        ordered_params=['location'],
        path_params=['location'],
        query_params=[],
        relative_path='v1/{+location}:evaluateInstances',
        request_field='googleCloudAiplatformV1EvaluateInstancesRequest',
        request_type_name='AiplatformProjectsLocationsEvaluateInstancesRequest',
        response_type_name='GoogleCloudAiplatformV1EvaluateInstancesResponse',
        supports_download=False,
    )

    def RetrieveContexts(self, request, global_params=None):
      r"""Retrieves relevant contexts for a query.

      Args:
        request: (AiplatformProjectsLocationsRetrieveContextsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RetrieveContextsResponse) The response message.
      """
      config = self.GetMethodConfig('RetrieveContexts')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveContexts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:retrieveContexts',
        http_method='POST',
        method_id='aiplatform.projects.locations.retrieveContexts',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:retrieveContexts',
        request_field='googleCloudAiplatformV1RetrieveContextsRequest',
        request_type_name='AiplatformProjectsLocationsRetrieveContextsRequest',
        response_type_name='GoogleCloudAiplatformV1RetrieveContextsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AiplatformV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class PublishersModelsService(base_api.BaseApiService):
    """Service class for the publishers_models resource."""

    _NAME = 'publishers_models'

    def __init__(self, client):
      super(AiplatformV1.PublishersModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a Model Garden publisher model.

      Args:
        request: (AiplatformPublishersModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PublisherModel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/publishers/{publishersId}/models/{modelsId}',
        http_method='GET',
        method_id='aiplatform.publishers.models.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['huggingFaceToken', 'isHuggingFaceModel', 'languageCode', 'view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformPublishersModelsGetRequest',
        response_type_name='GoogleCloudAiplatformV1PublisherModel',
        supports_download=False,
    )

  class PublishersService(base_api.BaseApiService):
    """Service class for the publishers resource."""

    _NAME = 'publishers'

    def __init__(self, client):
      super(AiplatformV1.PublishersService, self).__init__(client)
      self._upload_configs = {
          }
