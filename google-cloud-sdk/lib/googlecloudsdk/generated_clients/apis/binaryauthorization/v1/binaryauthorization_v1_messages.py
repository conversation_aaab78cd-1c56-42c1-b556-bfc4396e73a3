"""Generated message classes for binaryauthorization version v1.

The management interface for Binary Authorization, a service that provides
policy-based deployment validation and control for images deployed to Google
Kubernetes Engine (GKE), Anthos Service Mesh, Anthos Clusters, and Cloud Run.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'binaryauthorization'


class AdmissionRule(_messages.Message):
  r"""An admission rule specifies either that all container images used in a
  pod creation request must be attested to by one or more attestors, that all
  pod creations will be allowed, or that all pod creations will be denied.
  Images matching an admission allowlist pattern are exempted from admission
  rules and will never block a pod creation.

  Enums:
    EnforcementModeValueValuesEnum: Required. The action when a pod creation
      is denied by the admission rule.
    EvaluationModeValueValuesEnum: Required. How this admission rule will be
      evaluated.

  Fields:
    enforcementMode: Required. The action when a pod creation is denied by the
      admission rule.
    evaluationMode: Required. How this admission rule will be evaluated.
    requireAttestationsBy: Optional. The resource names of the attestors that
      must attest to a container image, in the format
      `projects/*/attestors/*`. Each attestor must exist before a policy can
      reference it. To add an attestor to a policy the principal issuing the
      policy change request must be able to read the attestor resource. Note:
      this field must be non-empty when the `evaluation_mode` field specifies
      `REQUIRE_ATTESTATION`, otherwise it must be empty.
  """

  class EnforcementModeValueValuesEnum(_messages.Enum):
    r"""Required. The action when a pod creation is denied by the admission
    rule.

    Values:
      ENFORCEMENT_MODE_UNSPECIFIED: Do not use.
      ENFORCED_BLOCK_AND_AUDIT_LOG: Enforce the admission rule by blocking the
        pod creation.
      DRYRUN_AUDIT_LOG_ONLY: Dryrun mode: Audit logging only. This will allow
        the pod creation as if the admission request had specified break-
        glass.
    """
    ENFORCEMENT_MODE_UNSPECIFIED = 0
    ENFORCED_BLOCK_AND_AUDIT_LOG = 1
    DRYRUN_AUDIT_LOG_ONLY = 2

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Required. How this admission rule will be evaluated.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Do not use.
      ALWAYS_ALLOW: This rule allows all pod creations.
      REQUIRE_ATTESTATION: This rule allows a pod creation if all the
        attestors listed in `require_attestations_by` have valid attestations
        for all of the images in the pod spec.
      ALWAYS_DENY: This rule denies all pod creations.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    ALWAYS_ALLOW = 1
    REQUIRE_ATTESTATION = 2
    ALWAYS_DENY = 3

  enforcementMode = _messages.EnumField('EnforcementModeValueValuesEnum', 1)
  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 2)
  requireAttestationsBy = _messages.StringField(3, repeated=True)


class AdmissionWhitelistPattern(_messages.Message):
  r"""An admission allowlist pattern exempts images from checks by admission
  rules.

  Fields:
    namePattern: An image name pattern to allowlist, in the form
      `registry/path/to/image`. This supports a trailing `*` wildcard, but
      this is allowed only in text after the `registry/` part. This also
      supports a trailing `**` wildcard which matches subdirectories of a
      given entry.
  """

  namePattern = _messages.StringField(1)


class AllowlistPattern(_messages.Message):
  r"""An allowlist pattern exempts images from checks by evaluation rules.

  Fields:
    namePattern: An image name pattern to allowlist, in the form
      `registry/path/to/image`. This supports a trailing `*` as a wildcard,
      but this is allowed only in text after the `registry/` part. Examples of
      valid patterns: gcr.io/nginx, gcr.io/nginx:2.*, gcr.io/*,
      gcr.io/nginx[@:]*, gcr.io/im@sha256:*,gcr.io/im@sha256:ab2345*.
  """

  namePattern = _messages.StringField(1)


class AllowlistResult(_messages.Message):
  r"""Result of evaluating an image name allowlist.

  Fields:
    matchedPattern: The allowlist pattern that the image matched.
  """

  matchedPattern = _messages.StringField(1)


class AttestationAuthenticator(_messages.Message):
  r"""An attestation authenticator that will be used to verify attestations.
  Typically this is just a set of public keys. Conceptually, an authenticator
  can be treated as always returning either "authenticated" or "not
  authenticated" when presented with a signed attestation (almost always
  assumed to be a [DSSE](https://github.com/secure-systems-lab/dsse)
  attestation). The details of how an authenticator makes this decision are
  specific to the type of 'authenticator' that this message wraps.

  Fields:
    displayName: Optional. A user-provided name for this
      `AttestationAuthenticator`. This field has no effect on the policy
      evaluation behavior except to improve readability of messages in
      evaluation results.
    pkixPublicKeySet: Optional. A set of raw PKIX SubjectPublicKeyInfo format
      public keys. If any public key in the set validates the attestation
      signature, then the signature is considered authenticated (i.e. any one
      key is sufficient to authenticate).
  """

  displayName = _messages.StringField(1)
  pkixPublicKeySet = _messages.MessageField('PkixPublicKeySet', 2)


class AttestationOccurrence(_messages.Message):
  r"""Occurrence that represents a single "attestation". The authenticity of
  an attestation can be verified using the attached signature. If the verifier
  trusts the public key of the signer, then verifying the signature is
  sufficient to establish trust. In this circumstance, the authority to which
  this attestation is attached is primarily useful for lookup (how to find
  this attestation if you already know the authority and artifact to be
  verified) and intent (for which authority this attestation was intended to
  sign.

  Fields:
    jwts: One or more JWTs encoding a self-contained attestation. Each JWT
      encodes the payload that it verifies within the JWT itself. Verifier
      implementation SHOULD ignore the `serialized_payload` field when
      verifying these JWTs. If only JWTs are present on this
      AttestationOccurrence, then the `serialized_payload` SHOULD be left
      empty. Each JWT SHOULD encode a claim specific to the `resource_uri` of
      this Occurrence, but this is not validated by Grafeas metadata API
      implementations. The JWT itself is opaque to Grafeas.
    serializedPayload: Required. The serialized payload that is verified by
      one or more `signatures`.
    signatures: One or more signatures over `serialized_payload`. Verifier
      implementations should consider this attestation message verified if at
      least one `signature` verifies `serialized_payload`. See `Signature` in
      common.proto for more details on signature structure and verification.
  """

  jwts = _messages.MessageField('Jwt', 1, repeated=True)
  serializedPayload = _messages.BytesField(2)
  signatures = _messages.MessageField('Signature', 3, repeated=True)


class AttestationSource(_messages.Message):
  r"""Specifies the locations for fetching the provenance attestations.

  Fields:
    containerAnalysisAttestationProjects: The IDs of the Google Cloud projects
      that store the SLSA attestations as Container Analysis Occurrences, in
      the format `projects/[PROJECT_ID]`. Maximum number of
      `container_analysis_attestation_projects` allowed in each
      `AttestationSource` is 10.
  """

  containerAnalysisAttestationProjects = _messages.StringField(1, repeated=True)


class Attestor(_messages.Message):
  r"""An attestor that attests to container image artifacts. An existing
  attestor cannot be modified except where indicated.

  Fields:
    description: Optional. A descriptive comment. This field may be updated.
      The field may be displayed in chooser dialogs.
    etag: Optional. A checksum, returned by the server, that can be sent on
      update requests to ensure the attestor has an up-to-date value before
      attempting to update it. See https://google.aip.dev/154.
    name: Required. The resource name, in the format:
      `projects/*/attestors/*`. This field may not be updated.
    updateTime: Output only. Time when the attestor was last updated.
    userOwnedGrafeasNote: This specifies how an attestation will be read, and
      how it will be used during policy enforcement.
  """

  description = _messages.StringField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  userOwnedGrafeasNote = _messages.MessageField('UserOwnedGrafeasNote', 5)


class AttestorPublicKey(_messages.Message):
  r"""An attestor public key that will be used to verify attestations signed
  by this attestor.

  Fields:
    asciiArmoredPgpPublicKey: ASCII-armored representation of a PGP public
      key, as the entire output by the command `gpg --export --armor
      <EMAIL>` (either LF or CRLF line endings). When using this
      field, `id` should be left blank. The Binary Authorization API handlers
      will calculate the ID and fill it in automatically. Binary Authorization
      computes this ID as the OpenPGP RFC4880 V4 fingerprint, represented as
      upper-case hex. If `id` is provided by the caller, it will be
      overwritten by the API-calculated ID.
    comment: Optional. A descriptive comment. This field may be updated.
    id: The ID of this public key. Signatures verified by Binary Authorization
      must include the ID of the public key that can be used to verify them,
      and that ID must match the contents of this field exactly. Additional
      restrictions on this field can be imposed based on which public key type
      is encapsulated. See the documentation on `public_key` cases below for
      details.
    pkixPublicKey: A raw PKIX SubjectPublicKeyInfo format public key. NOTE:
      `id` may be explicitly provided by the caller when using this type of
      public key, but it MUST be a valid RFC3986 URI. If `id` is left blank, a
      default one will be computed based on the digest of the DER encoding of
      the public key.
  """

  asciiArmoredPgpPublicKey = _messages.StringField(1)
  comment = _messages.StringField(2)
  id = _messages.StringField(3)
  pkixPublicKey = _messages.MessageField('PkixPublicKey', 4)


class BinaryauthorizationProjectsAttestorsCreateRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsCreateRequest object.

  Fields:
    attestor: A Attestor resource to be passed as the request body.
    attestorId: Required. The attestors ID.
    parent: Required. The parent of this attestor.
  """

  attestor = _messages.MessageField('Attestor', 1)
  attestorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BinaryauthorizationProjectsAttestorsDeleteRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsDeleteRequest object.

  Fields:
    name: Required. The name of the attestors to delete, in the format
      `projects/*/attestors/*`.
  """

  name = _messages.StringField(1, required=True)


class BinaryauthorizationProjectsAttestorsGetIamPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BinaryauthorizationProjectsAttestorsGetRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsGetRequest object.

  Fields:
    name: Required. The name of the attestor to retrieve, in the format
      `projects/*/attestors/*`.
  """

  name = _messages.StringField(1, required=True)


class BinaryauthorizationProjectsAttestorsListRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsListRequest object.

  Fields:
    pageSize: Requested page size. The server may return fewer results than
      requested. If unspecified, the server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
      Typically, this is the value of ListAttestorsResponse.next_page_token
      returned from the previous call to the `ListAttestors` method.
    parent: Required. The resource name of the project associated with the
      attestors, in the format `projects/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BinaryauthorizationProjectsAttestorsSetIamPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BinaryauthorizationProjectsAttestorsTestIamPermissionsRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsAttestorsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BinaryauthorizationProjectsAttestorsValidateAttestationOccurrenceRequest(_messages.Message):
  r"""A
  BinaryauthorizationProjectsAttestorsValidateAttestationOccurrenceRequest
  object.

  Fields:
    attestor: Required. The resource name of the Attestor of the occurrence,
      in the format `projects/*/attestors/*`.
    validateAttestationOccurrenceRequest: A
      ValidateAttestationOccurrenceRequest resource to be passed as the
      request body.
  """

  attestor = _messages.StringField(1, required=True)
  validateAttestationOccurrenceRequest = _messages.MessageField('ValidateAttestationOccurrenceRequest', 2)


class BinaryauthorizationProjectsGetPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsGetPolicyRequest object.

  Fields:
    name: Required. The resource name of the policy to retrieve, in the format
      `projects/*/policy`.
  """

  name = _messages.StringField(1, required=True)


class BinaryauthorizationProjectsPlatformsGkePoliciesEvaluateRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsGkePoliciesEvaluateRequest object.

  Fields:
    evaluateGkePolicyRequest: A EvaluateGkePolicyRequest resource to be passed
      as the request body.
    name: Required. The name of the platform policy to evaluate in the format
      `projects/*/platforms/*/policies/*`.
  """

  evaluateGkePolicyRequest = _messages.MessageField('EvaluateGkePolicyRequest', 1)
  name = _messages.StringField(2, required=True)


class BinaryauthorizationProjectsPlatformsListRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsListRequest object.

  Fields:
    pageSize: Requested page size. The server may return fewer results than
      requested. If unspecified, the server picks an appropriate default.
    pageToken: A token identifying a page of results the server should return.
      Typically, this is the value of ListPlatformsResponse.next_page_token
      returned from the previous call to the `ListPlatforms` method.
    parent: Required. Contains the name of the resource requested, specified
      using the format `projects/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BinaryauthorizationProjectsPlatformsPoliciesCreateRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsPoliciesCreateRequest object.

  Fields:
    parent: Required. The parent of this platform policy.
    platformPolicy: A PlatformPolicy resource to be passed as the request
      body.
    policyId: Required. The platform policy ID.
  """

  parent = _messages.StringField(1, required=True)
  platformPolicy = _messages.MessageField('PlatformPolicy', 2)
  policyId = _messages.StringField(3)


class BinaryauthorizationProjectsPlatformsPoliciesDeleteRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsPoliciesDeleteRequest object.

  Fields:
    etag: Optional. Used to prevent deleting the policy when another request
      has updated it since it was retrieved.
    name: Required. The name of the platform policy to delete, in the format
      `projects/*/platforms/*/policies/*`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class BinaryauthorizationProjectsPlatformsPoliciesGetRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsPoliciesGetRequest object.

  Fields:
    name: Required. The name of the platform policy to retrieve in the format
      `projects/*/platforms/*/policies/*`.
  """

  name = _messages.StringField(1, required=True)


class BinaryauthorizationProjectsPlatformsPoliciesListRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPlatformsPoliciesListRequest object.

  Fields:
    pageSize: Requested page size. The server may return fewer results than
      requested. If unspecified, the server picks an appropriate default.
    pageToken: A token identifying a page of results the server should return.
      Typically, this is the value of
      ListPlatformPoliciesResponse.next_page_token returned from the previous
      call to the `ListPlatformPolicies` method.
    parent: Required. The resource name of the platform associated with the
      platform policies using the format `projects/*/platforms/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BinaryauthorizationProjectsPolicyGetIamPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPolicyGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BinaryauthorizationProjectsPolicySetIamPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPolicySetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BinaryauthorizationProjectsPolicyTestIamPermissionsRequest(_messages.Message):
  r"""A BinaryauthorizationProjectsPolicyTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BinaryauthorizationSystempolicyGetPolicyRequest(_messages.Message):
  r"""A BinaryauthorizationSystempolicyGetPolicyRequest object.

  Fields:
    name: Required. The resource name, in the format `locations/*/policy`.
      Note that the system policy is not associated with a project.
  """

  name = _messages.StringField(1, required=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Check(_messages.Message):
  r"""A single check to perform against a Pod. Checks are grouped into
  `CheckSet` objects, which are defined by the top-level policy.

  Fields:
    alwaysDeny: Optional. A special-case check that always denies. Note that
      this still only applies when the scope of the `CheckSet` applies and the
      image isn't exempted by an image allowlist. This check is primarily
      useful for testing, or to set the default behavior for all unmatched
      scopes to "deny".
    displayName: Optional. A user-provided name for this check. This field has
      no effect on the policy evaluation behavior except to improve
      readability of messages in evaluation results.
    imageAllowlist: Optional. Images exempted from this check. If any of the
      patterns match the image url, the check will not be evaluated.
    imageFreshnessCheck: Optional. Require that an image is no older than a
      configured expiration time. Image age is determined by its upload time.
    sigstoreSignatureCheck: Optional. Require that an image was signed by
      Cosign with a trusted key. This check requires that both the image and
      signature are stored in Artifact Registry.
    simpleSigningAttestationCheck: Optional. Require a SimpleSigning-type
      attestation for every image in the deployment.
    slsaCheck: Optional. Require that an image was built by a trusted builder
      (such as Google Cloud Build), meets requirements for Supply chain Levels
      for Software Artifacts (SLSA), and was built from a trusted source code
      repostitory.
    trustedDirectoryCheck: Optional. Require that an image lives in a trusted
      directory.
    vulnerabilityCheck: Optional. Require that an image does not contain
      vulnerabilities that violate the configured rules, such as based on
      severity levels.
  """

  alwaysDeny = _messages.BooleanField(1)
  displayName = _messages.StringField(2)
  imageAllowlist = _messages.MessageField('ImageAllowlist', 3)
  imageFreshnessCheck = _messages.MessageField('ImageFreshnessCheck', 4)
  sigstoreSignatureCheck = _messages.MessageField('SigstoreSignatureCheck', 5)
  simpleSigningAttestationCheck = _messages.MessageField('SimpleSigningAttestationCheck', 6)
  slsaCheck = _messages.MessageField('SlsaCheck', 7)
  trustedDirectoryCheck = _messages.MessageField('TrustedDirectoryCheck', 8)
  vulnerabilityCheck = _messages.MessageField('VulnerabilityCheck', 9)


class CheckResult(_messages.Message):
  r"""Result of evaluating one check.

  Fields:
    allowlistResult: If the image was exempted by an allow_pattern in the
      check, contains the pattern that the image name matched.
    displayName: The name of the check.
    evaluationResult: If a check was evaluated, contains the result of the
      check.
    explanation: Explanation of this check result.
    index: The index of the check.
    type: The type of the check.
  """

  allowlistResult = _messages.MessageField('AllowlistResult', 1)
  displayName = _messages.StringField(2)
  evaluationResult = _messages.MessageField('EvaluationResult', 3)
  explanation = _messages.StringField(4)
  index = _messages.IntegerField(5)
  type = _messages.StringField(6)


class CheckResults(_messages.Message):
  r"""Result of evaluating one or more checks.

  Fields:
    results: Per-check details.
  """

  results = _messages.MessageField('CheckResult', 1, repeated=True)


class CheckSet(_messages.Message):
  r"""A conjunction of policy checks, scoped to a particular namespace or
  Kubernetes service account. In order for evaluation of a `CheckSet` to
  return "allowed" for a given image in a given Pod, one of the following
  conditions must be satisfied: * The image is explicitly exempted by an entry
  in `image_allowlist`, OR * ALL of the `checks` evaluate to "allowed".

  Fields:
    checks: Optional. The checks to apply. The ultimate result of evaluating
      the check set will be "allow" if and only if every check in `checks`
      evaluates to "allow". If `checks` is empty, the default behavior is
      "always allow".
    displayName: Optional. A user-provided name for this `CheckSet`. This
      field has no effect on the policy evaluation behavior except to improve
      readability of messages in evaluation results.
    imageAllowlist: Optional. Images exempted from this `CheckSet`. If any of
      the patterns match the image being evaluated, no checks in the
      `CheckSet` will be evaluated.
    scope: Optional. The scope to which this `CheckSet` applies. If unset or
      an empty string (the default), applies to all namespaces and service
      accounts. See the `Scope` message documentation for details on scoping
      rules.
  """

  checks = _messages.MessageField('Check', 1, repeated=True)
  displayName = _messages.StringField(2)
  imageAllowlist = _messages.MessageField('ImageAllowlist', 3)
  scope = _messages.MessageField('Scope', 4)


class CheckSetResult(_messages.Message):
  r"""Result of evaluating one check set.

  Fields:
    allowlistResult: If the image was exempted by an allow_pattern in the
      check set, contains the pattern that the image name matched.
    checkResults: If checks were evaluated, contains the results of evaluating
      each check.
    displayName: The name of the check set.
    explanation: Explanation of this check set result. Only populated if no
      checks were evaluated.
    index: The index of the check set.
    scope: The scope of the check set.
  """

  allowlistResult = _messages.MessageField('AllowlistResult', 1)
  checkResults = _messages.MessageField('CheckResults', 2)
  displayName = _messages.StringField(3)
  explanation = _messages.StringField(4)
  index = _messages.IntegerField(5)
  scope = _messages.MessageField('Scope', 6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EvaluateGkePolicyRequest(_messages.Message):
  r"""Request message for PlatformPolicyEvaluationService.EvaluateGkePolicy.

  Enums:
    AttestationModeValueValuesEnum: Optional. Configures the behavior for
      attesting results.

  Messages:
    ResourceValue: Required. JSON or YAML blob representing a Kubernetes
      resource.

  Fields:
    attestationMode: Optional. Configures the behavior for attesting results.
    resource: Required. JSON or YAML blob representing a Kubernetes resource.
  """

  class AttestationModeValueValuesEnum(_messages.Enum):
    r"""Optional. Configures the behavior for attesting results.

    Values:
      ATTESTATION_MODE_UNSPECIFIED: Unspecified. Results are not attested.
      GENERATE_DEPLOY: Generate and return deploy attestations in DSEE form.
    """
    ATTESTATION_MODE_UNSPECIFIED = 0
    GENERATE_DEPLOY = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceValue(_messages.Message):
    r"""Required. JSON or YAML blob representing a Kubernetes resource.

    Messages:
      AdditionalProperty: An additional property for a ResourceValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attestationMode = _messages.EnumField('AttestationModeValueValuesEnum', 1)
  resource = _messages.MessageField('ResourceValue', 2)


class EvaluateGkePolicyResponse(_messages.Message):
  r"""Response message for PlatformPolicyEvaluationService.EvaluateGkePolicy.

  Enums:
    VerdictValueValuesEnum: The result of evaluating all Pods in the request.

  Fields:
    attestations: If AttestationMode is set to `GENERATE_DEPLOY` and the top-
      level verdict is conformant, an attestation will be returned for each
      image in the request. Attestations are in the form of websafe base64
      encoded JSON DSSEs (https://github.com/secure-systems-
      lab/dsse/blob/master/envelope.md).
    results: Evaluation result for each Pod contained in the request.
    verdict: The result of evaluating all Pods in the request.
  """

  class VerdictValueValuesEnum(_messages.Enum):
    r"""The result of evaluating all Pods in the request.

    Values:
      VERDICT_UNSPECIFIED: Not specified. This should never be used.
      CONFORMANT: All Pods in the request conform to the policy.
      NON_CONFORMANT: At least one Pod does not conform to the policy.
      ERROR: Encountered at least one error evaluating a Pod and all other
        Pods conform to the policy. Non-conformance has precedence over
        errors.
    """
    VERDICT_UNSPECIFIED = 0
    CONFORMANT = 1
    NON_CONFORMANT = 2
    ERROR = 3

  attestations = _messages.StringField(1, repeated=True)
  results = _messages.MessageField('PodResult', 2, repeated=True)
  verdict = _messages.EnumField('VerdictValueValuesEnum', 3)


class EvaluationResult(_messages.Message):
  r"""Result of evaluating one check.

  Enums:
    VerdictValueValuesEnum: The result of evaluating this check.

  Fields:
    verdict: The result of evaluating this check.
  """

  class VerdictValueValuesEnum(_messages.Enum):
    r"""The result of evaluating this check.

    Values:
      CHECK_VERDICT_UNSPECIFIED: Not specified. This should never be used.
      CONFORMANT: The check was successfully evaluated and the image satisfied
        the check.
      NON_CONFORMANT: The check was successfully evaluated and the image did
        not satisfy the check.
      ERROR: The check was not successfully evaluated.
    """
    CHECK_VERDICT_UNSPECIFIED = 0
    CONFORMANT = 1
    NON_CONFORMANT = 2
    ERROR = 3

  verdict = _messages.EnumField('VerdictValueValuesEnum', 1)


class EvaluationRule(_messages.Message):
  r"""An evaluation rule specifies either that all container images used in a
  deployment request must be attested to by one or more Attestor, that the
  deployment will be always allowed, or that it is always denied.

  Enums:
    EnforcementModeValueValuesEnum: Required. Define the possible actions when
      a deployment is denied by an evaluation rule.
    EvaluationModeValueValuesEnum: Required. How this rule will be evaluated.

  Fields:
    enforcementMode: Required. Define the possible actions when a deployment
      is denied by an evaluation rule.
    evaluationMode: Required. How this rule will be evaluated.
    requiredAttestors: Optional. If the `evaluation_mode` is
      `REQUIRE_ATTESTATION`, this is the list of the attestors required for
      the deployment.
  """

  class EnforcementModeValueValuesEnum(_messages.Enum):
    r"""Required. Define the possible actions when a deployment is denied by
    an evaluation rule.

    Values:
      ENFORCEMENT_MODE_UNSPECIFIED: Do not use.
      ENFORCED_BLOCK_AND_AUDIT_LOG: Enforce the admission rule by blocking the
        deployment.
      DRYRUN_AUDIT_LOG_ONLY: Dryrun mode: Audit logging only. This will allow
        the deployment as if the admission request had specified break-glass.
    """
    ENFORCEMENT_MODE_UNSPECIFIED = 0
    ENFORCED_BLOCK_AND_AUDIT_LOG = 1
    DRYRUN_AUDIT_LOG_ONLY = 2

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Required. How this rule will be evaluated.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Do not use.
      ALWAYS_ALLOW: The deployment is always allowed.
      REQUIRE_ATTESTATION: The deployment requires attestations from certain
        attestors.
      ALWAYS_DENY: The deployment is always denied.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    ALWAYS_ALLOW = 1
    REQUIRE_ATTESTATION = 2
    ALWAYS_DENY = 3

  enforcementMode = _messages.EnumField('EnforcementModeValueValuesEnum', 1)
  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 2)
  requiredAttestors = _messages.MessageField('InlineAttestor', 3, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GkePolicy(_messages.Message):
  r"""A Binary Authorization policy for a GKE cluster. This is one type of
  policy that can occur as a `PlatformPolicy`.

  Fields:
    checkSets: Optional. The `CheckSet` objects to apply, scoped by namespace
      or namespace and service account. Exactly one `CheckSet` will be
      evaluated for a given Pod (unless the list is empty, in which case the
      behavior is "always allow"). If multiple `CheckSet` objects have scopes
      that match the namespace and service account of the Pod being evaluated,
      only the `CheckSet` with the MOST SPECIFIC scope will match. `CheckSet`
      objects must be listed in order of decreasing specificity, i.e. if a
      scope matches a given service account (which must include the
      namespace), it must come before a `CheckSet` with a scope matching just
      that namespace. This property is enforced by server-side validation. The
      purpose of this restriction is to ensure that if more than one
      `CheckSet` matches a given Pod, the `CheckSet` that will be evaluated
      will always be the first in the list to match (because if any other
      matches, it must be less specific). If `check_sets` is empty, the
      default behavior is to allow all images. If `check_sets` is non-empty,
      the last `check_sets` entry must always be a `CheckSet` with no scope
      set, i.e. a catchall to handle any situation not caught by the preceding
      `CheckSet` objects.
    imageAllowlist: Optional. Images exempted from this policy. If any of the
      patterns match the image being evaluated, the rest of the policy will
      not be evaluated.
  """

  checkSets = _messages.MessageField('CheckSet', 1, repeated=True)
  imageAllowlist = _messages.MessageField('ImageAllowlist', 2)


class IamPolicy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ImageAllowlist(_messages.Message):
  r"""Images that are exempted from normal checks based on name pattern only.

  Fields:
    allowPattern: Required. A disjunction of image patterns to allow. If any
      of these patterns match, then the image is considered exempted by this
      allowlist.
  """

  allowPattern = _messages.StringField(1, repeated=True)


class ImageFreshnessCheck(_messages.Message):
  r"""An image freshness check, which rejects images that were uploaded before
  the set number of days ago to the supported repositories.

  Fields:
    maxUploadAgeDays: Required. The max number of days that is allowed since
      the image was uploaded. Must be greater than zero.
  """

  maxUploadAgeDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ImageResult(_messages.Message):
  r"""Result of evaluating one image.

  Enums:
    VerdictValueValuesEnum: The result of evaluating this image.

  Fields:
    allowlistResult: If the image was exempted by a top-level allow_pattern,
      contains the allowlist pattern that the image name matched.
    checkSetResult: If a check set was evaluated, contains the result of the
      check set. Empty if there were no check sets.
    explanation: Explanation of this image result. Only populated if no check
      sets were evaluated.
    imageUri: Image URI from the request.
    verdict: The result of evaluating this image.
  """

  class VerdictValueValuesEnum(_messages.Enum):
    r"""The result of evaluating this image.

    Values:
      IMAGE_VERDICT_UNSPECIFIED: Not specified. This should never be used.
      CONFORMANT: Image conforms to the policy.
      NON_CONFORMANT: Image does not conform to the policy.
      ERROR: Error evaluating the image. Non-conformance has precedence over
        errors.
    """
    IMAGE_VERDICT_UNSPECIFIED = 0
    CONFORMANT = 1
    NON_CONFORMANT = 2
    ERROR = 3

  allowlistResult = _messages.MessageField('AllowlistResult', 1)
  checkSetResult = _messages.MessageField('CheckSetResult', 2)
  explanation = _messages.StringField(3)
  imageUri = _messages.StringField(4)
  verdict = _messages.EnumField('VerdictValueValuesEnum', 5)


class InlineAttestor(_messages.Message):
  r"""An attestor that attests to container image artifacts. This attestor is
  to be inlined as part of the policy.

  Fields:
    attestationNote: Required. The Grafeas resource name of an ATTESTATION
      Note, created by the user, in the form of `projects/*/notes/*`. An
      attestation by this attestor is stored as a Grafeas Attestation
      Occurrence that names a container image and that links to this Note.
      Grafeas is an external dependency.
    description: Optional. A description, for information purposes only.
    id: Required. An id used to identify the attestor in the policy. It should
      be unique within the same policy.
    publicKeys: Optional. Public keys that verify attestations signed by this
      attestor. If this field is non-empty, one of the specified public keys
      must verify that an attestation was signed by this attestor for the
      image specified in the evaluation request. If this field is empty, this
      attestor always returns that no valid attestations exist.
  """

  attestationNote = _messages.StringField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  publicKeys = _messages.MessageField('AttestorPublicKey', 4, repeated=True)


class InlineCloudRunPolicy(_messages.Message):
  r"""A binary authorization policy for Cloud Run deployments.

  Fields:
    allowlistPatterns: Optional. List of images that will be allowed
      regardless of the platform-based policies. Allowlists are always
      evaluated prior to evaluating any platform-based policies. An image name
      pattern to allowlist is in the form `registry/path/to/image`. A trailing
      `*` is supported as a wildcard, but this is allowed only in text after
      the `registry/` part.
    rule: Required. The evaluation rule used for evaluating a Cloud Run
      deployment.
  """

  allowlistPatterns = _messages.MessageField('AllowlistPattern', 1, repeated=True)
  rule = _messages.MessageField('EvaluationRule', 2)


class Jwt(_messages.Message):
  r"""A Jwt object.

  Fields:
    compactJwt: The compact encoding of a JWS, which is always three base64
      encoded strings joined by periods. For details, see:
      https://tools.ietf.org/html/rfc7515.html#section-3.1
  """

  compactJwt = _messages.StringField(1)


class ListAttestorsResponse(_messages.Message):
  r"""Response message for BinauthzManagementServiceV1.ListAttestors.

  Fields:
    attestors: The list of attestors.
    nextPageToken: A token to retrieve the next page of results. Pass this
      value in the ListAttestorsRequest.page_token field in the subsequent
      call to the `ListAttestors` method to retrieve the next page of results.
  """

  attestors = _messages.MessageField('Attestor', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPlatformPoliciesResponse(_messages.Message):
  r"""Response message for
  PlatformPolicyManagementService.ListPlatformPolicies.

  Fields:
    nextPageToken: A token to retrieve the next page of results. Pass this
      value in the ListPlatformPoliciesRequest.page_token field in the
      subsequent call to the `ListPlatformPolicies` method to retrieve the
      next page of results.
    platformPolicies: The list of platform policies.
  """

  nextPageToken = _messages.StringField(1)
  platformPolicies = _messages.MessageField('PlatformPolicy', 2, repeated=True)


class ListPlatformsResponse(_messages.Message):
  r"""Response message for PlatformPolicyManagementService.ListPlatforms.

  Fields:
    nextPageToken: A token to retrieve the next page of results. Pass this
      value in the ListPlatformsRequest.page_token field in the subsequent
      call to the `ListPlatforms` method to retrieve the next page of results.
    platforms: The list of platforms supported by Binary Authorization.
  """

  nextPageToken = _messages.StringField(1)
  platforms = _messages.MessageField('Platform', 2, repeated=True)


class PkixPublicKey(_messages.Message):
  r"""A public key in the PkixPublicKey
  [format](https://tools.ietf.org/html/rfc5280#section-4.1.2.7). Public keys
  of this type are typically textually encoded using the PEM format.

  Enums:
    SignatureAlgorithmValueValuesEnum: The signature algorithm used to verify
      a message against a signature using this key. These signature algorithm
      must match the structure and any object identifiers encoded in
      `public_key_pem` (i.e. this algorithm must match that of the public
      key).

  Fields:
    keyId: Optional. The ID of this public key. Signatures verified by Binary
      Authorization must include the ID of the public key that can be used to
      verify them. The ID must match exactly contents of the `key_id` field
      exactly. The ID may be explicitly provided by the caller, but it MUST be
      a valid RFC3986 URI. If `key_id` is left blank and this `PkixPublicKey`
      is not used in the context of a wrapper (see next paragraph), a default
      key ID will be computed based on the digest of the DER encoding of the
      public key. If this `PkixPublicKey` is used in the context of a wrapper
      that has its own notion of key ID (e.g. `AttestorPublicKey`), then this
      field can either match that value exactly, or be left blank, in which
      case it behaves exactly as though it is equal to that wrapper value.
    publicKeyPem: A PEM-encoded public key, as described in
      https://tools.ietf.org/html/rfc7468#section-13
    signatureAlgorithm: The signature algorithm used to verify a message
      against a signature using this key. These signature algorithm must match
      the structure and any object identifiers encoded in `public_key_pem`
      (i.e. this algorithm must match that of the public key).
  """

  class SignatureAlgorithmValueValuesEnum(_messages.Enum):
    r"""The signature algorithm used to verify a message against a signature
    using this key. These signature algorithm must match the structure and any
    object identifiers encoded in `public_key_pem` (i.e. this algorithm must
    match that of the public key).

    Values:
      SIGNATURE_ALGORITHM_UNSPECIFIED: Not specified.
      RSA_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      ECDSA_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
      ECDSA_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
      ECDSA_P521_SHA512: ECDSA on the NIST P-521 curve with a SHA512 digest.
      EC_SIGN_P521_SHA512: ECDSA on the NIST P-521 curve with a SHA512 digest.
    """
    SIGNATURE_ALGORITHM_UNSPECIFIED = 0
    RSA_PSS_2048_SHA256 = 1
    RSA_SIGN_PSS_2048_SHA256 = 2
    RSA_PSS_3072_SHA256 = 3
    RSA_SIGN_PSS_3072_SHA256 = 4
    RSA_PSS_4096_SHA256 = 5
    RSA_SIGN_PSS_4096_SHA256 = 6
    RSA_PSS_4096_SHA512 = 7
    RSA_SIGN_PSS_4096_SHA512 = 8
    RSA_SIGN_PKCS1_2048_SHA256 = 9
    RSA_SIGN_PKCS1_3072_SHA256 = 10
    RSA_SIGN_PKCS1_4096_SHA256 = 11
    RSA_SIGN_PKCS1_4096_SHA512 = 12
    ECDSA_P256_SHA256 = 13
    EC_SIGN_P256_SHA256 = 14
    ECDSA_P384_SHA384 = 15
    EC_SIGN_P384_SHA384 = 16
    ECDSA_P521_SHA512 = 17
    EC_SIGN_P521_SHA512 = 18

  keyId = _messages.StringField(1)
  publicKeyPem = _messages.StringField(2)
  signatureAlgorithm = _messages.EnumField('SignatureAlgorithmValueValuesEnum', 3)


class PkixPublicKeySet(_messages.Message):
  r"""A bundle of PKIX public keys, used to authenticate attestation
  signatures. Generally, a signature is considered to be authenticated by a
  `PkixPublicKeySet` if any of the public keys verify it (i.e. it is an "OR"
  of the keys).

  Fields:
    pkixPublicKeys: Required. `pkix_public_keys` must have at least one entry.
  """

  pkixPublicKeys = _messages.MessageField('PkixPublicKey', 1, repeated=True)


class Platform(_messages.Message):
  r"""A platform supported by Binary Authorization platform policy.

  Fields:
    name: Output only. The relative resource name of the platform supported by
      Binary Authorization platform policies, in the form of
      `projects/*/platforms/*`.
  """

  name = _messages.StringField(1)


class PlatformPolicy(_messages.Message):
  r"""A Binary Authorization platform policy for deployments on various
  platforms.

  Fields:
    cloudRunPolicy: Optional. Cloud Run platform-specific policy.
    description: Optional. A description comment about the policy.
    etag: Optional. Used to prevent updating the policy when another request
      has updated it since it was retrieved.
    gkePolicy: Optional. GKE platform-specific policy.
    name: Output only. The relative resource name of the Binary Authorization
      platform policy, in the form of `projects/*/platforms/*/policies/*`.
    updateTime: Output only. Time when the policy was last updated.
  """

  cloudRunPolicy = _messages.MessageField('InlineCloudRunPolicy', 1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  gkePolicy = _messages.MessageField('GkePolicy', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class PodResult(_messages.Message):
  r"""Result of evaluating the whole GKE policy for one Pod.

  Enums:
    VerdictValueValuesEnum: The result of evaluating this Pod.

  Fields:
    imageResults: Per-image details.
    kubernetesNamespace: The Kubernetes namespace of the Pod.
    kubernetesServiceAccount: The Kubernetes service account of the Pod.
    podName: The name of the Pod.
    verdict: The result of evaluating this Pod.
  """

  class VerdictValueValuesEnum(_messages.Enum):
    r"""The result of evaluating this Pod.

    Values:
      POD_VERDICT_UNSPECIFIED: Not specified. This should never be used.
      CONFORMANT: All images conform to the policy.
      NON_CONFORMANT: At least one image does not conform to the policy.
      ERROR: Encountered at least one error evaluating an image and all other
        images with non-error verdicts conform to the policy. Non-conformance
        has precedence over errors.
    """
    POD_VERDICT_UNSPECIFIED = 0
    CONFORMANT = 1
    NON_CONFORMANT = 2
    ERROR = 3

  imageResults = _messages.MessageField('ImageResult', 1, repeated=True)
  kubernetesNamespace = _messages.StringField(2)
  kubernetesServiceAccount = _messages.StringField(3)
  podName = _messages.StringField(4)
  verdict = _messages.EnumField('VerdictValueValuesEnum', 5)


class Policy(_messages.Message):
  r"""A policy for container image binary authorization.

  Enums:
    GlobalPolicyEvaluationModeValueValuesEnum: Optional. Controls the
      evaluation of a Google-maintained global admission policy for common
      system-level images. Images not covered by the global policy will be
      subject to the project admission policy. This setting has no effect when
      specified inside a global admission policy.

  Messages:
    ClusterAdmissionRulesValue: Optional. A valid policy has only one of the
      following rule maps non-empty, i.e. only one of
      `cluster_admission_rules`, `kubernetes_namespace_admission_rules`,
      `kubernetes_service_account_admission_rules`, or
      `istio_service_identity_admission_rules` can be non-empty. Per-cluster
      admission rules. Cluster spec format: `location.clusterId`. There can be
      at most one admission rule per cluster spec. A `location` is either a
      compute zone (e.g. us-central1-a) or a region (e.g. us-central1). For
      `clusterId` syntax restrictions see https://cloud.google.com/container-
      engine/reference/rest/v1/projects.zones.clusters.
    IstioServiceIdentityAdmissionRulesValue: Optional. Per-istio-service-
      identity admission rules. Istio service identity spec format:
      `spiffe:///ns//sa/` or `/ns//sa/` e.g. `spiffe://example.com/ns/test-
      ns/sa/default`
    KubernetesNamespaceAdmissionRulesValue: Optional. Per-kubernetes-namespace
      admission rules. K8s namespace spec format: `[a-z.-]+`, e.g. `some-
      namespace`
    KubernetesServiceAccountAdmissionRulesValue: Optional. Per-kubernetes-
      service-account admission rules. Service account spec format:
      `namespace:serviceaccount`. e.g. `test-ns:default`

  Fields:
    admissionWhitelistPatterns: Optional. Admission policy allowlisting. A
      matching admission request will always be permitted. This feature is
      typically used to exclude Google or third-party infrastructure images
      from Binary Authorization policies.
    clusterAdmissionRules: Optional. A valid policy has only one of the
      following rule maps non-empty, i.e. only one of
      `cluster_admission_rules`, `kubernetes_namespace_admission_rules`,
      `kubernetes_service_account_admission_rules`, or
      `istio_service_identity_admission_rules` can be non-empty. Per-cluster
      admission rules. Cluster spec format: `location.clusterId`. There can be
      at most one admission rule per cluster spec. A `location` is either a
      compute zone (e.g. us-central1-a) or a region (e.g. us-central1). For
      `clusterId` syntax restrictions see https://cloud.google.com/container-
      engine/reference/rest/v1/projects.zones.clusters.
    defaultAdmissionRule: Required. Default admission rule for a cluster
      without a per-cluster, per- kubernetes-service-account, or per-istio-
      service-identity admission rule.
    description: Optional. A descriptive comment.
    etag: Optional. A checksum, returned by the server, that can be sent on
      update requests to ensure the policy has an up-to-date value before
      attempting to update it. See https://google.aip.dev/154.
    globalPolicyEvaluationMode: Optional. Controls the evaluation of a Google-
      maintained global admission policy for common system-level images.
      Images not covered by the global policy will be subject to the project
      admission policy. This setting has no effect when specified inside a
      global admission policy.
    istioServiceIdentityAdmissionRules: Optional. Per-istio-service-identity
      admission rules. Istio service identity spec format: `spiffe:///ns//sa/`
      or `/ns//sa/` e.g. `spiffe://example.com/ns/test-ns/sa/default`
    kubernetesNamespaceAdmissionRules: Optional. Per-kubernetes-namespace
      admission rules. K8s namespace spec format: `[a-z.-]+`, e.g. `some-
      namespace`
    kubernetesServiceAccountAdmissionRules: Optional. Per-kubernetes-service-
      account admission rules. Service account spec format:
      `namespace:serviceaccount`. e.g. `test-ns:default`
    name: Output only. The resource name, in the format `projects/*/policy`.
      There is at most one policy per project.
    updateTime: Output only. Time when the policy was last updated.
  """

  class GlobalPolicyEvaluationModeValueValuesEnum(_messages.Enum):
    r"""Optional. Controls the evaluation of a Google-maintained global
    admission policy for common system-level images. Images not covered by the
    global policy will be subject to the project admission policy. This
    setting has no effect when specified inside a global admission policy.

    Values:
      GLOBAL_POLICY_EVALUATION_MODE_UNSPECIFIED: Not specified: `DISABLE` is
        assumed.
      ENABLE: Enables system policy evaluation.
      DISABLE: Disables system policy evaluation.
    """
    GLOBAL_POLICY_EVALUATION_MODE_UNSPECIFIED = 0
    ENABLE = 1
    DISABLE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ClusterAdmissionRulesValue(_messages.Message):
    r"""Optional. A valid policy has only one of the following rule maps non-
    empty, i.e. only one of `cluster_admission_rules`,
    `kubernetes_namespace_admission_rules`,
    `kubernetes_service_account_admission_rules`, or
    `istio_service_identity_admission_rules` can be non-empty. Per-cluster
    admission rules. Cluster spec format: `location.clusterId`. There can be
    at most one admission rule per cluster spec. A `location` is either a
    compute zone (e.g. us-central1-a) or a region (e.g. us-central1). For
    `clusterId` syntax restrictions see https://cloud.google.com/container-
    engine/reference/rest/v1/projects.zones.clusters.

    Messages:
      AdditionalProperty: An additional property for a
        ClusterAdmissionRulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        ClusterAdmissionRulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ClusterAdmissionRulesValue object.

      Fields:
        key: Name of the additional property.
        value: A AdmissionRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AdmissionRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IstioServiceIdentityAdmissionRulesValue(_messages.Message):
    r"""Optional. Per-istio-service-identity admission rules. Istio service
    identity spec format: `spiffe:///ns//sa/` or `/ns//sa/` e.g.
    `spiffe://example.com/ns/test-ns/sa/default`

    Messages:
      AdditionalProperty: An additional property for a
        IstioServiceIdentityAdmissionRulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        IstioServiceIdentityAdmissionRulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IstioServiceIdentityAdmissionRulesValue
      object.

      Fields:
        key: Name of the additional property.
        value: A AdmissionRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AdmissionRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KubernetesNamespaceAdmissionRulesValue(_messages.Message):
    r"""Optional. Per-kubernetes-namespace admission rules. K8s namespace spec
    format: `[a-z.-]+`, e.g. `some-namespace`

    Messages:
      AdditionalProperty: An additional property for a
        KubernetesNamespaceAdmissionRulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        KubernetesNamespaceAdmissionRulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KubernetesNamespaceAdmissionRulesValue
      object.

      Fields:
        key: Name of the additional property.
        value: A AdmissionRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AdmissionRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KubernetesServiceAccountAdmissionRulesValue(_messages.Message):
    r"""Optional. Per-kubernetes-service-account admission rules. Service
    account spec format: `namespace:serviceaccount`. e.g. `test-ns:default`

    Messages:
      AdditionalProperty: An additional property for a
        KubernetesServiceAccountAdmissionRulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        KubernetesServiceAccountAdmissionRulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a
      KubernetesServiceAccountAdmissionRulesValue object.

      Fields:
        key: Name of the additional property.
        value: A AdmissionRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AdmissionRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  admissionWhitelistPatterns = _messages.MessageField('AdmissionWhitelistPattern', 1, repeated=True)
  clusterAdmissionRules = _messages.MessageField('ClusterAdmissionRulesValue', 2)
  defaultAdmissionRule = _messages.MessageField('AdmissionRule', 3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  globalPolicyEvaluationMode = _messages.EnumField('GlobalPolicyEvaluationModeValueValuesEnum', 6)
  istioServiceIdentityAdmissionRules = _messages.MessageField('IstioServiceIdentityAdmissionRulesValue', 7)
  kubernetesNamespaceAdmissionRules = _messages.MessageField('KubernetesNamespaceAdmissionRulesValue', 8)
  kubernetesServiceAccountAdmissionRules = _messages.MessageField('KubernetesServiceAccountAdmissionRulesValue', 9)
  name = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class Scope(_messages.Message):
  r"""A scope specifier for `CheckSet` objects.

  Fields:
    kubernetesNamespace: Optional. Matches all Kubernetes service accounts in
      the provided namespace, unless a more specific
      `kubernetes_service_account` scope already matched.
    kubernetesServiceAccount: Optional. Matches a single Kubernetes service
      account, e.g. `my-namespace:my-service-account`.
      `kubernetes_service_account` scope is always more specific than
      `kubernetes_namespace` scope for the same namespace.
  """

  kubernetesNamespace = _messages.StringField(1)
  kubernetesServiceAccount = _messages.StringField(2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('IamPolicy', 1)


class Signature(_messages.Message):
  r"""Verifiers (e.g. Kritis implementations) MUST verify signatures with
  respect to the trust anchors defined in policy (e.g. a Kritis policy).
  Typically this means that the verifier has been configured with a map from
  `public_key_id` to public key material (and any required parameters, e.g.
  signing algorithm). In particular, verification implementations MUST NOT
  treat the signature `public_key_id` as anything more than a key lookup hint.
  The `public_key_id` DOES NOT validate or authenticate a public key; it only
  provides a mechanism for quickly selecting a public key ALREADY CONFIGURED
  on the verifier through a trusted channel. Verification implementations MUST
  reject signatures in any of the following circumstances: * The
  `public_key_id` is not recognized by the verifier. * The public key that
  `public_key_id` refers to does not verify the signature with respect to the
  payload. The `signature` contents SHOULD NOT be "attached" (where the
  payload is included with the serialized `signature` bytes). Verifiers MUST
  ignore any "attached" payload and only verify signatures with respect to
  explicitly provided payload (e.g. a `payload` field on the proto message
  that holds this Signature, or the canonical serialization of the proto
  message that holds this signature).

  Fields:
    publicKeyId: The identifier for the public key that verifies this
      signature. * The `public_key_id` is required. * The `public_key_id`
      SHOULD be an RFC3986 conformant URI. * When possible, the
      `public_key_id` SHOULD be an immutable reference, such as a
      cryptographic digest. Examples of valid `public_key_id`s: OpenPGP V4
      public key fingerprint: *
      "openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA" See
      https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr for more
      details on this scheme. RFC6920 digest-named SubjectPublicKeyInfo
      (digest of the DER serialization): *
      "ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU" * "nih:///sh
      a-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5"
    signature: The content of the signature, an opaque bytestring. The payload
      that this signature verifies MUST be unambiguously provided with the
      Signature during verification. A wrapper message might provide the
      payload explicitly. Alternatively, a message might have a canonical
      serialization that can always be unambiguously computed to derive the
      payload.
  """

  publicKeyId = _messages.StringField(1)
  signature = _messages.BytesField(2)


class SigstoreAuthority(_messages.Message):
  r"""A Sigstore authority, used to verify signatures that are created by
  Sigstore. An authority is analogous to an attestation authenticator,
  verifying that a signature is valid or invalid.

  Fields:
    displayName: Optional. A user-provided name for this `SigstoreAuthority`.
      This field has no effect on the policy evaluation behavior except to
      improve readability of messages in evaluation results.
    publicKeySet: Required. A simple set of public keys. A signature is
      considered valid if any keys in the set validate the signature.
  """

  displayName = _messages.StringField(1)
  publicKeySet = _messages.MessageField('SigstorePublicKeySet', 2)


class SigstorePublicKey(_messages.Message):
  r"""A Sigstore public key. `SigstorePublicKey` is the public key material
  used to authenticate Sigstore signatures.

  Fields:
    publicKeyPem: The public key material in PEM format.
  """

  publicKeyPem = _messages.StringField(1)


class SigstorePublicKeySet(_messages.Message):
  r"""A bundle of Sigstore public keys, used to verify Sigstore signatures. A
  signature is authenticated by a `SigstorePublicKeySet` if any of the keys
  verify it.

  Fields:
    publicKeys: Required. `public_keys` must have at least one entry.
  """

  publicKeys = _messages.MessageField('SigstorePublicKey', 1, repeated=True)


class SigstoreSignatureCheck(_messages.Message):
  r"""A Sigstore signature check, which verifies the Sigstore signature
  associated with an image.

  Fields:
    sigstoreAuthorities: Required. The authorities required by this check to
      verify the signature. A signature only needs to be verified by one
      authority to pass the check.
  """

  sigstoreAuthorities = _messages.MessageField('SigstoreAuthority', 1, repeated=True)


class SimpleSigningAttestationCheck(_messages.Message):
  r"""Require a signed [DSSE](https://github.com/secure-systems-lab/dsse)
  attestation with type SimpleSigning.

  Fields:
    attestationAuthenticators: Required. The authenticators required by this
      check to verify an attestation. Typically this is one or more PKIX
      public keys for signature verification. Only one authenticator needs to
      consider an attestation verified in order for an attestation to be
      considered fully authenticated. In otherwords, this list of
      authenticators is an "OR" of the authenticator results. At least one
      authenticator is required.
    containerAnalysisAttestationProjects: Optional. The projects where
      attestations are stored as Container Analysis Occurrences, in the format
      `projects/[PROJECT_ID]`. Only one attestation needs to successfully
      verify an image for this check to pass, so a single verified attestation
      found in any of `container_analysis_attestation_projects` is sufficient
      for the check to pass. A project ID must be used, not a project number.
      When fetching Occurrences from Container Analysis, only
      `AttestationOccurrence` kinds are considered. In the future, additional
      Occurrence kinds may be added to the query. Maximum number of
      `container_analysis_attestation_projects` allowed in each
      `SimpleSigningAttestationCheck` is 10.
  """

  attestationAuthenticators = _messages.MessageField('AttestationAuthenticator', 1, repeated=True)
  containerAnalysisAttestationProjects = _messages.StringField(2, repeated=True)


class SlsaCheck(_messages.Message):
  r"""A SLSA provenance attestation check, which ensures that images are built
  by a trusted builder using source code from its trusted repositories only.

  Fields:
    rules: Specifies a list of verification rules for the SLSA attestations.
      An image is considered compliant with the SlsaCheck if any of the rules
      are satisfied.
  """

  rules = _messages.MessageField('VerificationRule', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TrustedDirectoryCheck(_messages.Message):
  r"""A trusted directory check, which rejects images that do not come from
  the set of user-configured trusted directories.

  Fields:
    trustedDirPatterns: Required. List of trusted directory patterns. A
      pattern is in the form "registry/path/to/directory". The registry domain
      part is defined as two or more dot-separated words, e.g., `us.pkg.dev`,
      or `gcr.io`. Additionally, `*` can be used in three ways as wildcards:
      1. leading `*` to match varying prefixes in registry subdomain (useful
      for location prefixes); 2. trailing `*` after registry/ to match varying
      endings; 3. trailing `**` after registry/ to match "/" as well. For
      example: -- `gcr.io/my-project/my-repo` is valid to match a single
      directory -- `*-docker.pkg.dev/my-project/my-repo` or `*.gcr.io/my-
      project` are valid to match varying prefixes -- `gcr.io/my-project/*`
      will match all direct directories in `my-project` -- `gcr.io/my-
      project/**` would match all directories in `my-project` -- `gcr.i*` is
      not allowed since the registry is not completely specified --
      `sub*domain.gcr.io/nginx` is not valid because only leading `*` or
      trailing `*` are allowed. -- `*pkg.dev/my-project/my-repo` is not valid
      because leading `*` can only match subdomain -- `**-docker.pkg.dev` is
      not valid because one leading `*` is allowed, and that it cannot match
      `/`
  """

  trustedDirPatterns = _messages.StringField(1, repeated=True)


class UserOwnedGrafeasNote(_messages.Message):
  r"""An user owned Grafeas note references a Grafeas Attestation.Authority
  Note created by the user.

  Fields:
    delegationServiceAccountEmail: Output only. This field will contain the
      service account email address that this attestor will use as the
      principal when querying Container Analysis. Attestor administrators must
      grant this service account the IAM role needed to read attestations from
      the note_reference in Container Analysis
      (`containeranalysis.notes.occurrences.viewer`). This email address is
      fixed for the lifetime of the attestor, but callers should not make any
      other assumptions about the service account email; future versions may
      use an email based on a different naming pattern.
    noteReference: Required. The Grafeas resource name of a
      Attestation.Authority Note, created by the user, in the format:
      `projects/[PROJECT_ID]/notes/*`. This field may not be updated. A
      project ID must be used, not a project number. An attestation by this
      attestor is stored as a Grafeas Attestation.Authority Occurrence that
      names a container image and that links to this Note. Grafeas is an
      external dependency.
    publicKeys: Optional. Public keys that verify attestations signed by this
      attestor. This field may be updated. If this field is non-empty, one of
      the specified public keys must verify that an attestation was signed by
      this attestor for the image specified in the admission request. If this
      field is empty, this attestor always returns that no valid attestations
      exist.
  """

  delegationServiceAccountEmail = _messages.StringField(1)
  noteReference = _messages.StringField(2)
  publicKeys = _messages.MessageField('AttestorPublicKey', 3, repeated=True)


class ValidateAttestationOccurrenceRequest(_messages.Message):
  r"""Request message for ValidationHelperV1.ValidateAttestationOccurrence.

  Fields:
    attestation: Required. An AttestationOccurrence to be checked that it can
      be verified by the `Attestor`. It does not have to be an existing entity
      in Container Analysis. It must otherwise be a valid
      `AttestationOccurrence`.
    occurrenceNote: Required. The resource name of the Note to which the
      containing Occurrence is associated.
    occurrenceResourceUri: Required. The URI of the artifact (e.g. container
      image) that is the subject of the containing Occurrence.
  """

  attestation = _messages.MessageField('AttestationOccurrence', 1)
  occurrenceNote = _messages.StringField(2)
  occurrenceResourceUri = _messages.StringField(3)


class ValidateAttestationOccurrenceResponse(_messages.Message):
  r"""Response message for ValidationHelperV1.ValidateAttestationOccurrence.

  Enums:
    ResultValueValuesEnum: The result of the Attestation validation.

  Fields:
    denialReason: The reason for denial if the Attestation couldn't be
      validated.
    result: The result of the Attestation validation.
  """

  class ResultValueValuesEnum(_messages.Enum):
    r"""The result of the Attestation validation.

    Values:
      RESULT_UNSPECIFIED: Unspecified.
      VERIFIED: The Attestation was able to verified by the Attestor.
      ATTESTATION_NOT_VERIFIABLE: The Attestation was not able to verified by
        the Attestor.
    """
    RESULT_UNSPECIFIED = 0
    VERIFIED = 1
    ATTESTATION_NOT_VERIFIABLE = 2

  denialReason = _messages.StringField(1)
  result = _messages.EnumField('ResultValueValuesEnum', 2)


class VerificationRule(_messages.Message):
  r"""Specifies verification rules for evaluating the SLSA attestations
  including: which builders to trust, where to fetch the SLSA attestations
  generated by those builders, and other builder-specific evaluation rules
  such as which source repositories are trusted. An image is considered
  verified by the rule if any of the fetched SLSA attestations is verified.

  Enums:
    TrustedBuilderValueValuesEnum: Each verification rule is used for
      evaluation against provenances generated by a specific builder (group).
      For some of the builders, such as the Google Cloud Build, users don't
      need to explicitly specify their roots of trust in the policy since the
      evaluation service can automatically fetch them based on the builder
      (group).

  Fields:
    attestationSource: Specifies where to fetch the provenances attestations
      generated by the builder (group).
    configBasedBuildRequired: If true, require the image to be built from a
      top-level configuration. `trusted_source_repo_patterns` specifies the
      repositories containing this configuration.
    customConstraints: Optional. A CEL expression for specifying custom
      constraints on the provenance payload. This can be used when users want
      to specify expectations on provenance fields that are not covered by the
      general check. For example, users can use this field to require that
      certain parameters should never be used during the build process.
    trustedBuilder: Each verification rule is used for evaluation against
      provenances generated by a specific builder (group). For some of the
      builders, such as the Google Cloud Build, users don't need to explicitly
      specify their roots of trust in the policy since the evaluation service
      can automatically fetch them based on the builder (group).
    trustedSourceRepoPatterns: List of trusted source code repository URL
      patterns. These patterns match the full repository URL without its
      scheme (e.g. `https://`). The patterns must not include schemes. For
      example, the pattern `source.cloud.google.com/my-project/my-repo-name`
      matches the following URLs: - `source.cloud.google.com/my-project/my-
      repo-name` - `git+ssh://source.cloud.google.com/my-project/my-repo-name`
      - `https://source.cloud.google.com/my-project/my-repo-name` A pattern
      matches a URL either exactly or with `*` wildcards. `*` can be used in
      only two ways: 1. trailing `*` after hosturi/ to match varying endings;
      2. trailing `**` after hosturi/ to match `/` as well. `*` and `**` can
      only be used as wildcards and can only occur at the end of the pattern
      after a `/`. (So it's not possible to match a URL that contains literal
      `*`.) For example: - `github.com/my-project/my-repo` is valid to match a
      single repo - `github.com/my-project/*` will match all direct repos in
      `my-project` - `github.com/**` matches all repos in GitHub
  """

  class TrustedBuilderValueValuesEnum(_messages.Enum):
    r"""Each verification rule is used for evaluation against provenances
    generated by a specific builder (group). For some of the builders, such as
    the Google Cloud Build, users don't need to explicitly specify their roots
    of trust in the policy since the evaluation service can automatically
    fetch them based on the builder (group).

    Values:
      BUILDER_UNSPECIFIED: Should never happen.
      GOOGLE_CLOUD_BUILD: The whole Google Cloud Build (GCB) builder group,
        including all GCB builder types.
    """
    BUILDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_BUILD = 1

  attestationSource = _messages.MessageField('AttestationSource', 1)
  configBasedBuildRequired = _messages.BooleanField(2)
  customConstraints = _messages.StringField(3)
  trustedBuilder = _messages.EnumField('TrustedBuilderValueValuesEnum', 4)
  trustedSourceRepoPatterns = _messages.StringField(5, repeated=True)


class VulnerabilityCheck(_messages.Message):
  r"""An image vulnerability check, which rejects images that violate the
  configured vulnerability rules.

  Enums:
    MaximumFixableSeverityValueValuesEnum: Required. The threshold for
      severity for which a fix is currently available. This field is required
      and must be set.
    MaximumUnfixableSeverityValueValuesEnum: Required. The threshold for
      severity for which a fix isn't currently available. This field is
      required and must be set.

  Fields:
    allowedCves: Optional. A list of specific CVEs to ignore even if the
      vulnerability level violates `maximumUnfixableSeverity` or
      `maximumFixableSeverity`. CVEs are listed in the format of Container
      Analysis note id. For example: - CVE-2021-20305 - CVE-2020-10543 The
      CVEs are applicable regardless of note provider project, e.g., an entry
      of `CVE-2021-20305` will allow vulnerabilities with a note name of
      either `projects/goog-vulnz/notes/CVE-2021-20305` or `projects/CUSTOM-
      PROJECT/notes/CVE-2021-20305`.
    blockedCves: Optional. A list of specific CVEs to always raise warnings
      about even if the vulnerability level meets `maximumUnfixableSeverity`
      or `maximumFixableSeverity`. CVEs are listed in the format of Container
      Analysis note id. For example: - CVE-2021-20305 - CVE-2020-10543 The
      CVEs are applicable regardless of note provider project, e.g., an entry
      of `CVE-2021-20305` will block vulnerabilities with a note name of
      either `projects/goog-vulnz/notes/CVE-2021-20305` or `projects/CUSTOM-
      PROJECT/notes/CVE-2021-20305`.
    containerAnalysisVulnerabilityProjects: Optional. The projects where
      vulnerabilities are stored as Container Analysis Occurrences. Each
      project is expressed in the resource format of `projects/[PROJECT_ID]`,
      e.g., `projects/my-gcp-project`. An attempt will be made for each
      project to fetch vulnerabilities, and all valid vulnerabilities will be
      used to check against the vulnerability policy. If no valid scan is
      found in all projects configured here, an error will be returned for the
      check. Maximum number of `container_analysis_vulnerability_projects`
      allowed in each `VulnerabilityCheck` is 10.
    maximumFixableSeverity: Required. The threshold for severity for which a
      fix is currently available. This field is required and must be set.
    maximumUnfixableSeverity: Required. The threshold for severity for which a
      fix isn't currently available. This field is required and must be set.
  """

  class MaximumFixableSeverityValueValuesEnum(_messages.Enum):
    r"""Required. The threshold for severity for which a fix is currently
    available. This field is required and must be set.

    Values:
      MAXIMUM_ALLOWED_SEVERITY_UNSPECIFIED: Not specified.
      BLOCK_ALL: Block any vulnerability.
      MINIMAL: Allow only minimal severity.
      LOW: Allow only low severity and lower.
      MEDIUM: Allow medium severity and lower.
      HIGH: Allow high severity and lower.
      CRITICAL: Allow critical severity and lower.
      ALLOW_ALL: Allow all severity, even vulnerability with unspecified
        severity.
    """
    MAXIMUM_ALLOWED_SEVERITY_UNSPECIFIED = 0
    BLOCK_ALL = 1
    MINIMAL = 2
    LOW = 3
    MEDIUM = 4
    HIGH = 5
    CRITICAL = 6
    ALLOW_ALL = 7

  class MaximumUnfixableSeverityValueValuesEnum(_messages.Enum):
    r"""Required. The threshold for severity for which a fix isn't currently
    available. This field is required and must be set.

    Values:
      MAXIMUM_ALLOWED_SEVERITY_UNSPECIFIED: Not specified.
      BLOCK_ALL: Block any vulnerability.
      MINIMAL: Allow only minimal severity.
      LOW: Allow only low severity and lower.
      MEDIUM: Allow medium severity and lower.
      HIGH: Allow high severity and lower.
      CRITICAL: Allow critical severity and lower.
      ALLOW_ALL: Allow all severity, even vulnerability with unspecified
        severity.
    """
    MAXIMUM_ALLOWED_SEVERITY_UNSPECIFIED = 0
    BLOCK_ALL = 1
    MINIMAL = 2
    LOW = 3
    MEDIUM = 4
    HIGH = 5
    CRITICAL = 6
    ALLOW_ALL = 7

  allowedCves = _messages.StringField(1, repeated=True)
  blockedCves = _messages.StringField(2, repeated=True)
  containerAnalysisVulnerabilityProjects = _messages.StringField(3, repeated=True)
  maximumFixableSeverity = _messages.EnumField('MaximumFixableSeverityValueValuesEnum', 4)
  maximumUnfixableSeverity = _messages.EnumField('MaximumUnfixableSeverityValueValuesEnum', 5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    BinaryauthorizationProjectsAttestorsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BinaryauthorizationProjectsPolicyGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
