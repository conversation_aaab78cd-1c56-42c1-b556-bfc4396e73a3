"""Generated message classes for cloudcommerceconsumerprocurement version v1alpha1.

Enables consumers to procure products served by Cloud Marketplace platform
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudcommerceconsumerprocurement'


class CloudcommerceconsumerprocurementBillingAccountsAccountsCreateRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsAccountsCreateRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1Account: A
      GoogleCloudCommerceConsumerProcurementV1alpha1Account resource to be
      passed as the request body.
    parent: Required. The parent resource of this account. This field is of
      the form "/". Currently supported type: 'billingAccounts/{billing-
      account-id}'
  """

  googleCloudCommerceConsumerProcurementV1alpha1Account = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Account', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsAccountsDeleteRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsAccountsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the account to delete.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsAccountsGetRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsAccountsGetRequest
  object.

  Fields:
    name: Required. The resource name of the account to retrieve.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsAccountsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsAccountsListRequest
  object.

  Fields:
    pageSize: The maximum number of entries that are requested. The default
      page size is 25 and the maximum page size is 200.
    pageToken: The token for fetching the next page.
    parent: Required. The parent resource to query for accounts. This field is
      of the form `billingAccounts/{billing-account-id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudcommerceconsumerprocurementBillingAccountsConsentsCheckRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsConsentsCheckRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest
      resource to be passed as the request body.
    parent: Required. Parent of consents. Current supported format includes: -
      billingAccounts/{billing_account} - projects/{project_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsConsentsGrantRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsConsentsGrantRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest
      resource to be passed as the request body.
    parent: Required. Parent of the consent to grant. Current supported format
      includes: - billingAccounts/{billing_account} - projects/{project_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsConsentsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsConsentsListRequest
  object.

  Fields:
    agreement: Required. Leaving this field unset will throw an error. Valid
      format: commerceoffercatalog.googleapis.com/billingAccounts/{billing_acc
      ount}/offers/{offer_id}/agreements/{agreement_id}
    pageSize: The maximum number of results returned by this request.
    pageToken: The continuation token, which is used to page through large
      result sets. To get the next page of results, set this parameter to the
      value of `nextPageToken` from the previous response.
    parent: Required. Parent of consents. Current supported format: -
      billingAccounts/{billing_account}
  """

  agreement = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudcommerceconsumerprocurementBillingAccountsConsentsRevokeRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsConsentsRevokeRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest
      resource to be passed as the request body.
    name: Required. A consent to be reovked. Examples of valid names would be:
      - billingAccounts/{billing_account}/consents/{consent_id} -
      projects/{project_id}/consents/{consent_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersCancelRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersCancelRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest
      resource to be passed as the request body.
    name: Required. The resource name of the order.
  """

  googleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersEventsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersEventsListRequest
  object.

  Fields:
    pageSize: The maximum number of entries requested. The default page size
      is 25 and the maximum page size is 200.
    pageToken: The token for fetching the next page.
    parent: Required. The parent resource to request for events. This field
      has the format 'billingAccounts/{billing-account-id}/orders/{order-id}'.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersGetAuditLogRequest(_messages.Message):
  r"""A
  CloudcommerceconsumerprocurementBillingAccountsOrdersGetAuditLogRequest
  object.

  Fields:
    name: Required. The name of the auditLog to retrieve. Format:
      `billingAccounts/{billing_account}/orders/{order}/auditLog`
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersGetLicensePoolRequest(_messages.Message):
  r"""A
  CloudcommerceconsumerprocurementBillingAccountsOrdersGetLicensePoolRequest
  object.

  Fields:
    name: Required. The name of the license pool to get. Format:
      `billingAccounts/{billing_account}/orders/{order}/licensePool`
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersGetRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersGetRequest
  object.

  Fields:
    name: Required. The name of the order to retrieve.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolAssignRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolAssign
  Request object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1AssignRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1AssignRequest resource to
      be passed as the request body.
    parent: Required. License pool name.
  """

  googleCloudCommerceConsumerProcurementV1alpha1AssignRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AssignRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolEnumerateLicensedUsersRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolEnumer
  ateLicensedUsersRequest object.

  Fields:
    pageSize: Optional. The maximum number of users to return. The service may
      return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `EnumerateLicensedUsers` call. Provide this to retrieve the subsequent
      page.
    parent: Required. License pool name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolUnassignRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolUnassi
  gnRequest object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1UnassignRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1UnassignRequest resource
      to be passed as the request body.
    parent: Required. License pool name.
  """

  googleCloudCommerceConsumerProcurementV1alpha1UnassignRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1UnassignRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersListRequest
  object.

  Fields:
    filter: Filter that you can use to limit the list request. A query string
      that can match a selected set of attributes with string values. For
      example, `display_name=abc`. Supported query attributes are *
      `display_name` If the query contains special characters other than
      letters, underscore, or digits, the phrase must be quoted with double
      quotes. For example, `display_name="foo:bar"`, where the display name
      needs to be quoted because it contains special character colon. Queries
      can be combined with `OR`, and `NOT` to form more complex queries. You
      can also group them to force a desired evaluation order. For example,
      `display_name=abc OR display_name=def`.
    pageSize: The maximum number of entries requested. The default page size
      is 25 and the maximum page size is 200.
    pageToken: The token for fetching the next page.
    parent: Required. The parent resource to query for orders. This field has
      the form `billingAccounts/{billing-account-id}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersModifyRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersModifyRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest
      resource to be passed as the request body.
    name: Required. Name of the order to update.
  """

  googleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersOperationsGetRequest(_messages.Message):
  r"""A
  CloudcommerceconsumerprocurementBillingAccountsOrdersOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributions
  ListRequest object.

  Fields:
    pageSize: The maximum number of entries returned per call.
    pageToken: The token for fetching the next page of entries.
    parent: Required. The parent Order to query for OrderAttributions. This
      field is of the form `billingAccounts/{billing-account-
      id}/orders/{order-id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsOperationsGetRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributions
  OperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsPatchRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributions
  PatchRequest object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1OrderAttribution: A
      GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttribution resource
      to be passed as the request body.
    name: Output only. Resource name of the attribution configuration Format:
      billingAccounts/{billing_account}/orders/{order}/orderAttributions/{orde
      r_attribution} attribution_target references the Order parameter that
      defines the total attributable amount of this resource.
    updateMask: Optional. Mask used to indicate which parts of
      OrderAttribution are to be updated.
  """

  googleCloudCommerceConsumerProcurementV1alpha1OrderAttribution = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttribution', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudcommerceconsumerprocurementBillingAccountsOrdersPlaceRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersPlaceRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest resource
      to be passed as the request body.
    parent: Required. The resource name of the parent resource. This field has
      the form `billingAccounts/{billing-account-id}`.
  """

  googleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementBillingAccountsOrdersUpdateLicensePoolRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementBillingAccountsOrdersUpdateLicensePool
  Request object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1LicensePool: A
      GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool resource to be
      passed as the request body.
    name: Identifier. Format:
      `billingAccounts/{billing_account}/orders/{order}/licensePool`
    updateMask: Required. The list of fields to update.
  """

  googleCloudCommerceConsumerProcurementV1alpha1LicensePool = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudcommerceconsumerprocurementProjectsCheckEntitlementsRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsCheckEntitlementsRequest
  object.

  Fields:
    parent: Required. The consumer project Format: `projects/{project_number}`
      Required.
    service: Required. The one platform service name. Format:
      `services/{service_name}`. Required.
  """

  parent = _messages.StringField(1, required=True)
  service = _messages.StringField(2)


class CloudcommerceconsumerprocurementProjectsConsentsCheckRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsConsentsCheckRequest object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest
      resource to be passed as the request body.
    parent: Required. Parent of consents. Current supported format includes: -
      billingAccounts/{billing_account} - projects/{project_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementProjectsConsentsGrantRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsConsentsGrantRequest object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest
      resource to be passed as the request body.
    parent: Required. Parent of the consent to grant. Current supported format
      includes: - billingAccounts/{billing_account} - projects/{project_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementProjectsConsentsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsConsentsListRequest object.

  Fields:
    agreement: Required. Leaving this field unset will throw an error. Valid
      format: commerceoffercatalog.googleapis.com/billingAccounts/{billing_acc
      ount}/offers/{offer_id}/agreements/{agreement_id}
    pageSize: The maximum number of results returned by this request.
    pageToken: The continuation token, which is used to page through large
      result sets. To get the next page of results, set this parameter to the
      value of `nextPageToken` from the previous response.
    parent: Required. Parent of consents. Current supported format: -
      billingAccounts/{billing_account}
  """

  agreement = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudcommerceconsumerprocurementProjectsConsentsRevokeRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsConsentsRevokeRequest object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest: A
      GoogleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest
      resource to be passed as the request body.
    name: Required. A consent to be reovked. Examples of valid names would be:
      - billingAccounts/{billing_account}/consents/{consent_id} -
      projects/{project_id}/consents/{consent_id}
  """

  googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementProjectsEntitlementsGetRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsEntitlementsGetRequest object.

  Fields:
    name: Required. The name of the entitlement to retrieve. This field is one
      of the following forms: `projects/{project-
      number}/entitlements/{entitlement-id}` `projects/{project-
      id}/entitlements/{entitlement-id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementProjectsEntitlementsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsEntitlementsListRequest
  object.

  Fields:
    filter: Filter that can be used to limit the list request. A query string
      that can match a selected set of attributes with string values.
      Supported query attributes are * `services.service_name` * `service` *
      `offer` * `pending_change.new_offer` * `product_external_name` *
      `provider` Service queries have the format: `service="services/%s"`
      where %s is the OnePlatformServiceId and all values are surrounded with
      quote literals. Offer has the format: "billingAccounts/{billing-account-
      id}/offers/{offer-id}" for private offers or
      "services/{service}/standardOffers/{offer-id}" for standard offers.
      Related offer filters are formatted where %s is the above fully
      qualified Offer and all values are surrounded with quote literals. Ex.
      `offer="%s"` `pending_change.new_offer="%s"` Product and provider
      queries have the format: `product_external_name="pumpkin-saas"`
      `provider="pumpkindb"` If the query contains special characters other
      than letters, underscore, or digits, the phrase must be quoted with
      double quotes. For example, `service="services/%s"`, where the service
      query needs to be quoted because it contains special character forward
      slash. Queries can be combined with `OR`, and `NOT` to form more complex
      queries. You can also group them to force a desired evaluation order.
      E.g. `service="services/pumpkin"`.
    pageSize: The maximum number of entries requested. The default page size
      is 25 and the maximum page size is 200.
    pageToken: The token for fetching the next page.
    parent: Required. The parent resource to query for Entitlements. Currently
      the only parents supported are "projects/{project-number}" and
      "projects/{project-id}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudcommerceconsumerprocurementProjectsFreeTrialsCreateRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsFreeTrialsCreateRequest
  object.

  Fields:
    googleCloudCommerceConsumerProcurementV1alpha1FreeTrial: A
      GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial resource to be
      passed as the request body.
    parent: Required. The parent resource to query for FreeTrials. Currently
      the only parent supported is "projects/{project-id}".
  """

  googleCloudCommerceConsumerProcurementV1alpha1FreeTrial = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial', 1)
  parent = _messages.StringField(2, required=True)


class CloudcommerceconsumerprocurementProjectsFreeTrialsGetRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsFreeTrialsGetRequest object.

  Fields:
    name: Required. The name of the freeTrial to retrieve. This field is of
      the form `projects/{project-id}/freeTrials/{freetrial-id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudcommerceconsumerprocurementProjectsFreeTrialsListRequest(_messages.Message):
  r"""A CloudcommerceconsumerprocurementProjectsFreeTrialsListRequest object.

  Fields:
    filter: The filter that can be used to limit the list request. The filter
      is a query string that can match a selected set of attributes with
      string values. For example `product_external_name=1234-5678-ABCD-EFG`.
      Supported query attributes are * `product_external_name` * `provider` *
      `service` Service queries have the format:
      `service="services/{serviceID}"` where serviceID is the
      OnePlatformServiceId. If the query contains special characters other
      than letters, underscore, or digits, the phrase must be quoted with
      double quotes. For example, `product_external_name="foo:bar"`, where the
      product name needs to be quoted because it contains special character
      colon. Queries can be combined with `AND`, `OR`, and `NOT` to form more
      complex queries. They can also be grouped to force a desired evaluation
      order. For example, `provider=providers/E-1234 OR
      provider=providers/5678 AND NOT (product_external_name=foo-product)`.
      Connective `AND` can be omitted between two predicates. For example
      `provider=providers/E-1234 product_external_name=foo` is equivalent to
      `provider=providers/E-1234 AND product_external_name=foo`.
    pageSize: The maximum number of entries that are requested. The default
      page size is 25 and the maximum page size is 200.
    pageToken: The token for fetching the next page.
    parent: Required. The parent resource to query for FreeTrials. Currently
      the only parent supported is "projects/{project-id}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class GoogleCloudCommerceConsumerProcurementV1PlaceOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.PlaceOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1Account(_messages.Message):
  r"""Represents an account that was established by the customer with a
  service provider. When consuming services on external service provider's
  systems, the service provider generally needs to create a linked-account on
  their system to track customers. The account resource represents this
  relationship. Products/Services that are hosted by external service
  providers generally require an account to be present before they can be
  purchased and used. The metadata that indicates whether an Account is
  required for a purchase, or what parameters are needed for creating an
  Account is configured by service providers.

  Messages:
    PropertiesValue: Output only. Set of properties that the service provider
      supplied during account creation.

  Fields:
    approvals: Output only. The approvals for this account. These approvals
      are used to track actions that are permitted or have been completed by a
      customer within the context of the provider. This might include a sign
      up flow or a provisioning step, for example, that the provider can admit
      to having happened.
    createTime: Output only. The creation timestamp.
    name: Output only. The resource name of the account. Account names have
      the form `billingAccounts/{billing_account_id}/accounts/{account_id}`.
    properties: Output only. Set of properties that the service provider
      supplied during account creation.
    provider: Required. The identifier of the service provider that this
      account was created against. Provider has the format of
      `providers/{provider_id}`.
    updateTime: Output only. The last update timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Output only. Set of properties that the service provider supplied
    during account creation.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approvals = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AccountApproval', 1, repeated=True)
  createTime = _messages.StringField(2)
  name = _messages.StringField(3)
  properties = _messages.MessageField('PropertiesValue', 4)
  provider = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GoogleCloudCommerceConsumerProcurementV1alpha1AccountApproval(_messages.Message):
  r"""An approval for some action on an account.

  Enums:
    StateValueValuesEnum: The state of the approval.

  Fields:
    name: The name of the approval.
    reason: An explanation for the state of the approval.
    state: The state of the approval.
    updateTime: The last update timestamp of the approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the approval.

    Values:
      STATE_UNSPECIFIED: Sentinel value; do not use.
      PENDING: The approval is pending response from the provider. The
        approval state can transition to Account.Approval.State.APPROVED or
        Account.Approval.State.REJECTED.
      APPROVED: The approval has been granted by the provider.
      REJECTED: The approval has been rejected by the provider. A provider may
        choose to approve a previously rejected approval, so is it possible to
        transition to Account.Approval.State.APPROVED.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3

  name = _messages.StringField(1)
  reason = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudCommerceConsumerProcurementV1alpha1AddOnDetails(_messages.Message):
  r"""Information about an add-on line item.

  Fields:
    isAddOn: Output only. Indicates whether this item is an add-on.
  """

  isAddOn = _messages.BooleanField(1)


class GoogleCloudCommerceConsumerProcurementV1alpha1AssignRequest(_messages.Message):
  r"""Request message for LicenseManagementService.Assign.

  Fields:
    usernames: Required. Username. Format: `<EMAIL>`.
  """

  usernames = _messages.StringField(1, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1AssignResponse(_messages.Message):
  r"""Response message for LicenseManagementService.Assign."""


class GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocol(_messages.Message):
  r"""Assignment protocol for a license pool.

  Fields:
    autoAssignmentType: Allow automatic assignments triggered by data plane
      operations.
    manualAssignmentType: Allow manual assignments triggered by administrative
      operations only.
  """

  autoAssignmentType = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocolAutoAssignmentType', 1)
  manualAssignmentType = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocolManualAssignmentType', 2)


class GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocolAutoAssignmentType(_messages.Message):
  r"""Configuration for automatic assignments handled by data plane
  operations.

  Fields:
    inactiveLicenseTtl: Optional. The time to live for an inactive license.
      After this time has passed, the license will be automatically unassigned
      from the user. Must be at least 7 days, if set. If unset, the license
      will never expire.
  """

  inactiveLicenseTtl = _messages.StringField(1)


class GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocolManualAssignmentType(_messages.Message):
  r"""Allow manual assignments triggered by administrative operations only."""


class GoogleCloudCommerceConsumerProcurementV1alpha1AuditLog(_messages.Message):
  r"""Consumer Procurement Order Audit Log To be deprecated

  Fields:
    auditLogRecords: List of audit log records for an offer
    name: The resource name of the order auditLog Format:
      `billingAccounts/{billing_account}/orders/{order}/auditLog`
  """

  auditLogRecords = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AuditLogRecord', 1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1AuditLogRecord(_messages.Message):
  r"""Definition of an Audit Log Record

  Enums:
    ActionTypeValueValuesEnum: The type of action

  Fields:
    actionTime: The time when the action takes place
    actionType: The type of action
    offerId: The offer id corresponding to the log record.
    userEmail: The email of the user taking the action. This field can be
      empty for users authenticated through 3P identity provider.
    userName: The name of the user taking the action. For users authenticated
      through 3P identity provider (BYOID), the field value format is
      described in go/byoid-data-pattern:displaying-users.
  """

  class ActionTypeValueValuesEnum(_messages.Enum):
    r"""The type of action

    Values:
      ACTION_TYPE_UNSPECIFIED: Default value, do not use.
      ORDER_PLACED: The action of accepting an offer.
      ORDER_CANCELLED: Order Cancelation action.
      ORDER_MODIFIED: The action of modifying an order.
    """
    ACTION_TYPE_UNSPECIFIED = 0
    ORDER_PLACED = 1
    ORDER_CANCELLED = 2
    ORDER_MODIFIED = 3

  actionTime = _messages.StringField(1)
  actionType = _messages.EnumField('ActionTypeValueValuesEnum', 2)
  offerId = _messages.StringField(3)
  userEmail = _messages.StringField(4)
  userName = _messages.StringField(5)


class GoogleCloudCommerceConsumerProcurementV1alpha1CancelOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CancelOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest(_messages.Message):
  r"""Request message for ConsumerProcurementService.CancelOrder.

  Enums:
    CancellationPolicyValueValuesEnum: Optional. Cancellation policy of this
      request.

  Fields:
    cancellationPolicy: Optional. Cancellation policy of this request.
    etag: Optional. The weak etag, which can be optionally populated, of the
      order that this cancel request is based on. Validation checking will
      only happen if the invoker supplies this field.
  """

  class CancellationPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Cancellation policy of this request.

    Values:
      CANCELLATION_POLICY_UNSPECIFIED: If unspecified, cancellation will try
        to cancel the order, if order cannot be immediately cancelled, auto
        renewal will be turned off. However, caller should avoid using the
        value as it will yield a non-deterministic result. This is still
        supported mainly to maintain existing integrated usages and ensure
        backwards compatibility.
      CANCELLATION_POLICY_CANCEL_IMMEDIATELY: Request will cancel the whole
        order immediately, if order cannot be immediately cancelled, the
        request will fail.
      CANCELLATION_POLICY_CANCEL_AT_TERM_END: Request will cancel the auto
        renewal, if order is not subscription based, the request will fail.
    """
    CANCELLATION_POLICY_UNSPECIFIED = 0
    CANCELLATION_POLICY_CANCEL_IMMEDIATELY = 1
    CANCELLATION_POLICY_CANCEL_AT_TERM_END = 2

  cancellationPolicy = _messages.EnumField('CancellationPolicyValueValuesEnum', 1)
  etag = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest(_messages.Message):
  r"""Request message for check consents.

  Fields:
    agreement: Required. Agreement to be checked against. A valid format would
      be - commerceoffercatalog.googleapis.com/billingAccounts/{billing_accoun
      t}/offers/{offer_id}/agreements/{agreement_id} commerceoffercatalog.goog
      leapis.com/services/{service}/standardOffers/{offer_id}/agreements/{agre
      ement_id}
    financialContract: Financial contract this consent applies to. This is a
      system full resource name. E.g.: //commerceoffercatalog.googleapis.com/b
      illingAccounts/{billing_account}/offers/{offer-id}
    languageCode: The language code is used to find the agreement document if
      check consent doesn't pass. If this field is set, the method will
      attempt to locate the agreement document written in that language. If
      such document cannot be found, the request will fail. If this field is
      not set, the method will try two strategies in the following order: 1)
      reuse the language of document associated with the most recent consent,
      2) use the document written in the default language, while default
      language is set by the agreement owner. If neither strategy works, the
      request will fail. Please follow BCP 47
      (https://www.w3.org/International/articles/bcp47/) for the language
      string.
    offer: Offer associated with the consent. Formats include "commerceofferca
      talog.googleapis.com/billingAccounts/{billing_account}/offers/{offer_id}
      ". "commerceoffercatalog.googleapis.com/services/{service}/standardOffer
      s/{offer_id}".
  """

  agreement = _messages.StringField(1)
  financialContract = _messages.StringField(2)
  languageCode = _messages.StringField(3)
  offer = _messages.StringField(4)


class GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentResponse(_messages.Message):
  r"""Response for check consent.

  Fields:
    consent: The Consent for this agreement if a consent is active.
  """

  consent = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Consent', 1)


class GoogleCloudCommerceConsumerProcurementV1alpha1CheckEntitlementsResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.CheckEntitlements.

  Fields:
    entitlementCandidates: Output only. Can-be-used Entitlement Candidates.
      Expected to contain at most one entitlement unless the product is opted
      in go/ccm-purchasing:flat-fee-multi-sub-design
    entitlements: Output only. Available Entitlements. Expected to contain at
      most one entitlement unless the product is opted in go/ccm-
      purchasing:flat-fee-multi-sub-design
  """

  entitlementCandidates = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement', 1, repeated=True)
  entitlements = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement', 2, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1Consent(_messages.Message):
  r"""A consent resource represents the relationship between a user and an
  agreement.

  Enums:
    StateValueValuesEnum: Output only. State of current consent.

  Fields:
    agreement: Full name of the agreement that was agreed to for this consent,
      ## in the format of one of: "commerceoffercatalog.googleapis.com/billing
      Accounts/{billing_account}/offers/{offer_id}/agreements/{agreement_id}".
      "commerceoffercatalog.googleapis.com/services/{service}/standardOffers/{
      offer_id}/agreements/{agreement_id}".
    agreementDocument: Full name of the agreement document that was agreed to
      for this consent, ## in the format of one of: commerceoffercatalog.googl
      eapis.com/billingAccounts/{billing_account}/offers/{offer_id}/agreements
      /{agreement_id}/documents/{document_id}
    createTime: Output only. The creation time of current consent.
    financialContract: Financial contracts linked to this consent.
    name: The resource name of a consent. An examples of valid names would be
      in the format of: -
      "billingAccounts/{billing_account}/consents/{consent}". -
      "projects/{project_number}/consents/{consent}".
    offer: The name of the offer linked to this consent. It is in the format
      of: "commerceoffercatalog.googleapis.com/billingAccounts/{billing_accoun
      t}/offers/{offer_id}". "commerceoffercatalog.googleapis.com/services/{se
      rvice}/standardOffers/{offer_id}".
    state: Output only. State of current consent.
    updateTime: Output only. The update time of current consent.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of current consent.

    Values:
      STATE_UNSPECIFIED: Unspecified value for the state. Sentinel value; do
        not use.
      ACTIVE: Represent the approved state of the consent.
      REVOKED: Represent the revoked state of the consent.
      ROLLEDBACK: Represent the rolled back state of the consent.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    REVOKED = 2
    ROLLEDBACK = 3

  agreement = _messages.StringField(1)
  agreementDocument = _messages.StringField(2)
  createTime = _messages.StringField(3)
  financialContract = _messages.StringField(4)
  name = _messages.StringField(5)
  offer = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class GoogleCloudCommerceConsumerProcurementV1alpha1CreateAccountMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CreateAccount.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1CreateFreeTrialMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CreateFreeTrial.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1CustomPricing(_messages.Message):
  r"""Information about custom pricing on a resource.

  Fields:
    endTime: The end time of the custom pricing.
  """

  endTime = _messages.StringField(1)


class GoogleCloudCommerceConsumerProcurementV1alpha1DeleteAccountMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.DeleteAccount.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement(_messages.Message):
  r"""Entitlement represents the ability to use a product or services
  associated with a purchase within a Project. When the customer creates an
  Order, the system will create Entitlement resources under projects
  associated with the same billing account as the order, for all
  products/services procured in the order. Users can enable/disable
  Entitlements to allow/disallow using the product/service in a project. Next
  Id: 27

  Enums:
    StateValueValuesEnum: Output only. The state of the entitlement.

  Fields:
    changeHistory: Output only. Changes that are not pending anymore, e.g. it
      was effective at some point, or the change was reverted by the customer,
      or the change was rejected by partner. No more operations are allowed on
      these changes.
    createTime: Output only. The create timestamp.
    flavorExternalName: Output only. External name of the flavor this
      entitlement is created against. This field is populated when entitlement
      has currently associated flavor, it is empty when entitlement is
      UNAVAILABLE (if order is pending activation or order is already
      cancelled).
    name: Output only. The resource Name of the Entitlement. Entitlement names
      have the form `projects/{project_id}/entitlements/{entitlement_id}`.
    order: Output only. Order associated with this Entitlement. In the format
      of `billingAccounts/{billing_account}/orders/{order}`
    pendingChange: Output only. A change which is pending and not yet
      effective.
    productExternalName: Output only. External name of the product this
      entitlement is created against.
    provider: Output only. Provider associated with this Entitlement. In the
      format of `providers/{provider_id}`.
    state: Output only. The state of the entitlement.
    stateReason: Output only. An explanation for the entitlement's state.
      Mainly used in the case of
      `EntitlementState.ENTITLEMENT_STATE_UNAVAILABLE` states to explain why
      the entitlement is unavailable.
    updateTime: Output only. The last update timestamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the entitlement.

    Values:
      ENTITLEMENT_STATE_UNSPECIFIED: Sentinel value. Do not use.
      ENTITLEMENT_STATE_UNAVAILABLE: Indicates that the entitlement is
        unavailable and cannot be enabled.
      ENTITLEMENT_STATE_ENABLED: Indicates that the entitlement is enabled.
        The procured item is now usable.
      ENTITLEMENT_STATE_DISABLED: Indicates that the entitlement is disabled.
        The procured item is not usable.
      ENTITLEMENT_STATE_EXHAUSTED: Indicates that no more procured products
        can be added to the current project. This will be returned if there is
        already a consumer entitlement with resources deployed in another
        project and the product allows a single deployment only.
      ENTITLEMENT_STATE_INELIGIBLE: Indicates that the entitlement is
        ineligible for usage because the project is already enabled as a
        consumer on another entitlement of the same product.
    """
    ENTITLEMENT_STATE_UNSPECIFIED = 0
    ENTITLEMENT_STATE_UNAVAILABLE = 1
    ENTITLEMENT_STATE_ENABLED = 2
    ENTITLEMENT_STATE_DISABLED = 3
    ENTITLEMENT_STATE_EXHAUSTED = 4
    ENTITLEMENT_STATE_INELIGIBLE = 5

  changeHistory = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1EntitlementChange', 1, repeated=True)
  createTime = _messages.StringField(2)
  flavorExternalName = _messages.StringField(3)
  name = _messages.StringField(4)
  order = _messages.StringField(5)
  pendingChange = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1EntitlementChange', 6)
  productExternalName = _messages.StringField(7)
  provider = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  stateReason = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleCloudCommerceConsumerProcurementV1alpha1EntitlementChange(_messages.Message):
  r"""Entitlement change information. Next Id: 8

  Enums:
    ChangeStateValueValuesEnum: Output only. State of the change.
    ChangeStateReasonTypeValueValuesEnum: Output only. Predefined enum types
      for why this change is in current state.

  Fields:
    changeEffectiveTime: Output only. A time at which the change became or
      will become (in case of pending change) effective.
    changeState: Output only. State of the change.
    changeStateReasonType: Output only. Predefined enum types for why this
      change is in current state.
    newFlavorExternalName: Output only. Flavor external name after the change.
    oldFlavorExternalName: Output only. Flavor external name before the
      change.
  """

  class ChangeStateReasonTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Predefined enum types for why this change is in current
    state.

    Values:
      CHANGE_STATE_REASON_TYPE_UNSPECIFIED: Default value, indicating there is
        no predefined type for change state reason.
      CHANGE_STATE_REASON_TYPE_EXPIRED: Change is in current state due to term
        expiration.
      CHANGE_STATE_REASON_TYPE_USER_CANCELLED: Change is in current state due
        to user explicit cancellation.
      CHANGE_STATE_REASON_TYPE_SYSTEM_CANCELLED: Change is in current state
        due to system cancellation.
    """
    CHANGE_STATE_REASON_TYPE_UNSPECIFIED = 0
    CHANGE_STATE_REASON_TYPE_EXPIRED = 1
    CHANGE_STATE_REASON_TYPE_USER_CANCELLED = 2
    CHANGE_STATE_REASON_TYPE_SYSTEM_CANCELLED = 3

  class ChangeStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the change.

    Values:
      CHANGE_STATE_UNSPECIFIED: Sentinel value. Do not use.
      CHANGE_STATE_PENDING_APPROVAL: Change is in this state when a change is
        initiated and waiting for partner approval. This state is only
        applicable for pending change.
      CHANGE_STATE_APPROVED: Change is in this state, if the change was
        approved by partner or auto-approved but is pending to be effective.
        The change can be overwritten or cancelled depending on the new line
        item info property (pending Private Offer change cannot be cancelled
        and can only be overwritten by another Private Offer). This state is
        only applicable for pending change.
      CHANGE_STATE_COMPLETED: Change is in this state, if the change was
        activated and completed successfully. This state is only applicable
        for change in history.
      CHANGE_STATE_REJECTED: Change is in this state, if the change was
        rejected by partner. This state is only applicable for change in
        history.
      CHANGE_STATE_ABANDONED: Change is in this state, if it was abandoned by
        user. This state is only applicable for change in history.
      CHANGE_STATE_ACTIVATING: Change is in this state, if it is going through
        downstream provision, the change cannot be overwritten or cancelled in
        this state. This state is only applicable for pending change.
    """
    CHANGE_STATE_UNSPECIFIED = 0
    CHANGE_STATE_PENDING_APPROVAL = 1
    CHANGE_STATE_APPROVED = 2
    CHANGE_STATE_COMPLETED = 3
    CHANGE_STATE_REJECTED = 4
    CHANGE_STATE_ABANDONED = 5
    CHANGE_STATE_ACTIVATING = 6

  changeEffectiveTime = _messages.StringField(1)
  changeState = _messages.EnumField('ChangeStateValueValuesEnum', 2)
  changeStateReasonType = _messages.EnumField('ChangeStateReasonTypeValueValuesEnum', 3)
  newFlavorExternalName = _messages.StringField(4)
  oldFlavorExternalName = _messages.StringField(5)


class GoogleCloudCommerceConsumerProcurementV1alpha1EntitlementInfo(_messages.Message):
  r"""The benefit associated with a product purchase.

  Fields:
    id: ID of the entitlement info. Unique across all orders.
    services: The names of the Google Service Infrastructure services to
      enable.
  """

  id = _messages.StringField(1)
  services = _messages.StringField(2, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1EnumerateLicensedUsersResponse(_messages.Message):
  r"""Response message for LicenseManagementService.EnumerateLicensedUsers.

  Fields:
    licensedUsers: The list of licensed users.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  licensedUsers = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LicensedUser', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1Event(_messages.Message):
  r"""Consumer Procurement Order Event

  Enums:
    EventTypeValueValuesEnum: The type of action

  Fields:
    eventTime: The time when the event takes place
    eventType: The type of action
    name: Immutable. The resource name of the order event Format:
      `billingAccounts/{billing_account}/orders/{order}/events/{event}`
    offerId: The offer id corresponding to the event.
    userEmail: The email of the user taking the action. This field can be
      empty for users authenticated through 3P identity provider.
    userName: The name of the user taking the action. For users authenticated
      through 3P identity provider (BYOID), the field value format is
      described in go/byoid-data-pattern:displaying-users.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""The type of action

    Values:
      EVENT_TYPE_UNSPECIFIED: Default value, do not use.
      ORDER_PLACED: The action of accepting an offer.
      ORDER_CANCELLED: The action of cancelling an order.
      ORDER_MODIFIED: The action of modifying an order.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    ORDER_PLACED = 1
    ORDER_CANCELLED = 2
    ORDER_MODIFIED = 3

  eventTime = _messages.StringField(1)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 2)
  name = _messages.StringField(3)
  offerId = _messages.StringField(4)
  userEmail = _messages.StringField(5)
  userName = _messages.StringField(6)


class GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial(_messages.Message):
  r"""FreeTrial represents the free trial created for a specific offer and
  billing account with argentum. Free Trial resources are created by placing
  orders for 3p non-VM offers, or just enabling free trials for 1p offers and
  3p VM offers. Next Id: 7

  Fields:
    credit: Output only. Credit tracking the real time credit status.
    name: Output only. The resource name of the free trial item. This field is
      of the form: `projects/{project}/freeTrials/{free_trial}`. Present if
      free trial is created under the project's associated billing account for
      3p, or free trial is enabled for 1p product.
    productExternalName: External name for the product for which free trial
      exist. TODO(b/*********) Mark this field "output only" once the standard
      offer migration completes.
    provider: Provider of the products for which free trial exist. Provider
      has the format of `providers/{provider_id}`.
    service: The one platform service name associated with the free trial.
      Format: 'services/{service_name}'.
  """

  credit = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrialCredit', 1)
  name = _messages.StringField(2)
  productExternalName = _messages.StringField(3)
  provider = _messages.StringField(4)
  service = _messages.StringField(5)


class GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrialCredit(_messages.Message):
  r"""Credit represents the real time credit information.

  Fields:
    creationDate: Date credit was created.
    endTime: When the credit expires. If empty then there's no upper bound of
      credit's effective timespan (i.e. the credit never expires).
    remainingAmount: The amount of the credit remaining.
    startTime: When the credit becomes effective. If empty then there's no
      lower bound of credit's effective timespan (i.e. the credit becomes
      effective at the time of its creation). For credit creation, this cannot
      be in the past.
    value: The value of the credit.
  """

  creationDate = _messages.StringField(1)
  endTime = _messages.StringField(2)
  remainingAmount = _messages.MessageField('GoogleTypeMoney', 3)
  startTime = _messages.StringField(4)
  value = _messages.MessageField('GoogleTypeMoney', 5)


class GoogleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest(_messages.Message):
  r"""Request message to grant consent.

  Fields:
    consent: Required. A consent to be granted. Set only agreement_document
      field.
    validateOnly: Optional. Flag is used to dry run grant consent behavior for
      the VMs and K8s products. If set, returns empty consent if consent can
      be granted. If unset, grants consent if consent can be granted.
  """

  consent = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Consent', 1)
  validateOnly = _messages.BooleanField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool(_messages.Message):
  r"""A license pool represents a pool of licenses that can be assigned to
  users.

  Fields:
    availableLicenseCount: Output only. Licenses count that are available to
      be assigned.
    licenseAssignmentProtocol: Required. Assignment protocol for the license
      pool.
    name: Identifier. Format:
      `billingAccounts/{billing_account}/orders/{order}/licensePool`
    totalLicenseCount: Output only. Total number of licenses in the pool.
  """

  availableLicenseCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  licenseAssignmentProtocol = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AssignmentProtocol', 2)
  name = _messages.StringField(3)
  totalLicenseCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudCommerceConsumerProcurementV1alpha1LicensedUser(_messages.Message):
  r"""A licensed user.

  Fields:
    assignTime: Output only. Timestamp when the license was assigned.
    recentUsageTime: Output only. Timestamp when the license was recently
      used. This may not be the most recent usage time, and will be updated
      regularly (within 24 hours).
    username: Username. Format: `<EMAIL>`.
  """

  assignTime = _messages.StringField(1)
  recentUsageTime = _messages.StringField(2)
  username = _messages.StringField(3)


class GoogleCloudCommerceConsumerProcurementV1alpha1LineItem(_messages.Message):
  r"""A single item within an order.

  Fields:
    changeHistory: Output only. Changes made on the item that are not pending
      anymore which might be because they already took effect, were reverted
      by the customer, or were rejected by the partner. No more operations are
      allowed on these changes.
    lineItemId: Output only. Line item ID.
    lineItemInfo: Output only. Current state and information of this item. It
      tells what, e.g. which offer, is currently effective.
    pendingChange: Output only. A change made on the item which is pending and
      not yet effective. Absence of this field indicates the line item is not
      undergoing a change.
  """

  changeHistory = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemChange', 1, repeated=True)
  lineItemId = _messages.StringField(2)
  lineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 3)
  pendingChange = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemChange', 4)


class GoogleCloudCommerceConsumerProcurementV1alpha1LineItemChange(_messages.Message):
  r"""A change made on a line item.

  Enums:
    ChangeStateValueValuesEnum: Output only. State of the change.
    ChangeStateReasonTypeValueValuesEnum: Output only. Predefined enum types
      for why this line item change is in current state. For example, a line
      item change's state could be `LINE_ITEM_CHANGE_STATE_COMPLETED` because
      of end-of-term expiration, immediate cancellation initiated by the user,
      or system-initiated cancellation.
    ChangeTypeValueValuesEnum: Required. Type of the change to make.

  Fields:
    changeEffectiveTime: Output only. A time at which the change became or
      will become (in case of pending change) effective.
    changeId: Output only. Change ID. All changes made within one order update
      operation have the same change_id.
    changeState: Output only. State of the change.
    changeStateReasonType: Output only. Predefined enum types for why this
      line item change is in current state. For example, a line item change's
      state could be `LINE_ITEM_CHANGE_STATE_COMPLETED` because of end-of-term
      expiration, immediate cancellation initiated by the user, or system-
      initiated cancellation.
    changeType: Required. Type of the change to make.
    createTime: Output only. The time when change was initiated.
    newLineItemInfo: Line item info after the change.
    oldLineItemInfo: Output only. Line item info before the change.
    stateReason: Output only. Provider-supplied message explaining the
      LineItemChange's state. Mainly used to communicate progress and ETA for
      provisioning in the case of `PENDING_APPROVAL`, and to explain why the
      change request was denied or canceled in the case of `REJECTED` and
      `CANCELED` states.
    updateTime: Output only. The time when change was updated, e.g.
      approved/rejected by partners or cancelled by the user.
  """

  class ChangeStateReasonTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Predefined enum types for why this line item change is in
    current state. For example, a line item change's state could be
    `LINE_ITEM_CHANGE_STATE_COMPLETED` because of end-of-term expiration,
    immediate cancellation initiated by the user, or system-initiated
    cancellation.

    Values:
      LINE_ITEM_CHANGE_STATE_REASON_TYPE_UNSPECIFIED: Default value,
        indicating there's no predefined type for change state reason.
      LINE_ITEM_CHANGE_STATE_REASON_TYPE_EXPIRED: Change is in current state
        due to term expiration.
      LINE_ITEM_CHANGE_STATE_REASON_TYPE_USER_CANCELLED: Change is in current
        state due to user-initiated cancellation.
      LINE_ITEM_CHANGE_STATE_REASON_TYPE_SYSTEM_CANCELLED: Change is in
        current state due to system-initiated cancellation.
    """
    LINE_ITEM_CHANGE_STATE_REASON_TYPE_UNSPECIFIED = 0
    LINE_ITEM_CHANGE_STATE_REASON_TYPE_EXPIRED = 1
    LINE_ITEM_CHANGE_STATE_REASON_TYPE_USER_CANCELLED = 2
    LINE_ITEM_CHANGE_STATE_REASON_TYPE_SYSTEM_CANCELLED = 3

  class ChangeStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the change.

    Values:
      LINE_ITEM_CHANGE_STATE_UNSPECIFIED: Sentinel value. Do not use.
      LINE_ITEM_CHANGE_STATE_PENDING_APPROVAL: Change is in this state when a
        change is initiated and waiting for partner approval. This state is
        only applicable for pending change.
      LINE_ITEM_CHANGE_STATE_APPROVED: Change is in this state after it's
        approved by the partner or auto-approved but before it takes effect.
        The change can be overwritten or cancelled depending on the new line
        item info property (pending Private Offer change cannot be cancelled
        and can only be overwritten by another Private Offer). This state is
        only applicable for pending change.
      LINE_ITEM_CHANGE_STATE_COMPLETED: Change is in this state after it's
        been activated. This state is only applicable for change in history.
      LINE_ITEM_CHANGE_STATE_REJECTED: Change is in this state if it was
        rejected by the partner. This state is only applicable for change in
        history.
      LINE_ITEM_CHANGE_STATE_ABANDONED: Change is in this state if it was
        abandoned by the user. This state is only applicable for change in
        history.
      LINE_ITEM_CHANGE_STATE_ACTIVATING: Change is in this state if it's
        currently being provisioned downstream. The change can't be
        overwritten or cancelled when it's in this state. This state is only
        applicable for pending change.
    """
    LINE_ITEM_CHANGE_STATE_UNSPECIFIED = 0
    LINE_ITEM_CHANGE_STATE_PENDING_APPROVAL = 1
    LINE_ITEM_CHANGE_STATE_APPROVED = 2
    LINE_ITEM_CHANGE_STATE_COMPLETED = 3
    LINE_ITEM_CHANGE_STATE_REJECTED = 4
    LINE_ITEM_CHANGE_STATE_ABANDONED = 5
    LINE_ITEM_CHANGE_STATE_ACTIVATING = 6

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of the change to make.

    Values:
      LINE_ITEM_CHANGE_TYPE_UNSPECIFIED: Sentinel value. Do not use.
      LINE_ITEM_CHANGE_TYPE_CREATE: The change is to create a new line item.
      LINE_ITEM_CHANGE_TYPE_UPDATE: The change is to update an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_CANCEL: The change is to cancel an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION: The change is to revert a
        cancellation.
      LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL: The change is to disable free
        trial on this order.
    """
    LINE_ITEM_CHANGE_TYPE_UNSPECIFIED = 0
    LINE_ITEM_CHANGE_TYPE_CREATE = 1
    LINE_ITEM_CHANGE_TYPE_UPDATE = 2
    LINE_ITEM_CHANGE_TYPE_CANCEL = 3
    LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION = 4
    LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL = 5

  changeEffectiveTime = _messages.StringField(1)
  changeId = _messages.StringField(2)
  changeState = _messages.EnumField('ChangeStateValueValuesEnum', 3)
  changeStateReasonType = _messages.EnumField('ChangeStateReasonTypeValueValuesEnum', 4)
  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 5)
  createTime = _messages.StringField(6)
  newLineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 7)
  oldLineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 8)
  stateReason = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo(_messages.Message):
  r"""Line item information.

  Messages:
    SystemPropertiesValue: Output only. System provided key value pairs.

  Fields:
    addOnDetails: Output only. Add on information of a line item, if
      applicable.
    customPricing: Output only. The custom pricing information for this line
      item, if applicable.
    entitlementInfo: Output only. Entitlement info associated with this line
      item.
    flavorExternalName: External name of the flavor being purchased.
    offer: Optional. The name of the offer can have either of these formats:
      'billingAccounts/{billing_account}/offers/{offer}', or
      'services/{service}/standardOffers/{offer}'.
    parameters: Optional. User-provided parameters.
    productExternalName: External name of the product being purchased.
    quoteExternalName: Output only. External name of the quote this product is
      associated with. Present if the product is part of a Quote.
    subscription: Output only. Information about the subscription created, if
      applicable.
    systemProperties: Output only. System provided key value pairs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SystemPropertiesValue(_messages.Message):
    r"""Output only. System provided key value pairs.

    Messages:
      AdditionalProperty: An additional property for a SystemPropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SystemPropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SystemPropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addOnDetails = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1AddOnDetails', 1)
  customPricing = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1CustomPricing', 2)
  entitlementInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1EntitlementInfo', 3)
  flavorExternalName = _messages.StringField(4)
  offer = _messages.StringField(5)
  parameters = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Parameter', 6, repeated=True)
  productExternalName = _messages.StringField(7)
  quoteExternalName = _messages.StringField(8)
  subscription = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Subscription', 9)
  systemProperties = _messages.MessageField('SystemPropertiesValue', 10)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListAccountsResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.ListAccounts.

  Fields:
    accounts: The list of accounts in this response.
    nextPageToken: The token for fetching the next page.
  """

  accounts = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Account', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListConsentsResponse(_messages.Message):
  r"""Response message for the list consent request.

  Fields:
    consents: Consents matching the request.
    nextPageToken: Pagination token for large results.
  """

  consents = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Consent', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListEntitlementsResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.ListEntitlements.

  Fields:
    entitlements: The list of Entitlements in this response.
    nextPageToken: The token for fetching the next page.
  """

  entitlements = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListEventsResponse(_messages.Message):
  r"""Response to listing order events

  Fields:
    events: The list of events in this response.
    nextPageToken: The token for fetching the next page.
  """

  events = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Event', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListFreeTrialsResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.ListFreeTrials.

  Fields:
    freeTrials: The list of FreeTrialss in this response.
    nextPageToken: The token for fetching the next page.
  """

  freeTrials = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListOrderAttributionsResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.ListOrderAttributions.

  Fields:
    nextPageToken: The token for fetching the next page of entries.
    orderAttributions: The OrderAttributions from this response
  """

  nextPageToken = _messages.StringField(1)
  orderAttributions = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttribution', 2, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1ListOrdersResponse(_messages.Message):
  r"""Response message for ConsumerProcurementService.ListOrders.

  Fields:
    nextPageToken: The token for fetching the next page.
    orders: The list of orders in this response.
  """

  nextPageToken = _messages.StringField(1)
  orders = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1Order', 2, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.ModifyOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest(_messages.Message):
  r"""Request message for ConsumerProcurementService.ModifyOrder.

  Fields:
    displayName: Optional. Updated display name of the order, leave as empty
      if you do not want to update current display name.
    etag: Optional. The weak etag, which can be optionally populated, of the
      order that this modify request is based on. Validation checking will
      only happen if the invoker supplies this field.
    modifications: Optional. Modifications for an existing Order created by an
      Offer. Required when Offer based Order is being modified, except for
      when going from an offer to a public plan.
    modifyProductsOrderRequest: Optional. Modifies an existing non-quote
      order. Should only be used for offer-based orders when going from an
      offer to a public plan.
    modifyQuoteOrderRequest: Optional. Modifies an existing order for quote.
  """

  displayName = _messages.StringField(1)
  etag = _messages.StringField(2)
  modifications = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequestModification', 3, repeated=True)
  modifyProductsOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ModifyProductsOrderRequest', 4)
  modifyQuoteOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ModifyQuoteOrderRequest', 5)


class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequestModification(_messages.Message):
  r"""Modifications to make on the order.

  Enums:
    AutoRenewalBehaviorValueValuesEnum: Optional. Auto renewal behavior of the
      subscription for the update. Applied when change_type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE]. Follows plan default
      config when this field is not specified.
    ChangeTypeValueValuesEnum: Required. Type of change to make.

  Fields:
    autoRenewalBehavior: Optional. Auto renewal behavior of the subscription
      for the update. Applied when change_type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE]. Follows plan default
      config when this field is not specified.
    changeType: Required. Type of change to make.
    lineItemId: Required. ID of the existing line item to make change to.
      Required when change type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE] or
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CANCEL].
    newLineItemInfo: Optional. The line item to update to. Required when
      change_type is [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CREATE] or
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].
  """

  class AutoRenewalBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Auto renewal behavior of the subscription for the update.
    Applied when change_type is
    [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE]. Follows plan default
    config when this field is not specified.

    Values:
      AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED: If unspecified, the auto renewal
        behavior will follow the default config.
      AUTO_RENEWAL_BEHAVIOR_ENABLE: Auto Renewal will be enabled on
        subscription.
      AUTO_RENEWAL_BEHAVIOR_DISABLE: Auto Renewal will be disabled on
        subscription.
    """
    AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED = 0
    AUTO_RENEWAL_BEHAVIOR_ENABLE = 1
    AUTO_RENEWAL_BEHAVIOR_DISABLE = 2

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of change to make.

    Values:
      LINE_ITEM_CHANGE_TYPE_UNSPECIFIED: Sentinel value. Do not use.
      LINE_ITEM_CHANGE_TYPE_CREATE: The change is to create a new line item.
      LINE_ITEM_CHANGE_TYPE_UPDATE: The change is to update an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_CANCEL: The change is to cancel an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION: The change is to revert a
        cancellation.
      LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL: The change is to disable free
        trial on this order.
    """
    LINE_ITEM_CHANGE_TYPE_UNSPECIFIED = 0
    LINE_ITEM_CHANGE_TYPE_CREATE = 1
    LINE_ITEM_CHANGE_TYPE_UPDATE = 2
    LINE_ITEM_CHANGE_TYPE_CANCEL = 3
    LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION = 4
    LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL = 5

  autoRenewalBehavior = _messages.EnumField('AutoRenewalBehaviorValueValuesEnum', 1)
  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 2)
  lineItemId = _messages.StringField(3)
  newLineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 4)


class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyProductsOrderRequest(_messages.Message):
  r"""Request message to update an order for non-quote products.

  Fields:
    modifications: A GoogleCloudCommerceConsumerProcurementV1alpha1ModifyProdu
      ctsOrderRequestModification attribute.
  """

  modifications = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ModifyProductsOrderRequestModification', 1, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyProductsOrderRequestModification(_messages.Message):
  r"""Modifications to make on the order.

  Enums:
    AutoRenewalBehaviorValueValuesEnum: Auto renewal behavior of the
      subscription for the update. Applied when change_type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].
    ChangeTypeValueValuesEnum: Required. Type of change to make.

  Fields:
    autoRenewalBehavior: Auto renewal behavior of the subscription for the
      update. Applied when change_type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].
    changeType: Required. Type of change to make.
    lineItemId: ID of the existing line item to make change to. Required when
      change type is [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE] or
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CANCEL].
    newLineItemInfo: The line item to update to. Required when change_type is
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CREATE] or
      [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].
  """

  class AutoRenewalBehaviorValueValuesEnum(_messages.Enum):
    r"""Auto renewal behavior of the subscription for the update. Applied when
    change_type is [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].

    Values:
      AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED: If unspecified, the auto renewal
        behavior will follow the default config.
      AUTO_RENEWAL_BEHAVIOR_ENABLE: Auto Renewal will be enabled on
        subscription.
      AUTO_RENEWAL_BEHAVIOR_DISABLE: Auto Renewal will be disabled on
        subscription.
    """
    AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED = 0
    AUTO_RENEWAL_BEHAVIOR_ENABLE = 1
    AUTO_RENEWAL_BEHAVIOR_DISABLE = 2

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of change to make.

    Values:
      LINE_ITEM_CHANGE_TYPE_UNSPECIFIED: Sentinel value. Do not use.
      LINE_ITEM_CHANGE_TYPE_CREATE: The change is to create a new line item.
      LINE_ITEM_CHANGE_TYPE_UPDATE: The change is to update an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_CANCEL: The change is to cancel an existing line
        item.
      LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION: The change is to revert a
        cancellation.
      LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL: The change is to disable free
        trial on this order.
    """
    LINE_ITEM_CHANGE_TYPE_UNSPECIFIED = 0
    LINE_ITEM_CHANGE_TYPE_CREATE = 1
    LINE_ITEM_CHANGE_TYPE_UPDATE = 2
    LINE_ITEM_CHANGE_TYPE_CANCEL = 3
    LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION = 4
    LINE_ITEM_CHANGE_TYPE_DISABLE_FREE_TRIAL = 5

  autoRenewalBehavior = _messages.EnumField('AutoRenewalBehaviorValueValuesEnum', 1)
  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 2)
  lineItemId = _messages.StringField(3)
  newLineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 4)


class GoogleCloudCommerceConsumerProcurementV1alpha1ModifyQuoteOrderRequest(_messages.Message):
  r"""Request message for ConsumerProcurementService.ModifyOrder.

  Enums:
    AutoRenewalBehaviorValueValuesEnum: Auto renewal behavior of the
      subscription for the update. Applied when change_type is
      [QuoteChangeType.QUOTE_CHANGE_TYPE_UPDATE].
    ChangeTypeValueValuesEnum: Required. Type of change to make.

  Fields:
    autoRenewalBehavior: Auto renewal behavior of the subscription for the
      update. Applied when change_type is
      [QuoteChangeType.QUOTE_CHANGE_TYPE_UPDATE].
    changeType: Required. Type of change to make.
    newQuoteExternalName: External name of the new quote to update to.
      Required when change_type is [QuoteChangeType.QUOTE_CHANGE_TYPE_UPDATE].
  """

  class AutoRenewalBehaviorValueValuesEnum(_messages.Enum):
    r"""Auto renewal behavior of the subscription for the update. Applied when
    change_type is [QuoteChangeType.QUOTE_CHANGE_TYPE_UPDATE].

    Values:
      AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED: If unspecified, the auto renewal
        behavior will follow the default config.
      AUTO_RENEWAL_BEHAVIOR_ENABLE: Auto Renewal will be enabled on
        subscription.
      AUTO_RENEWAL_BEHAVIOR_DISABLE: Auto Renewal will be disabled on
        subscription.
    """
    AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED = 0
    AUTO_RENEWAL_BEHAVIOR_ENABLE = 1
    AUTO_RENEWAL_BEHAVIOR_DISABLE = 2

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of change to make.

    Values:
      QUOTE_CHANGE_TYPE_UNSPECIFIED: Sentinel value. Do not use.
      QUOTE_CHANGE_TYPE_UPDATE: The change is to update an existing order for
        quote.
      QUOTE_CHANGE_TYPE_CANCEL: The change is to cancel an existing order for
        quote.
      QUOTE_CHANGE_TYPE_REVERT_CANCELLATION: The change is to revert a
        cancellation for an order for quote.
    """
    QUOTE_CHANGE_TYPE_UNSPECIFIED = 0
    QUOTE_CHANGE_TYPE_UPDATE = 1
    QUOTE_CHANGE_TYPE_CANCEL = 2
    QUOTE_CHANGE_TYPE_REVERT_CANCELLATION = 3

  autoRenewalBehavior = _messages.EnumField('AutoRenewalBehaviorValueValuesEnum', 1)
  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 2)
  newQuoteExternalName = _messages.StringField(3)


class GoogleCloudCommerceConsumerProcurementV1alpha1Order(_messages.Message):
  r"""Represents a purchase made by a customer on Cloud Marketplace. Creating
  an order makes sure that both the Google backend systems as well as external
  service provider's systems (if needed) allow use of purchased products and
  ensures the appropriate billing events occur. An Order can be made against
  one Product with multiple add-ons (optional) or one Quote which might
  reference multiple products. Customers typically choose a price plan for
  each Product purchased when they create an order and can change their plan
  later, if the product allows.

  Enums:
    OrderStateValueValuesEnum: Output only. The state of the entire Order.
      This is different from the state of the line items in the Order. Orders
      are in the OrderState.ORDER_STATE_ACTIVE state when created, although
      line items may not be activated yet. Order state changes to
      OrderState.ORDER_STATE_PENDING_CANCELLATION after activation if the
      Order will be cancelled after the current term. The line item and its
      state might evolve independently, such as switching to another line
      item, without impacting the Order state until the entire Order is
      cancelled.

  Fields:
    account: The resource name of the account that this order is based on.
      Required if the creation of any products in the order requires an
      account to be present.
    cancelledLineItems: Output only. Line items that were cancelled.
    createTime: Output only. The creation timestamp.
    displayName: Required. The user-specified name of the order.
    etag: The weak etag of the order.
    lineItems: Output only. The items being purchased.
    name: Output only. The resource name of the order. Has the form
      `billingAccounts/{billing_account}/orders/{order}`.
    orderState: Output only. The state of the entire Order. This is different
      from the state of the line items in the Order. Orders are in the
      OrderState.ORDER_STATE_ACTIVE state when created, although line items
      may not be activated yet. Order state changes to
      OrderState.ORDER_STATE_PENDING_CANCELLATION after activation if the
      Order will be cancelled after the current term. The line item and its
      state might evolve independently, such as switching to another line
      item, without impacting the Order state until the entire Order is
      cancelled.
    provider: Provider of the products being purchased. Provider has the
      format of `providers/{provider}`.
    stateReason: Output only. An explanation for the order's state. Mainly
      used in the case of `OrderState.ORDER_STATE_CANCELLED` states to explain
      why the order is cancelled.
    updateTime: Output only. The last update timestamp.
  """

  class OrderStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the entire Order. This is different from the
    state of the line items in the Order. Orders are in the
    OrderState.ORDER_STATE_ACTIVE state when created, although line items may
    not be activated yet. Order state changes to
    OrderState.ORDER_STATE_PENDING_CANCELLATION after activation if the Order
    will be cancelled after the current term. The line item and its state
    might evolve independently, such as switching to another line item,
    without impacting the Order state until the entire Order is cancelled.

    Values:
      ORDER_STATE_UNSPECIFIED: Sentinel value. Do not use.
      ORDER_STATE_ACTIVE: The order is active.
      ORDER_STATE_CANCELLED: The order is cancelled.
      ORDER_STATE_PENDING_CANCELLATION: The order is being cancelled either by
        the user or by the system. The order stays in this state, if any
        product in the order allows use of the underlying resource until the
        end of the current billing cycle. Once the billing cycle completes,
        the resource will transition to OrderState.ORDER_STATE_CANCELLED
        state.
    """
    ORDER_STATE_UNSPECIFIED = 0
    ORDER_STATE_ACTIVE = 1
    ORDER_STATE_CANCELLED = 2
    ORDER_STATE_PENDING_CANCELLATION = 3

  account = _messages.StringField(1)
  cancelledLineItems = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItem', 2, repeated=True)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  lineItems = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItem', 6, repeated=True)
  name = _messages.StringField(7)
  orderState = _messages.EnumField('OrderStateValueValuesEnum', 8)
  provider = _messages.StringField(9)
  stateReason = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttribution(_messages.Message):
  r"""Determines how credits generated by this Order are assigned to specific
  targets (e.g. projects).

  Fields:
    allotments: The segments that define how total_attributable should be
      broken up. These are in priority order from highest to lowest. (The
      zero-indexed item has the highest priority.)
    intTotalAttributable: Output only. An integer amount of attributable
      resources.
    name: Output only. Resource name of the attribution configuration Format:
      billingAccounts/{billing_account}/orders/{order}/orderAttributions/{orde
      r_attribution} attribution_target references the Order parameter that
      defines the total attributable amount of this resource.
    unit: Output only. Human friendly name for what's being attributed
  """

  allotments = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttributionAllotment', 1, repeated=True)
  intTotalAttributable = _messages.IntegerField(2)
  name = _messages.StringField(3)
  unit = _messages.StringField(4)


class GoogleCloudCommerceConsumerProcurementV1alpha1OrderAttributionAllotment(_messages.Message):
  r"""Defines a specific chunk of credits that are assigned to specific
  targets.

  Fields:
    intAllotmentAmount: An integer allotment of resources.
    targets: Targets for this allotment. Both projects and folder names are
      supported. Targets should be associated with this billing account.
      Targets not associated with this billing account are ignored. Format:
      projects/{project_number} or folders/{folder_name}
  """

  intAllotmentAmount = _messages.IntegerField(1)
  targets = _messages.StringField(2, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1Parameter(_messages.Message):
  r"""User-provided Parameters.

  Fields:
    name: Name of the parameter.
    value: Value of parameter.
  """

  name = _messages.StringField(1)
  value = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1ParameterValue', 2)


class GoogleCloudCommerceConsumerProcurementV1alpha1ParameterValue(_messages.Message):
  r"""A GoogleCloudCommerceConsumerProcurementV1alpha1ParameterValue object.

  Fields:
    doubleValue: Represents a double value.
    int64Value: Represents an int64 value.
    stringValue: Represents a string value.
  """

  doubleValue = _messages.FloatField(1)
  int64Value = _messages.IntegerField(2)
  stringValue = _messages.StringField(3)


class GoogleCloudCommerceConsumerProcurementV1alpha1PlaceOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.PlaceOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest(_messages.Message):
  r"""Request message for ConsumerProcurementService.PlaceOrder.

  Enums:
    AutoRenewalBehaviorValueValuesEnum: Optional. Auto renewal behavior of the
      subscription associated with the order.

  Fields:
    account: The resource name of the account that this order is based on. If
      this field is not specified and the creation of any products in the
      order requires an account, system will look for existing account and
      auto create one if there is no existing one.
    autoRenewalBehavior: Optional. Auto renewal behavior of the subscription
      associated with the order.
    displayName: Required. The user-specified name of the order being placed.
    lineItemInfo: Optional. Places order for offer. Required when an offer-
      based order is being placed.
    placeProductsOrderRequest: Optional. Places order for non-quote products.
    placeQuoteOrderRequest: Optional. Places order for quote.
    provider: Required. Provider of the items being purchased. Provider has
      the format of `providers/{provider_id}`. Optional when an offer is
      specified. TODO(b/*********) Hide provider id in the consumer API.
    requestId: Optional. A unique identifier for this request. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 24 hours after the first request. The request ID must be a valid [
      UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format
      ).
    testConfig: Optional. Test configuration for the to-be-placed order.
      Placing test order is only allowed if the parent is a testing billing
      account for the service.
  """

  class AutoRenewalBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Auto renewal behavior of the subscription associated with
    the order.

    Values:
      AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED: If unspecified, the auto renewal
        behavior will follow the default config.
      AUTO_RENEWAL_BEHAVIOR_ENABLE: Auto Renewal will be enabled on
        subscription.
      AUTO_RENEWAL_BEHAVIOR_DISABLE: Auto Renewal will be disabled on
        subscription.
    """
    AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED = 0
    AUTO_RENEWAL_BEHAVIOR_ENABLE = 1
    AUTO_RENEWAL_BEHAVIOR_DISABLE = 2

  account = _messages.StringField(1)
  autoRenewalBehavior = _messages.EnumField('AutoRenewalBehaviorValueValuesEnum', 2)
  displayName = _messages.StringField(3)
  lineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 4, repeated=True)
  placeProductsOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1PlaceProductsOrderRequest', 5)
  placeQuoteOrderRequest = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1PlaceQuoteOrderRequest', 6)
  provider = _messages.StringField(7)
  requestId = _messages.StringField(8)
  testConfig = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1TestConfig', 9)


class GoogleCloudCommerceConsumerProcurementV1alpha1PlaceProductsOrderRequest(_messages.Message):
  r"""Request message to place an order for non-quote products.

  Fields:
    lineItemInfo: Required. Items to purchase within an order.
  """

  lineItemInfo = _messages.MessageField('GoogleCloudCommerceConsumerProcurementV1alpha1LineItemInfo', 1, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1PlaceQuoteOrderRequest(_messages.Message):
  r"""Request message to place an order for a quote.

  Fields:
    quoteExternalName: Required. External name of the quote to purchase.
  """

  quoteExternalName = _messages.StringField(1)


class GoogleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest(_messages.Message):
  r"""Request message to revoke a consent."""


class GoogleCloudCommerceConsumerProcurementV1alpha1Subscription(_messages.Message):
  r"""Subscription information.

  Fields:
    autoRenewalEnabled: Whether auto renewal is enabled by user choice on
      current subscription. This field indicates order/subscription status
      after pending plan change is cancelled or rejected.
    endTime: The timestamp when the subscription ends, if applicable.
    startTime: The timestamp when the subscription begins, if applicable.
  """

  autoRenewalEnabled = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  startTime = _messages.StringField(3)


class GoogleCloudCommerceConsumerProcurementV1alpha1TestConfig(_messages.Message):
  r"""The test configuration for the resource.

  Fields:
    isTesting: Whether the resource is for testing or not.
  """

  isTesting = _messages.BooleanField(1)


class GoogleCloudCommerceConsumerProcurementV1alpha1UnassignRequest(_messages.Message):
  r"""Request message for LicenseManagementService.Unassign.

  Fields:
    usernames: Required. Username. Format: `<EMAIL>`.
  """

  usernames = _messages.StringField(1, repeated=True)


class GoogleCloudCommerceConsumerProcurementV1alpha1UnassignResponse(_messages.Message):
  r"""Response message for LicenseManagementService.Unassign."""


class GoogleCloudCommerceConsumerProcurementV1alpha1UpdateOrderAttributionMetadata(_messages.Message):
  r"""Metadata for a long-running operation initiated by
  ConsumerProcurementService.UpdateOrderAttribution.
  """



class GoogleCloudCommerceConsumerProcurementV1mainCancelOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CancelOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1mainCreateAccountMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CreateAccount.
  """



class GoogleCloudCommerceConsumerProcurementV1mainCreateFreeTrialMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.CreateFreeTrial.
  """



class GoogleCloudCommerceConsumerProcurementV1mainDeleteAccountMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.DeleteAccount.
  """



class GoogleCloudCommerceConsumerProcurementV1mainModifyOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.ModifyOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1mainPlaceOrderMetadata(_messages.Message):
  r"""Message stored in the metadata field of the Operation returned by
  ConsumerProcurementService.PlaceOrder.
  """



class GoogleCloudCommerceConsumerProcurementV1mainUpdateOrderAttributionMetadata(_messages.Message):
  r"""Metadata for a long-running operation initiated by
  ConsumerProcurementService.UpdateOrderAttribution.
  """



class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeMoney(_messages.Message):
  r"""Represents an amount of money with its currency type.

  Fields:
    currencyCode: The three-letter currency code defined in ISO 4217.
    nanos: Number of nano (10^-9) units of the amount. The value must be
      between -999,999,999 and +999,999,999 inclusive. If `units` is positive,
      `nanos` must be positive or zero. If `units` is zero, `nanos` can be
      positive, zero, or negative. If `units` is negative, `nanos` must be
      negative or zero. For example $-1.75 is represented as `units`=-1 and
      `nanos`=-750,000,000.
    units: The whole units of the amount. For example if `currencyCode` is
      `"USD"`, then 1 unit is one US dollar.
  """

  currencyCode = _messages.StringField(1)
  nanos = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  units = _messages.IntegerField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
