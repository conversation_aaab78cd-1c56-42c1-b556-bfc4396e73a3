"""Generated client library for cloudcommerceconsumerprocurement version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudcommerceconsumerprocurement.v1alpha1 import cloudcommerceconsumerprocurement_v1alpha1_messages as messages


class CloudcommerceconsumerprocurementV1alpha1(base_api.BaseApiClient):
  """Generated client library for service cloudcommerceconsumerprocurement version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudcommerceconsumerprocurement.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudcommerceconsumerprocurement.mtls.googleapis.com/'

  _PACKAGE = 'cloudcommerceconsumerprocurement'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudcommerceconsumerprocurementV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudcommerceconsumerprocurement handle."""
    url = url or self.BASE_URL
    super(CloudcommerceconsumerprocurementV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.billingAccounts_accounts = self.BillingAccountsAccountsService(self)
    self.billingAccounts_consents = self.BillingAccountsConsentsService(self)
    self.billingAccounts_orders_events = self.BillingAccountsOrdersEventsService(self)
    self.billingAccounts_orders_licensePool = self.BillingAccountsOrdersLicensePoolService(self)
    self.billingAccounts_orders_operations = self.BillingAccountsOrdersOperationsService(self)
    self.billingAccounts_orders_orderAttributions_operations = self.BillingAccountsOrdersOrderAttributionsOperationsService(self)
    self.billingAccounts_orders_orderAttributions = self.BillingAccountsOrdersOrderAttributionsService(self)
    self.billingAccounts_orders = self.BillingAccountsOrdersService(self)
    self.billingAccounts = self.BillingAccountsService(self)
    self.projects_consents = self.ProjectsConsentsService(self)
    self.projects_entitlements = self.ProjectsEntitlementsService(self)
    self.projects_freeTrials = self.ProjectsFreeTrialsService(self)
    self.projects = self.ProjectsService(self)

  class BillingAccountsAccountsService(base_api.BaseApiService):
    """Service class for the billingAccounts_accounts resource."""

    _NAME = 'billingAccounts_accounts'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsAccountsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Account.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsAccountsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/accounts',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.accounts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/accounts',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1Account',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsAccountsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing Account. An account can only be deleted when there are no orders associated with that account.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsAccountsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/accounts/{accountsId}',
        http_method='DELETE',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.accounts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsAccountsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the requested Account resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsAccountsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Account) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/accounts/{accountsId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.accounts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsAccountsGetRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Account',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Account resources that the user has access to, within the scope of the parent resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsAccountsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListAccountsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/accounts',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.accounts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/accounts',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsAccountsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListAccountsResponse',
        supports_download=False,
    )

  class BillingAccountsConsentsService(base_api.BaseApiService):
    """Service class for the billingAccounts_consents resource."""

    _NAME = 'billingAccounts_consents'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsConsentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Check(self, request, global_params=None):
      r"""Checks if a customer's consents satisfy the current agreement.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsConsentsCheckRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentResponse) The response message.
      """
      config = self.GetMethodConfig('Check')
      return self._RunMethod(
          config, request, global_params=global_params)

    Check.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/consents:check',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.consents.check',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/consents:check',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsConsentsCheckRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentResponse',
        supports_download=False,
    )

    def Grant(self, request, global_params=None):
      r"""Grants consent.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsConsentsGrantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Consent) The response message.
      """
      config = self.GetMethodConfig('Grant')
      return self._RunMethod(
          config, request, global_params=global_params)

    Grant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/consents:grant',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.consents.grant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/consents:grant',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsConsentsGrantRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Consent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists current consents.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsConsentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/consents',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.consents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['agreement', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/consents',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsConsentsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListConsentsResponse',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revokes a consent. Revocation is only allowed on a revokable agreement with a current Consent.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsConsentsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Consent) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/consents/{consentsId}:revoke',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.consents.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:revoke',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsConsentsRevokeRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Consent',
        supports_download=False,
    )

  class BillingAccountsOrdersEventsService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders_events resource."""

    _NAME = 'billingAccounts_orders_events'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersEventsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of events associated with an order.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersEventsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListEventsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/events',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.events.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/events',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersEventsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListEventsResponse',
        supports_download=False,
    )

  class BillingAccountsOrdersLicensePoolService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders_licensePool resource."""

    _NAME = 'billingAccounts_orders_licensePool'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersLicensePoolService, self).__init__(client)
      self._upload_configs = {
          }

    def Assign(self, request, global_params=None):
      r"""Assigns a license to a user.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolAssignRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1AssignResponse) The response message.
      """
      config = self.GetMethodConfig('Assign')
      return self._RunMethod(
          config, request, global_params=global_params)

    Assign.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/licensePool:assign',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.licensePool.assign',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}:assign',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1AssignRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolAssignRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1AssignResponse',
        supports_download=False,
    )

    def EnumerateLicensedUsers(self, request, global_params=None):
      r"""Enumerates all users assigned a license.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolEnumerateLicensedUsersRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1EnumerateLicensedUsersResponse) The response message.
      """
      config = self.GetMethodConfig('EnumerateLicensedUsers')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnumerateLicensedUsers.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/licensePool:enumerateLicensedUsers',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.licensePool.enumerateLicensedUsers',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}:enumerateLicensedUsers',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolEnumerateLicensedUsersRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1EnumerateLicensedUsersResponse',
        supports_download=False,
    )

    def Unassign(self, request, global_params=None):
      r"""Unassigns a license from a user.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolUnassignRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1UnassignResponse) The response message.
      """
      config = self.GetMethodConfig('Unassign')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unassign.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/licensePool:unassign',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.licensePool.unassign',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}:unassign',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1UnassignRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersLicensePoolUnassignRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1UnassignResponse',
        supports_download=False,
    )

  class BillingAccountsOrdersOperationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders_operations resource."""

    _NAME = 'billingAccounts_orders_operations'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/operations/{operationsId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class BillingAccountsOrdersOrderAttributionsOperationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders_orderAttributions_operations resource."""

    _NAME = 'billingAccounts_orders_orderAttributions_operations'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersOrderAttributionsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/orderAttributions/{orderAttributionsId}/operations/{operationsId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.orderAttributions.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class BillingAccountsOrdersOrderAttributionsService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders_orderAttributions resource."""

    _NAME = 'billingAccounts_orders_orderAttributions'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersOrderAttributionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all OrderAttribution of the parent [Order].

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListOrderAttributionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/orderAttributions',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.orderAttributions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/orderAttributions',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListOrderAttributionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified OrderAttribution resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/orderAttributions/{orderAttributionsId}',
        http_method='PATCH',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.orderAttributions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1OrderAttribution',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersOrderAttributionsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class BillingAccountsOrdersService(base_api.BaseApiService):
    """Service class for the billingAccounts_orders resource."""

    _NAME = 'billingAccounts_orders'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsOrdersService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels an existing Order. Every product procured in the Order will be cancelled.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}:cancel',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1CancelOrderRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersCancelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested Order resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Order) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersGetRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Order',
        supports_download=False,
    )

    def GetAuditLog(self, request, global_params=None):
      r"""Returns the requested AuditLog resource. To be deprecated.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersGetAuditLogRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1AuditLog) The response message.
      """
      config = self.GetMethodConfig('GetAuditLog')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAuditLog.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/auditLog',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.getAuditLog',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersGetAuditLogRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1AuditLog',
        supports_download=False,
    )

    def GetLicensePool(self, request, global_params=None):
      r"""Gets the license pool.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersGetLicensePoolRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool) The response message.
      """
      config = self.GetMethodConfig('GetLicensePool')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetLicensePool.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/licensePool',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.getLicensePool',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersGetLicensePoolRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Order resources that the user has access to, within the scope of the parent resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListOrdersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/orders',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListOrdersResponse',
        supports_download=False,
    )

    def Modify(self, request, global_params=None):
      r"""Modifies an existing Order resource.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersModifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Modify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Modify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}:modify',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.modify',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:modify',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1ModifyOrderRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersModifyRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Place(self, request, global_params=None):
      r"""Creates a new Order. This API only supports GCP spend-based committed use discounts specified by GCP documentation. The returned long-running operation is in-progress until the backend completes the creation of the resource. Once completed, the order is in OrderState.ORDER_STATE_ACTIVE. In case of failure, the order resource will be removed.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersPlaceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Place')
      return self._RunMethod(
          config, request, global_params=global_params)

    Place.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders:place',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.place',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/orders:place',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1PlaceOrderRequest',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersPlaceRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def UpdateLicensePool(self, request, global_params=None):
      r"""Updates the license pool if one exists for this Order.

      Args:
        request: (CloudcommerceconsumerprocurementBillingAccountsOrdersUpdateLicensePoolRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool) The response message.
      """
      config = self.GetMethodConfig('UpdateLicensePool')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateLicensePool.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/billingAccounts/{billingAccountsId}/orders/{ordersId}/licensePool',
        http_method='PATCH',
        method_id='cloudcommerceconsumerprocurement.billingAccounts.orders.updateLicensePool',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1LicensePool',
        request_type_name='CloudcommerceconsumerprocurementBillingAccountsOrdersUpdateLicensePoolRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1LicensePool',
        supports_download=False,
    )

  class BillingAccountsService(base_api.BaseApiService):
    """Service class for the billingAccounts resource."""

    _NAME = 'billingAccounts'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.BillingAccountsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsConsentsService(base_api.BaseApiService):
    """Service class for the projects_consents resource."""

    _NAME = 'projects_consents'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.ProjectsConsentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Check(self, request, global_params=None):
      r"""Checks if a customer's consents satisfy the current agreement.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsConsentsCheckRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentResponse) The response message.
      """
      config = self.GetMethodConfig('Check')
      return self._RunMethod(
          config, request, global_params=global_params)

    Check.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/consents:check',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.projects.consents.check',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/consents:check',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1CheckConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementProjectsConsentsCheckRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1CheckConsentResponse',
        supports_download=False,
    )

    def Grant(self, request, global_params=None):
      r"""Grants consent.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsConsentsGrantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Consent) The response message.
      """
      config = self.GetMethodConfig('Grant')
      return self._RunMethod(
          config, request, global_params=global_params)

    Grant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/consents:grant',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.projects.consents.grant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/consents:grant',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1GrantConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementProjectsConsentsGrantRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Consent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists current consents.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsConsentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/consents',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.consents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['agreement', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/consents',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsConsentsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListConsentsResponse',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revokes a consent. Revocation is only allowed on a revokable agreement with a current Consent.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsConsentsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Consent) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/consents/{consentsId}:revoke',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.projects.consents.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:revoke',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1RevokeConsentRequest',
        request_type_name='CloudcommerceconsumerprocurementProjectsConsentsRevokeRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Consent',
        supports_download=False,
    )

  class ProjectsEntitlementsService(base_api.BaseApiService):
    """Service class for the projects_entitlements resource."""

    _NAME = 'projects_entitlements'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.ProjectsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested Entitlement resource.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsEntitlementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/entitlements/{entitlementsId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.entitlements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsEntitlementsGetRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1Entitlement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Entitlement resources that the user has access to, within the scope of the parent resource. This includes all Entitlements that are either parented by a billing account associated with the parent (project) and or the project is a consumer of an Order.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/entitlements',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/entitlements',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsEntitlementsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListEntitlementsResponse',
        supports_download=False,
    )

  class ProjectsFreeTrialsService(base_api.BaseApiService):
    """Service class for the projects_freeTrials resource."""

    _NAME = 'projects_freeTrials'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.ProjectsFreeTrialsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FreeTrial.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsFreeTrialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/freeTrials',
        http_method='POST',
        method_id='cloudcommerceconsumerprocurement.projects.freeTrials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/freeTrials',
        request_field='googleCloudCommerceConsumerProcurementV1alpha1FreeTrial',
        request_type_name='CloudcommerceconsumerprocurementProjectsFreeTrialsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the requested FreeTrial resource.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsFreeTrialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/freeTrials/{freeTrialsId}',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.freeTrials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsFreeTrialsGetRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1FreeTrial',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FreeTrial resources that the user has access to, within the scope of the parent resource.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsFreeTrialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1ListFreeTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/freeTrials',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.freeTrials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/freeTrials',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsFreeTrialsListRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1ListFreeTrialsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(CloudcommerceconsumerprocurementV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckEntitlements(self, request, global_params=None):
      r"""Returns all active entitlements based on project and service type in its request.

      Args:
        request: (CloudcommerceconsumerprocurementProjectsCheckEntitlementsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudCommerceConsumerProcurementV1alpha1CheckEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('CheckEntitlements')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckEntitlements.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}:checkEntitlements',
        http_method='GET',
        method_id='cloudcommerceconsumerprocurement.projects.checkEntitlements',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['service'],
        relative_path='v1alpha1/{+parent}:checkEntitlements',
        request_field='',
        request_type_name='CloudcommerceconsumerprocurementProjectsCheckEntitlementsRequest',
        response_type_name='GoogleCloudCommerceConsumerProcurementV1alpha1CheckEntitlementsResponse',
        supports_download=False,
    )
