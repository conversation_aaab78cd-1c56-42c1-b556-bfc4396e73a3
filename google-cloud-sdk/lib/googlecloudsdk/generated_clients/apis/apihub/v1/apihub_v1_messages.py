"""Generated message classes for apihub version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'apihub'


class ApihubProjectsLocationsApiHubInstancesCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApiHubInstancesCreateRequest object.

  Fields:
    apiHubInstanceId: Optional. Identifier to assign to the Api Hub instance.
      Must be unique within scope of the parent resource. If the field is not
      provided, system generated id will be used. This value should be 4-40
      characters, and valid characters are `/a-z[0-9]-_/`.
    googleCloudApihubV1ApiHubInstance: A GoogleCloudApihubV1ApiHubInstance
      resource to be passed as the request body.
    parent: Required. The parent resource for the Api Hub instance resource.
      Format: `projects/{project}/locations/{location}`
  """

  apiHubInstanceId = _messages.StringField(1)
  googleCloudApihubV1ApiHubInstance = _messages.MessageField('GoogleCloudApihubV1ApiHubInstance', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsApiHubInstancesDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApiHubInstancesDeleteRequest object.

  Fields:
    name: Required. The name of the Api Hub instance to delete. Format: `proje
      cts/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApiHubInstancesGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApiHubInstancesGetRequest object.

  Fields:
    name: Required. The name of the Api Hub instance to retrieve. Format: `pro
      jects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApiHubInstancesLookupRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApiHubInstancesLookupRequest object.

  Fields:
    parent: Required. There will always be only one Api Hub instance for a GCP
      project across all locations. The parent resource for the Api Hub
      instance resource. Format: `projects/{project}/locations/{location}`
  """

  parent = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisCreateRequest object.

  Fields:
    apiId: Optional. The ID to use for the API resource, which will become the
      final component of the API's resource name. This field is optional. * If
      provided, the same will be used. The service will throw an error if the
      specified id is already used by another API resource in the API hub. *
      If not provided, a system generated id will be used. This value should
      be 4-500 characters, and valid characters are /a-z[0-9]-_/.
    googleCloudApihubV1Api: A GoogleCloudApihubV1Api resource to be passed as
      the request body.
    parent: Required. The parent resource for the API resource. Format:
      `projects/{project}/locations/{location}`
  """

  apiId = _messages.StringField(1)
  googleCloudApihubV1Api = _messages.MessageField('GoogleCloudApihubV1Api', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsApisDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisDeleteRequest object.

  Fields:
    force: Optional. If set to true, any versions from this API will also be
      deleted. Otherwise, the request will only work if the API has no
      versions.
    name: Required. The name of the API resource to delete. Format:
      `projects/{project}/locations/{location}/apis/{api}`
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsApisGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisGetRequest object.

  Fields:
    name: Required. The name of the API resource to retrieve. Format:
      `projects/{project}/locations/{location}/apis/{api}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of ApiResources. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string. The comparison operator
      must be one of: `<`, `>`, `:` or `=`. Filters are not case sensitive.
      The following fields in the `ApiResource` are eligible for filtering: *
      `owner.email` - The email of the team which owns the ApiResource.
      Allowed comparison operators: `=`. * `create_time` - The time at which
      the ApiResource was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `display_name` - The display name
      of the ApiResource. Allowed comparison operators: `=`. *
      `target_user.enum_values.values.id` - The allowed value id of the target
      users attribute associated with the ApiResource. Allowed comparison
      operator is `:`. * `target_user.enum_values.values.display_name` - The
      allowed value display name of the target users attribute associated with
      the ApiResource. Allowed comparison operator is `:`. *
      `team.enum_values.values.id` - The allowed value id of the team
      attribute associated with the ApiResource. Allowed comparison operator
      is `:`. * `team.enum_values.values.display_name` - The allowed value
      display name of the team attribute associated with the ApiResource.
      Allowed comparison operator is `:`. *
      `business_unit.enum_values.values.id` - The allowed value id of the
      business unit attribute associated with the ApiResource. Allowed
      comparison operator is `:`. *
      `business_unit.enum_values.values.display_name` - The allowed value
      display name of the business unit attribute associated with the
      ApiResource. Allowed comparison operator is `:`. *
      `maturity_level.enum_values.values.id` - The allowed value id of the
      maturity level attribute associated with the ApiResource. Allowed
      comparison operator is `:`. *
      `maturity_level.enum_values.values.display_name` - The allowed value
      display name of the maturity level attribute associated with the
      ApiResource. Allowed comparison operator is `:`. *
      `api_style.enum_values.values.id` - The allowed value id of the api
      style attribute associated with the ApiResource. Allowed comparison
      operator is `:`. * `api_style.enum_values.values.display_name` - The
      allowed value display name of the api style attribute associated with
      the ApiResource. Allowed comparison operator is `:`. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.enum_values.values.id` - The
      allowed value id of the user defined enum attribute associated with the
      Resource. Allowed comparison operator is `:`. Here user-defined-
      attribute-enum-id is a placeholder that can be replaced with any user
      defined enum attribute name. * `attributes.projects/test-project-
      id/locations/test-location-id/ attributes/user-defined-attribute-
      id.enum_values.values.display_name` - The allowed value display name of
      the user defined enum attribute associated with the Resource. Allowed
      comparison operator is `:`. Here user-defined-attribute-enum-display-
      name is a placeholder that can be replaced with any user defined enum
      attribute enum name. * `attributes.projects/test-project-
      id/locations/test-location-id/ attributes/user-defined-attribute-
      id.string_values.values` - The allowed value of the user defined string
      attribute associated with the Resource. Allowed comparison operator is
      `:`. Here user-defined-attribute-string is a placeholder that can be
      replaced with any user defined string attribute name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.json_values.values` - The allowed
      value of the user defined JSON attribute associated with the Resource.
      Allowed comparison operator is `:`. Here user-defined-attribute-json is
      a placeholder that can be replaced with any user defined JSON attribute
      name. Expressions are combined with either `AND` logic operator or `OR`
      logical operator but not both of them together i.e. only one of the
      `AND` or `OR` operator can be used throughout the filter string and both
      the operators cannot be used together. No other logical operators are
      supported. At most three filter fields are allowed in the filter string
      and if provided more than that then `INVALID_ARGUMENT` error is returned
      by the API. Here are a few examples: * `owner.email =
      \"<EMAIL>\"` - - The owner team <NAME_EMAIL>_.
      * `owner.email = \"<EMAIL>\" AND create_time <
      \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
      The owner team <NAME_EMAIL>_ and the api was created
      before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. *
      `owner.email = \"<EMAIL>\" OR team.enum_values.values.id:
      apihub-team-id` - The filter string specifies the APIs where the owner
      team <NAME_EMAIL>_ or the id of the allowed value
      associated with the team attribute is _apihub-team-id_. * `owner.email =
      \"<EMAIL>\" OR team.enum_values.values.display_name: ApiHub
      Team` - The filter string specifies the APIs where the owner team email
      is _apihub@google.com_ or the display name of the allowed value
      associated with the team attribute is `ApiHub Team`. * `owner.email =
      \"<EMAIL>\" AND attributes.projects/test-project-
      id/locations/test-location-id/
      attributes/17650f90-4a29-4971-b3c0-d5532da3764b.enum_values.values.id:
      test_enum_id AND attributes.projects/test-project-id/locations/test-
      location-id/
      attributes/1765\0f90-4a29-5431-b3d0-d5532da3764c.string_values.values:
      test_string_value` - The filter string specifies the APIs where the
      owner team <NAME_EMAIL>_ and the id of the allowed value
      associated with the user defined attribute of type enum is
      _test_enum_id_ and the value of the user defined attribute of type
      string is _test_..
    pageSize: Optional. The maximum number of API resources to return. The
      service may return fewer than this value. If unspecified, at most 50
      Apis will be returned. The maximum value is 1000; values above 1000 will
      be coerced to 1000.
    pageToken: Optional. A page token, received from a previous `ListApis`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters (except page_size) provided to `ListApis` must match
      the call that provided the page token.
    parent: Required. The parent, which owns this collection of API resources.
      Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsApisPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisPatchRequest object.

  Fields:
    googleCloudApihubV1Api: A GoogleCloudApihubV1Api resource to be passed as
      the request body.
    name: Identifier. The name of the API resource in the API Hub. Format:
      `projects/{project}/locations/{location}/apis/{api}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Api = _messages.MessageField('GoogleCloudApihubV1Api', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsApisVersionsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsCreateRequest object.

  Fields:
    googleCloudApihubV1Version: A GoogleCloudApihubV1Version resource to be
      passed as the request body.
    parent: Required. The parent resource for API version. Format:
      `projects/{project}/locations/{location}/apis/{api}`
    versionId: Optional. The ID to use for the API version, which will become
      the final component of the version's resource name. This field is
      optional. * If provided, the same will be used. The service will throw
      an error if the specified id is already used by another version in the
      API resource. * If not provided, a system generated id will be used.
      This value should be 4-500 characters, overall resource name which will
      be of format
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`,
      its length is limited to 700 characters and valid characters are
      /a-z[0-9]-_/.
  """

  googleCloudApihubV1Version = _messages.MessageField('GoogleCloudApihubV1Version', 1)
  parent = _messages.StringField(2, required=True)
  versionId = _messages.StringField(3)


class ApihubProjectsLocationsApisVersionsDefinitionsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsDefinitionsGetRequest object.

  Fields:
    name: Required. The name of the definition to retrieve. Format: `projects/
      {project}/locations/{location}/apis/{api}/versions/{version}/definitions
      /{definition}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsDeleteRequest object.

  Fields:
    force: Optional. If set to true, any specs from this version will also be
      deleted. Otherwise, the request will only work if the version has no
      specs.
    name: Required. The name of the version to delete. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsApisVersionsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsGetRequest object.

  Fields:
    name: Required. The name of the API version to retrieve. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of Versions. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string, a number, or a boolean.
      The comparison operator must be one of: `<`, `>` or `=`. Filters are not
      case sensitive. The following fields in the `Version` are eligible for
      filtering: * `display_name` - The display name of the Version. Allowed
      comparison operators: `=`. * `create_time` - The time at which the
      Version was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `lifecycle.enum_values.values.id` -
      The allowed value id of the lifecycle attribute associated with the
      Version. Allowed comparison operators: `:`. *
      `lifecycle.enum_values.values.display_name` - The allowed value display
      name of the lifecycle attribute associated with the Version. Allowed
      comparison operators: `:`. * `compliance.enum_values.values.id` - The
      allowed value id of the compliances attribute associated with the
      Version. Allowed comparison operators: `:`. *
      `compliance.enum_values.values.display_name` - The allowed value display
      name of the compliances attribute associated with the Version. Allowed
      comparison operators: `:`. * `accreditation.enum_values.values.id` - The
      allowed value id of the accreditations attribute associated with the
      Version. Allowed comparison operators: `:`. *
      `accreditation.enum_values.values.display_name` - The allowed value
      display name of the accreditations attribute associated with the
      Version. Allowed comparison operators: `:`. * `attributes.projects/test-
      project-id/locations/test-location-id/ attributes/user-defined-
      attribute-id.enum_values.values.id` - The allowed value id of the user
      defined enum attribute associated with the Resource. Allowed comparison
      operator is `:`. Here user-defined-attribute-enum-id is a placeholder
      that can be replaced with any user defined enum attribute name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.enum_values.values.display_name` -
      The allowed value display name of the user defined enum attribute
      associated with the Resource. Allowed comparison operator is `:`. Here
      user-defined-attribute-enum-display-name is a placeholder that can be
      replaced with any user defined enum attribute enum name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.string_values.values` - The allowed
      value of the user defined string attribute associated with the Resource.
      Allowed comparison operator is `:`. Here user-defined-attribute-string
      is a placeholder that can be replaced with any user defined string
      attribute name. * `attributes.projects/test-project-id/locations/test-
      location-id/ attributes/user-defined-attribute-id.json_values.values` -
      The allowed value of the user defined JSON attribute associated with the
      Resource. Allowed comparison operator is `:`. Here user-defined-
      attribute-json is a placeholder that can be replaced with any user
      defined JSON attribute name. Expressions are combined with either `AND`
      logic operator or `OR` logical operator but not both of them together
      i.e. only one of the `AND` or `OR` operator can be used throughout the
      filter string and both the operators cannot be used together. No other
      logical operators are supported. At most three filter fields are allowed
      in the filter string and if provided more than that then
      `INVALID_ARGUMENT` error is returned by the API. Here are a few
      examples: * `lifecycle.enum_values.values.id: preview-id` - The filter
      string specifies that the id of the allowed value associated with the
      lifecycle attribute of the Version is _preview-id_. *
      `lifecycle.enum_values.values.display_name: \"Preview Display Name\"` -
      The filter string specifies that the display name of the allowed value
      associated with the lifecycle attribute of the Version is `Preview
      Display Name`. * `lifecycle.enum_values.values.id: preview-id AND
      create_time < \"2021-08-15T14:50:00Z\" AND create_time >
      \"2021-08-10T12:00:00Z\"` - The id of the allowed value associated with
      the lifecycle attribute of the Version is _preview-id_ and it was
      created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00
      UTC_. * `compliance.enum_values.values.id: gdpr-id OR
      compliance.enum_values.values.id: pci-dss-id` - The id of the allowed
      value associated with the compliance attribute is _gdpr-id_ or _pci-dss-
      id_. * `lifecycle.enum_values.values.id: preview-id AND
      attributes.projects/test-project-id/locations/test-location-id/
      attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values:
      test` - The filter string specifies that the id of the allowed value
      associated with the lifecycle attribute of the Version is _preview-id_
      and the value of the user defined attribute of type string is _test_.
    pageSize: Optional. The maximum number of versions to return. The service
      may return fewer than this value. If unspecified, at most 50 versions
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A page token, received from a previous `ListVersions`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters (except page_size) provided to `ListVersions` must
      match the call that provided the page token.
    parent: Required. The parent which owns this collection of API versions
      i.e., the API resource Format:
      `projects/{project}/locations/{location}/apis/{api}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsApisVersionsOperationsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsOperationsCreateRequest object.

  Fields:
    apiOperationId: Optional. The ID to use for the operation resource, which
      will become the final component of the operation's resource name. This
      field is optional. * If provided, the same will be used. The service
      will throw an error if the specified id is already used by another
      operation resource in the API hub. * If not provided, a system generated
      id will be used. This value should be 4-500 characters, overall resource
      name which will be of format `projects/{project}/locations/{location}/ap
      is/{api}/versions/{version}/operations/{operation}`, its length is
      limited to 700 characters, and valid characters are /a-z[0-9]-_/.
    googleCloudApihubV1ApiOperation: A GoogleCloudApihubV1ApiOperation
      resource to be passed as the request body.
    parent: Required. The parent resource for the operation resource. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  apiOperationId = _messages.StringField(1)
  googleCloudApihubV1ApiOperation = _messages.MessageField('GoogleCloudApihubV1ApiOperation', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsApisVersionsOperationsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsOperationsDeleteRequest object.

  Fields:
    name: Required. The name of the operation resource to delete. Format: `pro
      jects/{project}/locations/{location}/apis/{api}/versions/{version}/opera
      tions/{operation}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsOperationsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsOperationsGetRequest object.

  Fields:
    name: Required. The name of the operation to retrieve. Format: `projects/{
      project}/locations/{location}/apis/{api}/versions/{version}/operations/{
      operation}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsOperationsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsOperationsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of ApiOperations. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string or a boolean. The
      comparison operator must be one of: `<`, `>` or `=`. Filters are not
      case sensitive. The following fields in the `ApiOperation` are eligible
      for filtering: * `name` - The ApiOperation resource name. Allowed
      comparison operators: `=`. * `details.http_operation.path.path` - The
      http operation's complete path relative to server endpoint. Allowed
      comparison operators: `=`. * `details.http_operation.method` - The http
      operation method type. Allowed comparison operators: `=`. *
      `details.deprecated` - Indicates if the ApiOperation is deprecated.
      Allowed values are True / False indicating the deprycation status of the
      ApiOperation. Allowed comparison operators: `=`. * `create_time` - The
      time at which the ApiOperation was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `attributes.projects/test-project-
      id/locations/test-location-id/ attributes/user-defined-attribute-
      id.enum_values.values.id` - The allowed value id of the user defined
      enum attribute associated with the Resource. Allowed comparison operator
      is `:`. Here user-defined-attribute-enum-id is a placeholder that can be
      replaced with any user defined enum attribute name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.enum_values.values.display_name` -
      The allowed value display name of the user defined enum attribute
      associated with the Resource. Allowed comparison operator is `:`. Here
      user-defined-attribute-enum-display-name is a placeholder that can be
      replaced with any user defined enum attribute enum name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.string_values.values` - The allowed
      value of the user defined string attribute associated with the Resource.
      Allowed comparison operator is `:`. Here user-defined-attribute-string
      is a placeholder that can be replaced with any user defined string
      attribute name. * `attributes.projects/test-project-id/locations/test-
      location-id/ attributes/user-defined-attribute-id.json_values.values` -
      The allowed value of the user defined JSON attribute associated with the
      Resource. Allowed comparison operator is `:`. Here user-defined-
      attribute-json is a placeholder that can be replaced with any user
      defined JSON attribute name. Expressions are combined with either `AND`
      logic operator or `OR` logical operator but not both of them together
      i.e. only one of the `AND` or `OR` operator can be used throughout the
      filter string and both the operators cannot be used together. No other
      logical operators are supported. At most three filter fields are allowed
      in the filter string and if provided more than that then
      `INVALID_ARGUMENT` error is returned by the API. Here are a few
      examples: * `details.deprecated = True` - The ApiOperation is
      deprecated. * `details.http_operation.method = GET AND create_time <
      \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
      The method of the http operation of the ApiOperation is _GET_ and the
      spec was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10
      12:00:00 UTC_. * `details.http_operation.method = GET OR
      details.http_operation.method = POST`. - The http operation of the
      method of ApiOperation is _GET_ or _POST_. * `details.deprecated = True
      AND attributes.projects/test-project-id/locations/test-location-id/
      attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values:
      test` - The filter string specifies that the ApiOperation is deprecated
      and the value of the user defined attribute of type string is _test_.
    pageSize: Optional. The maximum number of operations to return. The
      service may return fewer than this value. If unspecified, at most 50
      operations will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListApiOperations` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (except page_size) provided to
      `ListApiOperations` must match the call that provided the page token.
    parent: Required. The parent which owns this collection of operations
      i.e., the API version. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsApisVersionsOperationsPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsOperationsPatchRequest object.

  Fields:
    googleCloudApihubV1ApiOperation: A GoogleCloudApihubV1ApiOperation
      resource to be passed as the request body.
    name: Identifier. The name of the operation. Format: `projects/{project}/l
      ocations/{location}/apis/{api}/versions/{version}/operations/{operation}
      `
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1ApiOperation = _messages.MessageField('GoogleCloudApihubV1ApiOperation', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsApisVersionsPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsPatchRequest object.

  Fields:
    googleCloudApihubV1Version: A GoogleCloudApihubV1Version resource to be
      passed as the request body.
    name: Identifier. The name of the version. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Version = _messages.MessageField('GoogleCloudApihubV1Version', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsApisVersionsSpecsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsCreateRequest object.

  Fields:
    googleCloudApihubV1Spec: A GoogleCloudApihubV1Spec resource to be passed
      as the request body.
    parent: Required. The parent resource for Spec. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
    specId: Optional. The ID to use for the spec, which will become the final
      component of the spec's resource name. This field is optional. * If
      provided, the same will be used. The service will throw an error if the
      specified id is already used by another spec in the API resource. * If
      not provided, a system generated id will be used. This value should be
      4-500 characters, overall resource name which will be of format `project
      s/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{sp
      ec}`, its length is limited to 1000 characters and valid characters are
      /a-z[0-9]-_/.
  """

  googleCloudApihubV1Spec = _messages.MessageField('GoogleCloudApihubV1Spec', 1)
  parent = _messages.StringField(2, required=True)
  specId = _messages.StringField(3)


class ApihubProjectsLocationsApisVersionsSpecsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsDeleteRequest object.

  Fields:
    name: Required. The name of the spec to delete. Format: `projects/{project
      }/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsSpecsGetContentsRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsGetContentsRequest object.

  Fields:
    name: Required. The name of the spec whose contents need to be retrieved.
      Format: `projects/{project}/locations/{location}/apis/{api}/versions/{ve
      rsion}/specs/{spec}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsSpecsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsGetRequest object.

  Fields:
    name: Required. The name of the spec to retrieve. Format: `projects/{proje
      ct}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsApisVersionsSpecsLintRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsLintRequest object.

  Fields:
    googleCloudApihubV1LintSpecRequest: A GoogleCloudApihubV1LintSpecRequest
      resource to be passed as the request body.
    name: Required. The name of the spec to be linted. Format: `projects/{proj
      ect}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`
  """

  googleCloudApihubV1LintSpecRequest = _messages.MessageField('GoogleCloudApihubV1LintSpecRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsApisVersionsSpecsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of Specs. A filter
      expression consists of a field name, a comparison operator, and a value
      for filtering. The value must be a string. The comparison operator must
      be one of: `<`, `>`, `:` or `=`. Filters are not case sensitive. The
      following fields in the `Spec` are eligible for filtering: *
      `display_name` - The display name of the Spec. Allowed comparison
      operators: `=`. * `create_time` - The time at which the Spec was
      created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `spec_type.enum_values.values.id` -
      The allowed value id of the spec_type attribute associated with the
      Spec. Allowed comparison operators: `:`. *
      `spec_type.enum_values.values.display_name` - The allowed value display
      name of the spec_type attribute associated with the Spec. Allowed
      comparison operators: `:`. * `lint_response.json_values.values` - The
      json value of the lint_response attribute associated with the Spec.
      Allowed comparison operators: `:`. * `mime_type` - The MIME type of the
      Spec. Allowed comparison operators: `=`. * `attributes.projects/test-
      project-id/locations/test-location-id/ attributes/user-defined-
      attribute-id.enum_values.values.id` - The allowed value id of the user
      defined enum attribute associated with the Resource. Allowed comparison
      operator is `:`. Here user-defined-attribute-enum-id is a placeholder
      that can be replaced with any user defined enum attribute name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.enum_values.values.display_name` -
      The allowed value display name of the user defined enum attribute
      associated with the Resource. Allowed comparison operator is `:`. Here
      user-defined-attribute-enum-display-name is a placeholder that can be
      replaced with any user defined enum attribute enum name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.string_values.values` - The allowed
      value of the user defined string attribute associated with the Resource.
      Allowed comparison operator is `:`. Here user-defined-attribute-string
      is a placeholder that can be replaced with any user defined string
      attribute name. * `attributes.projects/test-project-id/locations/test-
      location-id/ attributes/user-defined-attribute-id.json_values.values` -
      The allowed value of the user defined JSON attribute associated with the
      Resource. Allowed comparison operator is `:`. Here user-defined-
      attribute-json is a placeholder that can be replaced with any user
      defined JSON attribute name. Expressions are combined with either `AND`
      logic operator or `OR` logical operator but not both of them together
      i.e. only one of the `AND` or `OR` operator can be used throughout the
      filter string and both the operators cannot be used together. No other
      logical operators are supported. At most three filter fields are allowed
      in the filter string and if provided more than that then
      `INVALID_ARGUMENT` error is returned by the API. Here are a few
      examples: * `spec_type.enum_values.values.id: rest-id` - The filter
      string specifies that the id of the allowed value associated with the
      spec_type attribute is _rest-id_. *
      `spec_type.enum_values.values.display_name: \"Rest Display Name\"` - The
      filter string specifies that the display name of the allowed value
      associated with the spec_type attribute is `Rest Display Name`. *
      `spec_type.enum_values.values.id: grpc-id AND create_time <
      \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
      The id of the allowed value associated with the spec_type attribute is
      _grpc-id_ and the spec was created before _2021-08-15 14:50:00 UTC_ and
      after _2021-08-10 12:00:00 UTC_. * `spec_type.enum_values.values.id:
      rest-id OR spec_type.enum_values.values.id: grpc-id` - The id of the
      allowed value associated with the spec_type attribute is _rest-id_ or
      _grpc-id_. * `spec_type.enum_values.values.id: rest-id AND
      attributes.projects/test-project-id/locations/test-location-id/
      attributes/17650f90-4a29-4971-b3c0-d5532da3764b.enum_values.values.id:
      test` - The filter string specifies that the id of the allowed value
      associated with the spec_type attribute is _rest-id_ and the id of the
      allowed value associated with the user defined attribute of type enum is
      _test_.
    pageSize: Optional. The maximum number of specs to return. The service may
      return fewer than this value. If unspecified, at most 50 specs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous `ListSpecs`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSpecs` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of specs. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsApisVersionsSpecsPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsApisVersionsSpecsPatchRequest object.

  Fields:
    googleCloudApihubV1Spec: A GoogleCloudApihubV1Spec resource to be passed
      as the request body.
    name: Identifier. The name of the spec. Format: `projects/{project}/locati
      ons/{location}/apis/{api}/versions/{version}/specs/{spec}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Spec = _messages.MessageField('GoogleCloudApihubV1Spec', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsAttributesCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsAttributesCreateRequest object.

  Fields:
    attributeId: Optional. The ID to use for the attribute, which will become
      the final component of the attribute's resource name. This field is
      optional. * If provided, the same will be used. The service will throw
      an error if the specified id is already used by another attribute
      resource in the API hub. * If not provided, a system generated id will
      be used. This value should be 4-500 characters, and valid characters are
      /a-z[0-9]-_/.
    googleCloudApihubV1Attribute: A GoogleCloudApihubV1Attribute resource to
      be passed as the request body.
    parent: Required. The parent resource for Attribute. Format:
      `projects/{project}/locations/{location}`
  """

  attributeId = _messages.StringField(1)
  googleCloudApihubV1Attribute = _messages.MessageField('GoogleCloudApihubV1Attribute', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsAttributesDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsAttributesDeleteRequest object.

  Fields:
    name: Required. The name of the attribute to delete. Format:
      `projects/{project}/locations/{location}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsAttributesGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsAttributesGetRequest object.

  Fields:
    name: Required. The name of the attribute to retrieve. Format:
      `projects/{project}/locations/{location}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsAttributesListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsAttributesListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of Attributes. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string or a boolean. The
      comparison operator must be one of: `<`, `>` or `=`. Filters are not
      case sensitive. The following fields in the `Attribute` are eligible for
      filtering: * `display_name` - The display name of the Attribute. Allowed
      comparison operators: `=`. * `definition_type` - The definition type of
      the attribute. Allowed comparison operators: `=`. * `scope` - The scope
      of the attribute. Allowed comparison operators: `=`. * `data_type` - The
      type of the data of the attribute. Allowed comparison operators: `=`. *
      `mandatory` - Denotes whether the attribute is mandatory or not. Allowed
      comparison operators: `=`. * `create_time` - The time at which the
      Attribute was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. Expressions are combined with either
      `AND` logic operator or `OR` logical operator but not both of them
      together i.e. only one of the `AND` or `OR` operator can be used
      throughout the filter string and both the operators cannot be used
      together. No other logical operators are supported. At most three filter
      fields are allowed in the filter string and if provided more than that
      then `INVALID_ARGUMENT` error is returned by the API. Here are a few
      examples: * `display_name = production` - - The display name of the
      attribute is _production_. * `(display_name = production) AND
      (create_time < \"2021-08-15T14:50:00Z\") AND (create_time >
      \"2021-08-10T12:00:00Z\")` - The display name of the attribute is
      _production_ and the attribute was created before _2021-08-15 14:50:00
      UTC_ and after _2021-08-10 12:00:00 UTC_. * `display_name = production
      OR scope = api` - The attribute where the display name is _production_
      or the scope is _api_.
    pageSize: Optional. The maximum number of attribute resources to return.
      The service may return fewer than this value. If unspecified, at most 50
      attributes will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAttributes` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAttributes` must
      match the call that provided the page token.
    parent: Required. The parent resource for Attribute. Format:
      `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsAttributesPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsAttributesPatchRequest object.

  Fields:
    googleCloudApihubV1Attribute: A GoogleCloudApihubV1Attribute resource to
      be passed as the request body.
    name: Identifier. The name of the attribute in the API Hub. Format:
      `projects/{project}/locations/{location}/attributes/{attribute}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Attribute = _messages.MessageField('GoogleCloudApihubV1Attribute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsCollectApiDataRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCollectApiDataRequest object.

  Fields:
    googleCloudApihubV1CollectApiDataRequest: A
      GoogleCloudApihubV1CollectApiDataRequest resource to be passed as the
      request body.
    location: Required. The regional location of the API hub instance and its
      resources. Format: `projects/{project}/locations/{location}`
  """

  googleCloudApihubV1CollectApiDataRequest = _messages.MessageField('GoogleCloudApihubV1CollectApiDataRequest', 1)
  location = _messages.StringField(2, required=True)


class ApihubProjectsLocationsCurationsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCurationsCreateRequest object.

  Fields:
    curationId: Optional. The ID to use for the curation resource, which will
      become the final component of the curations's resource name. This field
      is optional. * If provided, the same will be used. The service will
      throw an error if the specified ID is already used by another curation
      resource in the API hub. * If not provided, a system generated ID will
      be used. This value should be 4-500 characters, and valid characters are
      /a-z[0-9]-_/.
    googleCloudApihubV1Curation: A GoogleCloudApihubV1Curation resource to be
      passed as the request body.
    parent: Required. The parent resource for the curation resource. Format:
      `projects/{project}/locations/{location}`
  """

  curationId = _messages.StringField(1)
  googleCloudApihubV1Curation = _messages.MessageField('GoogleCloudApihubV1Curation', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsCurationsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCurationsDeleteRequest object.

  Fields:
    name: Required. The name of the curation resource to delete. Format:
      `projects/{project}/locations/{location}/curations/{curation}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsCurationsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCurationsGetRequest object.

  Fields:
    name: Required. The name of the curation resource to retrieve. Format:
      `projects/{project}/locations/{location}/curations/{curation}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsCurationsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCurationsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of curation
      resources. A filter expression consists of a field name, a comparison
      operator, and a value for filtering. The value must be a string. The
      comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are
      case insensitive. The following fields in the `curation resource` are
      eligible for filtering: * `create_time` - The time at which the curation
      was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `display_name` - The display name
      of the curation. Allowed comparison operators: `=`. * `state` - The
      state of the curation. Allowed comparison operators: `=`. Expressions
      are combined with either `AND` logic operator or `OR` logical operator
      but not both of them together i.e. only one of the `AND` or `OR`
      operator can be used throughout the filter string and both the operators
      cannot be used together. No other logical operators are supported. At
      most three filter fields are allowed in the filter string and if
      provided more than that then `INVALID_ARGUMENT` error is returned by the
      API. Here are a few examples: * `create_time < \"2021-08-15T14:50:00Z\"
      AND create_time > \"2021-08-10T12:00:00Z\"` - The curation resource was
      created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00
      UTC_.
    pageSize: Optional. The maximum number of curation resources to return.
      The service may return fewer than this value. If unspecified, at most 50
      curations will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListCurations` call. Provide this to retrieve the subsequent page. When
      paginating, all other parameters (except page_size) provided to
      `ListCurations` must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of curation
      resources. Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsCurationsPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsCurationsPatchRequest object.

  Fields:
    googleCloudApihubV1Curation: A GoogleCloudApihubV1Curation resource to be
      passed as the request body.
    name: Identifier. The name of the curation. Format:
      `projects/{project}/locations/{location}/curations/{curation}`
    updateMask: Optional. The list of fields to update.
  """

  googleCloudApihubV1Curation = _messages.MessageField('GoogleCloudApihubV1Curation', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsDependenciesCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDependenciesCreateRequest object.

  Fields:
    dependencyId: Optional. The ID to use for the dependency resource, which
      will become the final component of the dependency's resource name. This
      field is optional. * If provided, the same will be used. The service
      will throw an error if duplicate id is provided by the client. * If not
      provided, a system generated id will be used. This value should be 4-500
      characters, and valid characters are `a-z[0-9]-_`.
    googleCloudApihubV1Dependency: A GoogleCloudApihubV1Dependency resource to
      be passed as the request body.
    parent: Required. The parent resource for the dependency resource. Format:
      `projects/{project}/locations/{location}`
  """

  dependencyId = _messages.StringField(1)
  googleCloudApihubV1Dependency = _messages.MessageField('GoogleCloudApihubV1Dependency', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsDependenciesDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDependenciesDeleteRequest object.

  Fields:
    name: Required. The name of the dependency resource to delete. Format:
      `projects/{project}/locations/{location}/dependencies/{dependency}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsDependenciesGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDependenciesGetRequest object.

  Fields:
    name: Required. The name of the dependency resource to retrieve. Format:
      `projects/{project}/locations/{location}/dependencies/{dependency}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsDependenciesListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDependenciesListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of Dependencies. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string. Allowed comparison
      operator is `=`. Filters are not case sensitive. The following fields in
      the `Dependency` are eligible for filtering: *
      `consumer.operation_resource_name` - The operation resource name for the
      consumer entity involved in a dependency. Allowed comparison operators:
      `=`. * `consumer.external_api_resource_name` - The external api resource
      name for the consumer entity involved in a dependency. Allowed
      comparison operators: `=`. * `supplier.operation_resource_name` - The
      operation resource name for the supplier entity involved in a
      dependency. Allowed comparison operators: `=`. *
      `supplier.external_api_resource_name` - The external api resource name
      for the supplier entity involved in a dependency. Allowed comparison
      operators: `=`. Expressions are combined with either `AND` logic
      operator or `OR` logical operator but not both of them together i.e.
      only one of the `AND` or `OR` operator can be used throughout the filter
      string and both the operators cannot be used together. No other logical
      operators are supported. At most three filter fields are allowed in the
      filter string and if provided more than that then `INVALID_ARGUMENT`
      error is returned by the API. For example,
      `consumer.operation_resource_name =
      \"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\" OR
      supplier.operation_resource_name =
      \"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\"` -
      The dependencies with either consumer or supplier operation resource
      name as
      _projects/p1/locations/global/apis/a1/versions/v1/operations/o1_.
    pageSize: Optional. The maximum number of dependency resources to return.
      The service may return fewer than this value. If unspecified, at most 50
      dependencies will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListDependencies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListDependencies`
      must match the call that provided the page token.
    parent: Required. The parent which owns this collection of dependency
      resources. Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsDependenciesPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDependenciesPatchRequest object.

  Fields:
    googleCloudApihubV1Dependency: A GoogleCloudApihubV1Dependency resource to
      be passed as the request body.
    name: Identifier. The name of the dependency in the API Hub. Format:
      `projects/{project}/locations/{location}/dependencies/{dependency}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Dependency = _messages.MessageField('GoogleCloudApihubV1Dependency', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsDeploymentsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDeploymentsCreateRequest object.

  Fields:
    deploymentId: Optional. The ID to use for the deployment resource, which
      will become the final component of the deployment's resource name. This
      field is optional. * If provided, the same will be used. The service
      will throw an error if the specified id is already used by another
      deployment resource in the API hub. * If not provided, a system
      generated id will be used. This value should be 4-500 characters, and
      valid characters are /a-z[0-9]-_/.
    googleCloudApihubV1Deployment: A GoogleCloudApihubV1Deployment resource to
      be passed as the request body.
    parent: Required. The parent resource for the deployment resource. Format:
      `projects/{project}/locations/{location}`
  """

  deploymentId = _messages.StringField(1)
  googleCloudApihubV1Deployment = _messages.MessageField('GoogleCloudApihubV1Deployment', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsDeploymentsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDeploymentsDeleteRequest object.

  Fields:
    name: Required. The name of the deployment resource to delete. Format:
      `projects/{project}/locations/{location}/deployments/{deployment}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsDeploymentsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment resource to retrieve. Format:
      `projects/{project}/locations/{location}/deployments/{deployment}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsDeploymentsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDeploymentsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of Deployments. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string. The comparison operator
      must be one of: `<`, `>` or `=`. Filters are not case sensitive. The
      following fields in the `Deployments` are eligible for filtering: *
      `display_name` - The display name of the Deployment. Allowed comparison
      operators: `=`. * `create_time` - The time at which the Deployment was
      created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed
      comparison operators: `>` and `<`. * `resource_uri` - A URI to the
      deployment resource. Allowed comparison operators: `=`. * `api_versions`
      - The API versions linked to this deployment. Allowed comparison
      operators: `:`. * `deployment_type.enum_values.values.id` - The allowed
      value id of the deployment_type attribute associated with the
      Deployment. Allowed comparison operators: `:`. *
      `deployment_type.enum_values.values.display_name` - The allowed value
      display name of the deployment_type attribute associated with the
      Deployment. Allowed comparison operators: `:`. *
      `slo.string_values.values` -The allowed string value of the slo
      attribute associated with the deployment. Allowed comparison operators:
      `:`. * `environment.enum_values.values.id` - The allowed value id of the
      environment attribute associated with the deployment. Allowed comparison
      operators: `:`. * `environment.enum_values.values.display_name` - The
      allowed value display name of the environment attribute associated with
      the deployment. Allowed comparison operators: `:`. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.enum_values.values.id` - The
      allowed value id of the user defined enum attribute associated with the
      Resource. Allowed comparison operator is `:`. Here user-defined-
      attribute-enum-id is a placeholder that can be replaced with any user
      defined enum attribute name. * `attributes.projects/test-project-
      id/locations/test-location-id/ attributes/user-defined-attribute-
      id.enum_values.values.display_name` - The allowed value display name of
      the user defined enum attribute associated with the Resource. Allowed
      comparison operator is `:`. Here user-defined-attribute-enum-display-
      name is a placeholder that can be replaced with any user defined enum
      attribute enum name. * `attributes.projects/test-project-
      id/locations/test-location-id/ attributes/user-defined-attribute-
      id.string_values.values` - The allowed value of the user defined string
      attribute associated with the Resource. Allowed comparison operator is
      `:`. Here user-defined-attribute-string is a placeholder that can be
      replaced with any user defined string attribute name. *
      `attributes.projects/test-project-id/locations/test-location-id/
      attributes/user-defined-attribute-id.json_values.values` - The allowed
      value of the user defined JSON attribute associated with the Resource.
      Allowed comparison operator is `:`. Here user-defined-attribute-json is
      a placeholder that can be replaced with any user defined JSON attribute
      name. Expressions are combined with either `AND` logic operator or `OR`
      logical operator but not both of them together i.e. only one of the
      `AND` or `OR` operator can be used throughout the filter string and both
      the operators cannot be used together. No other logical operators are
      supported. At most three filter fields are allowed in the filter string
      and if provided more than that then `INVALID_ARGUMENT` error is returned
      by the API. Here are a few examples: *
      `environment.enum_values.values.id: staging-id` - The allowed value id
      of the environment attribute associated with the Deployment is _staging-
      id_. * `environment.enum_values.values.display_name: \"Staging
      Deployment\"` - The allowed value display name of the environment
      attribute associated with the Deployment is `Staging Deployment`. *
      `environment.enum_values.values.id: production-id AND create_time <
      \"2021-08-15T14:50:00Z\" AND create_time > \"2021-08-10T12:00:00Z\"` -
      The allowed value id of the environment attribute associated with the
      Deployment is _production-id_ and Deployment was created before
      _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. *
      `environment.enum_values.values.id: production-id OR
      slo.string_values.values: \"99.99%\"` - The allowed value id of the
      environment attribute Deployment is _production-id_ or string value of
      the slo attribute is _99.99%_. * `environment.enum_values.values.id:
      staging-id AND attributes.projects/test-project-id/locations/test-
      location-id/
      attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values:
      test` - The filter string specifies that the allowed value id of the
      environment attribute associated with the Deployment is _staging-id_ and
      the value of the user defined attribute of type string is _test_.
    pageSize: Optional. The maximum number of deployment resources to return.
      The service may return fewer than this value. If unspecified, at most 50
      deployments will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListDeployments` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (except page_size) provided to
      `ListDeployments` must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of deployment
      resources. Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsDeploymentsPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsDeploymentsPatchRequest object.

  Fields:
    googleCloudApihubV1Deployment: A GoogleCloudApihubV1Deployment resource to
      be passed as the request body.
    name: Identifier. The name of the deployment. Format:
      `projects/{project}/locations/{location}/deployments/{deployment}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1Deployment = _messages.MessageField('GoogleCloudApihubV1Deployment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsExternalApisCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsExternalApisCreateRequest object.

  Fields:
    externalApiId: Optional. The ID to use for the External API resource,
      which will become the final component of the External API's resource
      name. This field is optional. * If provided, the same will be used. The
      service will throw an error if the specified id is already used by
      another External API resource in the API hub. * If not provided, a
      system generated id will be used. This value should be 4-500 characters,
      and valid characters are /a-z[0-9]-_/.
    googleCloudApihubV1ExternalApi: A GoogleCloudApihubV1ExternalApi resource
      to be passed as the request body.
    parent: Required. The parent resource for the External API resource.
      Format: `projects/{project}/locations/{location}`
  """

  externalApiId = _messages.StringField(1)
  googleCloudApihubV1ExternalApi = _messages.MessageField('GoogleCloudApihubV1ExternalApi', 2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsExternalApisDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsExternalApisDeleteRequest object.

  Fields:
    name: Required. The name of the External API resource to delete. Format:
      `projects/{project}/locations/{location}/externalApis/{externalApi}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsExternalApisGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsExternalApisGetRequest object.

  Fields:
    name: Required. The name of the External API resource to retrieve. Format:
      `projects/{project}/locations/{location}/externalApis/{externalApi}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsExternalApisListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsExternalApisListRequest object.

  Fields:
    pageSize: Optional. The maximum number of External API resources to
      return. The service may return fewer than this value. If unspecified, at
      most 50 ExternalApis will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListExternalApis` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (except page_size) provided to
      `ListExternalApis` must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of External API
      resources. Format: `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsExternalApisPatchRequest(_messages.Message):
  r"""A ApihubProjectsLocationsExternalApisPatchRequest object.

  Fields:
    googleCloudApihubV1ExternalApi: A GoogleCloudApihubV1ExternalApi resource
      to be passed as the request body.
    name: Identifier. Format:
      `projects/{project}/locations/{location}/externalApi/{externalApi}`.
    updateMask: Required. The list of fields to update.
  """

  googleCloudApihubV1ExternalApi = _messages.MessageField('GoogleCloudApihubV1ExternalApi', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsHostProjectRegistrationsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsHostProjectRegistrationsCreateRequest object.

  Fields:
    googleCloudApihubV1HostProjectRegistration: A
      GoogleCloudApihubV1HostProjectRegistration resource to be passed as the
      request body.
    hostProjectRegistrationId: Required. The ID to use for the Host Project
      Registration, which will become the final component of the host project
      registration's resource name. The ID must be the same as the Google
      cloud project specified in the host_project_registration.gcp_project
      field.
    parent: Required. The parent resource for the host project. Format:
      `projects/{project}/locations/{location}`
  """

  googleCloudApihubV1HostProjectRegistration = _messages.MessageField('GoogleCloudApihubV1HostProjectRegistration', 1)
  hostProjectRegistrationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApihubProjectsLocationsHostProjectRegistrationsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsHostProjectRegistrationsGetRequest object.

  Fields:
    name: Required. Host project registration resource name. projects/{project
      }/locations/{location}/hostProjectRegistrations/{host_project_registrati
      on_id}
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsHostProjectRegistrationsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsHostProjectRegistrationsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of
      HostProjectRegistrations. A filter expression consists of a field name,
      a comparison operator, and a value for filtering. The value must be a
      string. All standard operators as documented at
      https://google.aip.dev/160 are supported. The following fields in the
      `HostProjectRegistration` are eligible for filtering: * `name` - The
      name of the HostProjectRegistration. * `create_time` - The time at which
      the HostProjectRegistration was created. The value should be in the
      (RFC3339)[https://tools.ietf.org/html/rfc3339] format. * `gcp_project` -
      The Google cloud project associated with the HostProjectRegistration.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. The maximum number of host project registrations to
      return. The service may return fewer than this value. If unspecified, at
      most 50 host project registrations will be returned. The maximum value
      is 1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListHostProjectRegistrations` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters (except
      page_size) provided to `ListHostProjectRegistrations` must match the
      call that provided the page token.
    parent: Required. The parent, which owns this collection of host projects.
      Format: `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApihubProjectsLocationsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ApihubProjectsLocationsLookupRuntimeProjectAttachmentRequest(_messages.Message):
  r"""A ApihubProjectsLocationsLookupRuntimeProjectAttachmentRequest object.

  Fields:
    name: Required. Runtime project ID to look up runtime project attachment
      for. Lookup happens across all regions. Expected format:
      `projects/{project}/locations/{location}`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ApihubProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ApihubProjectsLocationsPluginsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsCreateRequest object.

  Fields:
    googleCloudApihubV1Plugin: A GoogleCloudApihubV1Plugin resource to be
      passed as the request body.
    parent: Required. The parent resource where this plugin will be created.
      Format: `projects/{project}/locations/{location}`.
    pluginId: Optional. The ID to use for the Plugin resource, which will
      become the final component of the Plugin's resource name. This field is
      optional. * If provided, the same will be used. The service will throw
      an error if the specified id is already used by another Plugin resource
      in the API hub instance. * If not provided, a system generated id will
      be used. This value should be 4-500 characters, overall resource name
      which will be of format
      `projects/{project}/locations/{location}/plugins/{plugin}`, its length
      is limited to 1000 characters and valid characters are /a-z[0-9]-_/.
  """

  googleCloudApihubV1Plugin = _messages.MessageField('GoogleCloudApihubV1Plugin', 1)
  parent = _messages.StringField(2, required=True)
  pluginId = _messages.StringField(3)


class ApihubProjectsLocationsPluginsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsDeleteRequest object.

  Fields:
    name: Required. The name of the Plugin resource to delete. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsDisableRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsDisableRequest object.

  Fields:
    googleCloudApihubV1DisablePluginRequest: A
      GoogleCloudApihubV1DisablePluginRequest resource to be passed as the
      request body.
    name: Required. The name of the plugin to disable. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`.
  """

  googleCloudApihubV1DisablePluginRequest = _messages.MessageField('GoogleCloudApihubV1DisablePluginRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsPluginsEnableRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsEnableRequest object.

  Fields:
    googleCloudApihubV1EnablePluginRequest: A
      GoogleCloudApihubV1EnablePluginRequest resource to be passed as the
      request body.
    name: Required. The name of the plugin to enable. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`.
  """

  googleCloudApihubV1EnablePluginRequest = _messages.MessageField('GoogleCloudApihubV1EnablePluginRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsPluginsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsGetRequest object.

  Fields:
    name: Required. The name of the plugin to retrieve. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsGetStyleGuideRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsGetStyleGuideRequest object.

  Fields:
    name: Required. The name of the spec to retrieve. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsInstancesCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesCreateRequest object.

  Fields:
    googleCloudApihubV1PluginInstance: A GoogleCloudApihubV1PluginInstance
      resource to be passed as the request body.
    parent: Required. The parent of the plugin instance resource. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`
    pluginInstanceId: Optional. The ID to use for the plugin instance, which
      will become the final component of the plugin instance's resource name.
      This field is optional. * If provided, the same will be used. The
      service will throw an error if the specified id is already used by
      another plugin instance in the plugin resource. * If not provided, a
      system generated id will be used. This value should be 4-500 characters,
      and valid characters are /a-z[0-9]-_/.
  """

  googleCloudApihubV1PluginInstance = _messages.MessageField('GoogleCloudApihubV1PluginInstance', 1)
  parent = _messages.StringField(2, required=True)
  pluginInstanceId = _messages.StringField(3)


class ApihubProjectsLocationsPluginsInstancesDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesDeleteRequest object.

  Fields:
    name: Required. The name of the plugin instance to delete. Format: `projec
      ts/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`
      .
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsInstancesDisableActionRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesDisableActionRequest object.

  Fields:
    googleCloudApihubV1DisablePluginInstanceActionRequest: A
      GoogleCloudApihubV1DisablePluginInstanceActionRequest resource to be
      passed as the request body.
    name: Required. The name of the plugin instance to disable. Format: `proje
      cts/{project}/locations/{location}/plugins/{plugin}/instances/{instance}
      `
  """

  googleCloudApihubV1DisablePluginInstanceActionRequest = _messages.MessageField('GoogleCloudApihubV1DisablePluginInstanceActionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsPluginsInstancesEnableActionRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesEnableActionRequest object.

  Fields:
    googleCloudApihubV1EnablePluginInstanceActionRequest: A
      GoogleCloudApihubV1EnablePluginInstanceActionRequest resource to be
      passed as the request body.
    name: Required. The name of the plugin instance to enable. Format: `projec
      ts/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`
  """

  googleCloudApihubV1EnablePluginInstanceActionRequest = _messages.MessageField('GoogleCloudApihubV1EnablePluginInstanceActionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsPluginsInstancesExecuteActionRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesExecuteActionRequest object.

  Fields:
    googleCloudApihubV1ExecutePluginInstanceActionRequest: A
      GoogleCloudApihubV1ExecutePluginInstanceActionRequest resource to be
      passed as the request body.
    name: Required. The name of the plugin instance to execute. Format: `proje
      cts/{project}/locations/{location}/plugins/{plugin}/instances/{instance}
      `
  """

  googleCloudApihubV1ExecutePluginInstanceActionRequest = _messages.MessageField('GoogleCloudApihubV1ExecutePluginInstanceActionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApihubProjectsLocationsPluginsInstancesGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesGetRequest object.

  Fields:
    name: Required. The name of the plugin instance to retrieve. Format: `proj
      ects/{project}/locations/{location}/plugins/{plugin}/instances/{instance
      }`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsInstancesListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsInstancesListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of plugin instances.
      A filter expression consists of a field name, a comparison operator, and
      a value for filtering. The value must be a string. The comparison
      operator must be one of: `<`, `>` or `=`. Filters are not case
      sensitive. The following fields in the `PluginInstances` are eligible
      for filtering: * `state` - The state of the Plugin Instance. Allowed
      comparison operators: `=`. Expressions are combined with either `AND`
      logic operator or `OR` logical operator but not both of them together
      i.e. only one of the `AND` or `OR` operator can be used throughout the
      filter string and both the operators cannot be used together. No other
      logical operators are supported. At most three filter fields are allowed
      in the filter string and if provided more than that then
      `INVALID_ARGUMENT` error is returned by the API. Here are a few
      examples: * `state = ENABLED` - The plugin instance is in enabled state.
    pageSize: Optional. The maximum number of hub plugins to return. The
      service may return fewer than this value. If unspecified, at most 50 hub
      plugins will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPluginInstances` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListPluginInstances` must match the call that provided the page token.
    parent: Required. The parent resource where this plugin will be created.
      Format: `projects/{project}/locations/{location}/plugins/{plugin}`. To
      list plugin instances for multiple plugins, use the - character instead
      of the plugin ID.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsPluginsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of plugins. A filter
      expression consists of a field name, a comparison operator, and a value
      for filtering. The value must be a string. The comparison operator must
      be one of: `<`, `>` or `=`. Filters are not case sensitive. The
      following fields in the `Plugins` are eligible for filtering: *
      `plugin_category` - The category of the Plugin. Allowed comparison
      operators: `=`. Expressions are combined with either `AND` logic
      operator or `OR` logical operator but not both of them together i.e.
      only one of the `AND` or `OR` operator can be used throughout the filter
      string and both the operators cannot be used together. No other logical
      operators are supported. At most three filter fields are allowed in the
      filter string and if provided more than that then `INVALID_ARGUMENT`
      error is returned by the API. Here are a few examples: *
      `plugin_category = ON_RAMP` - The plugin is of category on ramp.
    pageSize: Optional. The maximum number of hub plugins to return. The
      service may return fewer than this value. If unspecified, at most 50 hub
      plugins will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous `ListPlugins`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters (except page_size) provided to `ListPlugins` must match
      the call that provided the page token.
    parent: Required. The parent resource where this plugin will be created.
      Format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApihubProjectsLocationsPluginsStyleGuideGetContentsRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsStyleGuideGetContentsRequest object.

  Fields:
    name: Required. The name of the StyleGuide whose contents need to be
      retrieved. There is exactly one style guide resource per project per
      location. The expected format is
      `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsPluginsUpdateStyleGuideRequest(_messages.Message):
  r"""A ApihubProjectsLocationsPluginsUpdateStyleGuideRequest object.

  Fields:
    googleCloudApihubV1StyleGuide: A GoogleCloudApihubV1StyleGuide resource to
      be passed as the request body.
    name: Identifier. The name of the style guide. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`
    updateMask: Optional. The list of fields to update.
  """

  googleCloudApihubV1StyleGuide = _messages.MessageField('GoogleCloudApihubV1StyleGuide', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApihubProjectsLocationsRuntimeProjectAttachmentsCreateRequest(_messages.Message):
  r"""A ApihubProjectsLocationsRuntimeProjectAttachmentsCreateRequest object.

  Fields:
    googleCloudApihubV1RuntimeProjectAttachment: A
      GoogleCloudApihubV1RuntimeProjectAttachment resource to be passed as the
      request body.
    parent: Required. The parent resource for the Runtime Project Attachment.
      Format: `projects/{project}/locations/{location}`
    runtimeProjectAttachmentId: Required. The ID to use for the Runtime
      Project Attachment, which will become the final component of the Runtime
      Project Attachment's name. The ID must be the same as the project ID of
      the Google cloud project specified in the
      runtime_project_attachment.runtime_project field.
  """

  googleCloudApihubV1RuntimeProjectAttachment = _messages.MessageField('GoogleCloudApihubV1RuntimeProjectAttachment', 1)
  parent = _messages.StringField(2, required=True)
  runtimeProjectAttachmentId = _messages.StringField(3)


class ApihubProjectsLocationsRuntimeProjectAttachmentsDeleteRequest(_messages.Message):
  r"""A ApihubProjectsLocationsRuntimeProjectAttachmentsDeleteRequest object.

  Fields:
    name: Required. The name of the Runtime Project Attachment to delete.
      Format: `projects/{project}/locations/{location}/runtimeProjectAttachmen
      ts/{runtime_project_attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsRuntimeProjectAttachmentsGetRequest(_messages.Message):
  r"""A ApihubProjectsLocationsRuntimeProjectAttachmentsGetRequest object.

  Fields:
    name: Required. The name of the API resource to retrieve. Format: `project
      s/{project}/locations/{location}/runtimeProjectAttachments/{runtime_proj
      ect_attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApihubProjectsLocationsRuntimeProjectAttachmentsListRequest(_messages.Message):
  r"""A ApihubProjectsLocationsRuntimeProjectAttachmentsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of
      RuntimeProjectAttachments. A filter expression consists of a field name,
      a comparison operator, and a value for filtering. The value must be a
      string. All standard operators as documented at
      https://google.aip.dev/160 are supported. The following fields in the
      `RuntimeProjectAttachment` are eligible for filtering: * `name` - The
      name of the RuntimeProjectAttachment. * `create_time` - The time at
      which the RuntimeProjectAttachment was created. The value should be in
      the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. *
      `runtime_project` - The Google cloud project associated with the
      RuntimeProjectAttachment.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. The maximum number of runtime project attachments to
      return. The service may return fewer than this value. If unspecified, at
      most 50 runtime project attachments will be returned. The maximum value
      is 1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListRuntimeProjectAttachments` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters (except
      page_size) provided to `ListRuntimeProjectAttachments` must match the
      call that provided the page token.
    parent: Required. The parent, which owns this collection of runtime
      project attachments. Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApihubProjectsLocationsSearchResourcesRequest(_messages.Message):
  r"""A ApihubProjectsLocationsSearchResourcesRequest object.

  Fields:
    googleCloudApihubV1SearchResourcesRequest: A
      GoogleCloudApihubV1SearchResourcesRequest resource to be passed as the
      request body.
    location: Required. The resource name of the location which will be of the
      type `projects/{project_id}/locations/{location_id}`. This field is used
      to identify the instance of API-Hub in which resources should be
      searched.
  """

  googleCloudApihubV1SearchResourcesRequest = _messages.MessageField('GoogleCloudApihubV1SearchResourcesRequest', 1)
  location = _messages.StringField(2, required=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleCloudApihubV1APIMetadata(_messages.Message):
  r"""The API metadata.

  Fields:
    api: Required. The API resource to be pushed to Hub's collect layer. The
      ID of the API resource will be generated by Hub to ensure uniqueness
      across all APIs across systems.
    originalCreateTime: Optional. Timestamp indicating when the API was
      created at the source.
    originalId: Optional. The unique identifier of the API in the system where
      it was originally created.
    originalUpdateTime: Required. Timestamp indicating when the API was last
      updated at the source.
    versions: Optional. The list of versions present in an API resource.
  """

  api = _messages.MessageField('GoogleCloudApihubV1Api', 1)
  originalCreateTime = _messages.StringField(2)
  originalId = _messages.StringField(3)
  originalUpdateTime = _messages.StringField(4)
  versions = _messages.MessageField('GoogleCloudApihubV1VersionMetadata', 5, repeated=True)


class GoogleCloudApihubV1ActionExecutionDetail(_messages.Message):
  r"""The details for the action to execute.

  Fields:
    actionId: Required. The action id of the plugin to execute.
  """

  actionId = _messages.StringField(1)


class GoogleCloudApihubV1AllowedValue(_messages.Message):
  r"""The value that can be assigned to the attribute when the data type is
  enum.

  Fields:
    description: Optional. The detailed description of the allowed value.
    displayName: Required. The display name of the allowed value.
    id: Required. The ID of the allowed value. * If provided, the same will be
      used. The service will throw an error if the specified id is already
      used by another allowed value in the same attribute resource. * If not
      provided, a system generated id derived from the display name will be
      used. In this case, the service will handle conflict resolution by
      adding a system generated suffix in case of duplicates. This value
      should be 4-63 characters, and valid characters are /a-z-/.
    immutable: Optional. When set to true, the allowed value cannot be updated
      or deleted by the user. It can only be true for System defined
      attributes.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  id = _messages.StringField(3)
  immutable = _messages.BooleanField(4)


class GoogleCloudApihubV1Api(_messages.Message):
  r"""An API resource in the API Hub.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the API resource. The key is the attribute name. It will be of the
      format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    apiFunctionalRequirements: Optional. The api functional requirements
      associated with the API resource. Carinality is 1 for this attribute.
    apiRequirements: Optional. The api requirement doc associated with the API
      resource. Carinality is 1 for this attribute.
    apiStyle: Optional. The style of the API. This maps to the following
      system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-api-style`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    apiTechnicalRequirements: Optional. The api technical requirements
      associated with the API resource. Carinality is 1 for this attribute.
    attributes: Optional. The list of user defined attributes associated with
      the API resource. The key is the attribute name. It will be of the
      format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    businessUnit: Optional. The business unit owning the API. This maps to the
      following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-business-
      unit` attribute. The number of values for this attribute will be based
      on the cardinality of the attribute. The same can be retrieved via
      GetAttribute API. All values should be from the list of allowed values
      defined for the attribute.
    createTime: Output only. The time at which the API resource was created.
    description: Optional. The description of the API resource.
    displayName: Required. The display name of the API resource.
    documentation: Optional. The documentation for the API resource.
    fingerprint: Optional. Fingerprint of the API resource.
    maturityLevel: Optional. The maturity level of the API. This maps to the
      following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-maturity-
      level` attribute. The number of values for this attribute will be based
      on the cardinality of the attribute. The same can be retrieved via
      GetAttribute API. All values should be from the list of allowed values
      defined for the attribute.
    name: Identifier. The name of the API resource in the API Hub. Format:
      `projects/{project}/locations/{location}/apis/{api}`
    owner: Optional. Owner details for the API resource.
    selectedVersion: Optional. The selected version for an API resource. This
      can be used when special handling is needed on client side for
      particular version of the API. Format is
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
    sourceMetadata: Output only. The list of sources and metadata from the
      sources of the API resource.
    targetUser: Optional. The target users for the API. This maps to the
      following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-target-user`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    team: Optional. The team owning the API. This maps to the following system
      defined attribute:
      `projects/{project}/locations/{location}/attributes/system-team`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    updateTime: Output only. The time at which the API resource was last
      updated.
    versions: Output only. The list of versions present in an API resource.
      Note: An API resource can be associated with more than 1 version. Format
      is
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the API
    resource. The key is the attribute name. It will be of the format:
    `projects/{project}/locations/{location}/attributes/{attribute}`. The
    value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiFunctionalRequirements = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 1)
  apiRequirements = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)
  apiStyle = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 3)
  apiTechnicalRequirements = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 4)
  attributes = _messages.MessageField('AttributesValue', 5)
  businessUnit = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 6)
  createTime = _messages.StringField(7)
  description = _messages.StringField(8)
  displayName = _messages.StringField(9)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 10)
  fingerprint = _messages.StringField(11)
  maturityLevel = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 12)
  name = _messages.StringField(13)
  owner = _messages.MessageField('GoogleCloudApihubV1Owner', 14)
  selectedVersion = _messages.StringField(15)
  sourceMetadata = _messages.MessageField('GoogleCloudApihubV1SourceMetadata', 16, repeated=True)
  targetUser = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 17)
  team = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 18)
  updateTime = _messages.StringField(19)
  versions = _messages.StringField(20, repeated=True)


class GoogleCloudApihubV1ApiData(_messages.Message):
  r"""The API data to be collected.

  Fields:
    apiMetadataList: Optional. The list of API metadata.
  """

  apiMetadataList = _messages.MessageField('GoogleCloudApihubV1ApiMetadataList', 1)


class GoogleCloudApihubV1ApiHubInstance(_messages.Message):
  r"""An ApiHubInstance represents the instance resources of the API Hub.
  Currently, only one ApiHub instance is allowed for each project.

  Enums:
    StateValueValuesEnum: Output only. The current state of the ApiHub
      instance.

  Messages:
    LabelsValue: Optional. Instance labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    config: Required. Config of the ApiHub instance.
    createTime: Output only. Creation timestamp.
    description: Optional. Description of the ApiHub instance.
    labels: Optional. Instance labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    name: Identifier. Format: `projects/{project}/locations/{location}/apiHubI
      nstances/{apiHubInstance}`.
    state: Output only. The current state of the ApiHub instance.
    stateMessage: Output only. Extra information about ApiHub instance state.
      Currently the message would be populated when state is `FAILED`.
    updateTime: Output only. Last update timestamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the ApiHub instance.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      INACTIVE: The ApiHub instance has not been initialized or has been
        deleted.
      CREATING: The ApiHub instance is being created.
      ACTIVE: The ApiHub instance has been created and is ready for use.
      UPDATING: The ApiHub instance is being updated.
      DELETING: The ApiHub instance is being deleted.
      FAILED: The ApiHub instance encountered an error during a state change.
    """
    STATE_UNSPECIFIED = 0
    INACTIVE = 1
    CREATING = 2
    ACTIVE = 3
    UPDATING = 4
    DELETING = 5
    FAILED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Instance labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.MessageField('GoogleCloudApihubV1Config', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  stateMessage = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudApihubV1ApiHubResource(_messages.Message):
  r"""ApiHubResource is one of the resources such as Api, Operation,
  Deployment, Definition, Spec and Version resources stored in API-Hub.

  Fields:
    api: This represents Api resource in search results. Only name,
      display_name, description and owner fields are populated in search
      results.
    definition: This represents Definition resource in search results. Only
      name field is populated in search results.
    deployment: This represents Deployment resource in search results. Only
      name, display_name, description, deployment_type and api_versions fields
      are populated in search results.
    operation: This represents ApiOperation resource in search results. Only
      name, description, spec and details fields are populated in search
      results.
    spec: This represents Spec resource in search results. Only name,
      display_name, description, spec_type and documentation fields are
      populated in search results.
    version: This represents Version resource in search results. Only name,
      display_name, description, lifecycle, compliance and accreditation
      fields are populated in search results.
  """

  api = _messages.MessageField('GoogleCloudApihubV1Api', 1)
  definition = _messages.MessageField('GoogleCloudApihubV1Definition', 2)
  deployment = _messages.MessageField('GoogleCloudApihubV1Deployment', 3)
  operation = _messages.MessageField('GoogleCloudApihubV1ApiOperation', 4)
  spec = _messages.MessageField('GoogleCloudApihubV1Spec', 5)
  version = _messages.MessageField('GoogleCloudApihubV1Version', 6)


class GoogleCloudApihubV1ApiKeyConfig(_messages.Message):
  r"""Config for authentication with API key.

  Enums:
    HttpElementLocationValueValuesEnum: Required. The location of the API key.
      The default value is QUERY.

  Fields:
    apiKey: Required. The name of the SecretManager secret version resource
      storing the API key. Format:
      `projects/{project}/secrets/{secrete}/versions/{version}`. The
      `secretmanager.versions.access` permission should be granted to the
      service account accessing the secret.
    httpElementLocation: Required. The location of the API key. The default
      value is QUERY.
    name: Required. The parameter name of the API key. E.g. If the API request
      is "https://example.com/act?api_key=", "api_key" would be the parameter
      name.
  """

  class HttpElementLocationValueValuesEnum(_messages.Enum):
    r"""Required. The location of the API key. The default value is QUERY.

    Values:
      HTTP_ELEMENT_LOCATION_UNSPECIFIED: HTTP element location not specified.
      QUERY: Element is in the HTTP request query.
      HEADER: Element is in the HTTP request header.
      PATH: Element is in the HTTP request path.
      BODY: Element is in the HTTP request body.
      COOKIE: Element is in the HTTP request cookie.
    """
    HTTP_ELEMENT_LOCATION_UNSPECIFIED = 0
    QUERY = 1
    HEADER = 2
    PATH = 3
    BODY = 4
    COOKIE = 5

  apiKey = _messages.MessageField('GoogleCloudApihubV1Secret', 1)
  httpElementLocation = _messages.EnumField('HttpElementLocationValueValuesEnum', 2)
  name = _messages.StringField(3)


class GoogleCloudApihubV1ApiMetadataList(_messages.Message):
  r"""The message to hold repeated API metadata.

  Fields:
    apiMetadata: Required. The list of API metadata.
  """

  apiMetadata = _messages.MessageField('GoogleCloudApihubV1APIMetadata', 1, repeated=True)


class GoogleCloudApihubV1ApiOperation(_messages.Message):
  r"""Represents an operation contained in an API version in the API Hub. An
  operation is added/updated/deleted in an API version when a new spec is
  added or an existing spec is updated/deleted in a version. Currently, an
  operation will be created only corresponding to OpenAPI spec as parsing is
  supported for OpenAPI spec. Alternatively operations can be managed via
  create,update and delete APIs, creation of apiOperation can be possible only
  for version with no parsed operations and update/delete can be possible only
  for operations created via create API.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the API operation resource. The key is the attribute name. It will
      be of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    attributes: Optional. The list of user defined attributes associated with
      the API operation resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    createTime: Output only. The time at which the operation was created.
    details: Optional. Operation details. Note: Even though this field is
      optional, it is required for CreateApiOperation API and we will fail the
      request if not provided.
    name: Identifier. The name of the operation. Format: `projects/{project}/l
      ocations/{location}/apis/{api}/versions/{version}/operations/{operation}
      `
    sourceMetadata: Output only. The list of sources and metadata from the
      sources of the API operation.
    spec: Output only. The name of the spec will be of the format: `projects/{
      project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}
      ` Note:The name of the spec will be empty if the operation is created
      via CreateApiOperation API.
    updateTime: Output only. The time at which the operation was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the API
    operation resource. The key is the attribute name. It will be of the
    format: `projects/{project}/locations/{location}/attributes/{attribute}`.
    The value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleCloudApihubV1OperationDetails', 3)
  name = _messages.StringField(4)
  sourceMetadata = _messages.MessageField('GoogleCloudApihubV1SourceMetadata', 5, repeated=True)
  spec = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudApihubV1ApplicationIntegrationEndpointDetails(_messages.Message):
  r"""The details of the Application Integration endpoint to be triggered for
  curation.

  Fields:
    triggerId: Required. The API trigger ID of the Application Integration
      workflow.
    uri: Required. The endpoint URI should be a valid REST URI for triggering
      an Application Integration. Format: `https://integrations.googleapis.com
      /v1/{name=projects/*/locations/*/integrations/*}:execute` or `https://{l
      ocation}-
      integrations.googleapis.com/v1/{name=projects/*/locations/*/integrations
      /*}:execute`
  """

  triggerId = _messages.StringField(1)
  uri = _messages.StringField(2)


class GoogleCloudApihubV1Attribute(_messages.Message):
  r"""An attribute in the API Hub. An attribute is a name value pair which can
  be attached to different resources in the API hub based on the scope of the
  attribute. Attributes can either be pre-defined by the API Hub or created by
  users.

  Enums:
    DataTypeValueValuesEnum: Required. The type of the data of the attribute.
    DefinitionTypeValueValuesEnum: Output only. The definition type of the
      attribute.
    ScopeValueValuesEnum: Required. The scope of the attribute. It represents
      the resource in the API Hub to which the attribute can be linked.

  Fields:
    allowedValues: Optional. The list of allowed values when the attribute
      value is of type enum. This is required when the data_type of the
      attribute is ENUM. The maximum number of allowed values of an attribute
      will be 1000.
    cardinality: Optional. The maximum number of values that the attribute can
      have when associated with an API Hub resource. Cardinality 1 would
      represent a single-valued attribute. It must not be less than 1 or
      greater than 20. If not specified, the cardinality would be set to 1 by
      default and represent a single-valued attribute.
    createTime: Output only. The time at which the attribute was created.
    dataType: Required. The type of the data of the attribute.
    definitionType: Output only. The definition type of the attribute.
    description: Optional. The description of the attribute.
    displayName: Required. The display name of the attribute.
    mandatory: Output only. When mandatory is true, the attribute is mandatory
      for the resource specified in the scope. Only System defined attributes
      can be mandatory.
    name: Identifier. The name of the attribute in the API Hub. Format:
      `projects/{project}/locations/{location}/attributes/{attribute}`
    scope: Required. The scope of the attribute. It represents the resource in
      the API Hub to which the attribute can be linked.
    updateTime: Output only. The time at which the attribute was last updated.
  """

  class DataTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the data of the attribute.

    Values:
      DATA_TYPE_UNSPECIFIED: Attribute data type unspecified.
      ENUM: Attribute's value is of type enum.
      JSON: Attribute's value is of type json.
      STRING: Attribute's value is of type string.
      URI: Attribute's value is of type uri.
    """
    DATA_TYPE_UNSPECIFIED = 0
    ENUM = 1
    JSON = 2
    STRING = 3
    URI = 4

  class DefinitionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The definition type of the attribute.

    Values:
      DEFINITION_TYPE_UNSPECIFIED: Attribute definition type unspecified.
      SYSTEM_DEFINED: The attribute is predefined by the API Hub. Note that
        only the list of allowed values can be updated in this case via
        UpdateAttribute method.
      USER_DEFINED: The attribute is defined by the user.
    """
    DEFINITION_TYPE_UNSPECIFIED = 0
    SYSTEM_DEFINED = 1
    USER_DEFINED = 2

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Required. The scope of the attribute. It represents the resource in
    the API Hub to which the attribute can be linked.

    Values:
      SCOPE_UNSPECIFIED: Scope Unspecified.
      API: Attribute can be linked to an API.
      VERSION: Attribute can be linked to an API version.
      SPEC: Attribute can be linked to a Spec.
      API_OPERATION: Attribute can be linked to an API Operation.
      DEPLOYMENT: Attribute can be linked to a Deployment.
      DEPENDENCY: Attribute can be linked to a Dependency.
      DEFINITION: Attribute can be linked to a definition.
      EXTERNAL_API: Attribute can be linked to a ExternalAPI.
      PLUGIN: Attribute can be linked to a Plugin.
    """
    SCOPE_UNSPECIFIED = 0
    API = 1
    VERSION = 2
    SPEC = 3
    API_OPERATION = 4
    DEPLOYMENT = 5
    DEPENDENCY = 6
    DEFINITION = 7
    EXTERNAL_API = 8
    PLUGIN = 9

  allowedValues = _messages.MessageField('GoogleCloudApihubV1AllowedValue', 1, repeated=True)
  cardinality = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(3)
  dataType = _messages.EnumField('DataTypeValueValuesEnum', 4)
  definitionType = _messages.EnumField('DefinitionTypeValueValuesEnum', 5)
  description = _messages.StringField(6)
  displayName = _messages.StringField(7)
  mandatory = _messages.BooleanField(8)
  name = _messages.StringField(9)
  scope = _messages.EnumField('ScopeValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class GoogleCloudApihubV1AttributeValues(_messages.Message):
  r"""The attribute values associated with resource.

  Fields:
    attribute: Output only. The name of the attribute. Format:
      projects/{project}/locations/{location}/attributes/{attribute}
    enumValues: The attribute values associated with a resource in case
      attribute data type is enum.
    jsonValues: The attribute values associated with a resource in case
      attribute data type is JSON.
    stringValues: The attribute values associated with a resource in case
      attribute data type is string.
    uriValues: The attribute values associated with a resource in case
      attribute data type is URL, URI or IP, like gs://bucket-name/object-
      name.
  """

  attribute = _messages.StringField(1)
  enumValues = _messages.MessageField('GoogleCloudApihubV1EnumAttributeValues', 2)
  jsonValues = _messages.MessageField('GoogleCloudApihubV1StringAttributeValues', 3)
  stringValues = _messages.MessageField('GoogleCloudApihubV1StringAttributeValues', 4)
  uriValues = _messages.MessageField('GoogleCloudApihubV1StringAttributeValues', 5)


class GoogleCloudApihubV1AuthConfig(_messages.Message):
  r"""AuthConfig represents the authentication information.

  Enums:
    AuthTypeValueValuesEnum: Required. The authentication type.

  Fields:
    apiKeyConfig: Api Key Config.
    authType: Required. The authentication type.
    googleServiceAccountConfig: Google Service Account.
    oauth2ClientCredentialsConfig: Oauth2.0 Client Credentials.
    userPasswordConfig: User Password.
  """

  class AuthTypeValueValuesEnum(_messages.Enum):
    r"""Required. The authentication type.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication type not specified.
      NO_AUTH: No authentication.
      GOOGLE_SERVICE_ACCOUNT: Google service account authentication.
      USER_PASSWORD: Username and password authentication.
      API_KEY: API Key authentication.
      OAUTH2_CLIENT_CREDENTIALS: Oauth 2.0 client credentials grant
        authentication.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    NO_AUTH = 1
    GOOGLE_SERVICE_ACCOUNT = 2
    USER_PASSWORD = 3
    API_KEY = 4
    OAUTH2_CLIENT_CREDENTIALS = 5

  apiKeyConfig = _messages.MessageField('GoogleCloudApihubV1ApiKeyConfig', 1)
  authType = _messages.EnumField('AuthTypeValueValuesEnum', 2)
  googleServiceAccountConfig = _messages.MessageField('GoogleCloudApihubV1GoogleServiceAccountConfig', 3)
  oauth2ClientCredentialsConfig = _messages.MessageField('GoogleCloudApihubV1Oauth2ClientCredentialsConfig', 4)
  userPasswordConfig = _messages.MessageField('GoogleCloudApihubV1UserPasswordConfig', 5)


class GoogleCloudApihubV1AuthConfigTemplate(_messages.Message):
  r"""AuthConfigTemplate represents the authentication template for a plugin.

  Enums:
    SupportedAuthTypesValueListEntryValuesEnum:

  Fields:
    serviceAccount: Optional. The service account of the plugin hosting
      service. This service account should be granted the required permissions
      on the Auth Config parameters provided while creating the plugin
      instances corresponding to this plugin. For example, if the plugin
      instance auth config requires a secret manager secret, the service
      account should be granted the secretmanager.versions.access permission
      on the corresponding secret, if the plugin instance auth config contains
      a service account, the service account should be granted the
      iam.serviceAccounts.getAccessToken permission on the corresponding
      service account.
    supportedAuthTypes: Required. The list of authentication types supported
      by the plugin.
  """

  class SupportedAuthTypesValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedAuthTypesValueListEntryValuesEnum enum type.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication type not specified.
      NO_AUTH: No authentication.
      GOOGLE_SERVICE_ACCOUNT: Google service account authentication.
      USER_PASSWORD: Username and password authentication.
      API_KEY: API Key authentication.
      OAUTH2_CLIENT_CREDENTIALS: Oauth 2.0 client credentials grant
        authentication.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    NO_AUTH = 1
    GOOGLE_SERVICE_ACCOUNT = 2
    USER_PASSWORD = 3
    API_KEY = 4
    OAUTH2_CLIENT_CREDENTIALS = 5

  serviceAccount = _messages.MessageField('GoogleCloudApihubV1GoogleServiceAccountConfig', 1)
  supportedAuthTypes = _messages.EnumField('SupportedAuthTypesValueListEntryValuesEnum', 2, repeated=True)


class GoogleCloudApihubV1CollectApiDataRequest(_messages.Message):
  r"""The CollectApiData method's request.

  Enums:
    CollectionTypeValueValuesEnum: Required. The type of collection. Applies
      to all entries in api_data.

  Fields:
    actionId: Required. The action ID to be used for collecting the API data.
      This should map to one of the action IDs specified in action configs in
      the plugin.
    apiData: Required. The API data to be collected.
    collectionType: Required. The type of collection. Applies to all entries
      in api_data.
    pluginInstance: Required. The plugin instance collecting the API data.
      Format: `projects/{project}/locations/{location}/plugins/{plugin}/instan
      ces/{instance}`.
  """

  class CollectionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of collection. Applies to all entries in api_data.

    Values:
      COLLECTION_TYPE_UNSPECIFIED: The default value. This value is used if
        the collection type is omitted.
      COLLECTION_TYPE_UPSERT: The collection type is upsert. This should be
        used when an API is created or updated at the source.
      COLLECTION_TYPE_DELETE: The collection type is delete. This should be
        used when an API is deleted at the source.
    """
    COLLECTION_TYPE_UNSPECIFIED = 0
    COLLECTION_TYPE_UPSERT = 1
    COLLECTION_TYPE_DELETE = 2

  actionId = _messages.StringField(1)
  apiData = _messages.MessageField('GoogleCloudApihubV1ApiData', 2)
  collectionType = _messages.EnumField('CollectionTypeValueValuesEnum', 3)
  pluginInstance = _messages.StringField(4)


class GoogleCloudApihubV1Config(_messages.Message):
  r"""Available configurations to provision an ApiHub Instance.

  Enums:
    EncryptionTypeValueValuesEnum: Optional. Encryption type for the region.
      If the encryption type is CMEK, the cmek_key_name must be provided. If
      no encryption type is provided, GMEK will be used.

  Fields:
    cmekKeyName: Optional. The Customer Managed Encryption Key (CMEK) used for
      data encryption. The CMEK name should follow the format of `projects/([^
      /]+)/locations/([^/]+)/keyRings/([^/]+)/cryptoKeys/([^/]+)`, where the
      location must match the instance location. If the CMEK is not provided,
      a GMEK will be created for the instance.
    disableSearch: Optional. If true, the search will be disabled for the
      instance. The default value is false.
    encryptionType: Optional. Encryption type for the region. If the
      encryption type is CMEK, the cmek_key_name must be provided. If no
      encryption type is provided, GMEK will be used.
    vertexLocation: Optional. The name of the Vertex AI location where the
      data store is stored.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Encryption type for the region. If the encryption type is
    CMEK, the cmek_key_name must be provided. If no encryption type is
    provided, GMEK will be used.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: Encryption type unspecified.
      GMEK: Default encryption using Google managed encryption key.
      CMEK: Encryption using customer managed encryption key.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    GMEK = 1
    CMEK = 2

  cmekKeyName = _messages.StringField(1)
  disableSearch = _messages.BooleanField(2)
  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 3)
  vertexLocation = _messages.StringField(4)


class GoogleCloudApihubV1ConfigTemplate(_messages.Message):
  r"""ConfigTemplate represents the configuration template for a plugin.

  Fields:
    additionalConfigTemplate: Optional. The list of additional configuration
      variables for the plugin's configuration.
    authConfigTemplate: Optional. The authentication template for the plugin.
  """

  additionalConfigTemplate = _messages.MessageField('GoogleCloudApihubV1ConfigVariableTemplate', 1, repeated=True)
  authConfigTemplate = _messages.MessageField('GoogleCloudApihubV1AuthConfigTemplate', 2)


class GoogleCloudApihubV1ConfigValueOption(_messages.Message):
  r"""ConfigValueOption represents an option for a config variable of type
  enum or multi select.

  Fields:
    description: Optional. Description of the option.
    displayName: Required. Display name of the option.
    id: Required. Id of the option.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  id = _messages.StringField(3)


class GoogleCloudApihubV1ConfigVariable(_messages.Message):
  r"""ConfigVariable represents a additional configuration variable present in
  a PluginInstance Config or AuthConfig, based on a ConfigVariableTemplate.

  Fields:
    boolValue: Optional. The config variable value in case of config variable
      of type boolean.
    enumValue: Optional. The config variable value in case of config variable
      of type enum.
    intValue: Optional. The config variable value in case of config variable
      of type integer.
    key: Output only. Key will be the id to uniquely identify the config
      variable.
    multiIntValues: Optional. The config variable value in case of config
      variable of type multi integer.
    multiSelectValues: Optional. The config variable value in case of config
      variable of type multi select.
    multiStringValues: Optional. The config variable value in case of config
      variable of type multi string.
    secretValue: Optional. The config variable value in case of config
      variable of type secret.
    stringValue: Optional. The config variable value in case of config
      variable of type string.
  """

  boolValue = _messages.BooleanField(1)
  enumValue = _messages.MessageField('GoogleCloudApihubV1ConfigValueOption', 2)
  intValue = _messages.IntegerField(3)
  key = _messages.StringField(4)
  multiIntValues = _messages.MessageField('GoogleCloudApihubV1MultiIntValues', 5)
  multiSelectValues = _messages.MessageField('GoogleCloudApihubV1MultiSelectValues', 6)
  multiStringValues = _messages.MessageField('GoogleCloudApihubV1MultiStringValues', 7)
  secretValue = _messages.MessageField('GoogleCloudApihubV1Secret', 8)
  stringValue = _messages.StringField(9)


class GoogleCloudApihubV1ConfigVariableTemplate(_messages.Message):
  r"""ConfigVariableTemplate represents a configuration variable template
  present in a Plugin Config.

  Enums:
    ValueTypeValueValuesEnum: Required. Type of the parameter: string, int,
      bool etc.

  Fields:
    description: Optional. Description.
    enumOptions: Optional. Enum options. To be populated if `ValueType` is
      `ENUM`.
    id: Required. ID of the config variable. Must be unique within the
      configuration.
    multiSelectOptions: Optional. Multi select options. To be populated if
      `ValueType` is `MULTI_SELECT`.
    required: Optional. Flag represents that this `ConfigVariable` must be
      provided for a PluginInstance.
    validationRegex: Optional. Regular expression in RE2 syntax used for
      validating the `value` of a `ConfigVariable`.
    valueType: Required. Type of the parameter: string, int, bool etc.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of the parameter: string, int, bool etc.

    Values:
      VALUE_TYPE_UNSPECIFIED: Value type is not specified.
      STRING: Value type is string.
      INT: Value type is integer.
      BOOL: Value type is boolean.
      SECRET: Value type is secret.
      ENUM: Value type is enum.
      MULTI_SELECT: Value type is multi select.
      MULTI_STRING: Value type is multi string.
      MULTI_INT: Value type is multi int.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    STRING = 1
    INT = 2
    BOOL = 3
    SECRET = 4
    ENUM = 5
    MULTI_SELECT = 6
    MULTI_STRING = 7
    MULTI_INT = 8

  description = _messages.StringField(1)
  enumOptions = _messages.MessageField('GoogleCloudApihubV1ConfigValueOption', 2, repeated=True)
  id = _messages.StringField(3)
  multiSelectOptions = _messages.MessageField('GoogleCloudApihubV1ConfigValueOption', 4, repeated=True)
  required = _messages.BooleanField(5)
  validationRegex = _messages.StringField(6)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 7)


class GoogleCloudApihubV1Curation(_messages.Message):
  r"""A curation resource in the API Hub.

  Enums:
    LastExecutionErrorCodeValueValuesEnum: Output only. The error code of the
      last execution of the curation. The error code is populated only when
      the last execution state is failed.
    LastExecutionStateValueValuesEnum: Output only. The last execution state
      of the curation.

  Fields:
    createTime: Output only. The time at which the curation was created.
    description: Optional. The description of the curation.
    displayName: Required. The display name of the curation.
    endpoint: Required. The endpoint to be triggered for curation.
    lastExecutionErrorCode: Output only. The error code of the last execution
      of the curation. The error code is populated only when the last
      execution state is failed.
    lastExecutionErrorMessage: Output only. Error message describing the
      failure, if any, during the last execution of the curation.
    lastExecutionState: Output only. The last execution state of the curation.
    name: Identifier. The name of the curation. Format:
      `projects/{project}/locations/{location}/curations/{curation}`
    pluginInstanceActions: Output only. The plugin instances and associated
      actions that are using the curation. Note: A particular curation could
      be used by multiple plugin instances or multiple actions in a plugin
      instance.
    updateTime: Output only. The time at which the curation was last updated.
  """

  class LastExecutionErrorCodeValueValuesEnum(_messages.Enum):
    r"""Output only. The error code of the last execution of the curation. The
    error code is populated only when the last execution state is failed.

    Values:
      ERROR_CODE_UNSPECIFIED: Default unspecified error code.
      INTERNAL_ERROR: The execution failed due to an internal error.
      UNAUTHORIZED: The curation is not authorized to trigger the endpoint
        uri.
    """
    ERROR_CODE_UNSPECIFIED = 0
    INTERNAL_ERROR = 1
    UNAUTHORIZED = 2

  class LastExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. The last execution state of the curation.

    Values:
      LAST_EXECUTION_STATE_UNSPECIFIED: Default unspecified state.
      SUCCEEDED: The last curation execution was successful.
      FAILED: The last curation execution failed.
    """
    LAST_EXECUTION_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  endpoint = _messages.MessageField('GoogleCloudApihubV1Endpoint', 4)
  lastExecutionErrorCode = _messages.EnumField('LastExecutionErrorCodeValueValuesEnum', 5)
  lastExecutionErrorMessage = _messages.StringField(6)
  lastExecutionState = _messages.EnumField('LastExecutionStateValueValuesEnum', 7)
  name = _messages.StringField(8)
  pluginInstanceActions = _messages.MessageField('GoogleCloudApihubV1PluginInstanceActionID', 9, repeated=True)
  updateTime = _messages.StringField(10)


class GoogleCloudApihubV1CurationConfig(_messages.Message):
  r"""The curation information for this plugin instance.

  Enums:
    CurationTypeValueValuesEnum: Required. The curation type for this plugin
      instance.

  Fields:
    curationType: Required. The curation type for this plugin instance.
    customCuration: Optional. Custom curation information for this plugin
      instance.
  """

  class CurationTypeValueValuesEnum(_messages.Enum):
    r"""Required. The curation type for this plugin instance.

    Values:
      CURATION_TYPE_UNSPECIFIED: Default unspecified curation type.
      DEFAULT_CURATION_FOR_API_METADATA: Default curation for API metadata
        will be used.
      CUSTOM_CURATION_FOR_API_METADATA: Custom curation for API metadata will
        be used.
    """
    CURATION_TYPE_UNSPECIFIED = 0
    DEFAULT_CURATION_FOR_API_METADATA = 1
    CUSTOM_CURATION_FOR_API_METADATA = 2

  curationType = _messages.EnumField('CurationTypeValueValuesEnum', 1)
  customCuration = _messages.MessageField('GoogleCloudApihubV1CustomCuration', 2)


class GoogleCloudApihubV1CustomCuration(_messages.Message):
  r"""Custom curation information for this plugin instance.

  Fields:
    curation: Required. The unique name of the curation resource. This will be
      the name of the curation resource in the format:
      `projects/{project}/locations/{location}/curations/{curation}`
  """

  curation = _messages.StringField(1)


class GoogleCloudApihubV1Definition(_messages.Message):
  r"""Represents a definition for example schema, request, response
  definitions contained in an API version. A definition is
  added/updated/deleted in an API version when a new spec is added or an
  existing spec is updated/deleted in a version. Currently, definition will be
  created only corresponding to OpenAPI spec as parsing is supported for
  OpenAPI spec. Also, within OpenAPI spec, only `schema` object is supported.

  Enums:
    TypeValueValuesEnum: Output only. The type of the definition.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the definition resource. The key is the attribute name. It will be
      of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    attributes: Optional. The list of user defined attributes associated with
      the definition resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    createTime: Output only. The time at which the definition was created.
    name: Identifier. The name of the definition. Format: `projects/{project}/
      locations/{location}/apis/{api}/versions/{version}/definitions/{definiti
      on}`
    schema: Output only. The value of a schema definition.
    spec: Output only. The name of the spec from where the definition was
      parsed. Format is `projects/{project}/locations/{location}/apis/{api}/ve
      rsions/{version}/specs/{spec}`
    type: Output only. The type of the definition.
    updateTime: Output only. The time at which the definition was last
      updated.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the definition.

    Values:
      TYPE_UNSPECIFIED: Definition type unspecified.
      SCHEMA: Definition type schema.
    """
    TYPE_UNSPECIFIED = 0
    SCHEMA = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    definition resource. The key is the attribute name. It will be of the
    format: `projects/{project}/locations/{location}/attributes/{attribute}`.
    The value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  createTime = _messages.StringField(2)
  name = _messages.StringField(3)
  schema = _messages.MessageField('GoogleCloudApihubV1Schema', 4)
  spec = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class GoogleCloudApihubV1Dependency(_messages.Message):
  r"""A dependency resource defined in the API hub describes a dependency
  directed from a consumer to a supplier entity. A dependency can be defined
  between two Operations or between an Operation and External API.

  Enums:
    DiscoveryModeValueValuesEnum: Output only. Discovery mode of the
      dependency.
    StateValueValuesEnum: Output only. State of the dependency.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the dependency resource. The key is the attribute name. It will be
      of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    attributes: Optional. The list of user defined attributes associated with
      the dependency resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    consumer: Required. Immutable. The entity acting as the consumer in the
      dependency.
    createTime: Output only. The time at which the dependency was created.
    description: Optional. Human readable description corresponding of the
      dependency.
    discoveryMode: Output only. Discovery mode of the dependency.
    errorDetail: Output only. Error details of a dependency if the system has
      detected it internally.
    name: Identifier. The name of the dependency in the API Hub. Format:
      `projects/{project}/locations/{location}/dependencies/{dependency}`
    state: Output only. State of the dependency.
    supplier: Required. Immutable. The entity acting as the supplier in the
      dependency.
    updateTime: Output only. The time at which the dependency was last
      updated.
  """

  class DiscoveryModeValueValuesEnum(_messages.Enum):
    r"""Output only. Discovery mode of the dependency.

    Values:
      DISCOVERY_MODE_UNSPECIFIED: Default value. This value is unused.
      MANUAL: Manual mode of discovery when the dependency is defined by the
        user.
    """
    DISCOVERY_MODE_UNSPECIFIED = 0
    MANUAL = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the dependency.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PROPOSED: Dependency will be in a proposed state when it is newly
        identified by the API hub on its own.
      VALIDATED: Dependency will be in a validated state when it is validated
        by the admin or manually created in the API hub.
    """
    STATE_UNSPECIFIED = 0
    PROPOSED = 1
    VALIDATED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    dependency resource. The key is the attribute name. It will be of the
    format: `projects/{project}/locations/{location}/attributes/{attribute}`.
    The value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  consumer = _messages.MessageField('GoogleCloudApihubV1DependencyEntityReference', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  discoveryMode = _messages.EnumField('DiscoveryModeValueValuesEnum', 5)
  errorDetail = _messages.MessageField('GoogleCloudApihubV1DependencyErrorDetail', 6)
  name = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  supplier = _messages.MessageField('GoogleCloudApihubV1DependencyEntityReference', 9)
  updateTime = _messages.StringField(10)


class GoogleCloudApihubV1DependencyEntityReference(_messages.Message):
  r"""Reference to an entity participating in a dependency.

  Fields:
    displayName: Output only. Display name of the entity.
    externalApiResourceName: The resource name of an external API in the API
      Hub. Format:
      `projects/{project}/locations/{location}/externalApis/{external_api}`
    operationResourceName: The resource name of an operation in the API Hub.
      Format: `projects/{project}/locations/{location}/apis/{api}/versions/{ve
      rsion}/operations/{operation}`
  """

  displayName = _messages.StringField(1)
  externalApiResourceName = _messages.StringField(2)
  operationResourceName = _messages.StringField(3)


class GoogleCloudApihubV1DependencyErrorDetail(_messages.Message):
  r"""Details describing error condition of a dependency.

  Enums:
    ErrorValueValuesEnum: Optional. Error in the dependency.

  Fields:
    error: Optional. Error in the dependency.
    errorTime: Optional. Timestamp at which the error was found.
  """

  class ErrorValueValuesEnum(_messages.Enum):
    r"""Optional. Error in the dependency.

    Values:
      ERROR_UNSPECIFIED: Default value used for no error in the dependency.
      SUPPLIER_NOT_FOUND: Supplier entity has been deleted.
      SUPPLIER_RECREATED: Supplier entity has been recreated.
    """
    ERROR_UNSPECIFIED = 0
    SUPPLIER_NOT_FOUND = 1
    SUPPLIER_RECREATED = 2

  error = _messages.EnumField('ErrorValueValuesEnum', 1)
  errorTime = _messages.StringField(2)


class GoogleCloudApihubV1Deployment(_messages.Message):
  r"""Details of the deployment where APIs are hosted. A deployment could
  represent an Apigee proxy, API gateway, other Google Cloud services or non-
  Google Cloud services as well. A deployment entity is a root level entity in
  the API hub and exists independent of any API.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the deployment resource. The key is the attribute name. It will be
      of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    apiVersions: Output only. The API versions linked to this deployment.
      Note: A particular deployment could be linked to multiple different API
      versions (of same or different APIs).
    attributes: Optional. The list of user defined attributes associated with
      the deployment resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    createTime: Output only. The time at which the deployment was created.
    deploymentType: Required. The type of deployment. This maps to the
      following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-deployment-
      type` attribute. The number of values for this attribute will be based
      on the cardinality of the attribute. The same can be retrieved via
      GetAttribute API. All values should be from the list of allowed values
      defined for the attribute.
    description: Optional. The description of the deployment.
    displayName: Required. The display name of the deployment.
    documentation: Optional. The documentation of the deployment.
    endpoints: Required. The endpoints at which this deployment resource is
      listening for API requests. This could be a list of complete URIs,
      hostnames or an IP addresses.
    environment: Optional. The environment mapping to this deployment. This
      maps to the following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-environment`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    name: Identifier. The name of the deployment. Format:
      `projects/{project}/locations/{location}/deployments/{deployment}`
    resourceUri: Required. A uri that uniquely identfies the deployment within
      a particular gateway. For example, if the runtime resource is of type
      APIGEE_PROXY, then this field will be a combination of org, proxy name
      and environment.
    slo: Optional. The SLO for this deployment. This maps to the following
      system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-slo`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    sourceMetadata: Output only. The list of sources and metadata from the
      sources of the deployment.
    updateTime: Output only. The time at which the deployment was last
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    deployment resource. The key is the attribute name. It will be of the
    format: `projects/{project}/locations/{location}/attributes/{attribute}`.
    The value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiVersions = _messages.StringField(1, repeated=True)
  attributes = _messages.MessageField('AttributesValue', 2)
  createTime = _messages.StringField(3)
  deploymentType = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 4)
  description = _messages.StringField(5)
  displayName = _messages.StringField(6)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 7)
  endpoints = _messages.StringField(8, repeated=True)
  environment = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 9)
  name = _messages.StringField(10)
  resourceUri = _messages.StringField(11)
  slo = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 12)
  sourceMetadata = _messages.MessageField('GoogleCloudApihubV1SourceMetadata', 13, repeated=True)
  updateTime = _messages.StringField(14)


class GoogleCloudApihubV1DeploymentMetadata(_messages.Message):
  r"""The metadata associated with a deployment.

  Fields:
    deployment: Required. The deployment resource to be pushed to Hub's
      collect layer. The ID of the deployment will be generated by Hub.
    originalCreateTime: Optional. Timestamp indicating when the deployment was
      created at the source.
    originalId: Optional. The unique identifier of the deployment in the
      system where it was originally created.
    originalUpdateTime: Required. Timestamp indicating when the deployment was
      last updated at the source.
  """

  deployment = _messages.MessageField('GoogleCloudApihubV1Deployment', 1)
  originalCreateTime = _messages.StringField(2)
  originalId = _messages.StringField(3)
  originalUpdateTime = _messages.StringField(4)


class GoogleCloudApihubV1DisablePluginInstanceActionRequest(_messages.Message):
  r"""The DisablePluginInstanceAction method's request.

  Fields:
    actionId: Required. The action id to disable.
  """

  actionId = _messages.StringField(1)


class GoogleCloudApihubV1DisablePluginRequest(_messages.Message):
  r"""The DisablePlugin method's request."""


class GoogleCloudApihubV1Documentation(_messages.Message):
  r"""Documentation details.

  Fields:
    externalUri: Optional. The uri of the externally hosted documentation.
  """

  externalUri = _messages.StringField(1)


class GoogleCloudApihubV1EnablePluginInstanceActionRequest(_messages.Message):
  r"""The EnablePluginInstanceAction method's request.

  Fields:
    actionId: Required. The action id to enable.
  """

  actionId = _messages.StringField(1)


class GoogleCloudApihubV1EnablePluginRequest(_messages.Message):
  r"""The EnablePlugin method's request."""


class GoogleCloudApihubV1Endpoint(_messages.Message):
  r"""The endpoint to be triggered for curation. The endpoint will be invoked
  with a request payload containing ApiMetadata. Response should contain
  curated data in the form of ApiMetadata.

  Fields:
    applicationIntegrationEndpointDetails: Required. The details of the
      Application Integration endpoint to be triggered for curation.
  """

  applicationIntegrationEndpointDetails = _messages.MessageField('GoogleCloudApihubV1ApplicationIntegrationEndpointDetails', 1)


class GoogleCloudApihubV1EnumAttributeValues(_messages.Message):
  r"""The attribute values of data type enum.

  Fields:
    values: Required. The attribute values in case attribute data type is
      enum.
  """

  values = _messages.MessageField('GoogleCloudApihubV1AllowedValue', 1, repeated=True)


class GoogleCloudApihubV1ExecutePluginInstanceActionRequest(_messages.Message):
  r"""The ExecutePluginInstanceAction method's request.

  Fields:
    actionExecutionDetail: Required. The execution details for the action to
      execute.
  """

  actionExecutionDetail = _messages.MessageField('GoogleCloudApihubV1ActionExecutionDetail', 1)


class GoogleCloudApihubV1ExecutionStatus(_messages.Message):
  r"""The execution status for the plugin instance.

  Enums:
    CurrentExecutionStateValueValuesEnum: Output only. The current state of
      the execution.

  Fields:
    currentExecutionState: Output only. The current state of the execution.
    lastExecution: Output only. The last execution of the plugin instance.
  """

  class CurrentExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the execution.

    Values:
      CURRENT_EXECUTION_STATE_UNSPECIFIED: Default unspecified execution
        state.
      RUNNING: The plugin instance is executing.
      NOT_RUNNING: The plugin instance is not running an execution.
    """
    CURRENT_EXECUTION_STATE_UNSPECIFIED = 0
    RUNNING = 1
    NOT_RUNNING = 2

  currentExecutionState = _messages.EnumField('CurrentExecutionStateValueValuesEnum', 1)
  lastExecution = _messages.MessageField('GoogleCloudApihubV1LastExecution', 2)


class GoogleCloudApihubV1ExternalApi(_messages.Message):
  r"""An external API represents an API being provided by external sources.
  This can be used to model third-party APIs and can be used to define
  dependencies.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the Version resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    attributes: Optional. The list of user defined attributes associated with
      the Version resource. The key is the attribute name. It will be of the
      format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    createTime: Output only. Creation timestamp.
    description: Optional. Description of the external API. Max length is 2000
      characters (Unicode Code Points).
    displayName: Required. Display name of the external API. Max length is 63
      characters (Unicode Code Points).
    documentation: Optional. Documentation of the external API.
    endpoints: Optional. List of endpoints on which this API is accessible.
    name: Identifier. Format:
      `projects/{project}/locations/{location}/externalApi/{externalApi}`.
    paths: Optional. List of paths served by this API.
    updateTime: Output only. Last update timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    Version resource. The key is the attribute name. It will be of the format:
    `projects/{project}/locations/{location}/attributes/{attribute}`. The
    value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 5)
  endpoints = _messages.StringField(6, repeated=True)
  name = _messages.StringField(7)
  paths = _messages.StringField(8, repeated=True)
  updateTime = _messages.StringField(9)


class GoogleCloudApihubV1GoogleServiceAccountConfig(_messages.Message):
  r"""Config for Google service account authentication.

  Fields:
    serviceAccount: Required. The service account to be used for
      authenticating request. The `iam.serviceAccounts.getAccessToken`
      permission should be granted on this service account to the impersonator
      service account.
  """

  serviceAccount = _messages.StringField(1)


class GoogleCloudApihubV1HostProjectRegistration(_messages.Message):
  r"""Host project registration refers to the registration of a Google cloud
  project with Api Hub as a host project. This is the project where Api Hub is
  provisioned. It acts as the consumer project for the Api Hub instance
  provisioned. Multiple runtime projects can be attached to the host project
  and these attachments define the scope of Api Hub.

  Fields:
    createTime: Output only. The time at which the host project registration
      was created.
    gcpProject: Required. Immutable. Google cloud project name in the format:
      "projects/abc" or "projects/123". As input, project name with either
      project id or number are accepted. As output, this field will contain
      project number.
    name: Identifier. The name of the host project registration. Format: "proj
      ects/{project}/locations/{location}/hostProjectRegistrations/{host_proje
      ct_registration}".
  """

  createTime = _messages.StringField(1)
  gcpProject = _messages.StringField(2)
  name = _messages.StringField(3)


class GoogleCloudApihubV1HostingService(_messages.Message):
  r"""The information related to the service implemented by the plugin
  developer, used to invoke the plugin's functionality.

  Fields:
    serviceUri: Optional. The URI of the service implemented by the plugin
      developer, used to invoke the plugin's functionality. This information
      is only required for user defined plugins.
  """

  serviceUri = _messages.StringField(1)


class GoogleCloudApihubV1HttpOperation(_messages.Message):
  r"""The HTTP Operation.

  Enums:
    MethodValueValuesEnum: Optional. Operation method Note: Even though this
      field is optional, it is required for CreateApiOperation API and we will
      fail the request if not provided.

  Fields:
    method: Optional. Operation method Note: Even though this field is
      optional, it is required for CreateApiOperation API and we will fail the
      request if not provided.
    path: Optional. The path details for the Operation. Note: Even though this
      field is optional, it is required for CreateApiOperation API and we will
      fail the request if not provided.
  """

  class MethodValueValuesEnum(_messages.Enum):
    r"""Optional. Operation method Note: Even though this field is optional,
    it is required for CreateApiOperation API and we will fail the request if
    not provided.

    Values:
      METHOD_UNSPECIFIED: Method unspecified.
      GET: Get Operation type.
      PUT: Put Operation type.
      POST: Post Operation type.
      DELETE: Delete Operation type.
      OPTIONS: Options Operation type.
      HEAD: Head Operation type.
      PATCH: Patch Operation type.
      TRACE: Trace Operation type.
    """
    METHOD_UNSPECIFIED = 0
    GET = 1
    PUT = 2
    POST = 3
    DELETE = 4
    OPTIONS = 5
    HEAD = 6
    PATCH = 7
    TRACE = 8

  method = _messages.EnumField('MethodValueValuesEnum', 1)
  path = _messages.MessageField('GoogleCloudApihubV1Path', 2)


class GoogleCloudApihubV1Issue(_messages.Message):
  r"""Issue contains the details of a single issue found by the linter.

  Enums:
    SeverityValueValuesEnum: Required. Severity level of the rule violation.

  Fields:
    code: Required. Rule code unique to each rule defined in linter.
    message: Required. Human-readable message describing the issue found by
      the linter.
    path: Required. An array of strings indicating the location in the
      analyzed document where the rule was triggered.
    range: Required. Object describing where in the file the issue was found.
    severity: Required. Severity level of the rule violation.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. Severity level of the rule violation.

    Values:
      SEVERITY_UNSPECIFIED: Severity unspecified.
      SEVERITY_ERROR: Severity error.
      SEVERITY_WARNING: Severity warning.
      SEVERITY_INFO: Severity info.
      SEVERITY_HINT: Severity hint.
    """
    SEVERITY_UNSPECIFIED = 0
    SEVERITY_ERROR = 1
    SEVERITY_WARNING = 2
    SEVERITY_INFO = 3
    SEVERITY_HINT = 4

  code = _messages.StringField(1)
  message = _messages.StringField(2)
  path = _messages.StringField(3, repeated=True)
  range = _messages.MessageField('GoogleCloudApihubV1Range', 4)
  severity = _messages.EnumField('SeverityValueValuesEnum', 5)


class GoogleCloudApihubV1LastExecution(_messages.Message):
  r"""The result of the last execution of the plugin instance.

  Enums:
    ResultValueValuesEnum: Output only. The result of the last execution of
      the plugin instance.

  Fields:
    endTime: Output only. The last execution end time of the plugin instance.
    errorMessage: Output only. Error message describing the failure, if any,
      during the last execution.
    result: Output only. The result of the last execution of the plugin
      instance.
    startTime: Output only. The last execution start time of the plugin
      instance.
  """

  class ResultValueValuesEnum(_messages.Enum):
    r"""Output only. The result of the last execution of the plugin instance.

    Values:
      RESULT_UNSPECIFIED: Default unspecified execution result.
      SUCCEEDED: The plugin instance executed successfully.
      FAILED: The plugin instance execution failed.
    """
    RESULT_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2

  endTime = _messages.StringField(1)
  errorMessage = _messages.StringField(2)
  result = _messages.EnumField('ResultValueValuesEnum', 3)
  startTime = _messages.StringField(4)


class GoogleCloudApihubV1LintResponse(_messages.Message):
  r"""LintResponse contains the response from the linter.

  Enums:
    LinterValueValuesEnum: Required. Name of the linter used.
    StateValueValuesEnum: Required. Lint state represents success or failure
      for linting.

  Fields:
    createTime: Required. Timestamp when the linting response was generated.
    issues: Optional. Array of issues found in the analyzed document.
    linter: Required. Name of the linter used.
    source: Required. Name of the linting application.
    state: Required. Lint state represents success or failure for linting.
    summary: Optional. Summary of all issue types and counts for each severity
      level.
  """

  class LinterValueValuesEnum(_messages.Enum):
    r"""Required. Name of the linter used.

    Values:
      LINTER_UNSPECIFIED: Linter type unspecified.
      SPECTRAL: Linter type spectral.
      OTHER: Linter type other.
    """
    LINTER_UNSPECIFIED = 0
    SPECTRAL = 1
    OTHER = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Lint state represents success or failure for linting.

    Values:
      LINT_STATE_UNSPECIFIED: Lint state unspecified.
      LINT_STATE_SUCCESS: Linting was completed successfully.
      LINT_STATE_ERROR: Linting encountered errors.
    """
    LINT_STATE_UNSPECIFIED = 0
    LINT_STATE_SUCCESS = 1
    LINT_STATE_ERROR = 2

  createTime = _messages.StringField(1)
  issues = _messages.MessageField('GoogleCloudApihubV1Issue', 2, repeated=True)
  linter = _messages.EnumField('LinterValueValuesEnum', 3)
  source = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  summary = _messages.MessageField('GoogleCloudApihubV1SummaryEntry', 6, repeated=True)


class GoogleCloudApihubV1LintSpecRequest(_messages.Message):
  r"""The LintSpec method's request."""


class GoogleCloudApihubV1ListApiOperationsResponse(_messages.Message):
  r"""The ListApiOperations method's response.

  Fields:
    apiOperations: The operations corresponding to an API version.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  apiOperations = _messages.MessageField('GoogleCloudApihubV1ApiOperation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListApisResponse(_messages.Message):
  r"""The ListApis method's response.

  Fields:
    apis: The API resources present in the API hub.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  apis = _messages.MessageField('GoogleCloudApihubV1Api', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListAttributesResponse(_messages.Message):
  r"""The ListAttributes method's response.

  Fields:
    attributes: The list of all attributes.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  attributes = _messages.MessageField('GoogleCloudApihubV1Attribute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListCurationsResponse(_messages.Message):
  r"""The ListCurations method's response.

  Fields:
    curations: The curation resources present in the API hub.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  curations = _messages.MessageField('GoogleCloudApihubV1Curation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListDependenciesResponse(_messages.Message):
  r"""The ListDependencies method's response.

  Fields:
    dependencies: The dependency resources present in the API hub.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  dependencies = _messages.MessageField('GoogleCloudApihubV1Dependency', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListDeploymentsResponse(_messages.Message):
  r"""The ListDeployments method's response.

  Fields:
    deployments: The deployment resources present in the API hub.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  deployments = _messages.MessageField('GoogleCloudApihubV1Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListExternalApisResponse(_messages.Message):
  r"""The ListExternalApis method's response.

  Fields:
    externalApis: The External API resources present in the API hub.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  externalApis = _messages.MessageField('GoogleCloudApihubV1ExternalApi', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListHostProjectRegistrationsResponse(_messages.Message):
  r"""The ListHostProjectRegistrations method's response.

  Fields:
    hostProjectRegistrations: The list of host project registrations.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  hostProjectRegistrations = _messages.MessageField('GoogleCloudApihubV1HostProjectRegistration', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApihubV1ListPluginInstancesResponse(_messages.Message):
  r"""The ListPluginInstances method's response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    pluginInstances: The plugin instances from the specified parent resource.
  """

  nextPageToken = _messages.StringField(1)
  pluginInstances = _messages.MessageField('GoogleCloudApihubV1PluginInstance', 2, repeated=True)


class GoogleCloudApihubV1ListPluginsResponse(_messages.Message):
  r"""The ListPlugins method's response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    plugins: The plugins from the specified parent resource.
  """

  nextPageToken = _messages.StringField(1)
  plugins = _messages.MessageField('GoogleCloudApihubV1Plugin', 2, repeated=True)


class GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse(_messages.Message):
  r"""The ListRuntimeProjectAttachments method's response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    runtimeProjectAttachments: List of runtime project attachments.
  """

  nextPageToken = _messages.StringField(1)
  runtimeProjectAttachments = _messages.MessageField('GoogleCloudApihubV1RuntimeProjectAttachment', 2, repeated=True)


class GoogleCloudApihubV1ListSpecsResponse(_messages.Message):
  r"""The ListSpecs method's response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    specs: The specs corresponding to an API Version.
  """

  nextPageToken = _messages.StringField(1)
  specs = _messages.MessageField('GoogleCloudApihubV1Spec', 2, repeated=True)


class GoogleCloudApihubV1ListVersionsResponse(_messages.Message):
  r"""The ListVersions method's response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    versions: The versions corresponding to an API.
  """

  nextPageToken = _messages.StringField(1)
  versions = _messages.MessageField('GoogleCloudApihubV1Version', 2, repeated=True)


class GoogleCloudApihubV1LookupApiHubInstanceResponse(_messages.Message):
  r"""The LookupApiHubInstance method's response.`

  Fields:
    apiHubInstance: API Hub instance for a project if it exists, empty
      otherwise.
  """

  apiHubInstance = _messages.MessageField('GoogleCloudApihubV1ApiHubInstance', 1)


class GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse(_messages.Message):
  r"""The ListRuntimeProjectAttachments method's response.

  Fields:
    runtimeProjectAttachment: Runtime project attachment for a project if
      exists, empty otherwise.
  """

  runtimeProjectAttachment = _messages.MessageField('GoogleCloudApihubV1RuntimeProjectAttachment', 1)


class GoogleCloudApihubV1MultiIntValues(_messages.Message):
  r"""The config variable value of data type multi int.

  Fields:
    values: Optional. The config variable value of data type multi int.
  """

  values = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)


class GoogleCloudApihubV1MultiSelectValues(_messages.Message):
  r"""The config variable value of data type multi select.

  Fields:
    values: Optional. The config variable value of data type multi select.
  """

  values = _messages.MessageField('GoogleCloudApihubV1ConfigValueOption', 1, repeated=True)


class GoogleCloudApihubV1MultiStringValues(_messages.Message):
  r"""The config variable value of data type multi string.

  Fields:
    values: Optional. The config variable value of data type multi string.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudApihubV1Oauth2ClientCredentialsConfig(_messages.Message):
  r"""Parameters to support Oauth 2.0 client credentials grant authentication.
  See https://tools.ietf.org/html/rfc6749#section-1.3.4 for more details.

  Fields:
    clientId: Required. The client identifier.
    clientSecret: Required. Secret version reference containing the client
      secret. The `secretmanager.versions.access` permission should be granted
      to the service account accessing the secret.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.MessageField('GoogleCloudApihubV1Secret', 2)


class GoogleCloudApihubV1OpenApiSpecDetails(_messages.Message):
  r"""OpenApiSpecDetails contains the details parsed from an OpenAPI spec in
  addition to the fields mentioned in SpecDetails.

  Enums:
    FormatValueValuesEnum: Output only. The format of the spec.

  Fields:
    format: Output only. The format of the spec.
    owner: Output only. Owner details for the spec. This maps to
      `info.contact` in OpenAPI spec.
    version: Output only. The version in the spec. This maps to `info.version`
      in OpenAPI spec.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The format of the spec.

    Values:
      FORMAT_UNSPECIFIED: SpecFile type unspecified.
      OPEN_API_SPEC_2_0: OpenAPI Spec v2.0.
      OPEN_API_SPEC_3_0: OpenAPI Spec v3.0.
      OPEN_API_SPEC_3_1: OpenAPI Spec v3.1.
    """
    FORMAT_UNSPECIFIED = 0
    OPEN_API_SPEC_2_0 = 1
    OPEN_API_SPEC_3_0 = 2
    OPEN_API_SPEC_3_1 = 3

  format = _messages.EnumField('FormatValueValuesEnum', 1)
  owner = _messages.MessageField('GoogleCloudApihubV1Owner', 2)
  version = _messages.StringField(3)


class GoogleCloudApihubV1OperationDetails(_messages.Message):
  r"""The operation details parsed from the spec.

  Fields:
    deprecated: Optional. For OpenAPI spec, this will be set if
      `operation.deprecated`is marked as `true` in the spec.
    description: Optional. Description of the operation behavior. For OpenAPI
      spec, this will map to `operation.description` in the spec, in case
      description is empty, `operation.summary` will be used.
    documentation: Optional. Additional external documentation for this
      operation. For OpenAPI spec, this will map to `operation.documentation`
      in the spec.
    httpOperation: The HTTP Operation.
  """

  deprecated = _messages.BooleanField(1)
  description = _messages.StringField(2)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 3)
  httpOperation = _messages.MessageField('GoogleCloudApihubV1HttpOperation', 4)


class GoogleCloudApihubV1OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudApihubV1Owner(_messages.Message):
  r"""Owner details.

  Fields:
    displayName: Optional. The name of the owner.
    email: Required. The email of the owner.
  """

  displayName = _messages.StringField(1)
  email = _messages.StringField(2)


class GoogleCloudApihubV1Path(_messages.Message):
  r"""The path details derived from the spec.

  Fields:
    description: Optional. A short description for the path applicable to all
      operations.
    path: Optional. Complete path relative to server endpoint. Note: Even
      though this field is optional, it is required for CreateApiOperation API
      and we will fail the request if not provided.
  """

  description = _messages.StringField(1)
  path = _messages.StringField(2)


class GoogleCloudApihubV1Plugin(_messages.Message):
  r"""A plugin resource in the API Hub.

  Enums:
    OwnershipTypeValueValuesEnum: Output only. The type of the plugin,
      indicating whether it is 'SYSTEM_OWNED' or 'USER_OWNED'.
    PluginCategoryValueValuesEnum: Optional. The category of the plugin,
      identifying its primary category or purpose. This field is required for
      all plugins.
    StateValueValuesEnum: Output only. Represents the state of the plugin.
      Note this field will not be set for plugins developed via plugin
      framework as the state will be managed at plugin instance level.

  Fields:
    actionsConfig: Optional. The configuration of actions supported by the
      plugin.
    configTemplate: Optional. The configuration template for the plugin.
    createTime: Output only. Timestamp indicating when the plugin was created.
    description: Optional. The plugin description. Max length is 2000
      characters (Unicode code points).
    displayName: Required. The display name of the plugin. Max length is 50
      characters (Unicode code points).
    documentation: Optional. The documentation of the plugin, that explains
      how to set up and use the plugin.
    hostingService: Optional. This field is optional. It is used to notify the
      plugin hosting service for any lifecycle changes of the plugin instance
      and trigger execution of plugin instance actions in case of API hub
      managed actions. This field should be provided if the plugin instance
      lifecycle of the developed plugin needs to be managed from API hub.
      Also, in this case the plugin hosting service interface needs to be
      implemented. This field should not be provided if the plugin wants to
      manage plugin instance lifecycle events outside of hub interface and use
      plugin framework for only registering of plugin and plugin instances to
      capture the source of data into hub. Note, in this case the plugin
      hosting service interface is not required to be implemented. Also, the
      plugin instance lifecycle actions will be disabled from API hub's UI.
    name: Identifier. The name of the plugin. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}`
    ownershipType: Output only. The type of the plugin, indicating whether it
      is 'SYSTEM_OWNED' or 'USER_OWNED'.
    pluginCategory: Optional. The category of the plugin, identifying its
      primary category or purpose. This field is required for all plugins.
    state: Output only. Represents the state of the plugin. Note this field
      will not be set for plugins developed via plugin framework as the state
      will be managed at plugin instance level.
    type: Optional. The type of the API. This maps to the following system
      defined attribute:
      `projects/{project}/locations/{location}/attributes/system-plugin-type`
      attribute. The number of allowed values for this attribute will be based
      on the cardinality of the attribute. The same can be retrieved via
      GetAttribute API. All values should be from the list of allowed values
      defined for the attribute. Note this field is not required for plugins
      developed via plugin framework.
    updateTime: Output only. Timestamp indicating when the plugin was last
      updated.
  """

  class OwnershipTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the plugin, indicating whether it is
    'SYSTEM_OWNED' or 'USER_OWNED'.

    Values:
      OWNERSHIP_TYPE_UNSPECIFIED: Default unspecified type.
      SYSTEM_OWNED: System owned plugins are defined by API hub and are
        available out of the box in API hub.
      USER_OWNED: User owned plugins are defined by the user and need to be
        explicitly added to API hub via CreatePlugin method.
    """
    OWNERSHIP_TYPE_UNSPECIFIED = 0
    SYSTEM_OWNED = 1
    USER_OWNED = 2

  class PluginCategoryValueValuesEnum(_messages.Enum):
    r"""Optional. The category of the plugin, identifying its primary category
    or purpose. This field is required for all plugins.

    Values:
      PLUGIN_CATEGORY_UNSPECIFIED: Default unspecified plugin type.
      API_GATEWAY: API_GATEWAY plugins represent plugins built for API
        Gateways like Apigee.
      API_PRODUCER: API_PRODUCER plugins represent plugins built for API
        Producers like Cloud Run, Application Integration etc.
    """
    PLUGIN_CATEGORY_UNSPECIFIED = 0
    API_GATEWAY = 1
    API_PRODUCER = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents the state of the plugin. Note this field will
    not be set for plugins developed via plugin framework as the state will be
    managed at plugin instance level.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      ENABLED: The plugin is enabled.
      DISABLED: The plugin is disabled.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  actionsConfig = _messages.MessageField('GoogleCloudApihubV1PluginActionConfig', 1, repeated=True)
  configTemplate = _messages.MessageField('GoogleCloudApihubV1ConfigTemplate', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 6)
  hostingService = _messages.MessageField('GoogleCloudApihubV1HostingService', 7)
  name = _messages.StringField(8)
  ownershipType = _messages.EnumField('OwnershipTypeValueValuesEnum', 9)
  pluginCategory = _messages.EnumField('PluginCategoryValueValuesEnum', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  type = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 12)
  updateTime = _messages.StringField(13)


class GoogleCloudApihubV1PluginActionConfig(_messages.Message):
  r"""PluginActionConfig represents the configuration of an action supported
  by a plugin.

  Enums:
    TriggerModeValueValuesEnum: Required. The trigger mode supported by the
      action.

  Fields:
    description: Required. The description of the operation performed by the
      action.
    displayName: Required. The display name of the action.
    id: Required. The id of the action.
    triggerMode: Required. The trigger mode supported by the action.
  """

  class TriggerModeValueValuesEnum(_messages.Enum):
    r"""Required. The trigger mode supported by the action.

    Values:
      TRIGGER_MODE_UNSPECIFIED: Default unspecified mode.
      API_HUB_ON_DEMAND_TRIGGER: This action can be executed by invoking
        ExecutePluginInstanceAction API with the given action id. To support
        this, the plugin hosting service should handle this action id as part
        of execute call.
      API_HUB_SCHEDULE_TRIGGER: This action will be executed on schedule by
        invoking ExecutePluginInstanceAction API with the given action id. To
        set the schedule, the user can provide the cron expression in the
        PluginAction field for a given plugin instance. To support this, the
        plugin hosting service should handle this action id as part of execute
        call. Note, on demand execution will be supported by default in this
        trigger mode.
      NON_API_HUB_MANAGED: The execution of this plugin is not handled by API
        hub. In this case, the plugin hosting service need not handle this
        action id as part of the execute call.
    """
    TRIGGER_MODE_UNSPECIFIED = 0
    API_HUB_ON_DEMAND_TRIGGER = 1
    API_HUB_SCHEDULE_TRIGGER = 2
    NON_API_HUB_MANAGED = 3

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  id = _messages.StringField(3)
  triggerMode = _messages.EnumField('TriggerModeValueValuesEnum', 4)


class GoogleCloudApihubV1PluginInstance(_messages.Message):
  r"""Represents a plugin instance resource in the API Hub. A PluginInstance
  is a specific instance of a hub plugin with its own configuration, state,
  and execution details.

  Enums:
    StateValueValuesEnum: Output only. The current state of the plugin
      instance (e.g., enabled, disabled, provisioning).

  Messages:
    AdditionalConfigValue: Optional. The additional information for this
      plugin instance corresponding to the additional config template of the
      plugin. This information will be sent to plugin hosting service on each
      call to plugin hosted service. The key will be the
      config_variable_template.display_name to uniquely identify the config
      variable.

  Fields:
    actions: Required. The action status for the plugin instance.
    additionalConfig: Optional. The additional information for this plugin
      instance corresponding to the additional config template of the plugin.
      This information will be sent to plugin hosting service on each call to
      plugin hosted service. The key will be the
      config_variable_template.display_name to uniquely identify the config
      variable.
    authConfig: Optional. The authentication information for this plugin
      instance.
    createTime: Output only. Timestamp indicating when the plugin instance was
      created.
    displayName: Required. The display name for this plugin instance. Max
      length is 255 characters.
    errorMessage: Output only. Error message describing the failure, if any,
      during Create, Delete or ApplyConfig operation corresponding to the
      plugin instance.This field will only be populated if the plugin instance
      is in the ERROR or FAILED state.
    name: Identifier. The unique name of the plugin instance resource. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}/instances/{ins
      tance}`
    state: Output only. The current state of the plugin instance (e.g.,
      enabled, disabled, provisioning).
    updateTime: Output only. Timestamp indicating when the plugin instance was
      last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the plugin instance (e.g., enabled,
    disabled, provisioning).

    Values:
      STATE_UNSPECIFIED: Default unspecified state.
      CREATING: The plugin instance is being created.
      ACTIVE: The plugin instance is active and ready for executions. This is
        the only state where executions can run on the plugin instance.
      APPLYING_CONFIG: The updated config that contains additional_config and
        auth_config is being applied.
      ERROR: The ERROR state can come while applying config. Users can
        retrigger ApplyPluginInstanceConfig to restore the plugin instance
        back to active state. Note, In case the ERROR state happens while
        applying config (auth_config, additional_config), the plugin instance
        will reflect the config which was trying to be applied while error
        happened. In order to overwrite, trigger ApplyConfig with a new
        config.
      FAILED: The plugin instance is in a failed state. This indicates that an
        unrecoverable error occurred during a previous operation (Create,
        Delete).
      DELETING: The plugin instance is being deleted. Delete is only possible
        if there is no other operation running on the plugin instance and
        plugin instance action.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    APPLYING_CONFIG = 3
    ERROR = 4
    FAILED = 5
    DELETING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalConfigValue(_messages.Message):
    r"""Optional. The additional information for this plugin instance
    corresponding to the additional config template of the plugin. This
    information will be sent to plugin hosting service on each call to plugin
    hosted service. The key will be the config_variable_template.display_name
    to uniquely identify the config variable.

    Messages:
      AdditionalProperty: An additional property for a AdditionalConfigValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AdditionalConfigValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1ConfigVariable attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1ConfigVariable', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actions = _messages.MessageField('GoogleCloudApihubV1PluginInstanceAction', 1, repeated=True)
  additionalConfig = _messages.MessageField('AdditionalConfigValue', 2)
  authConfig = _messages.MessageField('GoogleCloudApihubV1AuthConfig', 3)
  createTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  errorMessage = _messages.StringField(6)
  name = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class GoogleCloudApihubV1PluginInstanceAction(_messages.Message):
  r"""PluginInstanceAction represents an action which can be executed in the
  plugin instance.

  Enums:
    StateValueValuesEnum: Output only. The current state of the plugin action
      in the plugin instance.

  Fields:
    actionId: Required. This should map to one of the action id specified in
      actions_config in the plugin.
    curationConfig: Optional. This configuration should be provided if the
      plugin action is publishing data to API hub curate layer.
    hubInstanceAction: Optional. The execution information for the plugin
      instance action done corresponding to an API hub instance.
    scheduleCronExpression: Optional. The schedule for this plugin instance
      action. This can only be set if the plugin supports
      API_HUB_SCHEDULE_TRIGGER mode for this action.
    scheduleTimeZone: Optional. The time zone for the schedule cron
      expression. If not provided, UTC will be used.
    state: Output only. The current state of the plugin action in the plugin
      instance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the plugin action in the plugin
    instance.

    Values:
      STATE_UNSPECIFIED: Default unspecified state.
      ENABLED: The action is enabled in the plugin instance i.e., executions
        can be triggered for this action.
      DISABLED: The action is disabled in the plugin instance i.e., no
        executions can be triggered for this action. This state indicates that
        the user explicitly disabled the instance, and no further action is
        needed unless the user wants to re-enable it.
      ENABLING: The action in the plugin instance is being enabled.
      DISABLING: The action in the plugin instance is being disabled.
      ERROR: The ERROR state can come while enabling/disabling plugin instance
        action. Users can retrigger enable, disable via
        EnablePluginInstanceAction and DisablePluginInstanceAction to restore
        the action back to enabled/disabled state. Note enable/disable on
        actions can only be triggered if plugin instance is in Active state.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    ENABLING = 3
    DISABLING = 4
    ERROR = 5

  actionId = _messages.StringField(1)
  curationConfig = _messages.MessageField('GoogleCloudApihubV1CurationConfig', 2)
  hubInstanceAction = _messages.MessageField('GoogleCloudApihubV1ExecutionStatus', 3)
  scheduleCronExpression = _messages.StringField(4)
  scheduleTimeZone = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudApihubV1PluginInstanceActionID(_messages.Message):
  r"""The plugin instance and associated action that is using the curation.

  Fields:
    actionId: Output only. The action ID that is using the curation. This
      should map to one of the action IDs specified in action configs in the
      plugin.
    pluginInstance: Output only. Plugin instance that is using the curation.
      Format is `projects/{project}/locations/{location}/plugins/{plugin}/inst
      ances/{instance}`
  """

  actionId = _messages.StringField(1)
  pluginInstance = _messages.StringField(2)


class GoogleCloudApihubV1PluginInstanceActionSource(_messages.Message):
  r"""PluginInstanceActionSource represents the plugin instance action source.

  Fields:
    actionId: Output only. The id of the plugin instance action.
    pluginInstance: Output only. The resource name of the source plugin
      instance. Format is `projects/{project}/locations/{location}/plugins/{pl
      ugin}/instances/{instance}`
  """

  actionId = _messages.StringField(1)
  pluginInstance = _messages.StringField(2)


class GoogleCloudApihubV1Point(_messages.Message):
  r"""Point within the file (line and character).

  Fields:
    character: Required. Character position within the line (zero-indexed).
    line: Required. Line number (zero-indexed).
  """

  character = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  line = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudApihubV1Range(_messages.Message):
  r"""Object describing where in the file the issue was found.

  Fields:
    end: Required. End of the issue.
    start: Required. Start of the issue.
  """

  end = _messages.MessageField('GoogleCloudApihubV1Point', 1)
  start = _messages.MessageField('GoogleCloudApihubV1Point', 2)


class GoogleCloudApihubV1RuntimeProjectAttachment(_messages.Message):
  r"""Runtime project attachment represents an attachment from the runtime
  project to the host project. Api Hub looks for deployments in the attached
  runtime projects and creates corresponding resources in Api Hub for the
  discovered deployments.

  Fields:
    createTime: Output only. Create time.
    name: Identifier. The resource name of a runtime project attachment.
      Format: "projects/{project}/locations/{location}/runtimeProjectAttachmen
      ts/{runtime_project_attachment}".
    runtimeProject: Required. Immutable. Google cloud project name in the
      format: "projects/abc" or "projects/123". As input, project name with
      either project id or number are accepted. As output, this field will
      contain project number.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  runtimeProject = _messages.StringField(3)


class GoogleCloudApihubV1Schema(_messages.Message):
  r"""The schema details derived from the spec. Currently, this entity is
  supported for OpenAPI spec only. For OpenAPI spec, this maps to the schema
  defined in the `definitions` section for OpenAPI 2.0 version and in
  `components.schemas` section for OpenAPI 3.0 and 3.1 version.

  Fields:
    displayName: Output only. The display name of the schema. This will map to
      the name of the schema in the spec.
    rawValue: Output only. The raw value of the schema definition
      corresponding to the schema name in the spec.
  """

  displayName = _messages.StringField(1)
  rawValue = _messages.BytesField(2)


class GoogleCloudApihubV1SearchResourcesRequest(_messages.Message):
  r"""The SearchResources method's request.

  Fields:
    filter: Optional. An expression that filters the list of search results. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string, a number, or a boolean.
      The comparison operator must be `=`. Filters are not case sensitive. The
      following field names are eligible for filtering: * `resource_type` -
      The type of resource in the search results. Must be one of the
      following: `Api`, `ApiOperation`, `Deployment`, `Definition`, `Spec` or
      `Version`. This field can only be specified once in the filter. Here are
      is an example: * `resource_type = Api` - The resource_type is _Api_.
    pageSize: Optional. The maximum number of search results to return. The
      service may return fewer than this value. If unspecified at most 10
      search results will be returned. If value is negative then
      `INVALID_ARGUMENT` error is returned. The maximum value is 25; values
      above 25 will be coerced to 25. While paginating, you can specify a new
      page size parameter for each page of search results to be listed.
    pageToken: Optional. A page token, received from a previous
      SearchResources call. Specify this parameter to retrieve the next page
      of transactions. When paginating, you must specify the `page_token`
      parameter and all the other parameters except page_size should be
      specified with the same value which was used in the previous call. If
      the other fields are set with a different value than the previous call
      then `INVALID_ARGUMENT` error is returned.
    query: Required. The free text search query. This query can contain
      keywords which could be related to any detail of the API-Hub resources
      such display names, descriptions, attributes etc.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)


class GoogleCloudApihubV1SearchResourcesResponse(_messages.Message):
  r"""Response for the SearchResources method.

  Fields:
    nextPageToken: Pass this token in the SearchResourcesRequest to continue
      to list results. If all results have been returned, this field is an
      empty string or not present in the response.
    searchResults: List of search results according to the filter and search
      query specified. The order of search results represents the ranking.
  """

  nextPageToken = _messages.StringField(1)
  searchResults = _messages.MessageField('GoogleCloudApihubV1SearchResult', 2, repeated=True)


class GoogleCloudApihubV1SearchResult(_messages.Message):
  r"""Represents the search results.

  Fields:
    resource: This represents the ApiHubResource. Note: Only selected fields
      of the resources are populated in response.
  """

  resource = _messages.MessageField('GoogleCloudApihubV1ApiHubResource', 1)


class GoogleCloudApihubV1Secret(_messages.Message):
  r"""Secret provides a reference to entries in Secret Manager.

  Fields:
    secretVersion: Required. The resource name of the secret version in the
      format, format as: `projects/*/secrets/*/versions/*`.
  """

  secretVersion = _messages.StringField(1)


class GoogleCloudApihubV1SourceMetadata(_messages.Message):
  r"""SourceMetadata represents the metadata for a resource at the source.

  Enums:
    SourceTypeValueValuesEnum: Output only. The type of the source.

  Fields:
    originalResourceCreateTime: Output only. The time at which the resource
      was created at the source.
    originalResourceId: Output only. The unique identifier of the resource at
      the source.
    originalResourceUpdateTime: Output only. The time at which the resource
      was last updated at the source.
    pluginInstanceActionSource: Output only. The source of the resource is a
      plugin instance action.
    sourceType: Output only. The type of the source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the source.

    Values:
      SOURCE_TYPE_UNSPECIFIED: Source type not specified.
      PLUGIN: Source type plugin.
    """
    SOURCE_TYPE_UNSPECIFIED = 0
    PLUGIN = 1

  originalResourceCreateTime = _messages.StringField(1)
  originalResourceId = _messages.StringField(2)
  originalResourceUpdateTime = _messages.StringField(3)
  pluginInstanceActionSource = _messages.MessageField('GoogleCloudApihubV1PluginInstanceActionSource', 4)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 5)


class GoogleCloudApihubV1Spec(_messages.Message):
  r"""Represents a spec associated with an API version in the API Hub. Note
  that specs of various types can be uploaded, however parsing of details is
  supported for OpenAPI spec currently.

  Enums:
    ParsingModeValueValuesEnum: Optional. Input only. Enum specifying the
      parsing mode for OpenAPI Specification (OAS) parsing.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the spec. The key is the attribute name. It will be of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    attributes: Optional. The list of user defined attributes associated with
      the spec. The key is the attribute name. It will be of the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    contents: Optional. Input only. The contents of the uploaded spec.
    createTime: Output only. The time at which the spec was created.
    details: Output only. Details parsed from the spec.
    displayName: Required. The display name of the spec. This can contain the
      file name of the spec.
    documentation: Optional. The documentation of the spec. For OpenAPI spec,
      this will be populated from `externalDocs` in OpenAPI spec.
    lintResponse: Optional. The lint response for the spec.
    name: Identifier. The name of the spec. Format: `projects/{project}/locati
      ons/{location}/apis/{api}/versions/{version}/specs/{spec}`
    parsingMode: Optional. Input only. Enum specifying the parsing mode for
      OpenAPI Specification (OAS) parsing.
    sourceMetadata: Output only. The list of sources and metadata from the
      sources of the spec.
    sourceUri: Optional. The URI of the spec source in case file is uploaded
      from an external version control system.
    specType: Required. The type of spec. The value should be one of the
      allowed values defined for
      `projects/{project}/locations/{location}/attributes/system-spec-type`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. Note, this field is mandatory if content is provided.
    updateTime: Output only. The time at which the spec was last updated.
  """

  class ParsingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Input only. Enum specifying the parsing mode for OpenAPI
    Specification (OAS) parsing.

    Values:
      PARSING_MODE_UNSPECIFIED: Defaults to `RELAXED`.
      RELAXED: Parsing of the Spec on create and update is relaxed, meaning
        that parsing errors the spec contents will not fail the API call.
      STRICT: Parsing of the Spec on create and update is strict, meaning that
        parsing errors in the spec contents will fail the API call.
    """
    PARSING_MODE_UNSPECIFIED = 0
    RELAXED = 1
    STRICT = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    spec. The key is the attribute name. It will be of the format:
    `projects/{project}/locations/{location}/attributes/{attribute}`. The
    value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  contents = _messages.MessageField('GoogleCloudApihubV1SpecContents', 2)
  createTime = _messages.StringField(3)
  details = _messages.MessageField('GoogleCloudApihubV1SpecDetails', 4)
  displayName = _messages.StringField(5)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 6)
  lintResponse = _messages.MessageField('GoogleCloudApihubV1LintResponse', 7)
  name = _messages.StringField(8)
  parsingMode = _messages.EnumField('ParsingModeValueValuesEnum', 9)
  sourceMetadata = _messages.MessageField('GoogleCloudApihubV1SourceMetadata', 10, repeated=True)
  sourceUri = _messages.StringField(11)
  specType = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 12)
  updateTime = _messages.StringField(13)


class GoogleCloudApihubV1SpecContents(_messages.Message):
  r"""The spec contents.

  Fields:
    contents: Required. The contents of the spec.
    mimeType: Required. The mime type of the content for example
      application/json, application/yaml, application/wsdl etc.
  """

  contents = _messages.BytesField(1)
  mimeType = _messages.StringField(2)


class GoogleCloudApihubV1SpecDetails(_messages.Message):
  r"""SpecDetails contains the details parsed from supported spec types.

  Fields:
    description: Output only. The description of the spec.
    openApiSpecDetails: Output only. Additional details apart from
      `OperationDetails` parsed from an OpenAPI spec. The OperationDetails
      parsed from the spec can be obtained by using ListAPIOperations method.
  """

  description = _messages.StringField(1)
  openApiSpecDetails = _messages.MessageField('GoogleCloudApihubV1OpenApiSpecDetails', 2)


class GoogleCloudApihubV1SpecMetadata(_messages.Message):
  r"""The metadata associated with a spec of the API version.

  Fields:
    originalCreateTime: Optional. Timestamp indicating when the spec was
      created at the source.
    originalId: Optional. The unique identifier of the spec in the system
      where it was originally created.
    originalUpdateTime: Required. Timestamp indicating when the spec was last
      updated at the source.
    spec: Required. The spec resource to be pushed to Hub's collect layer. The
      ID of the spec will be generated by Hub.
  """

  originalCreateTime = _messages.StringField(1)
  originalId = _messages.StringField(2)
  originalUpdateTime = _messages.StringField(3)
  spec = _messages.MessageField('GoogleCloudApihubV1Spec', 4)


class GoogleCloudApihubV1StringAttributeValues(_messages.Message):
  r"""The attribute values of data type string or JSON.

  Fields:
    values: Required. The attribute values in case attribute data type is
      string or JSON.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudApihubV1StyleGuide(_messages.Message):
  r"""Represents a singleton style guide resource to be used for linting Open
  API specs.

  Enums:
    LinterValueValuesEnum: Required. Target linter for the style guide.

  Fields:
    contents: Required. Input only. The contents of the uploaded style guide.
    linter: Required. Target linter for the style guide.
    name: Identifier. The name of the style guide. Format:
      `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`
  """

  class LinterValueValuesEnum(_messages.Enum):
    r"""Required. Target linter for the style guide.

    Values:
      LINTER_UNSPECIFIED: Linter type unspecified.
      SPECTRAL: Linter type spectral.
      OTHER: Linter type other.
    """
    LINTER_UNSPECIFIED = 0
    SPECTRAL = 1
    OTHER = 2

  contents = _messages.MessageField('GoogleCloudApihubV1StyleGuideContents', 1)
  linter = _messages.EnumField('LinterValueValuesEnum', 2)
  name = _messages.StringField(3)


class GoogleCloudApihubV1StyleGuideContents(_messages.Message):
  r"""The style guide contents.

  Fields:
    contents: Required. The contents of the style guide.
    mimeType: Required. The mime type of the content.
  """

  contents = _messages.BytesField(1)
  mimeType = _messages.StringField(2)


class GoogleCloudApihubV1SummaryEntry(_messages.Message):
  r"""Count of issues with a given severity.

  Enums:
    SeverityValueValuesEnum: Required. Severity of the issue.

  Fields:
    count: Required. Count of issues with the given severity.
    severity: Required. Severity of the issue.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. Severity of the issue.

    Values:
      SEVERITY_UNSPECIFIED: Severity unspecified.
      SEVERITY_ERROR: Severity error.
      SEVERITY_WARNING: Severity warning.
      SEVERITY_INFO: Severity info.
      SEVERITY_HINT: Severity hint.
    """
    SEVERITY_UNSPECIFIED = 0
    SEVERITY_ERROR = 1
    SEVERITY_WARNING = 2
    SEVERITY_INFO = 3
    SEVERITY_HINT = 4

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)


class GoogleCloudApihubV1UserPasswordConfig(_messages.Message):
  r"""Parameters to support Username and Password Authentication.

  Fields:
    password: Required. Secret version reference containing the password. The
      `secretmanager.versions.access` permission should be granted to the
      service account accessing the secret.
    username: Required. Username.
  """

  password = _messages.MessageField('GoogleCloudApihubV1Secret', 1)
  username = _messages.StringField(2)


class GoogleCloudApihubV1Version(_messages.Message):
  r"""Represents a version of the API resource in API hub. This is also
  referred to as the API version.

  Messages:
    AttributesValue: Optional. The list of user defined attributes associated
      with the Version resource. The key is the attribute name. It will be of
      the format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.

  Fields:
    accreditation: Optional. The accreditations associated with the API
      version. This maps to the following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-
      accreditation` attribute. The number of values for this attribute will
      be based on the cardinality of the attribute. The same can be retrieved
      via GetAttribute API. All values should be from the list of allowed
      values defined for the attribute.
    apiOperations: Output only. The operations contained in the API version.
      These operations will be added to the version when a new spec is added
      or when an existing spec is updated. Format is `projects/{project}/locat
      ions/{location}/apis/{api}/versions/{version}/operations/{operation}`
    attributes: Optional. The list of user defined attributes associated with
      the Version resource. The key is the attribute name. It will be of the
      format:
      `projects/{project}/locations/{location}/attributes/{attribute}`. The
      value is the attribute values associated with the resource.
    compliance: Optional. The compliance associated with the API version. This
      maps to the following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-compliance`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    createTime: Output only. The time at which the version was created.
    definitions: Output only. The definitions contained in the API version.
      These definitions will be added to the version when a new spec is added
      or when an existing spec is updated. Format is `projects/{project}/locat
      ions/{location}/apis/{api}/versions/{version}/definitions/{definition}`
    deployments: Optional. The deployments linked to this API version. Note: A
      particular API version could be deployed to multiple deployments (for
      dev deployment, UAT deployment, etc) Format is
      `projects/{project}/locations/{location}/deployments/{deployment}`
    description: Optional. The description of the version.
    displayName: Required. The display name of the version.
    documentation: Optional. The documentation of the version.
    lifecycle: Optional. The lifecycle of the API version. This maps to the
      following system defined attribute:
      `projects/{project}/locations/{location}/attributes/system-lifecycle`
      attribute. The number of values for this attribute will be based on the
      cardinality of the attribute. The same can be retrieved via GetAttribute
      API. All values should be from the list of allowed values defined for
      the attribute.
    name: Identifier. The name of the version. Format:
      `projects/{project}/locations/{location}/apis/{api}/versions/{version}`
    selectedDeployment: Optional. The selected deployment for a Version
      resource. This can be used when special handling is needed on client
      side for a particular deployment linked to the version. Format is
      `projects/{project}/locations/{location}/deployments/{deployment}`
    sourceMetadata: Output only. The list of sources and metadata from the
      sources of the version.
    specs: Output only. The specs associated with this version. Note that an
      API version can be associated with multiple specs. Format is `projects/{
      project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}
      `
    updateTime: Output only. The time at which the version was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. The list of user defined attributes associated with the
    Version resource. The key is the attribute name. It will be of the format:
    `projects/{project}/locations/{location}/attributes/{attribute}`. The
    value is the attribute values associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudApihubV1AttributeValues attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accreditation = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 1)
  apiOperations = _messages.StringField(2, repeated=True)
  attributes = _messages.MessageField('AttributesValue', 3)
  compliance = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 4)
  createTime = _messages.StringField(5)
  definitions = _messages.StringField(6, repeated=True)
  deployments = _messages.StringField(7, repeated=True)
  description = _messages.StringField(8)
  displayName = _messages.StringField(9)
  documentation = _messages.MessageField('GoogleCloudApihubV1Documentation', 10)
  lifecycle = _messages.MessageField('GoogleCloudApihubV1AttributeValues', 11)
  name = _messages.StringField(12)
  selectedDeployment = _messages.StringField(13)
  sourceMetadata = _messages.MessageField('GoogleCloudApihubV1SourceMetadata', 14, repeated=True)
  specs = _messages.StringField(15, repeated=True)
  updateTime = _messages.StringField(16)


class GoogleCloudApihubV1VersionMetadata(_messages.Message):
  r"""The metadata associated with a version of the API resource.

  Fields:
    deployments: Optional. The deployments linked to this API version. Note: A
      particular API version could be deployed to multiple deployments (for
      dev deployment, UAT deployment, etc.)
    originalCreateTime: Optional. Timestamp indicating when the version was
      created at the source.
    originalId: Optional. The unique identifier of the version in the system
      where it was originally created.
    originalUpdateTime: Required. Timestamp indicating when the version was
      last updated at the source.
    specs: Optional. The specs associated with this version. Note that an API
      version can be associated with multiple specs.
    version: Required. Represents a version of the API resource in API hub.
      The ID of the version will be generated by Hub.
  """

  deployments = _messages.MessageField('GoogleCloudApihubV1DeploymentMetadata', 1, repeated=True)
  originalCreateTime = _messages.StringField(2)
  originalId = _messages.StringField(3)
  originalUpdateTime = _messages.StringField(4)
  specs = _messages.MessageField('GoogleCloudApihubV1SpecMetadata', 5, repeated=True)
  version = _messages.MessageField('GoogleCloudApihubV1Version', 6)


class GoogleCloudCommonOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
