"""Generated client library for apihub version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.apihub.v1 import apihub_v1_messages as messages


class ApihubV1(base_api.BaseApiClient):
  """Generated client library for service apihub version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://apihub.googleapis.com/'
  MTLS_BASE_URL = 'https://apihub.mtls.googleapis.com/'

  _PACKAGE = 'apihub'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ApihubV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new apihub handle."""
    url = url or self.BASE_URL
    super(ApihubV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_apiHubInstances = self.ProjectsLocationsApiHubInstancesService(self)
    self.projects_locations_apis_versions_definitions = self.ProjectsLocationsApisVersionsDefinitionsService(self)
    self.projects_locations_apis_versions_operations = self.ProjectsLocationsApisVersionsOperationsService(self)
    self.projects_locations_apis_versions_specs = self.ProjectsLocationsApisVersionsSpecsService(self)
    self.projects_locations_apis_versions = self.ProjectsLocationsApisVersionsService(self)
    self.projects_locations_apis = self.ProjectsLocationsApisService(self)
    self.projects_locations_attributes = self.ProjectsLocationsAttributesService(self)
    self.projects_locations_curations = self.ProjectsLocationsCurationsService(self)
    self.projects_locations_dependencies = self.ProjectsLocationsDependenciesService(self)
    self.projects_locations_deployments = self.ProjectsLocationsDeploymentsService(self)
    self.projects_locations_externalApis = self.ProjectsLocationsExternalApisService(self)
    self.projects_locations_hostProjectRegistrations = self.ProjectsLocationsHostProjectRegistrationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_plugins_instances = self.ProjectsLocationsPluginsInstancesService(self)
    self.projects_locations_plugins_styleGuide = self.ProjectsLocationsPluginsStyleGuideService(self)
    self.projects_locations_plugins = self.ProjectsLocationsPluginsService(self)
    self.projects_locations_runtimeProjectAttachments = self.ProjectsLocationsRuntimeProjectAttachmentsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsApiHubInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_apiHubInstances resource."""

    _NAME = 'projects_locations_apiHubInstances'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApiHubInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Provisions instance resources for the API Hub.

      Args:
        request: (ApihubProjectsLocationsApiHubInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances',
        http_method='POST',
        method_id='apihub.projects.locations.apiHubInstances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['apiHubInstanceId'],
        relative_path='v1/{+parent}/apiHubInstances',
        request_field='googleCloudApihubV1ApiHubInstance',
        request_type_name='ApihubProjectsLocationsApiHubInstancesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the API hub instance.

      Args:
        request: (ApihubProjectsLocationsApiHubInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances/{apiHubInstancesId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.apiHubInstances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApiHubInstancesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single API Hub instance.

      Args:
        request: (ApihubProjectsLocationsApiHubInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ApiHubInstance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances/{apiHubInstancesId}',
        http_method='GET',
        method_id='apihub.projects.locations.apiHubInstances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApiHubInstancesGetRequest',
        response_type_name='GoogleCloudApihubV1ApiHubInstance',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks up an Api Hub instance in a given GCP project. There will always be only one Api Hub instance for a GCP project across all locations.

      Args:
        request: (ApihubProjectsLocationsApiHubInstancesLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1LookupApiHubInstanceResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances:lookup',
        http_method='GET',
        method_id='apihub.projects.locations.apiHubInstances.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apiHubInstances:lookup',
        request_field='',
        request_type_name='ApihubProjectsLocationsApiHubInstancesLookupRequest',
        response_type_name='GoogleCloudApihubV1LookupApiHubInstanceResponse',
        supports_download=False,
    )

  class ProjectsLocationsApisVersionsDefinitionsService(base_api.BaseApiService):
    """Service class for the projects_locations_apis_versions_definitions resource."""

    _NAME = 'projects_locations_apis_versions_definitions'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApisVersionsDefinitionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about a definition in an API version.

      Args:
        request: (ApihubProjectsLocationsApisVersionsDefinitionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Definition) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/definitions/{definitionsId}',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.definitions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsDefinitionsGetRequest',
        response_type_name='GoogleCloudApihubV1Definition',
        supports_download=False,
    )

  class ProjectsLocationsApisVersionsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_apis_versions_operations resource."""

    _NAME = 'projects_locations_apis_versions_operations'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApisVersionsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an apiOperation in an API version. An apiOperation can be created only if the version has no apiOperations which were created by parsing a spec.

      Args:
        request: (ApihubProjectsLocationsApisVersionsOperationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ApiOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations',
        http_method='POST',
        method_id='apihub.projects.locations.apis.versions.operations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['apiOperationId'],
        relative_path='v1/{+parent}/operations',
        request_field='googleCloudApihubV1ApiOperation',
        request_type_name='ApihubProjectsLocationsApisVersionsOperationsCreateRequest',
        response_type_name='GoogleCloudApihubV1ApiOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an operation in an API version and we can delete only the operations created via create API. If the operation was created by parsing the spec, then it can be deleted by editing or deleting the spec.

      Args:
        request: (ApihubProjectsLocationsApisVersionsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.apis.versions.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about a particular operation in API version.

      Args:
        request: (ApihubProjectsLocationsApisVersionsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ApiOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsOperationsGetRequest',
        response_type_name='GoogleCloudApihubV1ApiOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List operations in an API version.

      Args:
        request: (ApihubProjectsLocationsApisVersionsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListApiOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.operations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/operations',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsOperationsListRequest',
        response_type_name='GoogleCloudApihubV1ListApiOperationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an operation in an API version. The following fields in the ApiOperation resource can be updated: * details.description * details.documentation * details.http_operation.path * details.http_operation.method * details.deprecated * attributes The update_mask should be used to specify the fields being updated. An operation can be updated only if the operation was created via CreateApiOperation API. If the operation was created by parsing the spec, then it can be edited by updating the spec.

      Args:
        request: (ApihubProjectsLocationsApisVersionsOperationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ApiOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.apis.versions.operations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1ApiOperation',
        request_type_name='ApihubProjectsLocationsApisVersionsOperationsPatchRequest',
        response_type_name='GoogleCloudApihubV1ApiOperation',
        supports_download=False,
    )

  class ProjectsLocationsApisVersionsSpecsService(base_api.BaseApiService):
    """Service class for the projects_locations_apis_versions_specs resource."""

    _NAME = 'projects_locations_apis_versions_specs'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApisVersionsSpecsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Add a spec to an API version in the API hub. Multiple specs can be added to an API version. Note, while adding a spec, at least one of `contents` or `source_uri` must be provided. If `contents` is provided, then `spec_type` must also be provided. On adding a spec with contents to the version, the operations present in it will be added to the version.Note that the file contents in the spec should be of the same type as defined in the `projects/{project}/locations/{location}/attributes/system-spec-type` attribute associated with spec resource. Note that specs of various types can be uploaded, however parsing of details is supported for OpenAPI spec currently. In order to access the information parsed from the spec, use the GetSpec method. In order to access the raw contents for a particular spec, use the GetSpecContents method. In order to access the operations parsed from the spec, use the ListAPIOperations method.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Spec) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs',
        http_method='POST',
        method_id='apihub.projects.locations.apis.versions.specs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['specId'],
        relative_path='v1/{+parent}/specs',
        request_field='googleCloudApihubV1Spec',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsCreateRequest',
        response_type_name='GoogleCloudApihubV1Spec',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a spec. Deleting a spec will also delete the associated operations from the version.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.apis.versions.specs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about the information parsed from a spec. Note that this method does not return the raw spec contents. Use GetSpecContents method to retrieve the same.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Spec) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.specs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsGetRequest',
        response_type_name='GoogleCloudApihubV1Spec',
        supports_download=False,
    )

    def GetContents(self, request, global_params=None):
      r"""Get spec contents.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsGetContentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1SpecContents) The response message.
      """
      config = self.GetMethodConfig('GetContents')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}:contents',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.specs.getContents',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:contents',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsGetContentsRequest',
        response_type_name='GoogleCloudApihubV1SpecContents',
        supports_download=False,
    )

    def Lint(self, request, global_params=None):
      r"""Lints the requested spec and updates the corresponding API Spec with the lint response. This lint response will be available in all subsequent Get and List Spec calls to Core service.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsLintRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Lint')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lint.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}:lint',
        http_method='POST',
        method_id='apihub.projects.locations.apis.versions.specs.lint',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:lint',
        request_field='googleCloudApihubV1LintSpecRequest',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsLintRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List specs corresponding to a particular API resource.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListSpecsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.specs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/specs',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsListRequest',
        response_type_name='GoogleCloudApihubV1ListSpecsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update spec. The following fields in the spec can be updated: * display_name * source_uri * lint_response * attributes * contents * spec_type In case of an OAS spec, updating spec contents can lead to: 1. Creation, deletion and update of operations. 2. Creation, deletion and update of definitions. 3. Update of other info parsed out from the new spec. In case of contents or source_uri being present in update mask, spec_type must also be present. Also, spec_type can not be present in update mask if contents or source_uri is not present. The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsApisVersionsSpecsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Spec) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.apis.versions.specs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Spec',
        request_type_name='ApihubProjectsLocationsApisVersionsSpecsPatchRequest',
        response_type_name='GoogleCloudApihubV1Spec',
        supports_download=False,
    )

  class ProjectsLocationsApisVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_apis_versions resource."""

    _NAME = 'projects_locations_apis_versions'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApisVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an API version for an API resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsApisVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Version) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions',
        http_method='POST',
        method_id='apihub.projects.locations.apis.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['versionId'],
        relative_path='v1/{+parent}/versions',
        request_field='googleCloudApihubV1Version',
        request_type_name='ApihubProjectsLocationsApisVersionsCreateRequest',
        response_type_name='GoogleCloudApihubV1Version',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an API version. Version can only be deleted if all underlying specs, operations, definitions and linked deployments are deleted.

      Args:
        request: (ApihubProjectsLocationsApisVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.apis.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about the API version of an API resource. This will include information about the specs and operations present in the API version as well as the deployments linked to it.

      Args:
        request: (ApihubProjectsLocationsApisVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Version) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsGetRequest',
        response_type_name='GoogleCloudApihubV1Version',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List API versions of an API resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsApisVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions',
        http_method='GET',
        method_id='apihub.projects.locations.apis.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisVersionsListRequest',
        response_type_name='GoogleCloudApihubV1ListVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update API version. The following fields in the version can be updated currently: * display_name * description * documentation * deployments * lifecycle * compliance * accreditation * attributes The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsApisVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Version) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.apis.versions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Version',
        request_type_name='ApihubProjectsLocationsApisVersionsPatchRequest',
        response_type_name='GoogleCloudApihubV1Version',
        supports_download=False,
    )

  class ProjectsLocationsApisService(base_api.BaseApiService):
    """Service class for the projects_locations_apis resource."""

    _NAME = 'projects_locations_apis'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsApisService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an API resource in the API hub. Once an API resource is created, versions can be added to it.

      Args:
        request: (ApihubProjectsLocationsApisCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Api) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis',
        http_method='POST',
        method_id='apihub.projects.locations.apis.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['apiId'],
        relative_path='v1/{+parent}/apis',
        request_field='googleCloudApihubV1Api',
        request_type_name='ApihubProjectsLocationsApisCreateRequest',
        response_type_name='GoogleCloudApihubV1Api',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an API resource in the API hub. API can only be deleted if all underlying versions are deleted.

      Args:
        request: (ApihubProjectsLocationsApisDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.apis.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get API resource details including the API versions contained in it.

      Args:
        request: (ApihubProjectsLocationsApisGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Api) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}',
        http_method='GET',
        method_id='apihub.projects.locations.apis.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisGetRequest',
        response_type_name='GoogleCloudApihubV1Api',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List API resources in the API hub.

      Args:
        request: (ApihubProjectsLocationsApisListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListApisResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis',
        http_method='GET',
        method_id='apihub.projects.locations.apis.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/apis',
        request_field='',
        request_type_name='ApihubProjectsLocationsApisListRequest',
        response_type_name='GoogleCloudApihubV1ListApisResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an API resource in the API hub. The following fields in the API can be updated: * display_name * description * owner * documentation * target_user * team * business_unit * maturity_level * api_style * attributes The update_mask should be used to specify the fields being updated. Updating the owner field requires complete owner message and updates both owner and email fields.

      Args:
        request: (ApihubProjectsLocationsApisPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Api) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.apis.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Api',
        request_type_name='ApihubProjectsLocationsApisPatchRequest',
        response_type_name='GoogleCloudApihubV1Api',
        supports_download=False,
    )

  class ProjectsLocationsAttributesService(base_api.BaseApiService):
    """Service class for the projects_locations_attributes resource."""

    _NAME = 'projects_locations_attributes'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsAttributesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a user defined attribute. Certain pre defined attributes are already created by the API hub. These attributes will have type as `SYSTEM_DEFINED` and can be listed via ListAttributes method. Allowed values for the same can be updated via UpdateAttribute method.

      Args:
        request: (ApihubProjectsLocationsAttributesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attributes',
        http_method='POST',
        method_id='apihub.projects.locations.attributes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attributeId'],
        relative_path='v1/{+parent}/attributes',
        request_field='googleCloudApihubV1Attribute',
        request_type_name='ApihubProjectsLocationsAttributesCreateRequest',
        response_type_name='GoogleCloudApihubV1Attribute',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an attribute. Note: System defined attributes cannot be deleted. All associations of the attribute being deleted with any API hub resource will also get deleted.

      Args:
        request: (ApihubProjectsLocationsAttributesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.attributes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsAttributesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about the attribute.

      Args:
        request: (ApihubProjectsLocationsAttributesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}',
        http_method='GET',
        method_id='apihub.projects.locations.attributes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsAttributesGetRequest',
        response_type_name='GoogleCloudApihubV1Attribute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all attributes.

      Args:
        request: (ApihubProjectsLocationsAttributesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListAttributesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attributes',
        http_method='GET',
        method_id='apihub.projects.locations.attributes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attributes',
        request_field='',
        request_type_name='ApihubProjectsLocationsAttributesListRequest',
        response_type_name='GoogleCloudApihubV1ListAttributesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the attribute. The following fields in the Attribute resource can be updated: * display_name The display name can be updated for user defined attributes only. * description The description can be updated for user defined attributes only. * allowed_values To update the list of allowed values, clients need to use the fetched list of allowed values and add or remove values to or from the same list. The mutable allowed values can be updated for both user defined and System defined attributes. The immutable allowed values cannot be updated or deleted. The updated list of allowed values cannot be empty. If an allowed value that is already used by some resource's attribute is deleted, then the association between the resource and the attribute value will also be deleted. * cardinality The cardinality can be updated for user defined attributes only. Cardinality can only be increased during an update. The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsAttributesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.attributes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Attribute',
        request_type_name='ApihubProjectsLocationsAttributesPatchRequest',
        response_type_name='GoogleCloudApihubV1Attribute',
        supports_download=False,
    )

  class ProjectsLocationsCurationsService(base_api.BaseApiService):
    """Service class for the projects_locations_curations resource."""

    _NAME = 'projects_locations_curations'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsCurationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a curation resource in the API hub. Once a curation resource is created, plugin instances can start using it.

      Args:
        request: (ApihubProjectsLocationsCurationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Curation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/curations',
        http_method='POST',
        method_id='apihub.projects.locations.curations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['curationId'],
        relative_path='v1/{+parent}/curations',
        request_field='googleCloudApihubV1Curation',
        request_type_name='ApihubProjectsLocationsCurationsCreateRequest',
        response_type_name='GoogleCloudApihubV1Curation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a curation resource in the API hub. A curation can only be deleted if it's not being used by any plugin instance.

      Args:
        request: (ApihubProjectsLocationsCurationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.curations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsCurationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get curation resource details.

      Args:
        request: (ApihubProjectsLocationsCurationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Curation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}',
        http_method='GET',
        method_id='apihub.projects.locations.curations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsCurationsGetRequest',
        response_type_name='GoogleCloudApihubV1Curation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List curation resources in the API hub.

      Args:
        request: (ApihubProjectsLocationsCurationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListCurationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/curations',
        http_method='GET',
        method_id='apihub.projects.locations.curations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/curations',
        request_field='',
        request_type_name='ApihubProjectsLocationsCurationsListRequest',
        response_type_name='GoogleCloudApihubV1ListCurationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a curation resource in the API hub. The following fields in the curation can be updated: * display_name * description The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsCurationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Curation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.curations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Curation',
        request_type_name='ApihubProjectsLocationsCurationsPatchRequest',
        response_type_name='GoogleCloudApihubV1Curation',
        supports_download=False,
    )

  class ProjectsLocationsDependenciesService(base_api.BaseApiService):
    """Service class for the projects_locations_dependencies resource."""

    _NAME = 'projects_locations_dependencies'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsDependenciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a dependency between two entities in the API hub.

      Args:
        request: (ApihubProjectsLocationsDependenciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Dependency) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dependencies',
        http_method='POST',
        method_id='apihub.projects.locations.dependencies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dependencyId'],
        relative_path='v1/{+parent}/dependencies',
        request_field='googleCloudApihubV1Dependency',
        request_type_name='ApihubProjectsLocationsDependenciesCreateRequest',
        response_type_name='GoogleCloudApihubV1Dependency',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete the dependency resource.

      Args:
        request: (ApihubProjectsLocationsDependenciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.dependencies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsDependenciesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about a dependency resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsDependenciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Dependency) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}',
        http_method='GET',
        method_id='apihub.projects.locations.dependencies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsDependenciesGetRequest',
        response_type_name='GoogleCloudApihubV1Dependency',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List dependencies based on the provided filter and pagination parameters.

      Args:
        request: (ApihubProjectsLocationsDependenciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListDependenciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dependencies',
        http_method='GET',
        method_id='apihub.projects.locations.dependencies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/dependencies',
        request_field='',
        request_type_name='ApihubProjectsLocationsDependenciesListRequest',
        response_type_name='GoogleCloudApihubV1ListDependenciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a dependency based on the update_mask provided in the request. The following fields in the dependency can be updated: * description.

      Args:
        request: (ApihubProjectsLocationsDependenciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Dependency) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.dependencies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Dependency',
        request_type_name='ApihubProjectsLocationsDependenciesPatchRequest',
        response_type_name='GoogleCloudApihubV1Dependency',
        supports_download=False,
    )

  class ProjectsLocationsDeploymentsService(base_api.BaseApiService):
    """Service class for the projects_locations_deployments resource."""

    _NAME = 'projects_locations_deployments'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a deployment resource in the API hub. Once a deployment resource is created, it can be associated with API versions.

      Args:
        request: (ApihubProjectsLocationsDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Deployment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployments',
        http_method='POST',
        method_id='apihub.projects.locations.deployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deploymentId'],
        relative_path='v1/{+parent}/deployments',
        request_field='googleCloudApihubV1Deployment',
        request_type_name='ApihubProjectsLocationsDeploymentsCreateRequest',
        response_type_name='GoogleCloudApihubV1Deployment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a deployment resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.deployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsDeploymentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about a deployment and the API versions linked to it.

      Args:
        request: (ApihubProjectsLocationsDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Deployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}',
        http_method='GET',
        method_id='apihub.projects.locations.deployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsDeploymentsGetRequest',
        response_type_name='GoogleCloudApihubV1Deployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List deployment resources in the API hub.

      Args:
        request: (ApihubProjectsLocationsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployments',
        http_method='GET',
        method_id='apihub.projects.locations.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApihubProjectsLocationsDeploymentsListRequest',
        response_type_name='GoogleCloudApihubV1ListDeploymentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a deployment resource in the API hub. The following fields in the deployment resource can be updated: * display_name * description * documentation * deployment_type * resource_uri * endpoints * slo * environment * attributes The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Deployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.deployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1Deployment',
        request_type_name='ApihubProjectsLocationsDeploymentsPatchRequest',
        response_type_name='GoogleCloudApihubV1Deployment',
        supports_download=False,
    )

  class ProjectsLocationsExternalApisService(base_api.BaseApiService):
    """Service class for the projects_locations_externalApis resource."""

    _NAME = 'projects_locations_externalApis'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsExternalApisService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an External API resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsExternalApisCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ExternalApi) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/externalApis',
        http_method='POST',
        method_id='apihub.projects.locations.externalApis.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['externalApiId'],
        relative_path='v1/{+parent}/externalApis',
        request_field='googleCloudApihubV1ExternalApi',
        request_type_name='ApihubProjectsLocationsExternalApisCreateRequest',
        response_type_name='GoogleCloudApihubV1ExternalApi',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an External API resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsExternalApisDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.externalApis.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsExternalApisDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about an External API resource in the API hub.

      Args:
        request: (ApihubProjectsLocationsExternalApisGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ExternalApi) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}',
        http_method='GET',
        method_id='apihub.projects.locations.externalApis.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsExternalApisGetRequest',
        response_type_name='GoogleCloudApihubV1ExternalApi',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List External API resources in the API hub.

      Args:
        request: (ApihubProjectsLocationsExternalApisListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListExternalApisResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/externalApis',
        http_method='GET',
        method_id='apihub.projects.locations.externalApis.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/externalApis',
        request_field='',
        request_type_name='ApihubProjectsLocationsExternalApisListRequest',
        response_type_name='GoogleCloudApihubV1ListExternalApisResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an External API resource in the API hub. The following fields can be updated: * display_name * description * documentation * endpoints * paths The update_mask should be used to specify the fields being updated.

      Args:
        request: (ApihubProjectsLocationsExternalApisPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ExternalApi) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}',
        http_method='PATCH',
        method_id='apihub.projects.locations.externalApis.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1ExternalApi',
        request_type_name='ApihubProjectsLocationsExternalApisPatchRequest',
        response_type_name='GoogleCloudApihubV1ExternalApi',
        supports_download=False,
    )

  class ProjectsLocationsHostProjectRegistrationsService(base_api.BaseApiService):
    """Service class for the projects_locations_hostProjectRegistrations resource."""

    _NAME = 'projects_locations_hostProjectRegistrations'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsHostProjectRegistrationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a host project registration. A Google cloud project can be registered as a host project if it is not attached as a runtime project to another host project. A project can be registered as a host project only once. Subsequent register calls for the same project will fail.

      Args:
        request: (ApihubProjectsLocationsHostProjectRegistrationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1HostProjectRegistration) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations',
        http_method='POST',
        method_id='apihub.projects.locations.hostProjectRegistrations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hostProjectRegistrationId'],
        relative_path='v1/{+parent}/hostProjectRegistrations',
        request_field='googleCloudApihubV1HostProjectRegistration',
        request_type_name='ApihubProjectsLocationsHostProjectRegistrationsCreateRequest',
        response_type_name='GoogleCloudApihubV1HostProjectRegistration',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a host project registration.

      Args:
        request: (ApihubProjectsLocationsHostProjectRegistrationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1HostProjectRegistration) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations/{hostProjectRegistrationsId}',
        http_method='GET',
        method_id='apihub.projects.locations.hostProjectRegistrations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsHostProjectRegistrationsGetRequest',
        response_type_name='GoogleCloudApihubV1HostProjectRegistration',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists host project registrations.

      Args:
        request: (ApihubProjectsLocationsHostProjectRegistrationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListHostProjectRegistrationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations',
        http_method='GET',
        method_id='apihub.projects.locations.hostProjectRegistrations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/hostProjectRegistrations',
        request_field='',
        request_type_name='ApihubProjectsLocationsHostProjectRegistrationsListRequest',
        response_type_name='GoogleCloudApihubV1ListHostProjectRegistrationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ApihubProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='apihub.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='ApihubProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ApihubProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ApihubProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='apihub.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ApihubProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='apihub.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='ApihubProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPluginsInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_plugins_instances resource."""

    _NAME = 'projects_locations_plugins_instances'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsPluginsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Plugin instance in the API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pluginInstanceId'],
        relative_path='v1/{+parent}/instances',
        request_field='googleCloudApihubV1PluginInstance',
        request_type_name='ApihubProjectsLocationsPluginsInstancesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a plugin instance in the API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.plugins.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsInstancesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DisableAction(self, request, global_params=None):
      r"""Disables a plugin instance in the API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesDisableActionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DisableAction')
      return self._RunMethod(
          config, request, global_params=global_params)

    DisableAction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:disableAction',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.instances.disableAction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disableAction',
        request_field='googleCloudApihubV1DisablePluginInstanceActionRequest',
        request_type_name='ApihubProjectsLocationsPluginsInstancesDisableActionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def EnableAction(self, request, global_params=None):
      r"""Enables a plugin instance in the API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesEnableActionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('EnableAction')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnableAction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:enableAction',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.instances.enableAction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enableAction',
        request_field='googleCloudApihubV1EnablePluginInstanceActionRequest',
        request_type_name='ApihubProjectsLocationsPluginsInstancesEnableActionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExecuteAction(self, request, global_params=None):
      r"""Executes a plugin instance in the API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesExecuteActionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ExecuteAction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExecuteAction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:executeAction',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.instances.executeAction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:executeAction',
        request_field='googleCloudApihubV1ExecutePluginInstanceActionRequest',
        request_type_name='ApihubProjectsLocationsPluginsInstancesExecuteActionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an API Hub plugin instance.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1PluginInstance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsInstancesGetRequest',
        response_type_name='GoogleCloudApihubV1PluginInstance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all the plugins in a given project and location. `-` can be used as wildcard value for {plugin_id}.

      Args:
        request: (ApihubProjectsLocationsPluginsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListPluginInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/instances',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsInstancesListRequest',
        response_type_name='GoogleCloudApihubV1ListPluginInstancesResponse',
        supports_download=False,
    )

  class ProjectsLocationsPluginsStyleGuideService(base_api.BaseApiService):
    """Service class for the projects_locations_plugins_styleGuide resource."""

    _NAME = 'projects_locations_plugins_styleGuide'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsPluginsStyleGuideService, self).__init__(client)
      self._upload_configs = {
          }

    def GetContents(self, request, global_params=None):
      r"""Get the contents of the style guide.

      Args:
        request: (ApihubProjectsLocationsPluginsStyleGuideGetContentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1StyleGuideContents) The response message.
      """
      config = self.GetMethodConfig('GetContents')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide:contents',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.styleGuide.getContents',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:contents',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsStyleGuideGetContentsRequest',
        response_type_name='GoogleCloudApihubV1StyleGuideContents',
        supports_download=False,
    )

  class ProjectsLocationsPluginsService(base_api.BaseApiService):
    """Service class for the projects_locations_plugins resource."""

    _NAME = 'projects_locations_plugins'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsPluginsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an API Hub plugin resource in the API hub. Once a plugin is created, it can be used to create plugin instances.

      Args:
        request: (ApihubProjectsLocationsPluginsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Plugin) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pluginId'],
        relative_path='v1/{+parent}/plugins',
        request_field='googleCloudApihubV1Plugin',
        request_type_name='ApihubProjectsLocationsPluginsCreateRequest',
        response_type_name='GoogleCloudApihubV1Plugin',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a Plugin in API hub. Note, only user owned plugins can be deleted via this method.

      Args:
        request: (ApihubProjectsLocationsPluginsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.plugins.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disables a plugin. The `state` of the plugin after disabling is `DISABLED`.

      Args:
        request: (ApihubProjectsLocationsPluginsDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Plugin) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}:disable',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='googleCloudApihubV1DisablePluginRequest',
        request_type_name='ApihubProjectsLocationsPluginsDisableRequest',
        response_type_name='GoogleCloudApihubV1Plugin',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enables a plugin. The `state` of the plugin after enabling is `ENABLED`.

      Args:
        request: (ApihubProjectsLocationsPluginsEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Plugin) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}:enable',
        http_method='POST',
        method_id='apihub.projects.locations.plugins.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='googleCloudApihubV1EnablePluginRequest',
        request_type_name='ApihubProjectsLocationsPluginsEnableRequest',
        response_type_name='GoogleCloudApihubV1Plugin',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an API Hub plugin.

      Args:
        request: (ApihubProjectsLocationsPluginsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1Plugin) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsGetRequest',
        response_type_name='GoogleCloudApihubV1Plugin',
        supports_download=False,
    )

    def GetStyleGuide(self, request, global_params=None):
      r"""Get the style guide being used for linting.

      Args:
        request: (ApihubProjectsLocationsPluginsGetStyleGuideRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1StyleGuide) The response message.
      """
      config = self.GetMethodConfig('GetStyleGuide')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetStyleGuide.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.getStyleGuide',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsGetStyleGuideRequest',
        response_type_name='GoogleCloudApihubV1StyleGuide',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all the plugins in a given project and location.

      Args:
        request: (ApihubProjectsLocationsPluginsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListPluginsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins',
        http_method='GET',
        method_id='apihub.projects.locations.plugins.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/plugins',
        request_field='',
        request_type_name='ApihubProjectsLocationsPluginsListRequest',
        response_type_name='GoogleCloudApihubV1ListPluginsResponse',
        supports_download=False,
    )

    def UpdateStyleGuide(self, request, global_params=None):
      r"""Update the styleGuide to be used for liniting in by API hub.

      Args:
        request: (ApihubProjectsLocationsPluginsUpdateStyleGuideRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1StyleGuide) The response message.
      """
      config = self.GetMethodConfig('UpdateStyleGuide')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateStyleGuide.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide',
        http_method='PATCH',
        method_id='apihub.projects.locations.plugins.updateStyleGuide',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApihubV1StyleGuide',
        request_type_name='ApihubProjectsLocationsPluginsUpdateStyleGuideRequest',
        response_type_name='GoogleCloudApihubV1StyleGuide',
        supports_download=False,
    )

  class ProjectsLocationsRuntimeProjectAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_runtimeProjectAttachments resource."""

    _NAME = 'projects_locations_runtimeProjectAttachments'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsRuntimeProjectAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Attaches a runtime project to the host project.

      Args:
        request: (ApihubProjectsLocationsRuntimeProjectAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1RuntimeProjectAttachment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments',
        http_method='POST',
        method_id='apihub.projects.locations.runtimeProjectAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['runtimeProjectAttachmentId'],
        relative_path='v1/{+parent}/runtimeProjectAttachments',
        request_field='googleCloudApihubV1RuntimeProjectAttachment',
        request_type_name='ApihubProjectsLocationsRuntimeProjectAttachmentsCreateRequest',
        response_type_name='GoogleCloudApihubV1RuntimeProjectAttachment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a runtime project attachment in the API Hub. This call will detach the runtime project from the host project.

      Args:
        request: (ApihubProjectsLocationsRuntimeProjectAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments/{runtimeProjectAttachmentsId}',
        http_method='DELETE',
        method_id='apihub.projects.locations.runtimeProjectAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsRuntimeProjectAttachmentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a runtime project attachment.

      Args:
        request: (ApihubProjectsLocationsRuntimeProjectAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1RuntimeProjectAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments/{runtimeProjectAttachmentsId}',
        http_method='GET',
        method_id='apihub.projects.locations.runtimeProjectAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsRuntimeProjectAttachmentsGetRequest',
        response_type_name='GoogleCloudApihubV1RuntimeProjectAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List runtime projects attached to the host project.

      Args:
        request: (ApihubProjectsLocationsRuntimeProjectAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments',
        http_method='GET',
        method_id='apihub.projects.locations.runtimeProjectAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/runtimeProjectAttachments',
        request_field='',
        request_type_name='ApihubProjectsLocationsRuntimeProjectAttachmentsListRequest',
        response_type_name='GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ApihubV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def CollectApiData(self, request, global_params=None):
      r"""Collect API data from a source and push it to Hub's collect layer.

      Args:
        request: (ApihubProjectsLocationsCollectApiDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('CollectApiData')
      return self._RunMethod(
          config, request, global_params=global_params)

    CollectApiData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:collectApiData',
        http_method='POST',
        method_id='apihub.projects.locations.collectApiData',
        ordered_params=['location'],
        path_params=['location'],
        query_params=[],
        relative_path='v1/{+location}:collectApiData',
        request_field='googleCloudApihubV1CollectApiDataRequest',
        request_type_name='ApihubProjectsLocationsCollectApiDataRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ApihubProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='apihub.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApihubProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ApihubProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='apihub.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='ApihubProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

    def LookupRuntimeProjectAttachment(self, request, global_params=None):
      r"""Look up a runtime project attachment. This API can be called in the context of any project.

      Args:
        request: (ApihubProjectsLocationsLookupRuntimeProjectAttachmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse) The response message.
      """
      config = self.GetMethodConfig('LookupRuntimeProjectAttachment')
      return self._RunMethod(
          config, request, global_params=global_params)

    LookupRuntimeProjectAttachment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:lookupRuntimeProjectAttachment',
        http_method='GET',
        method_id='apihub.projects.locations.lookupRuntimeProjectAttachment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:lookupRuntimeProjectAttachment',
        request_field='',
        request_type_name='ApihubProjectsLocationsLookupRuntimeProjectAttachmentRequest',
        response_type_name='GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse',
        supports_download=False,
    )

    def SearchResources(self, request, global_params=None):
      r"""Search across API-Hub resources.

      Args:
        request: (ApihubProjectsLocationsSearchResourcesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApihubV1SearchResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchResources')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchResources.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:searchResources',
        http_method='POST',
        method_id='apihub.projects.locations.searchResources',
        ordered_params=['location'],
        path_params=['location'],
        query_params=[],
        relative_path='v1/{+location}:searchResources',
        request_field='googleCloudApihubV1SearchResourcesRequest',
        request_type_name='ApihubProjectsLocationsSearchResourcesRequest',
        response_type_name='GoogleCloudApihubV1SearchResourcesResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ApihubV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
