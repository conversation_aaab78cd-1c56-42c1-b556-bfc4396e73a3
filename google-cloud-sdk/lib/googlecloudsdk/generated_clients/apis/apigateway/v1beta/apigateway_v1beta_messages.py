"""Generated message classes for apigateway version v1beta.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'apigateway'


class ApigatewayApi(_messages.Message):
  r"""An API that can be served by one or more Gateways.

  Enums:
    StateValueValuesEnum: Output only. State of the API.

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    displayName: Optional. Display name.
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    managedService: Optional. Immutable. The name of a Google Managed Service
      ( https://cloud.google.com/service-
      infrastructure/docs/glossary#managed). If not specified, a new Service
      will automatically be created in the same project as this API.
    name: Output only. Resource name of the API. Format:
      projects/{project}/locations/global/apis/{api}
    state: Output only. State of the API.
    updateTime: Output only. Updated time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the API.

    Values:
      STATE_UNSPECIFIED: API does not have a state yet.
      CREATING: API is being created.
      ACTIVE: API is active.
      FAILED: API creation failed.
      DELETING: API is being deleted.
      UPDATING: API is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    FAILED = 3
    DELETING = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  managedService = _messages.StringField(4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class ApigatewayApiConfig(_messages.Message):
  r"""An API Configuration is a combination of settings for both the Managed
  Service and Gateways serving this API Config.

  Enums:
    StateValueValuesEnum: Output only. State of the API Config.

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    displayName: Optional. Display name.
    gatewayConfig: Immutable. Gateway specific configuration.
    gatewayServiceAccount: Immutable. The Google Cloud IAM Service Account
      that Gateways serving this config should use to authenticate to other
      services. This may either be the Service Account's email
      (`{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com`) or its full resource
      name (`projects/{PROJECT}/accounts/{UNIQUE_ID}`). This is most often
      used when the service is a GCP resource such as a Cloud Run Service or
      an IAP-secured service.
    grpcServices: Optional. gRPC service definition files. If specified,
      openapi_documents must not be included.
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    managedServiceConfigs: Optional. Service Configuration files. At least one
      must be included when using gRPC service definitions. See
      https://cloud.google.com/endpoints/docs/grpc/grpc-service-
      config#service_configuration_overview for the expected file contents. If
      multiple files are specified, the files are merged with the following
      rules: * All singular scalar fields are merged using "last one wins"
      semantics in the order of the files uploaded. * Repeated fields are
      concatenated. * Singular embedded messages are merged using these rules
      for nested fields.
    name: Output only. Resource name of the API Config. Format:
      projects/{project}/locations/global/apis/{api}/configs/{api_config}
    openapiDocuments: Optional. OpenAPI specification documents. If specified,
      grpc_services and managed_service_configs must not be included.
    serviceConfigId: Output only. The ID of the associated Service Config (
      https://cloud.google.com/service-infrastructure/docs/glossary#config).
    state: Output only. State of the API Config.
    updateTime: Output only. Updated time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the API Config.

    Values:
      STATE_UNSPECIFIED: API Config does not have a state yet.
      CREATING: API Config is being created and deployed to the API
        Controller.
      ACTIVE: API Config is ready for use by Gateways.
      FAILED: API Config creation failed.
      DELETING: API Config is being deleted.
      UPDATING: API Config is being updated.
      ACTIVATING: API Config settings are being activated in downstream
        systems. API Configs in this state cannot be used by Gateways.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    FAILED = 3
    DELETING = 4
    UPDATING = 5
    ACTIVATING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gatewayConfig = _messages.MessageField('ApigatewayGatewayConfig', 3)
  gatewayServiceAccount = _messages.StringField(4)
  grpcServices = _messages.MessageField('ApigatewayApiConfigGrpcServiceDefinition', 5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  managedServiceConfigs = _messages.MessageField('ApigatewayApiConfigFile', 7, repeated=True)
  name = _messages.StringField(8)
  openapiDocuments = _messages.MessageField('ApigatewayApiConfigOpenApiDocument', 9, repeated=True)
  serviceConfigId = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  updateTime = _messages.StringField(12)


class ApigatewayApiConfigFile(_messages.Message):
  r"""A lightweight description of a file.

  Fields:
    contents: The bytes that constitute the file.
    path: The file path (full or relative path). This is typically the path of
      the file when it is uploaded.
  """

  contents = _messages.BytesField(1)
  path = _messages.StringField(2)


class ApigatewayApiConfigGrpcServiceDefinition(_messages.Message):
  r"""A gRPC service definition.

  Fields:
    fileDescriptorSet: Input only. File descriptor set, generated by protoc.
      To generate, use protoc with imports and source info included. For an
      example test.proto file, the following command would put the value in a
      new file named out.pb. $ protoc --include_imports --include_source_info
      test.proto -o out.pb
    source: Optional. Uncompiled proto files associated with the descriptor
      set, used for display purposes (server-side compilation is not
      supported). These should match the inputs to 'protoc' command used to
      generate file_descriptor_set.
  """

  fileDescriptorSet = _messages.MessageField('ApigatewayApiConfigFile', 1)
  source = _messages.MessageField('ApigatewayApiConfigFile', 2, repeated=True)


class ApigatewayApiConfigOpenApiDocument(_messages.Message):
  r"""An OpenAPI Specification Document describing an API.

  Fields:
    document: The OpenAPI Specification document file.
  """

  document = _messages.MessageField('ApigatewayApiConfigFile', 1)


class ApigatewayAuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('ApigatewayAuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class ApigatewayAuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class ApigatewayBackendConfig(_messages.Message):
  r"""Configuration for all backends.

  Fields:
    googleServiceAccount: Google Cloud IAM service account used to sign OIDC
      tokens for backends that have authentication configured
      (https://cloud.google.com/service-infrastructure/docs/service-
      management/reference/rest/v1/services.configs#backend). This may either
      be the Service Account's email (i.e.
      "{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com") or its full resource
      name (i.e. "projects/{PROJECT}/accounts/{UNIQUE_ID}"). This is most
      often used when the backend is a GCP resource such as a Cloud Run
      Service or an IAP-secured service. Note that this token is always sent
      as an authorization header bearer token. The audience of the OIDC token
      is configured in the associated Service Config in the BackendRule option
      (https://github.com/googleapis/googleapis/blob/master/google/api/backend
      .proto#L125).
  """

  googleServiceAccount = _messages.StringField(1)


class ApigatewayBinding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('ApigatewayExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ApigatewayCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ApigatewayExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ApigatewayGateway(_messages.Message):
  r"""A Gateway is an API-aware HTTP proxy. It performs API-Method and/or API-
  Consumer specific actions based on an API Config such as authentication,
  policy enforcement, and backend selection.

  Enums:
    StateValueValuesEnum: Output only. The current state of the Gateway.

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    apiConfig: Required. Resource name of the API Config for this Gateway.
      Format:
      projects/{project}/locations/global/apis/{api}/configs/{apiConfig}
    createTime: Output only. Created time.
    defaultHostname: Output only. The default API Gateway host name of the
      form `{gateway_id}-{hash}.{region_code}.gateway.dev`.
    displayName: Optional. Display name.
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    name: Output only. Resource name of the Gateway. Format:
      projects/{project}/locations/{location}/gateways/{gateway}
    state: Output only. The current state of the Gateway.
    updateTime: Output only. Updated time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Gateway.

    Values:
      STATE_UNSPECIFIED: Gateway does not have a state yet.
      CREATING: Gateway is being created.
      ACTIVE: Gateway is running and ready for requests.
      FAILED: Gateway creation failed.
      DELETING: Gateway is being deleted.
      UPDATING: Gateway is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    FAILED = 3
    DELETING = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiConfig = _messages.StringField(1)
  createTime = _messages.StringField(2)
  defaultHostname = _messages.StringField(3)
  displayName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class ApigatewayGatewayConfig(_messages.Message):
  r"""Configuration settings for Gateways.

  Fields:
    backendConfig: Required. Backend settings that are applied to all backends
      of the Gateway.
  """

  backendConfig = _messages.MessageField('ApigatewayBackendConfig', 1)


class ApigatewayListApiConfigsResponse(_messages.Message):
  r"""Response message for ApiGatewayService.ListApiConfigs

  Fields:
    apiConfigs: API Configs.
    nextPageToken: Next page token.
    unreachableLocations: Locations that could not be reached.
  """

  apiConfigs = _messages.MessageField('ApigatewayApiConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class ApigatewayListApisResponse(_messages.Message):
  r"""Response message for ApiGatewayService.ListApis

  Fields:
    apis: APIs.
    nextPageToken: Next page token.
    unreachableLocations: Locations that could not be reached.
  """

  apis = _messages.MessageField('ApigatewayApi', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class ApigatewayListGatewaysResponse(_messages.Message):
  r"""Response message for ApiGatewayService.ListGateways

  Fields:
    gateways: Gateways.
    nextPageToken: Next page token.
    unreachableLocations: Locations that could not be reached.
  """

  gateways = _messages.MessageField('ApigatewayGateway', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class ApigatewayListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('ApigatewayLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ApigatewayListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('ApigatewayOperation', 2, repeated=True)


class ApigatewayLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ApigatewayOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('ApigatewayStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class ApigatewayOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    diagnostics: Output only. Diagnostics generated during processing of
      configuration source files.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  diagnostics = _messages.MessageField('ApigatewayOperationMetadataDiagnostic', 3, repeated=True)
  endTime = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class ApigatewayOperationMetadataDiagnostic(_messages.Message):
  r"""Diagnostic information from configuration processing.

  Fields:
    location: Location of the diagnostic.
    message: The diagnostic message.
  """

  location = _messages.StringField(1)
  message = _messages.StringField(2)


class ApigatewayPolicy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('ApigatewayAuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('ApigatewayBinding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ApigatewayProjectsLocationsApisConfigsCreateRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsCreateRequest object.

  Fields:
    apiConfigId: Required. Identifier to assign to the API Config. Must be
      unique within scope of the parent resource.
    apigatewayApiConfig: A ApigatewayApiConfig resource to be passed as the
      request body.
    parent: Required. Parent resource of the API Config, of the form:
      `projects/*/locations/global/apis/*`
  """

  apiConfigId = _messages.StringField(1)
  apigatewayApiConfig = _messages.MessageField('ApigatewayApiConfig', 2)
  parent = _messages.StringField(3, required=True)


class ApigatewayProjectsLocationsApisConfigsDeleteRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/apis/*/configs/*`
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsApisConfigsGetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsApisConfigsGetRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsGetRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which fields of the API Config are returned
      in the response. Defaults to `BASIC` view.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/apis/*/configs/*`
    view: Specifies which fields of the API Config are returned in the
      response. Defaults to `BASIC` view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which fields of the API Config are returned in the response.
    Defaults to `BASIC` view.

    Values:
      CONFIG_VIEW_UNSPECIFIED: <no description>
      BASIC: Do not include configuration source files.
      FULL: Include configuration source files.
    """
    CONFIG_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ApigatewayProjectsLocationsApisConfigsListRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsListRequest object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the API Config, of the form:
      `projects/*/locations/global/apis/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApigatewayProjectsLocationsApisConfigsPatchRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsPatchRequest object.

  Fields:
    apigatewayApiConfig: A ApigatewayApiConfig resource to be passed as the
      request body.
    name: Output only. Resource name of the API Config. Format:
      projects/{project}/locations/global/apis/{api}/configs/{api_config}
    updateMask: Field mask is used to specify the fields to be overwritten in
      the ApiConfig resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  apigatewayApiConfig = _messages.MessageField('ApigatewayApiConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigatewayProjectsLocationsApisConfigsSetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsSetIamPolicyRequest object.

  Fields:
    apigatewaySetIamPolicyRequest: A ApigatewaySetIamPolicyRequest resource to
      be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewaySetIamPolicyRequest = _messages.MessageField('ApigatewaySetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsApisConfigsTestIamPermissionsRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisConfigsTestIamPermissionsRequest
  object.

  Fields:
    apigatewayTestIamPermissionsRequest: A ApigatewayTestIamPermissionsRequest
      resource to be passed as the request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewayTestIamPermissionsRequest = _messages.MessageField('ApigatewayTestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsApisCreateRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisCreateRequest object.

  Fields:
    apiId: Required. Identifier to assign to the API. Must be unique within
      scope of the parent resource.
    apigatewayApi: A ApigatewayApi resource to be passed as the request body.
    parent: Required. Parent resource of the API, of the form:
      `projects/*/locations/global`
  """

  apiId = _messages.StringField(1)
  apigatewayApi = _messages.MessageField('ApigatewayApi', 2)
  parent = _messages.StringField(3, required=True)


class ApigatewayProjectsLocationsApisDeleteRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/apis/*`
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsApisGetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsApisGetRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/apis/*`
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsApisListRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisListRequest object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the API, of the form:
      `projects/*/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApigatewayProjectsLocationsApisPatchRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisPatchRequest object.

  Fields:
    apigatewayApi: A ApigatewayApi resource to be passed as the request body.
    name: Output only. Resource name of the API. Format:
      projects/{project}/locations/global/apis/{api}
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Api resource by the update. The fields specified in the update_mask
      are relative to the resource, not the full request. A field will be
      overwritten if it is in the mask. If the user does not provide a mask
      then all fields will be overwritten.
  """

  apigatewayApi = _messages.MessageField('ApigatewayApi', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigatewayProjectsLocationsApisSetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisSetIamPolicyRequest object.

  Fields:
    apigatewaySetIamPolicyRequest: A ApigatewaySetIamPolicyRequest resource to
      be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewaySetIamPolicyRequest = _messages.MessageField('ApigatewaySetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsApisTestIamPermissionsRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsApisTestIamPermissionsRequest object.

  Fields:
    apigatewayTestIamPermissionsRequest: A ApigatewayTestIamPermissionsRequest
      resource to be passed as the request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewayTestIamPermissionsRequest = _messages.MessageField('ApigatewayTestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsGatewaysCreateRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysCreateRequest object.

  Fields:
    apigatewayGateway: A ApigatewayGateway resource to be passed as the
      request body.
    gatewayId: Required. Identifier to assign to the Gateway. Must be unique
      within scope of the parent resource.
    parent: Required. Parent resource of the Gateway, of the form:
      `projects/*/locations/*`
  """

  apigatewayGateway = _messages.MessageField('ApigatewayGateway', 1)
  gatewayId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigatewayProjectsLocationsGatewaysDeleteRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/gateways/*`
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsGatewaysGetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsGatewaysGetRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/gateways/*`
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsGatewaysListRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysListRequest object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the Gateway, of the form:
      `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApigatewayProjectsLocationsGatewaysPatchRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysPatchRequest object.

  Fields:
    apigatewayGateway: A ApigatewayGateway resource to be passed as the
      request body.
    name: Output only. Resource name of the Gateway. Format:
      projects/{project}/locations/{location}/gateways/{gateway}
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Gateway resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  apigatewayGateway = _messages.MessageField('ApigatewayGateway', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigatewayProjectsLocationsGatewaysSetIamPolicyRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysSetIamPolicyRequest object.

  Fields:
    apigatewaySetIamPolicyRequest: A ApigatewaySetIamPolicyRequest resource to
      be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewaySetIamPolicyRequest = _messages.MessageField('ApigatewaySetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsGatewaysTestIamPermissionsRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGatewaysTestIamPermissionsRequest object.

  Fields:
    apigatewayTestIamPermissionsRequest: A ApigatewayTestIamPermissionsRequest
      resource to be passed as the request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  apigatewayTestIamPermissionsRequest = _messages.MessageField('ApigatewayTestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsGetRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsListRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ApigatewayProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsOperationsCancelRequest object.

  Fields:
    apigatewayCancelOperationRequest: A ApigatewayCancelOperationRequest
      resource to be passed as the request body.
    name: The name of the operation resource to be cancelled.
  """

  apigatewayCancelOperationRequest = _messages.MessageField('ApigatewayCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigatewayProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ApigatewayProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ApigatewayProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ApigatewaySetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('ApigatewayPolicy', 1)
  updateMask = _messages.StringField(2)


class ApigatewayStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class ApigatewayTestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class ApigatewayTestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ApigatewayProjectsLocationsApisGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ApigatewayProjectsLocationsApisConfigsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ApigatewayProjectsLocationsGatewaysGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
