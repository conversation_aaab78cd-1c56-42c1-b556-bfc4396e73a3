"""Generated message classes for clouddebugger version v2.

Examines the call stack and variables of a running application without
stopping or slowing it down. (Deprecated)
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'clouddebugger'


class AliasContext(_messages.Message):
  r"""An alias to a repo revision.

  Enums:
    KindValueValuesEnum: The alias kind.

  Fields:
    kind: The alias kind.
    name: The alias name.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The alias kind.

    Values:
      ANY: Do not use.
      FIXED: Git tag
      MOVABLE: Git branch
      OTHER: OTHER is used to specify non-standard aliases, those not of the
        kinds above. For example, if a Git repo has a ref named
        "refs/foo/bar", it is considered to be of kind OTHER.
    """
    ANY = 0
    FIXED = 1
    MOVABLE = 2
    OTHER = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)


class Breakpoint(_messages.Message):
  r"""------------------------------------------------------------------------
  ------ ## Breakpoint (the resource) Represents the breakpoint specification,
  status and results.

  Enums:
    ActionValueValuesEnum: Action that the agent should perform when the code
      at the breakpoint location is hit.
    LogLevelValueValuesEnum: Indicates the severity of the log. Only relevant
      when action is `LOG`.
    StateValueValuesEnum: The current state of the breakpoint.

  Messages:
    LabelsValue: A set of custom breakpoint properties, populated by the
      agent, to be displayed to the user.

  Fields:
    action: Action that the agent should perform when the code at the
      breakpoint location is hit.
    canaryExpireTime: The deadline for the breakpoint to stay in CANARY_ACTIVE
      state. The value is meaningless when the breakpoint is not in
      CANARY_ACTIVE state.
    condition: Condition that triggers the breakpoint. The condition is a
      compound boolean expression composed using expressions in a programming
      language at the source location.
    createTime: Time this breakpoint was created by the server in seconds
      resolution.
    evaluatedExpressions: Values of evaluated expressions at breakpoint time.
      The evaluated expressions appear in exactly the same order they are
      listed in the `expressions` field. The `name` field holds the original
      expression text, the `value` or `members` field holds the result of the
      evaluated expression. If the expression cannot be evaluated, the
      `status` inside the `Variable` will indicate an error and contain the
      error text.
    expressions: List of read-only expressions to evaluate at the breakpoint
      location. The expressions are composed using expressions in the
      programming language at the source location. If the breakpoint action is
      `LOG`, the evaluated expressions are included in log statements.
    finalTime: Time this breakpoint was finalized as seen by the server in
      seconds resolution.
    id: Breakpoint identifier, unique in the scope of the debuggee.
    isFinalState: When true, indicates that this is a final result and the
      breakpoint state will not change from here on.
    labels: A set of custom breakpoint properties, populated by the agent, to
      be displayed to the user.
    location: Breakpoint source location.
    logLevel: Indicates the severity of the log. Only relevant when action is
      `LOG`.
    logMessageFormat: Only relevant when action is `LOG`. Defines the message
      to log when the breakpoint hits. The message may include parameter
      placeholders `$0`, `$1`, etc. These placeholders are replaced with the
      evaluated value of the appropriate expression. Expressions not
      referenced in `log_message_format` are not logged. Example: `Message
      received, id = $0, count = $1` with `expressions` = `[ message.id,
      message.count ]`.
    stackFrames: The stack at breakpoint time, where stack_frames[0]
      represents the most recently entered function.
    state: The current state of the breakpoint.
    status: Breakpoint status. The status includes an error flag and a human
      readable message. This field is usually unset. The message can be either
      informational or an error message. Regardless, clients should always
      display the text message back to the user. Error status indicates
      complete failure of the breakpoint. Example (non-final state): `Still
      loading symbols...` Examples (final state): * `Invalid line number`
      referring to location * `Field f not found in class C` referring to
      condition
    userEmail: E-mail address of the user that created this breakpoint
    variableTable: The `variable_table` exists to aid with computation, memory
      and network traffic optimization. It enables storing a variable once and
      reference it from multiple variables, including variables stored in the
      `variable_table` itself. For example, the same `this` object, which may
      appear at many levels of the stack, can have all of its data stored once
      in this table. The stack frame variables then would hold only a
      reference to it. The variable `var_table_index` field is an index into
      this repeated field. The stored objects are nameless and get their name
      from the referencing variable. The effective variable is a merge of the
      referencing variable and the referenced variable.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Action that the agent should perform when the code at the breakpoint
    location is hit.

    Values:
      CAPTURE: Capture stack frame and variables and update the breakpoint.
        The data is only captured once. After that the breakpoint is set in a
        final state.
      LOG: Log each breakpoint hit. The breakpoint remains active until
        deleted or expired.
    """
    CAPTURE = 0
    LOG = 1

  class LogLevelValueValuesEnum(_messages.Enum):
    r"""Indicates the severity of the log. Only relevant when action is `LOG`.

    Values:
      INFO: Information log message.
      WARNING: Warning log message.
      ERROR: Error log message.
    """
    INFO = 0
    WARNING = 1
    ERROR = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the breakpoint.

    Values:
      STATE_UNSPECIFIED: Breakpoint state UNSPECIFIED.
      STATE_CANARY_PENDING_AGENTS: Enabling canary but no agents are
        available.
      STATE_CANARY_ACTIVE: Enabling canary and successfully assigning canary
        agents.
      STATE_ROLLING_TO_ALL: Breakpoint rolling out to all agents.
      STATE_IS_FINAL: Breakpoint is hit/complete/failed.
    """
    STATE_UNSPECIFIED = 0
    STATE_CANARY_PENDING_AGENTS = 1
    STATE_CANARY_ACTIVE = 2
    STATE_ROLLING_TO_ALL = 3
    STATE_IS_FINAL = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A set of custom breakpoint properties, populated by the agent, to be
    displayed to the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  canaryExpireTime = _messages.StringField(2)
  condition = _messages.StringField(3)
  createTime = _messages.StringField(4)
  evaluatedExpressions = _messages.MessageField('Variable', 5, repeated=True)
  expressions = _messages.StringField(6, repeated=True)
  finalTime = _messages.StringField(7)
  id = _messages.StringField(8)
  isFinalState = _messages.BooleanField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  location = _messages.MessageField('SourceLocation', 11)
  logLevel = _messages.EnumField('LogLevelValueValuesEnum', 12)
  logMessageFormat = _messages.StringField(13)
  stackFrames = _messages.MessageField('StackFrame', 14, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 15)
  status = _messages.MessageField('StatusMessage', 16)
  userEmail = _messages.StringField(17)
  variableTable = _messages.MessageField('Variable', 18, repeated=True)


class CloudRepoSourceContext(_messages.Message):
  r"""A CloudRepoSourceContext denotes a particular revision in a cloud repo
  (a repo hosted by the Google Cloud Platform).

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    aliasName: The name of an alias (branch, tag, etc.).
    repoId: The ID of the repo.
    revisionId: A revision ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  aliasName = _messages.StringField(2)
  repoId = _messages.MessageField('RepoId', 3)
  revisionId = _messages.StringField(4)


class CloudWorkspaceId(_messages.Message):
  r"""A CloudWorkspaceId is a unique identifier for a cloud workspace. A cloud
  workspace is a place associated with a repo where modified files can be
  stored before they are committed.

  Fields:
    name: The unique name of the workspace within the repo. This is the name
      chosen by the client in the Source API's CreateWorkspace method.
    repoId: The ID of the repo containing the workspace.
  """

  name = _messages.StringField(1)
  repoId = _messages.MessageField('RepoId', 2)


class CloudWorkspaceSourceContext(_messages.Message):
  r"""A CloudWorkspaceSourceContext denotes a workspace at a particular
  snapshot.

  Fields:
    snapshotId: The ID of the snapshot. An empty snapshot_id refers to the
      most recent snapshot.
    workspaceId: The ID of the workspace.
  """

  snapshotId = _messages.StringField(1)
  workspaceId = _messages.MessageField('CloudWorkspaceId', 2)


class ClouddebuggerControllerDebuggeesBreakpointsListRequest(_messages.Message):
  r"""A ClouddebuggerControllerDebuggeesBreakpointsListRequest object.

  Fields:
    agentId: Identifies the agent. This is the ID returned in the
      RegisterDebuggee response.
    debuggeeId: Required. Identifies the debuggee.
    successOnTimeout: If set to `true` (recommended), returns
      `google.rpc.Code.OK` status and sets the `wait_expired` response field
      to `true` when the server-selected timeout has expired. If set to
      `false` (deprecated), returns `google.rpc.Code.ABORTED` status when the
      server-selected timeout has expired.
    waitToken: A token that, if specified, blocks the method call until the
      list of active breakpoints has changed, or a server-selected timeout has
      expired. The value should be set from the `next_wait_token` field in the
      last response. The initial value should be set to `"init"`.
  """

  agentId = _messages.StringField(1)
  debuggeeId = _messages.StringField(2, required=True)
  successOnTimeout = _messages.BooleanField(3)
  waitToken = _messages.StringField(4)


class ClouddebuggerControllerDebuggeesBreakpointsUpdateRequest(_messages.Message):
  r"""A ClouddebuggerControllerDebuggeesBreakpointsUpdateRequest object.

  Fields:
    debuggeeId: Required. Identifies the debuggee being debugged.
    id: Breakpoint identifier, unique in the scope of the debuggee.
    updateActiveBreakpointRequest: A UpdateActiveBreakpointRequest resource to
      be passed as the request body.
  """

  debuggeeId = _messages.StringField(1, required=True)
  id = _messages.StringField(2, required=True)
  updateActiveBreakpointRequest = _messages.MessageField('UpdateActiveBreakpointRequest', 3)


class ClouddebuggerDebuggerDebuggeesBreakpointsDeleteRequest(_messages.Message):
  r"""A ClouddebuggerDebuggerDebuggeesBreakpointsDeleteRequest object.

  Fields:
    breakpointId: Required. ID of the breakpoint to delete.
    clientVersion: Required. The client version making the call. Schema:
      `domain/type/version` (e.g., `google.com/intellij/v1`).
    debuggeeId: Required. ID of the debuggee whose breakpoint to delete.
  """

  breakpointId = _messages.StringField(1, required=True)
  clientVersion = _messages.StringField(2)
  debuggeeId = _messages.StringField(3, required=True)


class ClouddebuggerDebuggerDebuggeesBreakpointsGetRequest(_messages.Message):
  r"""A ClouddebuggerDebuggerDebuggeesBreakpointsGetRequest object.

  Fields:
    breakpointId: Required. ID of the breakpoint to get.
    clientVersion: Required. The client version making the call. Schema:
      `domain/type/version` (e.g., `google.com/intellij/v1`).
    debuggeeId: Required. ID of the debuggee whose breakpoint to get.
  """

  breakpointId = _messages.StringField(1, required=True)
  clientVersion = _messages.StringField(2)
  debuggeeId = _messages.StringField(3, required=True)


class ClouddebuggerDebuggerDebuggeesBreakpointsListRequest(_messages.Message):
  r"""A ClouddebuggerDebuggerDebuggeesBreakpointsListRequest object.

  Enums:
    ActionValueValueValuesEnum: Only breakpoints with the specified action
      will pass the filter.

  Fields:
    action_value: Only breakpoints with the specified action will pass the
      filter.
    clientVersion: Required. The client version making the call. Schema:
      `domain/type/version` (e.g., `google.com/intellij/v1`).
    debuggeeId: Required. ID of the debuggee whose breakpoints to list.
    includeAllUsers: When set to `true`, the response includes the list of
      breakpoints set by any user. Otherwise, it includes only breakpoints set
      by the caller.
    includeInactive: When set to `true`, the response includes active and
      inactive breakpoints. Otherwise, it includes only active breakpoints.
    stripResults: This field is deprecated. The following fields are always
      stripped out of the result: `stack_frames`, `evaluated_expressions` and
      `variable_table`.
    waitToken: A wait token that, if specified, blocks the call until the
      breakpoints list has changed, or a server selected timeout has expired.
      The value should be set from the last response. The error code
      `google.rpc.Code.ABORTED` (RPC) is returned on wait timeout, which
      should be called again with the same `wait_token`.
  """

  class ActionValueValueValuesEnum(_messages.Enum):
    r"""Only breakpoints with the specified action will pass the filter.

    Values:
      CAPTURE: Capture stack frame and variables and update the breakpoint.
        The data is only captured once. After that the breakpoint is set in a
        final state.
      LOG: Log each breakpoint hit. The breakpoint remains active until
        deleted or expired.
    """
    CAPTURE = 0
    LOG = 1

  action_value = _messages.EnumField('ActionValueValueValuesEnum', 1)
  clientVersion = _messages.StringField(2)
  debuggeeId = _messages.StringField(3, required=True)
  includeAllUsers = _messages.BooleanField(4)
  includeInactive = _messages.BooleanField(5)
  stripResults = _messages.BooleanField(6)
  waitToken = _messages.StringField(7)


class ClouddebuggerDebuggerDebuggeesBreakpointsSetRequest(_messages.Message):
  r"""A ClouddebuggerDebuggerDebuggeesBreakpointsSetRequest object.

  Enums:
    CanaryOptionValueValuesEnum: The canary option set by the user upon
      setting breakpoint.

  Fields:
    breakpoint: A Breakpoint resource to be passed as the request body.
    canaryOption: The canary option set by the user upon setting breakpoint.
    clientVersion: Required. The client version making the call. Schema:
      `domain/type/version` (e.g., `google.com/intellij/v1`).
    debuggeeId: Required. ID of the debuggee where the breakpoint is to be
      set.
  """

  class CanaryOptionValueValuesEnum(_messages.Enum):
    r"""The canary option set by the user upon setting breakpoint.

    Values:
      CANARY_OPTION_UNSPECIFIED: Depends on the canary_mode of the debuggee.
      CANARY_OPTION_TRY_ENABLE: Enable the canary for this breakpoint if the
        canary_mode of the debuggee is not CANARY_MODE_ALWAYS_ENABLED or
        CANARY_MODE_ALWAYS_DISABLED.
      CANARY_OPTION_TRY_DISABLE: Disable the canary for this breakpoint if the
        canary_mode of the debuggee is not CANARY_MODE_ALWAYS_ENABLED or
        CANARY_MODE_ALWAYS_DISABLED.
    """
    CANARY_OPTION_UNSPECIFIED = 0
    CANARY_OPTION_TRY_ENABLE = 1
    CANARY_OPTION_TRY_DISABLE = 2

  breakpoint = _messages.MessageField('Breakpoint', 1)
  canaryOption = _messages.EnumField('CanaryOptionValueValuesEnum', 2)
  clientVersion = _messages.StringField(3)
  debuggeeId = _messages.StringField(4, required=True)


class ClouddebuggerDebuggerDebuggeesListRequest(_messages.Message):
  r"""A ClouddebuggerDebuggerDebuggeesListRequest object.

  Fields:
    clientVersion: Required. The client version making the call. Schema:
      `domain/type/version` (e.g., `google.com/intellij/v1`).
    includeInactive: When set to `true`, the result includes all debuggees.
      Otherwise, the result includes only debuggees that are active.
    project: Required. Project number of a Google Cloud project whose
      debuggees to list.
  """

  clientVersion = _messages.StringField(1)
  includeInactive = _messages.BooleanField(2)
  project = _messages.StringField(3)


class Debuggee(_messages.Message):
  r"""Represents the debugged application. The application may include one or
  more replicated processes executing the same code. Each of these processes
  is attached with a debugger agent, carrying out the debugging commands.
  Agents attached to the same debuggee identify themselves as such by using
  exactly the same Debuggee message value when registering.

  Enums:
    CanaryModeValueValuesEnum: Used when setting breakpoint canary for this
      debuggee.

  Messages:
    LabelsValue: A set of custom debuggee properties, populated by the agent,
      to be displayed to the user.

  Fields:
    agentVersion: Version ID of the agent. Schema: `domain/language-
      platform/vmajor.minor` (for example `google.com/java-gcp/v1.1`).
    canaryMode: Used when setting breakpoint canary for this debuggee.
    description: Human readable description of the debuggee. Including a
      human-readable project name, environment name and version information is
      recommended.
    extSourceContexts: References to the locations and revisions of the source
      code used in the deployed application.
    id: Unique identifier for the debuggee generated by the controller
      service.
    isDisabled: If set to `true`, indicates that the agent should disable
      itself and detach from the debuggee.
    isInactive: If set to `true`, indicates that Controller service does not
      detect any activity from the debuggee agents and the application is
      possibly stopped.
    labels: A set of custom debuggee properties, populated by the agent, to be
      displayed to the user.
    project: Project the debuggee is associated with. Use project number or id
      when registering a Google Cloud Platform project.
    sourceContexts: References to the locations and revisions of the source
      code used in the deployed application.
    status: Human readable message to be displayed to the user about this
      debuggee. Absence of this field indicates no status. The message can be
      either informational or an error status.
    uniquifier: Uniquifier to further distinguish the application. It is
      possible that different applications might have identical values in the
      debuggee message, thus, incorrectly identified as a single application
      by the Controller service. This field adds salt to further distinguish
      the application. Agents should consider seeding this field with value
      that identifies the code, binary, configuration and environment.
  """

  class CanaryModeValueValuesEnum(_messages.Enum):
    r"""Used when setting breakpoint canary for this debuggee.

    Values:
      CANARY_MODE_UNSPECIFIED: CANARY_MODE_UNSPECIFIED is equivalent to
        CANARY_MODE_ALWAYS_DISABLED so that if the debuggee is not configured
        to use the canary feature, the feature will be disabled.
      CANARY_MODE_ALWAYS_ENABLED: Always enable breakpoint canary regardless
        of the value of breakpoint's canary option.
      CANARY_MODE_ALWAYS_DISABLED: Always disable breakpoint canary regardless
        of the value of breakpoint's canary option.
      CANARY_MODE_DEFAULT_ENABLED: Depends on the breakpoint's canary option.
        Enable canary by default if the breakpoint's canary option is not
        specified.
      CANARY_MODE_DEFAULT_DISABLED: Depends on the breakpoint's canary option.
        Disable canary by default if the breakpoint's canary option is not
        specified.
    """
    CANARY_MODE_UNSPECIFIED = 0
    CANARY_MODE_ALWAYS_ENABLED = 1
    CANARY_MODE_ALWAYS_DISABLED = 2
    CANARY_MODE_DEFAULT_ENABLED = 3
    CANARY_MODE_DEFAULT_DISABLED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A set of custom debuggee properties, populated by the agent, to be
    displayed to the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  agentVersion = _messages.StringField(1)
  canaryMode = _messages.EnumField('CanaryModeValueValuesEnum', 2)
  description = _messages.StringField(3)
  extSourceContexts = _messages.MessageField('ExtendedSourceContext', 4, repeated=True)
  id = _messages.StringField(5)
  isDisabled = _messages.BooleanField(6)
  isInactive = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  project = _messages.StringField(9)
  sourceContexts = _messages.MessageField('SourceContext', 10, repeated=True)
  status = _messages.MessageField('StatusMessage', 11)
  uniquifier = _messages.StringField(12)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExtendedSourceContext(_messages.Message):
  r"""An ExtendedSourceContext is a SourceContext combined with additional
  details describing the context.

  Messages:
    LabelsValue: Labels with user defined metadata.

  Fields:
    context: Any source context.
    labels: Labels with user defined metadata.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  context = _messages.MessageField('SourceContext', 1)
  labels = _messages.MessageField('LabelsValue', 2)


class FormatMessage(_messages.Message):
  r"""Represents a message with parameters.

  Fields:
    format: Format template for the message. The `format` uses placeholders
      `$0`, `$1`, etc. to reference parameters. `$$` can be used to denote the
      `$` character. Examples: * `Failed to load '$0' which helps debug $1 the
      first time it is loaded. Again, $0 is very important.` * `Please pay
      $$10 to use $0 instead of $1.`
    parameters: Optional parameters to be embedded into the message.
  """

  format = _messages.StringField(1)
  parameters = _messages.StringField(2, repeated=True)


class GerritSourceContext(_messages.Message):
  r"""A SourceContext referring to a Gerrit project.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    aliasName: The name of an alias (branch, tag, etc.).
    gerritProject: The full project name within the host. Projects may be
      nested, so "project/subproject" is a valid project name. The "repo name"
      is hostURI/project.
    hostUri: The URI of a running Gerrit instance.
    revisionId: A revision (commit) ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  aliasName = _messages.StringField(2)
  gerritProject = _messages.StringField(3)
  hostUri = _messages.StringField(4)
  revisionId = _messages.StringField(5)


class GetBreakpointResponse(_messages.Message):
  r"""Response for getting breakpoint information.

  Fields:
    breakpoint: Complete breakpoint state. The fields `id` and `location` are
      guaranteed to be set.
  """

  breakpoint = _messages.MessageField('Breakpoint', 1)


class GitSourceContext(_messages.Message):
  r"""A GitSourceContext denotes a particular revision in a third party Git
  repository (e.g. GitHub).

  Fields:
    revisionId: Git commit hash. required.
    url: Git repository URL.
  """

  revisionId = _messages.StringField(1)
  url = _messages.StringField(2)


class ListActiveBreakpointsResponse(_messages.Message):
  r"""Response for listing active breakpoints.

  Fields:
    breakpoints: List of all active breakpoints. The fields `id` and
      `location` are guaranteed to be set on each breakpoint.
    nextWaitToken: A token that can be used in the next method call to block
      until the list of breakpoints changes.
    waitExpired: If set to `true`, indicates that there is no change to the
      list of active breakpoints and the server-selected timeout has expired.
      The `breakpoints` field would be empty and should be ignored.
  """

  breakpoints = _messages.MessageField('Breakpoint', 1, repeated=True)
  nextWaitToken = _messages.StringField(2)
  waitExpired = _messages.BooleanField(3)


class ListBreakpointsResponse(_messages.Message):
  r"""Response for listing breakpoints.

  Fields:
    breakpoints: List of breakpoints matching the request. The fields `id` and
      `location` are guaranteed to be set on each breakpoint. The fields:
      `stack_frames`, `evaluated_expressions` and `variable_table` are cleared
      on each breakpoint regardless of its status.
    nextWaitToken: A wait token that can be used in the next call to `list`
      (REST) or `ListBreakpoints` (RPC) to block until the list of breakpoints
      has changes.
  """

  breakpoints = _messages.MessageField('Breakpoint', 1, repeated=True)
  nextWaitToken = _messages.StringField(2)


class ListDebuggeesResponse(_messages.Message):
  r"""Response for listing debuggees.

  Fields:
    debuggees: List of debuggees accessible to the calling user. The fields
      `debuggee.id` and `description` are guaranteed to be set. The
      `description` field is a human readable field provided by agents and can
      be displayed to users.
  """

  debuggees = _messages.MessageField('Debuggee', 1, repeated=True)


class ProjectRepoId(_messages.Message):
  r"""Selects a repo using a Google Cloud Platform project ID (e.g. winged-
  cargo-31) and a repo name within that project.

  Fields:
    projectId: The ID of the project.
    repoName: The name of the repo. Leave empty for the default repo.
  """

  projectId = _messages.StringField(1)
  repoName = _messages.StringField(2)


class RegisterDebuggeeRequest(_messages.Message):
  r"""Request to register a debuggee.

  Fields:
    debuggee: Required. Debuggee information to register. The fields
      `project`, `uniquifier`, `description` and `agent_version` of the
      debuggee must be set.
  """

  debuggee = _messages.MessageField('Debuggee', 1)


class RegisterDebuggeeResponse(_messages.Message):
  r"""Response for registering a debuggee.

  Fields:
    agentId: A unique ID generated for the agent. Each RegisterDebuggee
      request will generate a new agent ID.
    debuggee: Debuggee resource. The field `id` is guaranteed to be set (in
      addition to the echoed fields). If the field `is_disabled` is set to
      `true`, the agent should disable itself by removing all breakpoints and
      detaching from the application. It should however continue to poll
      `RegisterDebuggee` until reenabled.
  """

  agentId = _messages.StringField(1)
  debuggee = _messages.MessageField('Debuggee', 2)


class RepoId(_messages.Message):
  r"""A unique identifier for a cloud repo.

  Fields:
    projectRepoId: A combination of a project ID and a repo name.
    uid: A server-assigned, globally unique identifier.
  """

  projectRepoId = _messages.MessageField('ProjectRepoId', 1)
  uid = _messages.StringField(2)


class SetBreakpointResponse(_messages.Message):
  r"""Response for setting a breakpoint.

  Fields:
    breakpoint: Breakpoint resource. The field `id` is guaranteed to be set
      (in addition to the echoed fields).
  """

  breakpoint = _messages.MessageField('Breakpoint', 1)


class SourceContext(_messages.Message):
  r"""A SourceContext is a reference to a tree of files. A SourceContext
  together with a path point to a unique revision of a single file or
  directory.

  Fields:
    cloudRepo: A SourceContext referring to a revision in a cloud repo.
    cloudWorkspace: A SourceContext referring to a snapshot in a cloud
      workspace.
    gerrit: A SourceContext referring to a Gerrit project.
    git: A SourceContext referring to any third party Git repo (e.g. GitHub).
  """

  cloudRepo = _messages.MessageField('CloudRepoSourceContext', 1)
  cloudWorkspace = _messages.MessageField('CloudWorkspaceSourceContext', 2)
  gerrit = _messages.MessageField('GerritSourceContext', 3)
  git = _messages.MessageField('GitSourceContext', 4)


class SourceLocation(_messages.Message):
  r"""Represents a location in the source code.

  Fields:
    column: Column within a line. The first column in a line as the value `1`.
      Agents that do not support setting breakpoints on specific columns
      ignore this field.
    line: Line inside the file. The first line in the file has the value `1`.
    path: Path to the source file within the source context of the target
      binary.
  """

  column = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  line = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  path = _messages.StringField(3)


class StackFrame(_messages.Message):
  r"""Represents a stack frame context.

  Fields:
    arguments: Set of arguments passed to this function. Note that this might
      not be populated for all stack frames.
    function: Demangled function name at the call site.
    locals: Set of local variables at the stack frame location. Note that this
      might not be populated for all stack frames.
    location: Source location of the call site.
  """

  arguments = _messages.MessageField('Variable', 1, repeated=True)
  function = _messages.StringField(2)
  locals = _messages.MessageField('Variable', 3, repeated=True)
  location = _messages.MessageField('SourceLocation', 4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StatusMessage(_messages.Message):
  r"""Represents a contextual status message. The message can indicate an
  error or informational status, and refer to specific parts of the containing
  object. For example, the `Breakpoint.status` field can indicate an error
  referring to the `BREAKPOINT_SOURCE_LOCATION` with the message `Location not
  found`.

  Enums:
    RefersToValueValuesEnum: Reference to which the message applies.

  Fields:
    description: Status message text.
    isError: Distinguishes errors from informational messages.
    refersTo: Reference to which the message applies.
  """

  class RefersToValueValuesEnum(_messages.Enum):
    r"""Reference to which the message applies.

    Values:
      UNSPECIFIED: Status doesn't refer to any particular input.
      BREAKPOINT_SOURCE_LOCATION: Status applies to the breakpoint and is
        related to its location.
      BREAKPOINT_CONDITION: Status applies to the breakpoint and is related to
        its condition.
      BREAKPOINT_EXPRESSION: Status applies to the breakpoint and is related
        to its expressions.
      BREAKPOINT_AGE: Status applies to the breakpoint and is related to its
        age.
      BREAKPOINT_CANARY_FAILED: Status applies to the breakpoint when the
        breakpoint failed to exit the canary state.
      VARIABLE_NAME: Status applies to the entire variable.
      VARIABLE_VALUE: Status applies to variable value (variable name is
        valid).
    """
    UNSPECIFIED = 0
    BREAKPOINT_SOURCE_LOCATION = 1
    BREAKPOINT_CONDITION = 2
    BREAKPOINT_EXPRESSION = 3
    BREAKPOINT_AGE = 4
    BREAKPOINT_CANARY_FAILED = 5
    VARIABLE_NAME = 6
    VARIABLE_VALUE = 7

  description = _messages.MessageField('FormatMessage', 1)
  isError = _messages.BooleanField(2)
  refersTo = _messages.EnumField('RefersToValueValuesEnum', 3)


class UpdateActiveBreakpointRequest(_messages.Message):
  r"""Request to update an active breakpoint.

  Fields:
    breakpoint: Required. Updated breakpoint information. The field `id` must
      be set. The agent must echo all Breakpoint specification fields in the
      update.
  """

  breakpoint = _messages.MessageField('Breakpoint', 1)


class UpdateActiveBreakpointResponse(_messages.Message):
  r"""Response for updating an active breakpoint. The message is defined to
  allow future extensions.
  """



class Variable(_messages.Message):
  r"""Represents a variable or an argument possibly of a compound object type.
  Note how the following variables are represented: 1) A simple variable: int
  x = 5 { name: "x", value: "5", type: "int" } // Captured variable 2) A
  compound object: struct T { int m1; int m2; }; T x = { 3, 7 }; { // Captured
  variable name: "x", type: "T", members { name: "m1", value: "3", type: "int"
  }, members { name: "m2", value: "7", type: "int" } } 3) A pointer where the
  pointee was captured: T x = { 3, 7 }; T* p = &x; { // Captured variable
  name: "p", type: "T*", value: "0x00500500", members { name: "m1", value:
  "3", type: "int" }, members { name: "m2", value: "7", type: "int" } } 4) A
  pointer where the pointee was not captured: T* p = new T; { // Captured
  variable name: "p", type: "T*", value: "0x00400400" status { is_error: true,
  description { format: "unavailable" } } } The status should describe the
  reason for the missing value, such as ``, ``, ``. Note that a null pointer
  should not have members. 5) An unnamed value: int* p = new int(7); { //
  Captured variable name: "p", value: "0x00500500", type: "int*", members {
  value: "7", type: "int" } } 6) An unnamed pointer where the pointee was not
  captured: int* p = new int(7); int** pp = &p; { // Captured variable name:
  "pp", value: "0x00500500", type: "int**", members { value: "0x00400400",
  type: "int*" status { is_error: true, description: { format: "unavailable" }
  } } } } To optimize computation, memory and network traffic, variables that
  repeat in the output multiple times can be stored once in a shared variable
  table and be referenced using the `var_table_index` field. The variables
  stored in the shared table are nameless and are essentially a partition of
  the complete variable. To reconstruct the complete variable, merge the
  referencing variable with the referenced variable. When using the shared
  variable table, the following variables: T x = { 3, 7 }; T* p = &x; T& r =
  x; { name: "x", var_table_index: 3, type: "T" } // Captured variables {
  name: "p", value "0x00500500", type="T*", var_table_index: 3 } { name: "r",
  type="T&", var_table_index: 3 } { // Shared variable table entry #3: members
  { name: "m1", value: "3", type: "int" }, members { name: "m2", value: "7",
  type: "int" } } Note that the pointer address is stored with the referencing
  variable and not with the referenced variable. This allows the referenced
  variable to be shared between pointers and references. The type field is
  optional. The debugger agent may or may not support it.

  Fields:
    members: Members contained or pointed to by the variable.
    name: Name of the variable, if any.
    status: Status associated with the variable. This field will usually stay
      unset. A status of a single variable only applies to that variable or
      expression. The rest of breakpoint data still remains valid. Variables
      might be reported in error state even when breakpoint is not in final
      state. The message may refer to variable name with `refers_to` set to
      `VARIABLE_NAME`. Alternatively `refers_to` will be set to
      `VARIABLE_VALUE`. In either case variable value and members will be
      unset. Example of error message applied to name: `Invalid expression
      syntax`. Example of information message applied to value: `Not
      captured`. Examples of error message applied to value: * `Malformed
      string`, * `Field f not found in class C` * `Null pointer dereference`
    type: Variable type (e.g. `MyClass`). If the variable is split with
      `var_table_index`, `type` goes next to `value`. The interpretation of a
      type is agent specific. It is recommended to include the dynamic type
      rather than a static type of an object.
    value: Simple value of the variable.
    varTableIndex: Reference to a variable in the shared variable table. More
      than one variable can reference the same variable in the table. The
      `var_table_index` field is an index into `variable_table` in Breakpoint.
  """

  members = _messages.MessageField('Variable', 1, repeated=True)
  name = _messages.StringField(2)
  status = _messages.MessageField('StatusMessage', 3)
  type = _messages.StringField(4)
  value = _messages.StringField(5)
  varTableIndex = _messages.IntegerField(6, variant=_messages.Variant.INT32)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ClouddebuggerDebuggerDebuggeesBreakpointsListRequest, 'action_value', 'action.value')
