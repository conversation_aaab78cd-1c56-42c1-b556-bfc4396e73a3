"""Generated message classes for accesscontextmanager version v1alpha.

An API for setting attribute based access control to requests to Google Cloud
services. *Warning:* Do not mix *v1alpha* and *v1* API usage in the same
access policy. The v1alpha API supports new Access Context Manager features,
which may have different attributes or behaviors that are not supported by v1.
The practice of mixed API usage within a policy may result in the inability to
update that policy, including any access levels or service perimeters
belonging to it. It is not recommended to use both v1 and v1alpha for
modifying policies with critical service perimeters. Modifications using
v1alpha should be limited to policies with non-production/non-critical service
perimeters.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'accesscontextmanager'


class AccessContextManagerOperationMetadata(_messages.Message):
  r"""Metadata of Access Context Manager's Long Running Operations."""


class AccessLevel(_messages.Message):
  r"""An `AccessLevel` is a label that can be applied to requests to Google
  Cloud services, along with a list of requirements necessary for the label to
  be applied.

  Fields:
    accessLevelFeatures: Output only. Access level features that are used to
      determine the behavior of the access level.
    basic: A `BasicLevel` composed of `Conditions`.
    custom: A `CustomLevel` written in the Common Expression Language.
    description: Description of the `AccessLevel` and its use. Does not affect
      behavior.
    name: Identifier. Resource name for the `AccessLevel`. Format:
      `accessPolicies/{access_policy}/accessLevels/{access_level}`. The
      `access_level` component must begin with a letter, followed by
      alphanumeric characters or `_`. Its maximum length is 50 characters.
      After you create an `AccessLevel`, you cannot change its `name`.
    title: Human readable title. Must be unique within the Policy.
  """

  accessLevelFeatures = _messages.MessageField('AccessLevelFeatures', 1)
  basic = _messages.MessageField('BasicLevel', 2)
  custom = _messages.MessageField('CustomLevel', 3)
  description = _messages.StringField(4)
  name = _messages.StringField(5)
  title = _messages.StringField(6)


class AccessLevelFeatures(_messages.Message):
  r"""Fields capturing features about the access level. Output only.

  Fields:
    canBeNested: Output only. Indicates that the access level is able to be
      nested in other access levels.
    hasAmendableConditions: Output only. Indicates whether there is a
      amendable response defined within access level conditions. Set to false
      if deny is the only configured result for all conditions.
    hasRemediations: Output only. Deprecated field that needs to be cleaned up
      after Pantheon validation.
  """

  canBeNested = _messages.BooleanField(1)
  hasAmendableConditions = _messages.BooleanField(2)
  hasRemediations = _messages.BooleanField(3)


class AccessPolicy(_messages.Message):
  r"""`AccessPolicy` is a container for `AccessLevels` (which define the
  necessary attributes to use Google Cloud services) and `ServicePerimeters`
  (which define regions of services able to freely pass data within a
  perimeter). An access policy is globally visible within an organization, and
  the restrictions it specifies apply to all projects within an organization.

  Fields:
    etag: Output only. An opaque identifier for the current version of the
      `AccessPolicy`. This will always be a strongly validated etag, meaning
      that two Access Policies will be identical if and only if their etags
      are identical. Clients should not expect this to be in any specific
      format.
    name: Identifier. Resource name of the `AccessPolicy`. Format:
      `accessPolicies/{access_policy}`
    parent: Immutable. The parent of this `AccessPolicy` in the Cloud Resource
      Hierarchy Format: `organizations/{organization_id}`
    scopes: The scopes of the AccessPolicy. Scopes define which resources a
      policy can restrict and where its resources can be referenced. For
      example, policy A with `scopes=["folders/123"]` has the following
      behavior: - ServicePerimeter can only restrict projects within
      `folders/123`. - ServicePerimeter within policy A can only reference
      access levels defined within policy A. - Only one policy can include a
      given scope; thus, attempting to create a second policy which includes
      `folders/123` will result in an error. If no scopes are provided, then
      any resource within the organization can be restricted. Scopes cannot be
      modified after a policy is created. Policies can only have a single
      scope. Format: list of `folders/{folder_number}` or
      `projects/{project_number}`
    title: Required. Human readable title. Does not affect behavior.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3)
  scopes = _messages.StringField(4, repeated=True)
  title = _messages.StringField(5)


class AccessScope(_messages.Message):
  r"""Access scope represents the client scope, etc. to which the settings
  will be applied to.

  Fields:
    clientScope: Optional. Client scope for this access scope.
  """

  clientScope = _messages.MessageField('ClientScope', 1)


class AccessSettings(_messages.Message):
  r"""Access settings represent the set of conditions that must be met for
  access to be granted. At least one of the fields must be set.

  Fields:
    accessLevels: Optional. Access level that a user must have to be granted
      access. Only one access level is supported, not multiple. This repeated
      field must have exactly one element. Example:
      "accessPolicies/9522/accessLevels/device_trusted"
    sessionSettings: Optional. Session settings applied to user access on a
      given AccessScope.
  """

  accessLevels = _messages.StringField(1, repeated=True)
  sessionSettings = _messages.MessageField('SessionSettings', 2)


class AccesscontextmanagerAccessPoliciesAccessLevelsCreateRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsCreateRequest object.

  Fields:
    accessLevel: A AccessLevel resource to be passed as the request body.
    parent: Required. Resource name for the access policy which owns this
      Access Level. Format: `accessPolicies/{policy_id}`
  """

  accessLevel = _messages.MessageField('AccessLevel', 1)
  parent = _messages.StringField(2, required=True)


class AccesscontextmanagerAccessPoliciesAccessLevelsDeleteRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsDeleteRequest object.

  Fields:
    name: Required. Resource name for the Access Level. Format:
      `accessPolicies/{policy_id}/accessLevels/{access_level_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesAccessLevelsGetRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsGetRequest object.

  Enums:
    AccessLevelFormatValueValuesEnum: Whether to return `BasicLevels` in the
      Cloud Common Expression Language rather than as `BasicLevels`. Defaults
      to AS_DEFINED, where Access Levels are returned as `BasicLevels` or
      `CustomLevels` based on how they were created. If set to CEL, all Access
      Levels are returned as `CustomLevels`. In the CEL case, `BasicLevels`
      are translated to equivalent `CustomLevels`.

  Fields:
    accessLevelFormat: Whether to return `BasicLevels` in the Cloud Common
      Expression Language rather than as `BasicLevels`. Defaults to
      AS_DEFINED, where Access Levels are returned as `BasicLevels` or
      `CustomLevels` based on how they were created. If set to CEL, all Access
      Levels are returned as `CustomLevels`. In the CEL case, `BasicLevels`
      are translated to equivalent `CustomLevels`.
    name: Required. Resource name for the Access Level. Format:
      `accessPolicies/{policy_id}/accessLevels/{access_level_id}`
  """

  class AccessLevelFormatValueValuesEnum(_messages.Enum):
    r"""Whether to return `BasicLevels` in the Cloud Common Expression
    Language rather than as `BasicLevels`. Defaults to AS_DEFINED, where
    Access Levels are returned as `BasicLevels` or `CustomLevels` based on how
    they were created. If set to CEL, all Access Levels are returned as
    `CustomLevels`. In the CEL case, `BasicLevels` are translated to
    equivalent `CustomLevels`.

    Values:
      LEVEL_FORMAT_UNSPECIFIED: The format was not specified.
      AS_DEFINED: Uses the format the resource was defined in. BasicLevels are
        returned as BasicLevels, CustomLevels are returned as CustomLevels.
      CEL: Use Cloud Common Expression Language when returning the resource.
        Both BasicLevels and CustomLevels are returned as CustomLevels.
    """
    LEVEL_FORMAT_UNSPECIFIED = 0
    AS_DEFINED = 1
    CEL = 2

  accessLevelFormat = _messages.EnumField('AccessLevelFormatValueValuesEnum', 1)
  name = _messages.StringField(2, required=True)


class AccesscontextmanagerAccessPoliciesAccessLevelsListRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsListRequest object.

  Enums:
    AccessLevelFormatValueValuesEnum: Whether to return `BasicLevels` in the
      Cloud Common Expression language, as `CustomLevels`, rather than as
      `BasicLevels`. Defaults to returning `AccessLevels` in the format they
      were defined.

  Fields:
    accessLevelFormat: Whether to return `BasicLevels` in the Cloud Common
      Expression language, as `CustomLevels`, rather than as `BasicLevels`.
      Defaults to returning `AccessLevels` in the format they were defined.
    pageSize: Number of Access Levels to include in the list. Default 100.
    pageToken: Next page token for the next batch of Access Level instances.
      Defaults to the first page of results.
    parent: Required. Resource name for the access policy to list Access
      Levels from. Format: `accessPolicies/{policy_id}`
  """

  class AccessLevelFormatValueValuesEnum(_messages.Enum):
    r"""Whether to return `BasicLevels` in the Cloud Common Expression
    language, as `CustomLevels`, rather than as `BasicLevels`. Defaults to
    returning `AccessLevels` in the format they were defined.

    Values:
      LEVEL_FORMAT_UNSPECIFIED: The format was not specified.
      AS_DEFINED: Uses the format the resource was defined in. BasicLevels are
        returned as BasicLevels, CustomLevels are returned as CustomLevels.
      CEL: Use Cloud Common Expression Language when returning the resource.
        Both BasicLevels and CustomLevels are returned as CustomLevels.
    """
    LEVEL_FORMAT_UNSPECIFIED = 0
    AS_DEFINED = 1
    CEL = 2

  accessLevelFormat = _messages.EnumField('AccessLevelFormatValueValuesEnum', 1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AccesscontextmanagerAccessPoliciesAccessLevelsPatchRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsPatchRequest object.

  Fields:
    accessLevel: A AccessLevel resource to be passed as the request body.
    name: Identifier. Resource name for the `AccessLevel`. Format:
      `accessPolicies/{access_policy}/accessLevels/{access_level}`. The
      `access_level` component must begin with a letter, followed by
      alphanumeric characters or `_`. Its maximum length is 50 characters.
      After you create an `AccessLevel`, you cannot change its `name`.
    updateMask: Required. Mask to control which fields get updated. Must be
      non-empty.
  """

  accessLevel = _messages.MessageField('AccessLevel', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AccesscontextmanagerAccessPoliciesAccessLevelsReplaceAllRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAccessLevelsReplaceAllRequest
  object.

  Fields:
    parent: Required. Resource name for the access policy which owns these
      Access Levels. Format: `accessPolicies/{policy_id}`
    replaceAccessLevelsRequest: A ReplaceAccessLevelsRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  replaceAccessLevelsRequest = _messages.MessageField('ReplaceAccessLevelsRequest', 2)


class AccesscontextmanagerAccessPoliciesAccessLevelsTestIamPermissionsRequest(_messages.Message):
  r"""A
  AccesscontextmanagerAccessPoliciesAccessLevelsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsCreateRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsCreateRequest
  object.

  Fields:
    authorizedOrgsDesc: A AuthorizedOrgsDesc resource to be passed as the
      request body.
    parent: Required. Resource name for the access policy which owns this
      Authorized Orgs Desc. Format: `accessPolicies/{policy_id}`
  """

  authorizedOrgsDesc = _messages.MessageField('AuthorizedOrgsDesc', 1)
  parent = _messages.StringField(2, required=True)


class AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsDeleteRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsDeleteRequest
  object.

  Fields:
    name: Required. Resource name for the Authorized Orgs Desc. Format:
      `accessPolicies/{policy_id}/authorizedOrgsDesc/{authorized_orgs_desc_id}
      `
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsGetRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsGetRequest
  object.

  Fields:
    name: Required. Resource name for the Authorized Orgs Desc. Format: `acces
      sPolicies/{policy_id}/authorizedOrgsDescs/{authorized_orgs_descs_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsListRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsListRequest
  object.

  Fields:
    pageSize: Number of Authorized Orgs Descs to include in the list. Default
      100.
    pageToken: Next page token for the next batch of Authorized Orgs Desc
      instances. Defaults to the first page of results.
    parent: Required. Resource name for the access policy to list Authorized
      Orgs Desc from. Format: `accessPolicies/{policy_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsPatchRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsPatchRequest
  object.

  Fields:
    authorizedOrgsDesc: A AuthorizedOrgsDesc resource to be passed as the
      request body.
    name: Identifier. Resource name for the `AuthorizedOrgsDesc`. Format: `acc
      essPolicies/{access_policy}/authorizedOrgsDescs/{authorized_orgs_desc}`.
      The `authorized_orgs_desc` component must begin with a letter, followed
      by alphanumeric characters or `_`. After you create an
      `AuthorizedOrgsDesc`, you cannot change its `name`.
    updateMask: Required. Mask to control which fields get updated. Must be
      non-empty.
  """

  authorizedOrgsDesc = _messages.MessageField('AuthorizedOrgsDesc', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AccesscontextmanagerAccessPoliciesDeleteRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesDeleteRequest object.

  Fields:
    name: Required. Resource name for the access policy to delete. Format
      `accessPolicies/{policy_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class AccesscontextmanagerAccessPoliciesGetRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesGetRequest object.

  Fields:
    name: Required. Resource name for the access policy to get. Format
      `accessPolicies/{policy_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesListRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesListRequest object.

  Fields:
    pageSize: Number of AccessPolicy instances to include in the list. Default
      100.
    pageToken: Next page token for the next batch of AccessPolicy instances.
      Defaults to the first page of results.
    parent: Required. Resource name for the container to list AccessPolicy
      instances from. Format: `organizations/{org_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class AccesscontextmanagerAccessPoliciesPatchRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesPatchRequest object.

  Fields:
    accessPolicy: A AccessPolicy resource to be passed as the request body.
    name: Identifier. Resource name of the `AccessPolicy`. Format:
      `accessPolicies/{access_policy}`
    updateMask: Required. Mask to control which fields get updated. Must be
      non-empty.
  """

  accessPolicy = _messages.MessageField('AccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AccesscontextmanagerAccessPoliciesServicePerimetersCommitRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersCommitRequest
  object.

  Fields:
    commitServicePerimetersRequest: A CommitServicePerimetersRequest resource
      to be passed as the request body.
    parent: Required. Resource name for the parent Access Policy which owns
      all Service Perimeters in scope for the commit operation. Format:
      `accessPolicies/{policy_id}`
  """

  commitServicePerimetersRequest = _messages.MessageField('CommitServicePerimetersRequest', 1)
  parent = _messages.StringField(2, required=True)


class AccesscontextmanagerAccessPoliciesServicePerimetersCreateRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersCreateRequest
  object.

  Fields:
    parent: Required. Resource name for the access policy which owns this
      Service Perimeter. Format: `accessPolicies/{policy_id}`
    servicePerimeter: A ServicePerimeter resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  servicePerimeter = _messages.MessageField('ServicePerimeter', 2)


class AccesscontextmanagerAccessPoliciesServicePerimetersDeleteRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersDeleteRequest
  object.

  Fields:
    name: Required. Resource name for the Service Perimeter. Format:
      `accessPolicies/{policy_id}/servicePerimeters/{service_perimeter_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesServicePerimetersGetRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersGetRequest object.

  Fields:
    name: Required. Resource name for the Service Perimeter. Format:
      `accessPolicies/{policy_id}/servicePerimeters/{service_perimeters_id}`
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerAccessPoliciesServicePerimetersListRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersListRequest object.

  Fields:
    pageSize: Number of Service Perimeters to include in the list. Default
      100.
    pageToken: Next page token for the next batch of Service Perimeter
      instances. Defaults to the first page of results.
    parent: Required. Resource name for the access policy to list Service
      Perimeters from. Format: `accessPolicies/{policy_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AccesscontextmanagerAccessPoliciesServicePerimetersPatchRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersPatchRequest
  object.

  Fields:
    name: Identifier. Resource name for the `ServicePerimeter`. Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`.
      The `service_perimeter` component must begin with a letter, followed by
      alphanumeric characters or `_`. After you create a `ServicePerimeter`,
      you cannot change its `name`.
    servicePerimeter: A ServicePerimeter resource to be passed as the request
      body.
    updateMask: Required. Mask to control which fields get updated. Must be
      non-empty.
  """

  name = _messages.StringField(1, required=True)
  servicePerimeter = _messages.MessageField('ServicePerimeter', 2)
  updateMask = _messages.StringField(3)


class AccesscontextmanagerAccessPoliciesServicePerimetersReplaceAllRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesServicePerimetersReplaceAllRequest
  object.

  Fields:
    parent: Required. Resource name for the access policy which owns these
      Service Perimeters. Format: `accessPolicies/{policy_id}`
    replaceServicePerimetersRequest: A ReplaceServicePerimetersRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  replaceServicePerimetersRequest = _messages.MessageField('ReplaceServicePerimetersRequest', 2)


class AccesscontextmanagerAccessPoliciesServicePerimetersTestIamPermissionsRequest(_messages.Message):
  r"""A
  AccesscontextmanagerAccessPoliciesServicePerimetersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class AccesscontextmanagerAccessPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class AccesscontextmanagerAccessPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A AccesscontextmanagerAccessPoliciesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class AccesscontextmanagerOperationsGetRequest(_messages.Message):
  r"""A AccesscontextmanagerOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerOrganizationsGcpUserAccessBindingsCreateRequest(_messages.Message):
  r"""A AccesscontextmanagerOrganizationsGcpUserAccessBindingsCreateRequest
  object.

  Fields:
    gcpUserAccessBinding: A GcpUserAccessBinding resource to be passed as the
      request body.
    parent: Required. Example: "organizations/256"
  """

  gcpUserAccessBinding = _messages.MessageField('GcpUserAccessBinding', 1)
  parent = _messages.StringField(2, required=True)


class AccesscontextmanagerOrganizationsGcpUserAccessBindingsDeleteRequest(_messages.Message):
  r"""A AccesscontextmanagerOrganizationsGcpUserAccessBindingsDeleteRequest
  object.

  Fields:
    name: Required. Example:
      "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerOrganizationsGcpUserAccessBindingsGetRequest(_messages.Message):
  r"""A AccesscontextmanagerOrganizationsGcpUserAccessBindingsGetRequest
  object.

  Fields:
    name: Required. Example:
      "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerOrganizationsGcpUserAccessBindingsListRequest(_messages.Message):
  r"""A AccesscontextmanagerOrganizationsGcpUserAccessBindingsListRequest
  object.

  Fields:
    filter: Optional. The literal filter pipelines to be returned. See
      https://google.aip.dev/160 for more details. Accepts values: *
      principal:group_key * principal:service_account OR
      principal:service_account_project_number. If this field is empty or not
      one of the above, the default value is "principal:group_key".
    pageSize: Optional. Maximum number of items to return. The server may
      return fewer items. If left blank, the server may return any number of
      items.
    pageToken: Optional. If left blank, returns the first page. To enumerate
      all items, use the next_page_token from your previous list operation.
    parent: Required. Example: "organizations/256"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AccesscontextmanagerOrganizationsGcpUserAccessBindingsPatchRequest(_messages.Message):
  r"""A AccesscontextmanagerOrganizationsGcpUserAccessBindingsPatchRequest
  object.

  Fields:
    append: Optional. This field controls whether or not certain repeated
      settings in the update request overwrite or append to existing settings
      on the binding. If true, then append. Otherwise overwrite. So far, only
      scoped_access_settings with session_settings supports appending. Global
      access_levels, access_levels in scoped_access_settings,
      dry_run_access_levels, and session_settings are not compatible with
      append functionality, and the request will return an error if
      append=true when these settings are in the update_mask. The request will
      also return an error if append=true when "scoped_access_settings" is not
      set in the update_mask.
    gcpUserAccessBinding: A GcpUserAccessBinding resource to be passed as the
      request body.
    name: Immutable. Identifier. Assigned by the server during creation. The
      last segment has an arbitrary length and has only URI unreserved
      characters (as defined by [RFC 3986 Section
      2.3](https://tools.ietf.org/html/rfc3986#section-2.3)). Should not be
      specified by the client during creation. Example:
      "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
    updateMask: Required. Only the fields specified in this mask are updated.
      Because name and group_key cannot be changed, update_mask is required
      and may only contain the following fields: `access_levels`,
      `dry_run_access_levels`, `session_settings`, and
      `scoped_access_settings`. Example: update_mask { paths: "access_levels"
      }
  """

  append = _messages.BooleanField(1)
  gcpUserAccessBinding = _messages.MessageField('GcpUserAccessBinding', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class AccesscontextmanagerServicesGetRequest(_messages.Message):
  r"""A AccesscontextmanagerServicesGetRequest object.

  Fields:
    name: The name of the service to get information about. The names must be
      in the same format as used in defining a service perimeter, for example,
      `storage.googleapis.com`.
  """

  name = _messages.StringField(1, required=True)


class AccesscontextmanagerServicesListRequest(_messages.Message):
  r"""A AccesscontextmanagerServicesListRequest object.

  Fields:
    pageSize: This flag specifies the maximum number of services to return per
      page. Default is 100.
    pageToken: Token to start on a later page. Default is the first page.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)


class AddRequestHeader(_messages.Message):
  r"""Adds a request header to the API.

  Fields:
    key: HTTP header key.
    value: HTTP header value.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class ApiOperation(_messages.Message):
  r"""Identification for an API Operation.

  Fields:
    methodSelectors: API methods or permissions to allow. Method or permission
      must belong to the service specified by `service_name` field. A single
      MethodSelector entry with `*` specified for the `method` field will
      allow all methods AND permissions for the service specified in
      `service_name`.
    serviceName: The name of the API whose methods or permissions the
      IngressPolicy or EgressPolicy want to allow. A single ApiOperation with
      `service_name` field set to `*` will allow all methods AND permissions
      for all services.
  """

  methodSelectors = _messages.MessageField('MethodSelector', 1, repeated=True)
  serviceName = _messages.StringField(2)


class Application(_messages.Message):
  r"""An application that accesses Google Cloud APIs.

  Fields:
    clientId: The OAuth client ID of the application.
    name: The name of the application. Example: "Cloud Console"
  """

  clientId = _messages.StringField(1)
  name = _messages.StringField(2)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthorizedOrgsDesc(_messages.Message):
  r"""`AuthorizedOrgsDesc` contains data for an organization's authorization
  policy.

  Enums:
    AssetTypeValueValuesEnum: The asset type of this authorized orgs desc.
      Valid values are `ASSET_TYPE_DEVICE`, and
      `ASSET_TYPE_CREDENTIAL_STRENGTH`.
    AuthorizationDirectionValueValuesEnum: The direction of the authorization
      relationship between this organization and the organizations listed in
      the `orgs` field. The valid values for this field include the following:
      `AUTHORIZATION_DIRECTION_FROM`: Allows this organization to evaluate
      traffic in the organizations listed in the `orgs` field.
      `AUTHORIZATION_DIRECTION_TO`: Allows the organizations listed in the
      `orgs` field to evaluate the traffic in this organization. For the
      authorization relationship to take effect, all of the organizations must
      authorize and specify the appropriate relationship direction. For
      example, if organization A authorized organization B and C to evaluate
      its traffic, by specifying `AUTHORIZATION_DIRECTION_TO` as the
      authorization direction, organizations B and C must specify
      `AUTHORIZATION_DIRECTION_FROM` as the authorization direction in their
      `AuthorizedOrgsDesc` resource.
    AuthorizationTypeValueValuesEnum: A granular control type for
      authorization levels. Valid value is `AUTHORIZATION_TYPE_TRUST`.

  Fields:
    assetType: The asset type of this authorized orgs desc. Valid values are
      `ASSET_TYPE_DEVICE`, and `ASSET_TYPE_CREDENTIAL_STRENGTH`.
    authorizationDirection: The direction of the authorization relationship
      between this organization and the organizations listed in the `orgs`
      field. The valid values for this field include the following:
      `AUTHORIZATION_DIRECTION_FROM`: Allows this organization to evaluate
      traffic in the organizations listed in the `orgs` field.
      `AUTHORIZATION_DIRECTION_TO`: Allows the organizations listed in the
      `orgs` field to evaluate the traffic in this organization. For the
      authorization relationship to take effect, all of the organizations must
      authorize and specify the appropriate relationship direction. For
      example, if organization A authorized organization B and C to evaluate
      its traffic, by specifying `AUTHORIZATION_DIRECTION_TO` as the
      authorization direction, organizations B and C must specify
      `AUTHORIZATION_DIRECTION_FROM` as the authorization direction in their
      `AuthorizedOrgsDesc` resource.
    authorizationType: A granular control type for authorization levels. Valid
      value is `AUTHORIZATION_TYPE_TRUST`.
    name: Identifier. Resource name for the `AuthorizedOrgsDesc`. Format: `acc
      essPolicies/{access_policy}/authorizedOrgsDescs/{authorized_orgs_desc}`.
      The `authorized_orgs_desc` component must begin with a letter, followed
      by alphanumeric characters or `_`. After you create an
      `AuthorizedOrgsDesc`, you cannot change its `name`.
    orgs: The list of organization ids in this AuthorizedOrgsDesc. Format:
      `organizations/` Example: `organizations/123456`
  """

  class AssetTypeValueValuesEnum(_messages.Enum):
    r"""The asset type of this authorized orgs desc. Valid values are
    `ASSET_TYPE_DEVICE`, and `ASSET_TYPE_CREDENTIAL_STRENGTH`.

    Values:
      ASSET_TYPE_UNSPECIFIED: No asset type specified.
      ASSET_TYPE_DEVICE: Device asset type.
      ASSET_TYPE_CREDENTIAL_STRENGTH: Credential strength asset type.
    """
    ASSET_TYPE_UNSPECIFIED = 0
    ASSET_TYPE_DEVICE = 1
    ASSET_TYPE_CREDENTIAL_STRENGTH = 2

  class AuthorizationDirectionValueValuesEnum(_messages.Enum):
    r"""The direction of the authorization relationship between this
    organization and the organizations listed in the `orgs` field. The valid
    values for this field include the following:
    `AUTHORIZATION_DIRECTION_FROM`: Allows this organization to evaluate
    traffic in the organizations listed in the `orgs` field.
    `AUTHORIZATION_DIRECTION_TO`: Allows the organizations listed in the
    `orgs` field to evaluate the traffic in this organization. For the
    authorization relationship to take effect, all of the organizations must
    authorize and specify the appropriate relationship direction. For example,
    if organization A authorized organization B and C to evaluate its traffic,
    by specifying `AUTHORIZATION_DIRECTION_TO` as the authorization direction,
    organizations B and C must specify `AUTHORIZATION_DIRECTION_FROM` as the
    authorization direction in their `AuthorizedOrgsDesc` resource.

    Values:
      AUTHORIZATION_DIRECTION_UNSPECIFIED: No direction specified.
      AUTHORIZATION_DIRECTION_TO: Specified orgs will evaluate traffic.
      AUTHORIZATION_DIRECTION_FROM: Specified orgs' traffic will be evaluated.
    """
    AUTHORIZATION_DIRECTION_UNSPECIFIED = 0
    AUTHORIZATION_DIRECTION_TO = 1
    AUTHORIZATION_DIRECTION_FROM = 2

  class AuthorizationTypeValueValuesEnum(_messages.Enum):
    r"""A granular control type for authorization levels. Valid value is
    `AUTHORIZATION_TYPE_TRUST`.

    Values:
      AUTHORIZATION_TYPE_UNSPECIFIED: No authorization type specified.
      AUTHORIZATION_TYPE_TRUST: This authorization relationship is "trust".
    """
    AUTHORIZATION_TYPE_UNSPECIFIED = 0
    AUTHORIZATION_TYPE_TRUST = 1

  assetType = _messages.EnumField('AssetTypeValueValuesEnum', 1)
  authorizationDirection = _messages.EnumField('AuthorizationDirectionValueValuesEnum', 2)
  authorizationType = _messages.EnumField('AuthorizationTypeValueValuesEnum', 3)
  name = _messages.StringField(4)
  orgs = _messages.StringField(5, repeated=True)


class BasicLevel(_messages.Message):
  r"""`BasicLevel` is an `AccessLevel` using a set of recommended features.

  Enums:
    CombiningFunctionValueValuesEnum: How the `conditions` list should be
      combined to determine if a request is granted this `AccessLevel`. If AND
      is used, each `Condition` in `conditions` must be satisfied for the
      `AccessLevel` to be applied. If OR is used, at least one `Condition` in
      `conditions` must be satisfied for the `AccessLevel` to be applied.
      Default behavior is AND.

  Fields:
    combiningFunction: How the `conditions` list should be combined to
      determine if a request is granted this `AccessLevel`. If AND is used,
      each `Condition` in `conditions` must be satisfied for the `AccessLevel`
      to be applied. If OR is used, at least one `Condition` in `conditions`
      must be satisfied for the `AccessLevel` to be applied. Default behavior
      is AND.
    conditions: Required. A list of requirements for the `AccessLevel` to be
      granted.
  """

  class CombiningFunctionValueValuesEnum(_messages.Enum):
    r"""How the `conditions` list should be combined to determine if a request
    is granted this `AccessLevel`. If AND is used, each `Condition` in
    `conditions` must be satisfied for the `AccessLevel` to be applied. If OR
    is used, at least one `Condition` in `conditions` must be satisfied for
    the `AccessLevel` to be applied. Default behavior is AND.

    Values:
      AND: All `Conditions` must be true for the `BasicLevel` to be true.
      OR: If at least one `Condition` is true, then the `BasicLevel` is true.
    """
    AND = 0
    OR = 1

  combiningFunction = _messages.EnumField('CombiningFunctionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ClientScope(_messages.Message):
  r"""Client scope represents the application, etc. subject to this binding's
  restrictions.

  Fields:
    restrictedClientApplication: Optional. The application that is subject to
      this binding's scope.
  """

  restrictedClientApplication = _messages.MessageField('Application', 1)


class CommitServicePerimetersRequest(_messages.Message):
  r"""A request to commit dry-run specs in all Service Perimeters belonging to
  an Access Policy.

  Fields:
    etag: Optional. The etag for the version of the Access Policy that this
      commit operation is to be performed on. If, at the time of commit, the
      etag for the Access Policy stored in Access Context Manager is different
      from the specified etag, then the commit operation will not be performed
      and the call will fail. This field is not required. If etag is not
      provided, the operation will be performed as if a valid etag is
      provided.
  """

  etag = _messages.StringField(1)


class CommitServicePerimetersResponse(_messages.Message):
  r"""A response to CommitServicePerimetersRequest. This will be put inside of
  Operation.response field.

  Fields:
    servicePerimeters: List of all the Service Perimeter instances in the
      Access Policy.
  """

  servicePerimeters = _messages.MessageField('ServicePerimeter', 1, repeated=True)


class Condition(_messages.Message):
  r"""A condition necessary for an `AccessLevel` to be granted. The Condition
  is an AND over its fields. So a Condition is true if: 1) the request IP is
  from one of the listed subnetworks AND 2) the originating device complies
  with the listed device policy AND 3) all listed access levels are granted
  AND 4) the request was sent at a time allowed by the DateTimeRestriction AND
  5) the request was sent from one of the specified vpc_network_sources.

  Fields:
    devicePolicy: Device specific restrictions, all restrictions must hold for
      the Condition to be true. If not specified, all devices are allowed.
    ipSubnetworks: CIDR block IP subnetwork specification. May be IPv4 or
      IPv6. Note that for a CIDR IP address block, the specified IP address
      portion must be properly truncated (i.e. all the host bits must be zero)
      or the input is considered malformed. For example, "*********/24" is
      accepted but "*********/24" is not. Similarly, for IPv6, "2001:db8::/32"
      is accepted whereas "2001:db8::1/32" is not. The originating IP of a
      request must be in one of the listed subnets in order for this Condition
      to be true. If this field is empty, all IP addresses are allowed.
    members: The request must be made by one of the provided user or service
      accounts. Groups are not supported. Syntax: `user:{emailid}`
      `serviceAccount:{emailid}` If not specified, a request may come from any
      user.
    negate: Whether to negate the Condition. If true, the Condition becomes a
      NAND over its non-empty fields. Any non-empty field criteria evaluating
      to false will result in the Condition to be satisfied. Defaults to
      false.
    regions: The request must originate from one of the provided
      countries/regions. Must be valid ISO 3166-1 alpha-2 codes.
    requiredAccessLevels: A list of other access levels defined in the same
      `Policy`, referenced by resource name. Referencing an `AccessLevel`
      which does not exist is an error. All access levels listed must be
      granted for the Condition to be true. Example:
      "`accessPolicies/MY_POLICY/accessLevels/LEVEL_NAME"`
    risk: The request must have acceptable risk profile. Following constraints
      apply to its use: - It cannot be negated and cannot be nested. - If set,
      no other attributes can be applied within a Condition. - If set, you may
      optionally specify a amendable response.
    unsatisfiedResult: The result to apply if the condition is not met.
    vpcNetworkSources: The request must originate from one of the provided VPC
      networks in Google Cloud. Cannot specify this field together with
      `ip_subnetworks`.
  """

  devicePolicy = _messages.MessageField('DevicePolicy', 1)
  ipSubnetworks = _messages.StringField(2, repeated=True)
  members = _messages.StringField(3, repeated=True)
  negate = _messages.BooleanField(4)
  regions = _messages.StringField(5, repeated=True)
  requiredAccessLevels = _messages.StringField(6, repeated=True)
  risk = _messages.MessageField('Risk', 7)
  unsatisfiedResult = _messages.MessageField('UnsatisfiedResult', 8)
  vpcNetworkSources = _messages.MessageField('VpcNetworkSource', 9, repeated=True)


class CustomLevel(_messages.Message):
  r"""`CustomLevel` is an `AccessLevel` using the Cloud Common Expression
  Language to represent the necessary conditions for the level to apply to a
  request. See CEL spec at: https://github.com/google/cel-spec

  Fields:
    expr: Required. A Cloud CEL expression evaluating to a boolean.
  """

  expr = _messages.MessageField('Expr', 1)


class DevicePolicy(_messages.Message):
  r"""`DevicePolicy` specifies device specific restrictions necessary to
  acquire a given access level. A `DevicePolicy` specifies requirements for
  requests from devices to be granted access levels, it does not do any
  enforcement on the device. `DevicePolicy` acts as an AND over all specified
  fields, and each repeated field is an OR over its elements. Any unset fields
  are ignored. For example, if the proto is { os_type : DESKTOP_WINDOWS,
  os_type : DESKTOP_LINUX, encryption_status: ENCRYPTED}, then the
  DevicePolicy will be true for requests originating from encrypted Linux
  desktops and encrypted Windows desktops.

  Enums:
    AllowedDeviceManagementLevelsValueListEntryValuesEnum:
    AllowedEncryptionStatusesValueListEntryValuesEnum:

  Fields:
    allowedDeviceManagementLevels: Allowed device management levels, an empty
      list allows all management levels.
    allowedEncryptionStatuses: Allowed encryptions statuses, an empty list
      allows all statuses.
    osConstraints: Allowed OS versions, an empty list allows all types and all
      versions.
    requireAdminApproval: Whether the device needs to be approved by the
      customer admin.
    requireCorpOwned: Whether the device needs to be corp owned.
    requireScreenlock: Whether or not screenlock is required for the
      DevicePolicy to be true. Defaults to `false`.
  """

  class AllowedDeviceManagementLevelsValueListEntryValuesEnum(_messages.Enum):
    r"""AllowedDeviceManagementLevelsValueListEntryValuesEnum enum type.

    Values:
      MANAGEMENT_UNSPECIFIED: The device's management level is not specified
        or not known.
      NONE: The device is not managed.
      BASIC: Basic management is enabled, which is generally limited to
        monitoring and wiping the corporate account.
      COMPLETE: Complete device management. This includes more thorough
        monitoring and the ability to directly manage the device (such as
        remote wiping). This can be enabled through the Android Enterprise
        Platform.
    """
    MANAGEMENT_UNSPECIFIED = 0
    NONE = 1
    BASIC = 2
    COMPLETE = 3

  class AllowedEncryptionStatusesValueListEntryValuesEnum(_messages.Enum):
    r"""AllowedEncryptionStatusesValueListEntryValuesEnum enum type.

    Values:
      ENCRYPTION_UNSPECIFIED: The encryption status of the device is not
        specified or not known.
      ENCRYPTION_UNSUPPORTED: The device does not support encryption.
      UNENCRYPTED: The device supports encryption, but is currently
        unencrypted.
      ENCRYPTED: The device is encrypted.
    """
    ENCRYPTION_UNSPECIFIED = 0
    ENCRYPTION_UNSUPPORTED = 1
    UNENCRYPTED = 2
    ENCRYPTED = 3

  allowedDeviceManagementLevels = _messages.EnumField('AllowedDeviceManagementLevelsValueListEntryValuesEnum', 1, repeated=True)
  allowedEncryptionStatuses = _messages.EnumField('AllowedEncryptionStatusesValueListEntryValuesEnum', 2, repeated=True)
  osConstraints = _messages.MessageField('OsConstraint', 3, repeated=True)
  requireAdminApproval = _messages.BooleanField(4)
  requireCorpOwned = _messages.BooleanField(5)
  requireScreenlock = _messages.BooleanField(6)


class EgressFrom(_messages.Message):
  r"""Defines the conditions under which an EgressPolicy matches a request.
  Conditions based on information about the source of the request. Note that
  if the destination of the request also is protected by a ServicePerimeter,
  then that ServicePerimeter must have an IngressPolicy which allows access in
  order for this request to succeed.

  Enums:
    IdentityTypeValueValuesEnum: Specifies the type of identities that are
      allowed access to outside the perimeter. If left unspecified, then
      members of `identities` field will be allowed access.
    SourceRestrictionValueValuesEnum: Whether to enforce traffic restrictions
      based on `sources` field. If the `sources` fields is non-empty, then
      this field must be set to `SOURCE_RESTRICTION_ENABLED`.

  Fields:
    identities: A list of identities that are allowed access through
      EgressPolicy. Identities can be an individual user, service account,
      Google group, or third-party identity. For third-party identity, only
      single identities are supported and other identity types are not
      supported. The `v1` identities that have the prefix `user`, `group`,
      `serviceAccount`, and `principal` in
      https://cloud.google.com/iam/docs/principal-identifiers#v1 are
      supported.
    identityType: Specifies the type of identities that are allowed access to
      outside the perimeter. If left unspecified, then members of `identities`
      field will be allowed access.
    sourceRestriction: Whether to enforce traffic restrictions based on
      `sources` field. If the `sources` fields is non-empty, then this field
      must be set to `SOURCE_RESTRICTION_ENABLED`.
    sources: Sources that this EgressPolicy authorizes access from. If this
      field is not empty, then `source_restriction` must be set to
      `SOURCE_RESTRICTION_ENABLED`.
  """

  class IdentityTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the type of identities that are allowed access to outside
    the perimeter. If left unspecified, then members of `identities` field
    will be allowed access.

    Values:
      IDENTITY_TYPE_UNSPECIFIED: No blanket identity group specified.
      ANY_IDENTITY: Authorize access from all identities outside the
        perimeter.
      ANY_USER_ACCOUNT: Authorize access from all human users outside the
        perimeter.
      ANY_SERVICE_ACCOUNT: Authorize access from all service accounts outside
        the perimeter.
    """
    IDENTITY_TYPE_UNSPECIFIED = 0
    ANY_IDENTITY = 1
    ANY_USER_ACCOUNT = 2
    ANY_SERVICE_ACCOUNT = 3

  class SourceRestrictionValueValuesEnum(_messages.Enum):
    r"""Whether to enforce traffic restrictions based on `sources` field. If
    the `sources` fields is non-empty, then this field must be set to
    `SOURCE_RESTRICTION_ENABLED`.

    Values:
      SOURCE_RESTRICTION_UNSPECIFIED: Enforcement preference unspecified, will
        not enforce traffic restrictions based on `sources` in EgressFrom.
      SOURCE_RESTRICTION_ENABLED: Enforcement preference enabled, traffic
        restrictions will be enforced based on `sources` in EgressFrom.
      SOURCE_RESTRICTION_DISABLED: Enforcement preference disabled, will not
        enforce traffic restrictions based on `sources` in EgressFrom.
    """
    SOURCE_RESTRICTION_UNSPECIFIED = 0
    SOURCE_RESTRICTION_ENABLED = 1
    SOURCE_RESTRICTION_DISABLED = 2

  identities = _messages.StringField(1, repeated=True)
  identityType = _messages.EnumField('IdentityTypeValueValuesEnum', 2)
  sourceRestriction = _messages.EnumField('SourceRestrictionValueValuesEnum', 3)
  sources = _messages.MessageField('EgressSource', 4, repeated=True)


class EgressPolicy(_messages.Message):
  r"""Policy for egress from perimeter. EgressPolicies match requests based on
  `egress_from` and `egress_to` stanzas. For an EgressPolicy to match, both
  `egress_from` and `egress_to` stanzas must be matched. If an EgressPolicy
  matches a request, the request is allowed to span the ServicePerimeter
  boundary. For example, an EgressPolicy can be used to allow VMs on networks
  within the ServicePerimeter to access a defined set of projects outside the
  perimeter in certain contexts (e.g. to read data from a Cloud Storage bucket
  or query against a BigQuery dataset). EgressPolicies are concerned with the
  *resources* that a request relates as well as the API services and API
  actions being used. They do not related to the direction of data movement.
  More detailed documentation for this concept can be found in the
  descriptions of EgressFrom and EgressTo.

  Fields:
    egressFrom: Defines conditions on the source of a request causing this
      EgressPolicy to apply.
    egressTo: Defines the conditions on the ApiOperation and destination
      resources that cause this EgressPolicy to apply.
    title: Optional. Human-readable title for the egress rule. The title must
      be unique within the perimeter and can not exceed 100 characters. Within
      the access policy, the combined length of all rule titles must not
      exceed 240,000 characters.
  """

  egressFrom = _messages.MessageField('EgressFrom', 1)
  egressTo = _messages.MessageField('EgressTo', 2)
  title = _messages.StringField(3)


class EgressSource(_messages.Message):
  r"""The source that EgressPolicy authorizes access from inside the
  ServicePerimeter to somewhere outside the ServicePerimeter boundaries.

  Fields:
    accessLevel: An AccessLevel resource name that allows protected resources
      inside the ServicePerimeters to access outside the ServicePerimeter
      boundaries. AccessLevels listed must be in the same policy as this
      ServicePerimeter. Referencing a nonexistent AccessLevel will cause an
      error. If an AccessLevel name is not specified, only resources within
      the perimeter can be accessed through Google Cloud calls with request
      origins within the perimeter. Example:
      `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is
      specified for `access_level`, then all EgressSources will be allowed.
    resource: A Google Cloud resource from the service perimeter that you want
      to allow to access data outside the perimeter. This field supports only
      projects. The project format is `projects/{project_number}`. You can't
      use `*` in this field to allow all Google Cloud resources.
  """

  accessLevel = _messages.StringField(1)
  resource = _messages.StringField(2)


class EgressTo(_messages.Message):
  r"""Defines the conditions under which an EgressPolicy matches a request.
  Conditions are based on information about the ApiOperation intended to be
  performed on the `resources` specified. Note that if the destination of the
  request is also protected by a ServicePerimeter, then that ServicePerimeter
  must have an IngressPolicy which allows access in order for this request to
  succeed. The request must match `operations` AND `resources` fields in order
  to be allowed egress out of the perimeter.

  Fields:
    externalResources: A list of external resources that are allowed to be
      accessed. Only AWS and Azure resources are supported. For Amazon S3, the
      supported formats are s3://BUCKET_NAME, s3a://BUCKET_NAME, and
      s3n://BUCKET_NAME. For Azure Storage, the supported format is
      azure://myaccount.blob.core.windows.net/CONTAINER_NAME. A request
      matches if it contains an external resource in this list (Example:
      s3://bucket/path). Currently '*' is not allowed.
    operations: A list of ApiOperations allowed to be performed by the sources
      specified in the corresponding EgressFrom. A request matches if it uses
      an operation/service in this list.
    resources: A list of resources, currently only projects in the form
      `projects/`, that are allowed to be accessed by sources defined in the
      corresponding EgressFrom. A request matches if it contains a resource in
      this list. If `*` is specified for `resources`, then this EgressTo rule
      will authorize access to all resources outside the perimeter.
    roles: IAM roles that represent the set of operations allowed to be
      performed by the sources specified in the corresponding EgressFrom.
  """

  externalResources = _messages.StringField(1, repeated=True)
  operations = _messages.MessageField('ApiOperation', 2, repeated=True)
  resources = _messages.StringField(3, repeated=True)
  roles = _messages.StringField(4, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GcpUserAccessBinding(_messages.Message):
  r"""Restricts access to Cloud Console and Google Cloud APIs for a set of
  users using Context-Aware Access.

  Fields:
    accessLevels: Optional. Access level that a user must have to be granted
      access. Only one access level is supported, not multiple. This repeated
      field must have exactly one element. Example:
      "accessPolicies/9522/accessLevels/device_trusted"
    dryRunAccessLevels: Optional. Dry run access level that will be evaluated
      but will not be enforced. The access denial based on dry run policy will
      be logged. Only one access level is supported, not multiple. This list
      must have exactly one element. Example:
      "accessPolicies/9522/accessLevels/device_trusted"
    groupKey: Optional. Immutable. Google Group id whose members are subject
      to this binding's restrictions. See "id" in the [G Suite Directory API's
      Groups resource] (https://developers.google.com/admin-
      sdk/directory/v1/reference/groups#resource). If a group's email
      address/alias is changed, this resource will continue to point at the
      changed group. This field does not accept group email addresses or
      aliases. Example: "01d520gv4vjcrht"
    name: Immutable. Identifier. Assigned by the server during creation. The
      last segment has an arbitrary length and has only URI unreserved
      characters (as defined by [RFC 3986 Section
      2.3](https://tools.ietf.org/html/rfc3986#section-2.3)). Should not be
      specified by the client during creation. Example:
      "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
    principal: Optional. Immutable. The principal that is subject to the
      access policies in this policy binding.
    restrictedClientApplications: Optional. A list of applications that are
      subject to this binding's restrictions. If the list is empty, the
      binding restrictions will universally apply to all applications.
    scopedAccessSettings: Optional. A list of scoped access settings that set
      this binding's restrictions on a subset of applications. This field
      cannot be set if restricted_client_applications is set.
    sessionSettings: Optional. The Google Cloud session length (GCSL) policy
      for the group key.
  """

  accessLevels = _messages.StringField(1, repeated=True)
  dryRunAccessLevels = _messages.StringField(2, repeated=True)
  groupKey = _messages.StringField(3)
  name = _messages.StringField(4)
  principal = _messages.MessageField('Principal', 5)
  restrictedClientApplications = _messages.MessageField('Application', 6, repeated=True)
  scopedAccessSettings = _messages.MessageField('ScopedAccessSettings', 7, repeated=True)
  sessionSettings = _messages.MessageField('SessionSettings', 8)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class IngressFrom(_messages.Message):
  r"""Defines the conditions under which an IngressPolicy matches a request.
  Conditions are based on information about the source of the request. The
  request must satisfy what is defined in `sources` AND identity related
  fields in order to match.

  Enums:
    IdentityTypeValueValuesEnum: Specifies the type of identities that are
      allowed access from outside the perimeter. If left unspecified, then
      members of `identities` field will be allowed access.

  Fields:
    identities: A list of identities that are allowed access through
      IngressPolicy. Identities can be an individual user, service account,
      Google group, or third-party identity. For third-party identity, only
      single identities are supported and other identity types are not
      supported. The `v1` identities that have the prefix `user`, `group`,
      `serviceAccount`, and `principal` in
      https://cloud.google.com/iam/docs/principal-identifiers#v1 are
      supported.
    identityType: Specifies the type of identities that are allowed access
      from outside the perimeter. If left unspecified, then members of
      `identities` field will be allowed access.
    sources: Sources that this IngressPolicy authorizes access from.
  """

  class IdentityTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the type of identities that are allowed access from outside
    the perimeter. If left unspecified, then members of `identities` field
    will be allowed access.

    Values:
      IDENTITY_TYPE_UNSPECIFIED: No blanket identity group specified.
      ANY_IDENTITY: Authorize access from all identities outside the
        perimeter.
      ANY_USER_ACCOUNT: Authorize access from all human users outside the
        perimeter.
      ANY_SERVICE_ACCOUNT: Authorize access from all service accounts outside
        the perimeter.
    """
    IDENTITY_TYPE_UNSPECIFIED = 0
    ANY_IDENTITY = 1
    ANY_USER_ACCOUNT = 2
    ANY_SERVICE_ACCOUNT = 3

  identities = _messages.StringField(1, repeated=True)
  identityType = _messages.EnumField('IdentityTypeValueValuesEnum', 2)
  sources = _messages.MessageField('IngressSource', 3, repeated=True)


class IngressPolicy(_messages.Message):
  r"""Policy for ingress into ServicePerimeter. IngressPolicies match requests
  based on `ingress_from` and `ingress_to` stanzas. For an ingress policy to
  match, both the `ingress_from` and `ingress_to` stanzas must be matched. If
  an IngressPolicy matches a request, the request is allowed through the
  perimeter boundary from outside the perimeter. For example, access from the
  internet can be allowed either based on an AccessLevel or, for traffic
  hosted on Google Cloud, the project of the source network. For access from
  private networks, using the project of the hosting network is required.
  Individual ingress policies can be limited by restricting which services
  and/or actions they match using the `ingress_to` field.

  Fields:
    ingressFrom: Defines the conditions on the source of a request causing
      this IngressPolicy to apply.
    ingressTo: Defines the conditions on the ApiOperation and request
      destination that cause this IngressPolicy to apply.
    title: Optional. Human-readable title for the ingress rule. The title must
      be unique within the perimeter and can not exceed 100 characters. Within
      the access policy, the combined length of all rule titles must not
      exceed 240,000 characters.
  """

  ingressFrom = _messages.MessageField('IngressFrom', 1)
  ingressTo = _messages.MessageField('IngressTo', 2)
  title = _messages.StringField(3)


class IngressSource(_messages.Message):
  r"""The source that IngressPolicy authorizes access from.

  Fields:
    accessLevel: An AccessLevel resource name that allows resources within the
      ServicePerimeters to be accessed from the internet. AccessLevels listed
      must be in the same policy as this ServicePerimeter. Referencing a
      nonexistent AccessLevel will cause an error. If an AccessLevel
      AccessLevel name is not specified, resources within the perimeter can
      only be accessed through Google Cloud calls with request origins within
      the perimeter. Example:
      `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is
      specified for `access_level`, then all IngressSources will be allowed.
    resource: A Google Cloud resource that is allowed to ingress the
      perimeter. Requests from these resources are allowed to access perimeter
      data. Only projects and VPCs are allowed. Project format:
      `projects/{project_number}`. VPC network format: `//compute.googleapis.c
      om/projects/{PROJECT_ID}/global/networks/{NETWORK_NAME}`. The resource
      might be in any Google Cloud organization, not just the organization
      that the perimeter is defined in. `*` is not allowed, the case of
      allowing all Google Cloud resources only is not supported.
  """

  accessLevel = _messages.StringField(1)
  resource = _messages.StringField(2)


class IngressTo(_messages.Message):
  r"""Defines the conditions under which an IngressPolicy matches a request.
  Conditions are based on information about the ApiOperation intended to be
  performed on the target resource of the request. The request must satisfy
  what is defined in `operations` AND `resources` in order to match.

  Fields:
    operations: A list of ApiOperations the sources specified in corresponding
      IngressFrom are allowed to perform in this ServicePerimeter.
    resources: A list of resources, currently only projects in the form
      `projects/`, protected by this ServicePerimeter that are allowed to be
      accessed by sources defined in the corresponding IngressFrom. If a
      single `*` is specified, then access to all resources inside the
      perimeter are allowed.
    roles: IAM roles that represent the set of operations that the sources
      specified in the corresponding IngressFrom are allowed to perform in
      this ServicePerimeter.
  """

  operations = _messages.MessageField('ApiOperation', 1, repeated=True)
  resources = _messages.StringField(2, repeated=True)
  roles = _messages.StringField(3, repeated=True)


class ListAccessLevelsResponse(_messages.Message):
  r"""A response to `ListAccessLevelsRequest`.

  Fields:
    accessLevels: List of the Access Level instances.
    nextPageToken: The pagination token to retrieve the next page of results.
      If the value is empty, no further results remain.
  """

  accessLevels = _messages.MessageField('AccessLevel', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAccessPoliciesResponse(_messages.Message):
  r"""A response to `ListAccessPoliciesRequest`.

  Fields:
    accessPolicies: List of the AccessPolicy instances.
    nextPageToken: The pagination token to retrieve the next page of results.
      If the value is empty, no further results remain.
  """

  accessPolicies = _messages.MessageField('AccessPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAuthorizedOrgsDescsResponse(_messages.Message):
  r"""A response to `ListAuthorizedOrgsDescsRequest`.

  Fields:
    authorizedOrgsDescs: List of the Authorized Orgs Desc instances.
    nextPageToken: The pagination token to retrieve the next page of results.
      If the value is empty, no further results remain.
  """

  authorizedOrgsDescs = _messages.MessageField('AuthorizedOrgsDesc', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGcpUserAccessBindingsResponse(_messages.Message):
  r"""Response of ListGcpUserAccessBindings.

  Fields:
    gcpUserAccessBindings: GcpUserAccessBinding
    nextPageToken: Token to get the next page of items. If blank, there are no
      more items.
  """

  gcpUserAccessBindings = _messages.MessageField('GcpUserAccessBinding', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListServicePerimetersResponse(_messages.Message):
  r"""A response to `ListServicePerimetersRequest`.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of results.
      If the value is empty, no further results remain.
    servicePerimeters: List of the Service Perimeter instances.
  """

  nextPageToken = _messages.StringField(1)
  servicePerimeters = _messages.MessageField('ServicePerimeter', 2, repeated=True)


class ListSupportedServicesResponse(_messages.Message):
  r"""A response to `ListSupportedServicesRequest`.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of results.
      If the value is empty, no further results remain.
    supportedServices: List of services supported by VPC-SC instances.
  """

  nextPageToken = _messages.StringField(1)
  supportedServices = _messages.MessageField('SupportedService', 2, repeated=True)


class MethodSelector(_messages.Message):
  r"""An allowed method or permission of a service specified in ApiOperation.

  Fields:
    method: A valid method name for the corresponding `service_name` in
      ApiOperation. If `*` is used as the value for the `method`, then ALL
      methods and permissions are allowed.
    permission: A valid Cloud IAM permission for the corresponding
      `service_name` in ApiOperation.
  """

  method = _messages.StringField(1)
  permission = _messages.StringField(2)


class Modifier(_messages.Message):
  r"""Modifier to apply to the API requests.

  Fields:
    addRequestHeader: Adds additional HTTP request headers.
  """

  addRequestHeader = _messages.MessageField('AddRequestHeader', 1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OsConstraint(_messages.Message):
  r"""A restriction on the OS type and version of devices making requests.

  Enums:
    OsTypeValueValuesEnum: Required. The allowed OS type.

  Fields:
    minimumVersion: The minimum allowed OS version. If not set, any version of
      this OS satisfies the constraint. Format: `"major.minor.patch"`.
      Examples: `"10.5.301"`, `"9.2.1"`.
    osType: Required. The allowed OS type.
    requireVerifiedChromeOs: Only allows requests from devices with a verified
      Chrome OS. Verifications includes requirements that the device is
      enterprise-managed, conformant to domain policies, and the caller has
      permission to call the API targeted by the request.
  """

  class OsTypeValueValuesEnum(_messages.Enum):
    r"""Required. The allowed OS type.

    Values:
      OS_UNSPECIFIED: The operating system of the device is not specified or
        not known.
      DESKTOP_MAC: A desktop Mac operating system.
      DESKTOP_WINDOWS: A desktop Windows operating system.
      DESKTOP_LINUX: A desktop Linux operating system.
      DESKTOP_CHROME_OS: A desktop ChromeOS operating system.
      ANDROID: An Android operating system.
      IOS: An iOS operating system.
    """
    OS_UNSPECIFIED = 0
    DESKTOP_MAC = 1
    DESKTOP_WINDOWS = 2
    DESKTOP_LINUX = 3
    DESKTOP_CHROME_OS = 4
    ANDROID = 5
    IOS = 6

  minimumVersion = _messages.StringField(1)
  osType = _messages.EnumField('OsTypeValueValuesEnum', 2)
  requireVerifiedChromeOs = _messages.BooleanField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Principal(_messages.Message):
  r"""The comprehensive identity container supporting all 1p and 3p
  identities. Only one of them can be set to create an access binding. Next
  ID: 5

  Fields:
    serviceAccount: Immutable. Service account email used to assign policies
      to a single 1p service account.
    serviceAccountProjectNumber: Immutable. Project number used to assign
      policies to all service accounts in a Google Cloud project.
  """

  serviceAccount = _messages.StringField(1)
  serviceAccountProjectNumber = _messages.StringField(2)


class ReplaceAccessLevelsRequest(_messages.Message):
  r"""A request to replace all existing Access Levels in an Access Policy with
  the Access Levels provided. This is done atomically.

  Fields:
    accessLevels: Required. The desired Access Levels that should replace all
      existing Access Levels in the Access Policy.
    etag: Optional. The etag for the version of the Access Policy that this
      replace operation is to be performed on. If, at the time of replace, the
      etag for the Access Policy stored in Access Context Manager is different
      from the specified etag, then the replace operation will not be
      performed and the call will fail. This field is not required. If etag is
      not provided, the operation will be performed as if a valid etag is
      provided.
  """

  accessLevels = _messages.MessageField('AccessLevel', 1, repeated=True)
  etag = _messages.StringField(2)


class ReplaceAccessLevelsResponse(_messages.Message):
  r"""A response to ReplaceAccessLevelsRequest. This will be put inside of
  Operation.response field.

  Fields:
    accessLevels: List of the Access Level instances.
  """

  accessLevels = _messages.MessageField('AccessLevel', 1, repeated=True)


class ReplaceServicePerimetersRequest(_messages.Message):
  r"""A request to replace all existing Service Perimeters in an Access Policy
  with the Service Perimeters provided. This is done atomically.

  Fields:
    etag: Optional. The etag for the version of the Access Policy that this
      replace operation is to be performed on. If, at the time of replace, the
      etag for the Access Policy stored in Access Context Manager is different
      from the specified etag, then the replace operation will not be
      performed and the call will fail. This field is not required. If etag is
      not provided, the operation will be performed as if a valid etag is
      provided.
    servicePerimeters: Required. The desired Service Perimeters that should
      replace all existing Service Perimeters in the Access Policy.
  """

  etag = _messages.StringField(1)
  servicePerimeters = _messages.MessageField('ServicePerimeter', 2, repeated=True)


class ReplaceServicePerimetersResponse(_messages.Message):
  r"""A response to ReplaceServicePerimetersRequest. This will be put inside
  of Operation.response field.

  Fields:
    servicePerimeters: List of the Service Perimeter instances.
  """

  servicePerimeters = _messages.MessageField('ServicePerimeter', 1, repeated=True)


class Risk(_messages.Message):
  r"""Risk-based access level.

  Fields:
    userManagedRisk: The user managed risk associated with the access level.
  """

  userManagedRisk = _messages.MessageField('UserManagedRisk', 1)


class RiskType(_messages.Message):
  r"""The type of the risk used to calculate the access level risk score.

  Fields:
    atypicalLocation: The request is from an identity that has issued requests
      from atypical locations.
    identityReputation: The request is from an identity that has a low
      reputation (e.g. due to dormancy).
    maliciousActivity: The request is from an identity that has performed
      potentially malicious activity (e.g. mass deletion of backups).
    maliciousSource: The request is associated with signals (e.g. network)
      that indicate a malicious source.
    repeatAction: The request is from an identity that has issued repeated,
      suspicious requests (e.g. too many requests with permission denied).
  """

  atypicalLocation = _messages.BooleanField(1)
  identityReputation = _messages.BooleanField(2)
  maliciousActivity = _messages.BooleanField(3)
  maliciousSource = _messages.BooleanField(4)
  repeatAction = _messages.BooleanField(5)


class ScopedAccessSettings(_messages.Message):
  r"""A relationship between access settings and its scope.

  Fields:
    activeSettings: Optional. Access settings for this scoped access settings.
      This field may be empty if dry_run_settings is set.
    dryRunSettings: Optional. Dry-run access settings for this scoped access
      settings. This field may be empty if active_settings is set.
    scope: Optional. Application, etc. to which the access settings will be
      applied to. Implicitly, this is the scoped access settings key; as such,
      it must be unique and non-empty.
  """

  activeSettings = _messages.MessageField('AccessSettings', 1)
  dryRunSettings = _messages.MessageField('AccessSettings', 2)
  scope = _messages.MessageField('AccessScope', 3)


class ServicePattern(_messages.Message):
  r"""Service patterns used to allow access.

  Fields:
    modifiers: Modifiers to apply to the requests that match the URL pattern.
    pattern: URL pattern to allow. Only patterns of ".googleapis.com/*",
      "www.googleapis.com//*" and "*.appspot.com/* form are supported, where
      should be alphanumerical name.
    service: Supported service to allow.
  """

  modifiers = _messages.MessageField('Modifier', 1, repeated=True)
  pattern = _messages.StringField(2)
  service = _messages.StringField(3)


class ServicePerimeter(_messages.Message):
  r"""`ServicePerimeter` describes a set of Google Cloud resources which can
  freely import and export data amongst themselves, but not export outside of
  the `ServicePerimeter`. If a request with a source within this
  `ServicePerimeter` has a target outside of the `ServicePerimeter`, the
  request is blocked. Otherwise the request is allowed. There are two types of
  service perimeter: regular and bridge. Regular perimeters cannot overlap, a
  single Google Cloud project or VPC network can only belong to a single
  regular perimeter. Perimeter bridges can contain only Google Cloud projects
  as members, a single Google Cloud project might belong to multiple Service
  perimeter bridges.

  Enums:
    PerimeterTypeValueValuesEnum: Perimeter type indicator. A single project
      or VPC network is allowed to be a member of single regular perimeter,
      but a project can be in multiple service perimeter bridges. A project
      cannot be a included in a perimeter bridge without being included in
      regular perimeter. For perimeter bridges, the restricted service list as
      well as access level lists must be empty.

  Fields:
    description: Description of the `ServicePerimeter` and its use. Does not
      affect behavior.
    etag: Optional. An opaque identifier for the current version of the
      `ServicePerimeter`. This identifier does not follow any specific format.
      If an etag is not provided, the operation will be performed as if a
      valid etag is provided.
    name: Identifier. Resource name for the `ServicePerimeter`. Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`.
      The `service_perimeter` component must begin with a letter, followed by
      alphanumeric characters or `_`. After you create a `ServicePerimeter`,
      you cannot change its `name`.
    perimeterType: Perimeter type indicator. A single project or VPC network
      is allowed to be a member of single regular perimeter, but a project can
      be in multiple service perimeter bridges. A project cannot be a included
      in a perimeter bridge without being included in regular perimeter. For
      perimeter bridges, the restricted service list as well as access level
      lists must be empty.
    spec: Proposed (or dry run) ServicePerimeter configuration. This
      configuration allows to specify and test ServicePerimeter configuration
      without enforcing actual access restrictions. Only allowed to be set
      when the "use_explicit_dry_run_spec" flag is set.
    status: Current ServicePerimeter configuration. Specifies sets of
      resources, restricted services and access levels that determine
      perimeter content and boundaries.
    title: Human readable title. Must be unique within the Policy.
    useExplicitDryRunSpec: Use explicit dry run spec flag. Ordinarily, a dry-
      run spec implicitly exists for all Service Perimeters, and that spec is
      identical to the status for those Service Perimeters. When this flag is
      set, it inhibits the generation of the implicit spec, thereby allowing
      the user to explicitly provide a configuration ("spec") to use in a dry-
      run version of the Service Perimeter. This allows the user to test
      changes to the enforced config ("status") without actually enforcing
      them. This testing is done through analyzing the differences between
      currently enforced and suggested restrictions. use_explicit_dry_run_spec
      must bet set to True if any of the fields in the spec are set to non-
      default values.
  """

  class PerimeterTypeValueValuesEnum(_messages.Enum):
    r"""Perimeter type indicator. A single project or VPC network is allowed
    to be a member of single regular perimeter, but a project can be in
    multiple service perimeter bridges. A project cannot be a included in a
    perimeter bridge without being included in regular perimeter. For
    perimeter bridges, the restricted service list as well as access level
    lists must be empty.

    Values:
      PERIMETER_TYPE_REGULAR: Regular Perimeter. When no value is specified,
        the perimeter uses this type.
      PERIMETER_TYPE_BRIDGE: Perimeter Bridge.
    """
    PERIMETER_TYPE_REGULAR = 0
    PERIMETER_TYPE_BRIDGE = 1

  description = _messages.StringField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  perimeterType = _messages.EnumField('PerimeterTypeValueValuesEnum', 4)
  spec = _messages.MessageField('ServicePerimeterConfig', 5)
  status = _messages.MessageField('ServicePerimeterConfig', 6)
  title = _messages.StringField(7)
  useExplicitDryRunSpec = _messages.BooleanField(8)


class ServicePerimeterConfig(_messages.Message):
  r"""`ServicePerimeterConfig` specifies a set of Google Cloud resources that
  describe specific Service Perimeter configuration.

  Fields:
    accessLevels: A list of `AccessLevel` resource names that allow resources
      within the `ServicePerimeter` to be accessed from the internet.
      `AccessLevels` listed must be in the same policy as this
      `ServicePerimeter`. Referencing a nonexistent `AccessLevel` is a syntax
      error. If no `AccessLevel` names are listed, resources within the
      perimeter can only be accessed through Google Cloud calls with request
      origins within the perimeter. Example:
      `"accessPolicies/MY_POLICY/accessLevels/MY_LEVEL"`. For Service
      Perimeter Bridge, must be empty.
    egressPolicies: List of EgressPolicies to apply to the perimeter. A
      perimeter may have multiple EgressPolicies, each of which is evaluated
      separately. Access is granted if any EgressPolicy grants it. Must be
      empty for a perimeter bridge.
    ingressPolicies: List of IngressPolicies to apply to the perimeter. A
      perimeter may have multiple IngressPolicies, each of which is evaluated
      separately. Access is granted if any Ingress Policy grants it. Must be
      empty for a perimeter bridge.
    resources: A list Google Cloud resources that are inside of the service
      perimeter. Only projects, VPCs are allowed. Project format:
      `projects/{project_number}`. VPC network format: `//compute.googleapis.c
      om/projects/{PROJECT_ID}/global/networks/{NETWORK_NAME}`.
    restrictedServices: Google Cloud services that are subject to the Service
      Perimeter restrictions. For example, if `storage.googleapis.com` is
      specified, access to the storage buckets inside the perimeter must meet
      the perimeter's access restrictions.
    vpcAccessibleServices: Configuration for APIs allowed within Perimeter.
  """

  accessLevels = _messages.StringField(1, repeated=True)
  egressPolicies = _messages.MessageField('EgressPolicy', 2, repeated=True)
  ingressPolicies = _messages.MessageField('IngressPolicy', 3, repeated=True)
  resources = _messages.StringField(4, repeated=True)
  restrictedServices = _messages.StringField(5, repeated=True)
  vpcAccessibleServices = _messages.MessageField('VpcAccessibleServices', 6)


class SessionSettings(_messages.Message):
  r"""Stores settings related to Google Cloud Session Length including session
  duration, the type of challenge (i.e. method) they should face when their
  session expires, and other related settings.

  Enums:
    SessionReauthMethodValueValuesEnum: Optional. Session method when user's
      Google Cloud session is up.

  Fields:
    maxInactivity: Optional. How long a user is allowed to take between
      actions before a new access token must be issued. Only set for Google
      Cloud apps.
    sessionLength: Optional. The session length. Setting this field to zero is
      equal to disabling reauth. Also can set infinite session by flipping the
      enabled bit to false below. If use_oidc_max_age is true, for OIDC apps,
      the session length will be the minimum of this field and OIDC max_age
      param.
    sessionLengthEnabled: Optional. This field enables or disables Google
      Cloud session length. When false, all fields set above will be
      disregarded and the session length is basically infinite.
    sessionReauthMethod: Optional. Session method when user's Google Cloud
      session is up.
    useOidcMaxAge: Optional. Only useful for OIDC apps. When false, the OIDC
      max_age param, if passed in the authentication request will be ignored.
      When true, the re-auth period will be the minimum of the session_length
      field and the max_age OIDC param.
  """

  class SessionReauthMethodValueValuesEnum(_messages.Enum):
    r"""Optional. Session method when user's Google Cloud session is up.

    Values:
      SESSION_REAUTH_METHOD_UNSPECIFIED: If method is undefined in the API,
        LOGIN will be used by default.
      LOGIN: The user will be prompted to perform regular login. Users who are
        enrolled for two-step verification and haven't chosen "Remember this
        computer" will be prompted for their second factor.
      SECURITY_KEY: The user will be prompted to authenticate using their
        security key. If no security key has been configured, then
        authentication will fallback to LOGIN.
      PASSWORD: The user will be prompted for their password.
    """
    SESSION_REAUTH_METHOD_UNSPECIFIED = 0
    LOGIN = 1
    SECURITY_KEY = 2
    PASSWORD = 3

  maxInactivity = _messages.StringField(1)
  sessionLength = _messages.StringField(2)
  sessionLengthEnabled = _messages.BooleanField(3)
  sessionReauthMethod = _messages.EnumField('SessionReauthMethodValueValuesEnum', 4)
  useOidcMaxAge = _messages.BooleanField(5)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SupportedService(_messages.Message):
  r"""`SupportedService` specifies the VPC Service Controls and its
  properties.

  Enums:
    ServiceSupportStageValueValuesEnum: The support stage of the service.
    SupportStageValueValuesEnum: The support stage of the service.

  Fields:
    availableOnRestrictedVip: True if the service is available on the
      restricted VIP. Services on the restricted VIP typically either support
      VPC Service Controls or are core infrastructure services required for
      the functioning of Google Cloud.
    knownLimitations: True if the service is supported with some limitations.
      Check [documentation](https://cloud.google.com/vpc-service-
      controls/docs/supported-products) for details.
    name: The service name/address of the supported service, such as
      'service.googleapis.com'
    serviceSupportStage: The support stage of the service.
    supportStage: The support stage of the service.
    supportedMethods: The list of the supported methods. This field exists
      only in response to GetSupportedService
    title: The name of the supported product, such as 'Cloud Product API'.
  """

  class ServiceSupportStageValueValuesEnum(_messages.Enum):
    r"""The support stage of the service.

    Values:
      SERVICE_SUPPORT_STAGE_UNSPECIFIED: Do not use this default value.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      PREVIEW: PREVIEW indicates a pre-release stage where the product is
        functionally complete but undergoing real-world testing.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed.
    """
    SERVICE_SUPPORT_STAGE_UNSPECIFIED = 0
    GA = 1
    PREVIEW = 2
    DEPRECATED = 3

  class SupportStageValueValuesEnum(_messages.Enum):
    r"""The support stage of the service.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  availableOnRestrictedVip = _messages.BooleanField(1)
  knownLimitations = _messages.BooleanField(2)
  name = _messages.StringField(3)
  serviceSupportStage = _messages.EnumField('ServiceSupportStageValueValuesEnum', 4)
  supportStage = _messages.EnumField('SupportStageValueValuesEnum', 5)
  supportedMethods = _messages.MessageField('MethodSelector', 6, repeated=True)
  title = _messages.StringField(7)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UnsatisfiedResult(_messages.Message):
  r"""The result to apply if the condition is not met. By default, the result
  is deny.

  Enums:
    ResultTypeValueValuesEnum: The type of result to apply if the condition is
      not met.

  Fields:
    amendments: List of amendments to apply if the condition is not met. If
      ALL amendments are satisfied, the condition is as well. For example, a
      successful user reauthentication may resolve a failing risk condition. -
      It applies only when result_type == AMENDABLE. - Only a single amendment
      i.e. "responses.reauthRequired" is allowed today.
    remediations: Deprecated field that needs to be cleaned up after Pantheon
      validation.
    resultType: The type of result to apply if the condition is not met.
  """

  class ResultTypeValueValuesEnum(_messages.Enum):
    r"""The type of result to apply if the condition is not met.

    Values:
      DENY: Default type of result.
      REMEDIATION: Deprecated field that needs to be cleaned up after Pantheon
        validation.
      AMENDABLE: The result is amendable. Currently, the only supported
        amendable is reauth.
    """
    DENY = 0
    REMEDIATION = 1
    AMENDABLE = 2

  amendments = _messages.StringField(1, repeated=True)
  remediations = _messages.StringField(2, repeated=True)
  resultType = _messages.EnumField('ResultTypeValueValuesEnum', 3)


class UnsupportedServicesOptions(_messages.Message):
  r"""Specifies private access paths that the restriction applies to for
  unsupported services. Currently, only "private.googleapis.com" is supported.
  If empty, the VPC accessible services restriction will not be guaranteed for
  unsupported services.

  Fields:
    restrictedAccessPaths: The list of private access paths that the
      restriction applies to for unsupported services.
  """

  restrictedAccessPaths = _messages.StringField(1, repeated=True)


class UserManagedRisk(_messages.Message):
  r"""User managed risk associated with the access level.

  Fields:
    riskType: The type of the risks associated with the access level.
  """

  riskType = _messages.MessageField('RiskType', 1)


class VpcAccessibleServices(_messages.Message):
  r"""Specifies how APIs are allowed to communicate within the Service
  Perimeter.

  Fields:
    allowedServicePatterns: Specifies which Google services are allowed to be
      accessed from VPC networks in the service perimeter.
    allowedServices: The list of APIs usable within the Service Perimeter.
      Must be empty unless 'enable_restriction' is True. You can specify a
      list of individual services, as well as include the 'RESTRICTED-
      SERVICES' value, which automatically includes all of the services
      protected by the perimeter.
    enableRestriction: Whether to restrict API calls within the Service
      Perimeter to the list of APIs specified in 'allowed_services'.
    unsupportedServicesOptions: Specifies which private access paths the
      restriction applies to for unsupported services.
  """

  allowedServicePatterns = _messages.MessageField('ServicePattern', 1, repeated=True)
  allowedServices = _messages.StringField(2, repeated=True)
  enableRestriction = _messages.BooleanField(3)
  unsupportedServicesOptions = _messages.MessageField('UnsupportedServicesOptions', 4)


class VpcNetworkSource(_messages.Message):
  r"""The originating network source in Google Cloud.

  Fields:
    vpcSubnetwork: Sub-segment ranges of a VPC network.
  """

  vpcSubnetwork = _messages.MessageField('VpcSubNetwork', 1)


class VpcSubNetwork(_messages.Message):
  r"""Sub-segment ranges inside of a VPC Network.

  Fields:
    network: Required. Network name. If the network is not part of the
      organization, the `compute.network.get` permission must be granted to
      the caller. Format: `//compute.googleapis.com/projects/{PROJECT_ID}/glob
      al/networks/{NETWORK_NAME}` Example:
      `//compute.googleapis.com/projects/my-project/global/networks/network-1`
    vpcIpSubnetworks: CIDR block IP subnetwork specification. The IP address
      must be an IPv4 address and can be a public or private IP address. Note
      that for a CIDR IP address block, the specified IP address portion must
      be properly truncated (i.e. all the host bits must be zero) or the input
      is considered malformed. For example, "*********/24" is accepted but
      "*********/24" is not. If empty, all IP addresses are allowed.
  """

  network = _messages.StringField(1)
  vpcIpSubnetworks = _messages.StringField(2, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
