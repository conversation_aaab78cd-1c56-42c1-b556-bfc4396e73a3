"""Generated client library for accesscontextmanager version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.accesscontextmanager.v1alpha import accesscontextmanager_v1alpha_messages as messages


class AccesscontextmanagerV1alpha(base_api.BaseApiClient):
  """Generated client library for service accesscontextmanager version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://accesscontextmanager.googleapis.com/'
  MTLS_BASE_URL = 'https://accesscontextmanager.mtls.googleapis.com/'

  _PACKAGE = 'accesscontextmanager'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AccesscontextmanagerV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new accesscontextmanager handle."""
    url = url or self.BASE_URL
    super(AccesscontextmanagerV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.accessPolicies_accessLevels = self.AccessPoliciesAccessLevelsService(self)
    self.accessPolicies_authorizedOrgsDescs = self.AccessPoliciesAuthorizedOrgsDescsService(self)
    self.accessPolicies_servicePerimeters = self.AccessPoliciesServicePerimetersService(self)
    self.accessPolicies = self.AccessPoliciesService(self)
    self.operations = self.OperationsService(self)
    self.organizations_gcpUserAccessBindings = self.OrganizationsGcpUserAccessBindingsService(self)
    self.organizations = self.OrganizationsService(self)
    self.services = self.ServicesService(self)

  class AccessPoliciesAccessLevelsService(base_api.BaseApiService):
    """Service class for the accessPolicies_accessLevels resource."""

    _NAME = 'accessPolicies_accessLevels'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.AccessPoliciesAccessLevelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an access level. The long- running operation from this RPC has a successful status after the access level propagates to long-lasting storage. If access levels contain errors, an error response is returned for the first error encountered.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.accessLevels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/accessLevels',
        request_field='accessLevel',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an access level based on the resource name. The long-running operation from this RPC has a successful status after the access level has been removed from long-lasting storage.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}',
        http_method='DELETE',
        method_id='accesscontextmanager.accessPolicies.accessLevels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an access level based on the resource name.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AccessLevel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.accessLevels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['accessLevelFormat'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsGetRequest',
        response_type_name='AccessLevel',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all access levels for an access policy.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAccessLevelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.accessLevels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessLevelFormat', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/accessLevels',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsListRequest',
        response_type_name='ListAccessLevelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an access level. The long- running operation from this RPC has a successful status after the changes to the access level propagate to long-lasting storage. If access levels contain errors, an error response is returned for the first error encountered.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}',
        http_method='PATCH',
        method_id='accesscontextmanager.accessPolicies.accessLevels.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='accessLevel',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReplaceAll(self, request, global_params=None):
      r"""Replaces all existing access levels in an access policy with the access levels provided. This is done atomically. The long-running operation from this RPC has a successful status after all replacements propagate to long-lasting storage. If the replacement contains errors, an error response is returned for the first error encountered. Upon error, the replacement is cancelled, and existing access levels are not affected. The Operation.response field contains ReplaceAccessLevelsResponse. Removing access levels contained in existing service perimeters result in an error.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsReplaceAllRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReplaceAll')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceAll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels:replaceAll',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.accessLevels.replaceAll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/accessLevels:replaceAll',
        request_field='replaceAccessLevelsRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsReplaceAllRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the IAM permissions that the caller has on the specified Access Context Manager resource. The resource can be an AccessPolicy, AccessLevel, or ServicePerimeter. This method does not support other resources.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAccessLevelsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}:testIamPermissions',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.accessLevels.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesAccessLevelsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class AccessPoliciesAuthorizedOrgsDescsService(base_api.BaseApiService):
    """Service class for the accessPolicies_authorizedOrgsDescs resource."""

    _NAME = 'accessPolicies_authorizedOrgsDescs'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.AccessPoliciesAuthorizedOrgsDescsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an authorized orgs desc. The long-running operation from this RPC has a successful status after the authorized orgs desc propagates to long-lasting storage. If a authorized orgs desc contains errors, an error response is returned for the first error encountered. The name of this `AuthorizedOrgsDesc` will be assigned during creation.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/authorizedOrgsDescs',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.authorizedOrgsDescs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/authorizedOrgsDescs',
        request_field='authorizedOrgsDesc',
        request_type_name='AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an authorized orgs desc based on the resource name. The long-running operation from this RPC has a successful status after the authorized orgs desc is removed from long-lasting storage.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/authorizedOrgsDescs/{authorizedOrgsDescsId}',
        http_method='DELETE',
        method_id='accesscontextmanager.accessPolicies.authorizedOrgsDescs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an authorized orgs desc based on the resource name.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthorizedOrgsDesc) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/authorizedOrgsDescs/{authorizedOrgsDescsId}',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.authorizedOrgsDescs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsGetRequest',
        response_type_name='AuthorizedOrgsDesc',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all authorized orgs descs for an access policy.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizedOrgsDescsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/authorizedOrgsDescs',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.authorizedOrgsDescs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/authorizedOrgsDescs',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsListRequest',
        response_type_name='ListAuthorizedOrgsDescsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an authorized orgs desc. The long-running operation from this RPC has a successful status after the authorized orgs desc propagates to long-lasting storage. If a authorized orgs desc contains errors, an error response is returned for the first error encountered. Only the organization list in `AuthorizedOrgsDesc` can be updated. The name, authorization_type, asset_type and authorization_direction cannot be updated.

      Args:
        request: (AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/authorizedOrgsDescs/{authorizedOrgsDescsId}',
        http_method='PATCH',
        method_id='accesscontextmanager.accessPolicies.authorizedOrgsDescs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='authorizedOrgsDesc',
        request_type_name='AccesscontextmanagerAccessPoliciesAuthorizedOrgsDescsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class AccessPoliciesServicePerimetersService(base_api.BaseApiService):
    """Service class for the accessPolicies_servicePerimeters resource."""

    _NAME = 'accessPolicies_servicePerimeters'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.AccessPoliciesServicePerimetersService, self).__init__(client)
      self._upload_configs = {
          }

    def Commit(self, request, global_params=None):
      r"""Commits the dry-run specification for all the service perimeters in an access policy. A commit operation on a service perimeter involves copying its `spec` field to the `status` field of the service perimeter. Only service perimeters with `use_explicit_dry_run_spec` field set to true are affected by a commit operation. The long-running operation from this RPC has a successful status after the dry-run specifications for all the service perimeters have been committed. If a commit fails, it causes the long-running operation to return an error response and the entire commit operation is cancelled. When successful, the Operation.response field contains CommitServicePerimetersResponse. The `dry_run` and the `spec` fields are cleared after a successful commit operation.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersCommitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Commit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Commit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters:commit',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.commit',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/servicePerimeters:commit',
        request_field='commitServicePerimetersRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersCommitRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a service perimeter. The long-running operation from this RPC has a successful status after the service perimeter propagates to long-lasting storage. If a service perimeter contains errors, an error response is returned for the first error encountered.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/servicePerimeters',
        request_field='servicePerimeter',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a service perimeter based on the resource name. The long-running operation from this RPC has a successful status after the service perimeter is removed from long-lasting storage.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}',
        http_method='DELETE',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a service perimeter based on the resource name.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServicePerimeter) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersGetRequest',
        response_type_name='ServicePerimeter',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all service perimeters for an access policy.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicePerimetersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/servicePerimeters',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersListRequest',
        response_type_name='ListServicePerimetersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a service perimeter. The long-running operation from this RPC has a successful status after the service perimeter propagates to long-lasting storage. If a service perimeter contains errors, an error response is returned for the first error encountered.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}',
        http_method='PATCH',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='servicePerimeter',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReplaceAll(self, request, global_params=None):
      r"""Replace all existing service perimeters in an access policy with the service perimeters provided. This is done atomically. The long-running operation from this RPC has a successful status after all replacements propagate to long-lasting storage. Replacements containing errors result in an error response for the first error encountered. Upon an error, replacement are cancelled and existing service perimeters are not affected. The Operation.response field contains ReplaceServicePerimetersResponse.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersReplaceAllRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReplaceAll')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceAll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters:replaceAll',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.replaceAll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/servicePerimeters:replaceAll',
        request_field='replaceServicePerimetersRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersReplaceAllRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the IAM permissions that the caller has on the specified Access Context Manager resource. The resource can be an AccessPolicy, AccessLevel, or ServicePerimeter. This method does not support other resources.

      Args:
        request: (AccesscontextmanagerAccessPoliciesServicePerimetersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}:testIamPermissions',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.servicePerimeters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesServicePerimetersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class AccessPoliciesService(base_api.BaseApiService):
    """Service class for the accessPolicies resource."""

    _NAME = 'accessPolicies'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.AccessPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an access policy. This method fails if the organization already has an access policy. The long-running operation has a successful status after the access policy propagates to long-lasting storage. Syntactic and basic semantic errors are returned in `metadata` as a BadRequest proto.

      Args:
        request: (AccessPolicy) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.create',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha/accessPolicies',
        request_field='<request>',
        request_type_name='AccessPolicy',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an access policy based on the resource name. The long-running operation has a successful status after the access policy is removed from long-lasting storage.

      Args:
        request: (AccesscontextmanagerAccessPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}',
        http_method='DELETE',
        method_id='accesscontextmanager.accessPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns an access policy based on the name.

      Args:
        request: (AccesscontextmanagerAccessPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}',
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesGetRequest',
        response_type_name='AccessPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy for the specified Access Context Manager access policy.

      Args:
        request: (AccesscontextmanagerAccessPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}:getIamPolicy',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all access policies in an organization.

      Args:
        request: (AccesscontextmanagerAccessPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAccessPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='accesscontextmanager.accessPolicies.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken', 'parent'],
        relative_path='v1alpha/accessPolicies',
        request_field='',
        request_type_name='AccesscontextmanagerAccessPoliciesListRequest',
        response_type_name='ListAccessPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an access policy. The long-running operation from this RPC has a successful status after the changes to the access policy propagate to long-lasting storage.

      Args:
        request: (AccesscontextmanagerAccessPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}',
        http_method='PATCH',
        method_id='accesscontextmanager.accessPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='accessPolicy',
        request_type_name='AccesscontextmanagerAccessPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policy for the specified Access Context Manager access policy. This method replaces the existing IAM policy on the access policy. The IAM policy controls the set of users who can perform specific operations on the Access Context Manager access policy.

      Args:
        request: (AccesscontextmanagerAccessPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the IAM permissions that the caller has on the specified Access Context Manager resource. The resource can be an AccessPolicy, AccessLevel, or ServicePerimeter. This method does not support other resources.

      Args:
        request: (AccesscontextmanagerAccessPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/accessPolicies/{accessPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='accesscontextmanager.accessPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='AccesscontextmanagerAccessPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AccesscontextmanagerOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/operations/{operationsId}',
        http_method='GET',
        method_id='accesscontextmanager.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsGcpUserAccessBindingsService(base_api.BaseApiService):
    """Service class for the organizations_gcpUserAccessBindings resource."""

    _NAME = 'organizations_gcpUserAccessBindings'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.OrganizationsGcpUserAccessBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a GcpUserAccessBinding. If the client specifies a name, the server ignores it. Fails if a resource already exists with the same group_key. Completion of this long-running operation does not necessarily signify that the new binding is deployed onto all affected users, which may take more time.

      Args:
        request: (AccesscontextmanagerOrganizationsGcpUserAccessBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/gcpUserAccessBindings',
        http_method='POST',
        method_id='accesscontextmanager.organizations.gcpUserAccessBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/gcpUserAccessBindings',
        request_field='gcpUserAccessBinding',
        request_type_name='AccesscontextmanagerOrganizationsGcpUserAccessBindingsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a GcpUserAccessBinding. Completion of this long-running operation does not necessarily signify that the binding deletion is deployed onto all affected users, which may take more time.

      Args:
        request: (AccesscontextmanagerOrganizationsGcpUserAccessBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/gcpUserAccessBindings/{gcpUserAccessBindingsId}',
        http_method='DELETE',
        method_id='accesscontextmanager.organizations.gcpUserAccessBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerOrganizationsGcpUserAccessBindingsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the GcpUserAccessBinding with the given name.

      Args:
        request: (AccesscontextmanagerOrganizationsGcpUserAccessBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GcpUserAccessBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/gcpUserAccessBindings/{gcpUserAccessBindingsId}',
        http_method='GET',
        method_id='accesscontextmanager.organizations.gcpUserAccessBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AccesscontextmanagerOrganizationsGcpUserAccessBindingsGetRequest',
        response_type_name='GcpUserAccessBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all GcpUserAccessBindings for a Google Cloud organization.

      Args:
        request: (AccesscontextmanagerOrganizationsGcpUserAccessBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGcpUserAccessBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/gcpUserAccessBindings',
        http_method='GET',
        method_id='accesscontextmanager.organizations.gcpUserAccessBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/gcpUserAccessBindings',
        request_field='',
        request_type_name='AccesscontextmanagerOrganizationsGcpUserAccessBindingsListRequest',
        response_type_name='ListGcpUserAccessBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a GcpUserAccessBinding. Completion of this long-running operation does not necessarily signify that the changed binding is deployed onto all affected users, which may take more time.

      Args:
        request: (AccesscontextmanagerOrganizationsGcpUserAccessBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/gcpUserAccessBindings/{gcpUserAccessBindingsId}',
        http_method='PATCH',
        method_id='accesscontextmanager.organizations.gcpUserAccessBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['append', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='gcpUserAccessBinding',
        request_type_name='AccesscontextmanagerOrganizationsGcpUserAccessBindingsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(AccesscontextmanagerV1alpha.ServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a VPS-SC Supported Service by name.

      Args:
        request: (AccesscontextmanagerServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SupportedService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='accesscontextmanager.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/services/{name}',
        request_field='',
        request_type_name='AccesscontextmanagerServicesGetRequest',
        response_type_name='SupportedService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all VPC-SC supported services.

      Args:
        request: (AccesscontextmanagerServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSupportedServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='accesscontextmanager.services.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/services',
        request_field='',
        request_type_name='AccesscontextmanagerServicesListRequest',
        response_type_name='ListSupportedServicesResponse',
        supports_download=False,
    )
