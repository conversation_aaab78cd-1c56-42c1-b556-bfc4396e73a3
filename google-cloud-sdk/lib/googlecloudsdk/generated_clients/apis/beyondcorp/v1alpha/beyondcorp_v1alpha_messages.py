"""Generated message classes for beyondcorp version v1alpha.

Beyondcorp Enterprise provides identity and context aware access controls for
enterprise resources and enables zero-trust access. Using the Beyondcorp
Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity
solutions.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'beyondcorp'


class AllocatedConnection(_messages.Message):
  r"""Allocated connection of the AppGateway.

  Fields:
    ingressPort: Required. The ingress port of an allocated connection
    pscUri: Required. The PSC uri of an allocated connection
  """

  ingressPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pscUri = _messages.StringField(2)


class AppGateway(_messages.Message):
  r"""A BeyondCorp AppGateway resource represents a BeyondCorp protected
  AppGateway to a remote application. It creates all the necessary GCP
  components needed for creating a BeyondCorp protected AppGateway. Multiple
  connectors can be authorised for a single AppGateway.

  Enums:
    HostTypeValueValuesEnum: Required. The type of hosting used by the
      AppGateway.
    StateValueValuesEnum: Output only. The current state of the AppGateway.
    TypeValueValuesEnum: Required. The type of network connectivity used by
      the AppGateway.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    allocatedConnections: Output only. A list of connections allocated for the
      Gateway
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the AppGateway.
      Cannot exceed 64 characters.
    hostType: Required. The type of hosting used by the AppGateway.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Required. Unique resource name of the AppGateway. The name is
      ignored when creating an AppGateway.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current state of the AppGateway.
    type: Required. The type of network connectivity used by the AppGateway.
    uid: Output only. A unique identifier for the instance generated by the
      system.
    updateTime: Output only. Timestamp when the resource was last modified.
    uri: Output only. Server-defined URI for this resource.
  """

  class HostTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of hosting used by the AppGateway.

    Values:
      HOST_TYPE_UNSPECIFIED: Default value. This value is unused.
      GCP_REGIONAL_MIG: AppGateway hosted in a GCP regional managed instance
        group.
    """
    HOST_TYPE_UNSPECIFIED = 0
    GCP_REGIONAL_MIG = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the AppGateway.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: AppGateway is being created.
      CREATED: AppGateway has been created.
      UPDATING: AppGateway's configuration is being updated.
      DELETING: AppGateway is being deleted.
      DOWN: AppGateway is down and may be restored in the future. This happens
        when CCFE sends ProjectState = OFF.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    UPDATING = 3
    DELETING = 4
    DOWN = 5

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of network connectivity used by the AppGateway.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      TCP_PROXY: TCP Proxy based BeyondCorp Connection. API will default to
        this if unset.
    """
    TYPE_UNSPECIFIED = 0
    TCP_PROXY = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allocatedConnections = _messages.MessageField('AllocatedConnection', 1, repeated=True)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  hostType = _messages.EnumField('HostTypeValueValuesEnum', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  satisfiesPzi = _messages.BooleanField(7)
  satisfiesPzs = _messages.BooleanField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  uri = _messages.StringField(13)


class AppGatewayOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of `1`, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ApplicationEndpoint(_messages.Message):
  r"""ApplicationEndpoint represents a remote application endpoint.

  Fields:
    host: Required. Hostname or IP address of the remote application endpoint.
    port: Required. Port of the remote application endpoint.
  """

  host = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesTes
  tIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsDeleteRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetRequest object.

  Fields:
    name: Required. The resource name of the PartnerTenant using the form: `or
      ganizations/{organization_id}/locations/global/partnerTenants/{partner_t
      enant_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsListRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsListRequest
  object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation. All
      fields in the PartnerTenant message are supported. For example, the
      following query will return the PartnerTenants with displayName "test-
      tenant" organizations/${ORG_ID}/locations/${LOCATION}/partnerTenants?fil
      ter=displayName="test-tenant" Nested fields are also supported. The
      follow query will return PartnerTenants with internal_tenant_id "1234" o
      rganizations/${ORG_ID}/locations/${LOCATION}/partnerTenants?filter=partn
      erMetadata.internalTenantId="1234" For more information, please refer to
      https://google.aip.dev/160.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListPartnerTenantsResponse, if any.
    parent: Required. The parent organization to which the PartnerTenants
      belong. Format: `organizations/{organization_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsTestIa
  mPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpOrganizationsLocationsGlobalPartnerTenantsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsGlobalPartnerTenantsTestIamPermissions
  Request object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest
  object.

  Enums:
    AggregationValueValuesEnum: Required. Aggregation type. Available
      aggregation could be fetched by calling insight list and get APIs in
      `BASIC` view.

  Fields:
    aggregation: Required. Aggregation type. Available aggregation could be
      fetched by calling insight list and get APIs in `BASIC` view.
    customGrouping_fieldFilter: Optional. Filterable parameters to be added to
      the grouping clause. Available fields could be fetched by calling
      insight list and get APIs in `BASIC` view. `=` is the only comparison
      operator supported. `AND` is the only logical operator supported. Usage:
      field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only
      `AND` conditions are allowed. NOTE: Use the `filter_alias` from
      `Insight.Metadata.Field` message for the filtering the corresponding
      fields in this filter field. (These expressions are based on the filter
      language described at https://google.aip.dev/160).
    customGrouping_groupFields: Required. Fields to be used for grouping.
      NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for
      declaring the fields to be grouped-by here.
    endTime: Required. Ending time for the duration for which insight is to be
      pulled.
    fieldFilter: Optional. Other filterable/configurable parameters as
      applicable to the selected insight. Available fields could be fetched by
      calling insight list and get APIs in `BASIC` view. `=` is the only
      comparison operator supported. `AND` is the only logical operator
      supported. Usage: field_filter="fieldName1=fieldVal1 AND
      fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE:
      Use the `filter_alias` from `Insight.Metadata.Field` message for the
      filtering the corresponding fields in this filter field. (These
      expressions are based on the filter language described at
      https://google.aip.dev/160).
    group: Optional. Group id of the available groupings for the insight.
      Available groupings could be fetched by calling insight list and get
      APIs in `BASIC` view.
    insight: Required. The resource name of the insight using the form: `organ
      izations/{organization_id}/locations/{location_id}/insights/{insight_id}
      ` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. Used to fetch the page represented by the token.
      Fetches the first page when not set.
    startTime: Required. Starting time for the duration for which insight is
      to be pulled.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Required. Aggregation type. Available aggregation could be fetched by
    calling insight list and get APIs in `BASIC` view.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  customGrouping_fieldFilter = _messages.StringField(2)
  customGrouping_groupFields = _messages.StringField(3, repeated=True)
  endTime = _messages.StringField(4)
  fieldFilter = _messages.StringField(5)
  group = _messages.StringField(6)
  insight = _messages.StringField(7, required=True)
  pageSize = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(9)
  startTime = _messages.StringField(10)


class BeyondcorpOrganizationsLocationsInsightsGetRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsInsightsGetRequest object.

  Enums:
    ViewValueValuesEnum: Required. Metadata only or full data view.

  Fields:
    name: Required. The resource name of the insight using the form: `organiza
      tions/{organization_id}/locations/{location_id}/insights/{insight_id}`
      `projects/{project_id}/locations/{location_id}/insights/{insight_id}`
    view: Required. Metadata only or full data view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Required. Metadata only or full data view.

    Values:
      INSIGHT_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Include basic metadata about the insight, but not the insight
        data. This is the default value (for both ListInsights and
        GetInsight).
      FULL: Include everything.
    """
    INSIGHT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BeyondcorpOrganizationsLocationsInsightsListRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsInsightsListRequest object.

  Enums:
    AggregationValueValuesEnum: Optional. Aggregation type. The default is
      'DAILY'.
    ViewValueValuesEnum: Required. List only metadata or full data.

  Fields:
    aggregation: Optional. Aggregation type. The default is 'DAILY'.
    endTime: Optional. Ending time for the duration for which insights are to
      be pulled. The default is the current time.
    filter: Optional. Filter expression to restrict the insights returned.
      Supported filter fields: * `type` * `category` * `subCategory` Examples:
      * "category = application AND type = count" * "category = application
      AND subCategory = iap" * "type = status" Allowed values: * type: [count,
      latency, status, list] * category: [application, device, request,
      security] * subCategory: [iap, caa, webprotect] NOTE: Only equality
      based comparison is allowed. Only `AND` conjunction is allowed. NOTE:
      The 'AND' in the filter field needs to be in capital letters only. NOTE:
      Just filtering on `subCategory` is not allowed. It should be passed in
      with the parent `category` too. (These expressions are based on the
      filter language described at https://google.aip.dev/160).
    orderBy: Optional. Hint for how to order the results. This is currently
      ignored.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      NOTE: Default page size is 50.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The resource name of InsightMetadata using the form:
      `organizations/{organization_id}/locations/{location}`
      `projects/{project_id}/locations/{location_id}`
    startTime: Optional. Starting time for the duration for which insights are
      to be pulled. The default is 7 days before the current time.
    view: Required. List only metadata or full data.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Optional. Aggregation type. The default is 'DAILY'.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  class ViewValueValuesEnum(_messages.Enum):
    r"""Required. List only metadata or full data.

    Values:
      INSIGHT_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Include basic metadata about the insight, but not the insight
        data. This is the default value (for both ListInsights and
        GetInsight).
      FULL: Include everything.
    """
    INSIGHT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  endTime = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  startTime = _messages.StringField(8)
  view = _messages.EnumField('ViewValueValuesEnum', 9)


class BeyondcorpOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BeyondcorpOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BeyondcorpOrganizationsLocationsSubscriptionsCancelRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsCancelRequest object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BeyondcorpOrganizationsLocationsSubscriptionsCreateRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsCreateRequest object.

  Fields:
    googleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription: A
      GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription
      resource to be passed as the request body.
    parent: Required. The resource name of the subscription location using the
      form: `organizations/{organization_id}/locations/{location}`
  """

  googleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription', 1)
  parent = _messages.StringField(2, required=True)


class BeyondcorpOrganizationsLocationsSubscriptionsGetRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsGetRequest object.

  Fields:
    name: Required. The resource name of Subscription using the form: `organiz
      ations/{organization_id}/locations/{location}/subscriptions/{subscriptio
      n_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpOrganizationsLocationsSubscriptionsListRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListSubscriptionsRequest, if any.
    parent: Required. The resource name of Subscription using the form:
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BeyondcorpOrganizationsLocationsSubscriptionsPatchRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsPatchRequest object.

  Fields:
    googleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription: A
      GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription
      resource to be passed as the request body.
    name: Identifier. Unique resource name of the Subscription. The name is
      ignored when creating a subscription.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Subscription resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. Mutable
      fields: seat_count.
  """

  googleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BeyondcorpOrganizationsLocationsSubscriptionsRestartRequest(_messages.Message):
  r"""A BeyondcorpOrganizationsLocationsSubscriptionsRestartRequest object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BeyondcorpProjectsLocationsAppConnectionsCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsCreateRequest object.

  Fields:
    appConnectionId: Optional. User-settable AppConnection resource ID. * Must
      start with a letter. * Must contain between 4-63 characters from
      `/a-z-/`. * Must end with a number or a letter.
    googleCloudBeyondcorpAppconnectionsV1alphaAppConnection: A
      GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection resource to be
      passed as the request body.
    parent: Required. The resource project name of the AppConnection location
      using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  appConnectionId = _messages.StringField(1)
  googleCloudBeyondcorpAppconnectionsV1alphaAppConnection = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsAppConnectionsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsDeleteRequest object.

  Fields:
    name: Required. BeyondCorp Connector name using the form: `projects/{proje
      ct_id}/locations/{location_id}/appConnections/{app_connection_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsAppConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppConnectionsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsGetRequest object.

  Fields:
    name: Required. BeyondCorp AppConnection name using the form: `projects/{p
      roject_id}/locations/{location_id}/appConnections/{app_connection_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsAppConnectionsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListAppConnectionsRequest, if any.
    parent: Required. The resource name of the AppConnection location using
      the form: `projects/{project_id}/locations/{location_id}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsAppConnectionsPatchRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsPatchRequest object.

  Fields:
    allowMissing: Optional. If set as true, will create the resource if it is
      not found.
    googleCloudBeyondcorpAppconnectionsV1alphaAppConnection: A
      GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection resource to be
      passed as the request body.
    name: Required. Unique resource name of the AppConnection. The name is
      ignored when creating a AppConnection.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from [BeyondCorp.AppConnection]: * `labels` *
      `display_name` * `application_endpoint` * `connectors`
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  allowMissing = _messages.BooleanField(1)
  googleCloudBeyondcorpAppconnectionsV1alphaAppConnection = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class BeyondcorpProjectsLocationsAppConnectionsResolveRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsResolveRequest object.

  Fields:
    appConnectorId: Required. BeyondCorp Connector name of the connector
      associated with those AppConnections using the form: `projects/{project_
      id}/locations/{location_id}/appConnectors/{app_connector_id}`
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ResolveAppConnectionsResponse, if any.
    parent: Required. The resource name of the AppConnection location using
      the form: `projects/{project_id}/locations/{location_id}`
  """

  appConnectorId = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BeyondcorpProjectsLocationsAppConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectionsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppConnectorsCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsCreateRequest object.

  Fields:
    appConnectorId: Optional. User-settable AppConnector resource ID. * Must
      start with a letter. * Must contain between 4-63 characters from
      `/a-z-/`. * Must end with a number or a letter.
    googleCloudBeyondcorpAppconnectorsV1alphaAppConnector: A
      GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector resource to be
      passed as the request body.
    parent: Required. The resource project name of the AppConnector location
      using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  appConnectorId = _messages.StringField(1)
  googleCloudBeyondcorpAppconnectorsV1alphaAppConnector = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsAppConnectorsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsDeleteRequest object.

  Fields:
    name: Required. BeyondCorp AppConnector name using the form: `projects/{pr
      oject_id}/locations/{location_id}/appConnectors/{app_connector_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsAppConnectorsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppConnectorsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsGetRequest object.

  Fields:
    name: Required. BeyondCorp AppConnector name using the form: `projects/{pr
      oject_id}/locations/{location_id}/appConnectors/{app_connector_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsAppConnectorsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListAppConnectorsRequest, if any.
    parent: Required. The resource name of the AppConnector location using the
      form: `projects/{project_id}/locations/{location_id}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsAppConnectorsPatchRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsPatchRequest object.

  Fields:
    googleCloudBeyondcorpAppconnectorsV1alphaAppConnector: A
      GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector resource to be
      passed as the request body.
    name: Required. Unique resource name of the AppConnector. The name is
      ignored when creating a AppConnector.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from [BeyondCorp.AppConnector]: * `labels` *
      `display_name`
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  googleCloudBeyondcorpAppconnectorsV1alphaAppConnector = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsAppConnectorsReportStatusRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsReportStatusRequest object.

  Fields:
    appConnector: Required. BeyondCorp Connector name using the form:
      `projects/{project_id}/locations/{location_id}/connectors/{connector}`
    googleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest: A
      GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest resource to
      be passed as the request body.
  """

  appConnector = _messages.StringField(1, required=True)
  googleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest', 2)


class BeyondcorpProjectsLocationsAppConnectorsResolveInstanceConfigRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsResolveInstanceConfigRequest
  object.

  Fields:
    appConnector: Required. BeyondCorp AppConnector name using the form: `proj
      ects/{project_id}/locations/{location_id}/appConnectors/{app_connector}`
  """

  appConnector = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsAppConnectorsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppConnectorsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppConnectorsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppGatewaysCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysCreateRequest object.

  Fields:
    appGateway: A AppGateway resource to be passed as the request body.
    appGatewayId: Optional. User-settable AppGateway resource ID. * Must start
      with a letter. * Must contain between 4-63 characters from `/a-z-/`. *
      Must end with a number or a letter.
    parent: Required. The resource project name of the AppGateway location
      using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  appGateway = _messages.MessageField('AppGateway', 1)
  appGatewayId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsAppGatewaysDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysDeleteRequest object.

  Fields:
    name: Required. BeyondCorp AppGateway name using the form: `projects/{proj
      ect_id}/locations/{location_id}/appGateways/{app_gateway_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsAppGatewaysGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppGatewaysGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysGetRequest object.

  Fields:
    name: Required. BeyondCorp AppGateway name using the form: `projects/{proj
      ect_id}/locations/{location_id}/appGateways/{app_gateway_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsAppGatewaysListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListAppGatewaysRequest, if any.
    parent: Required. The resource name of the AppGateway location using the
      form: `projects/{project_id}/locations/{location_id}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsAppGatewaysSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsAppGatewaysTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsAppGatewaysTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationDomainsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationDomainsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationDomainsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationDomainsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationDomainsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationDomainsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsApplicationsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsApplicationsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsClientConnectorServicesGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsClientConnectorServicesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsClientConnectorServicesSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsClientConnectorServicesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsClientConnectorServicesTestIamPermissionsRequest(_messages.Message):
  r"""A
  BeyondcorpProjectsLocationsClientConnectorServicesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsClientGatewaysGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsClientGatewaysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsClientGatewaysTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsClientGatewaysTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsConnectionsCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    connectionId: Optional. User-settable connection resource ID. * Must start
      with a letter. * Must contain between 4-63 characters from `/a-z-/`. *
      Must end with a number or a letter.
    parent: Required. The resource project name of the connection location
      using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  connection = _messages.MessageField('Connection', 1)
  connectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsConnectionsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsDeleteRequest object.

  Fields:
    name: Required. BeyondCorp Connector name using the form: `projects/{proje
      ct_id}/locations/{location_id}/connections/{connection_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsConnectionsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsGetRequest object.

  Fields:
    name: Required. BeyondCorp Connection name using the form: `projects/{proj
      ect_id}/locations/{location_id}/connections/{connection_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsConnectionsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListConnectionsRequest, if any.
    parent: Required. The resource name of the connection location using the
      form: `projects/{project_id}/locations/{location_id}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsConnectionsPatchRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsPatchRequest object.

  Fields:
    allowMissing: Optional. If set as true, will create the resource if it is
      not found.
    connection: A Connection resource to be passed as the request body.
    name: Required. Unique resource name of the connection. The name is
      ignored when creating a connection.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from [BeyondCorp.Connection]: * `labels` *
      `display_name` * `application_endpoint` * `connectors`
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  allowMissing = _messages.BooleanField(1)
  connection = _messages.MessageField('Connection', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class BeyondcorpProjectsLocationsConnectionsResolveRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsResolveRequest object.

  Fields:
    connectorId: Required. BeyondCorp Connector name of the connector
      associated with those connections using the form: `projects/{project_id}
      /locations/{location_id}/connectors/{connector_id}`
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ResolveConnectionsResponse, if any.
    parent: Required. The resource name of the connection location using the
      form: `projects/{project_id}/locations/{location_id}`
  """

  connectorId = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BeyondcorpProjectsLocationsConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectionsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsConnectorsCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsCreateRequest object.

  Fields:
    connector: A Connector resource to be passed as the request body.
    connectorId: Optional. User-settable connector resource ID. * Must start
      with a letter. * Must contain between 4-63 characters from `/a-z-/`. *
      Must end with a number or a letter.
    parent: Required. The resource project name of the connector location
      using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  connector = _messages.MessageField('Connector', 1)
  connectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsConnectorsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsDeleteRequest object.

  Fields:
    name: Required. BeyondCorp Connector name using the form:
      `projects/{project_id}/locations/{location_id}/connectors/{connector_id}
      `
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsConnectorsGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsConnectorsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsGetRequest object.

  Fields:
    name: Required. BeyondCorp Connector name using the form:
      `projects/{project_id}/locations/{location_id}/connectors/{connector_id}
      `
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsConnectorsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListConnectorsRequest, if any.
    parent: Required. The resource name of the connector location using the
      form: `projects/{project_id}/locations/{location_id}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsConnectorsPatchRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsPatchRequest object.

  Fields:
    connector: A Connector resource to be passed as the request body.
    name: Required. Unique resource name of the connector. The name is ignored
      when creating a connector.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from [BeyondCorp.Connector]: * `labels` *
      `display_name`
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  connector = _messages.MessageField('Connector', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BeyondcorpProjectsLocationsConnectorsReportStatusRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsReportStatusRequest object.

  Fields:
    connector: Required. BeyondCorp Connector name using the form:
      `projects/{project_id}/locations/{location_id}/connectors/{connector}`
    reportStatusRequest: A ReportStatusRequest resource to be passed as the
      request body.
  """

  connector = _messages.StringField(1, required=True)
  reportStatusRequest = _messages.MessageField('ReportStatusRequest', 2)


class BeyondcorpProjectsLocationsConnectorsResolveInstanceConfigRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsResolveInstanceConfigRequest
  object.

  Fields:
    connector: Required. BeyondCorp Connector name using the form:
      `projects/{project_id}/locations/{location_id}/connectors/{connector}`
  """

  connector = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsConnectorsSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsConnectorsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsCreateRequest(_messages.Message):
  r"""A
  BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsCreateRequest
  object.

  Fields:
    applicationId: Optional. User-settable Application resource ID. * Must
      start with a letter. * Must contain between 4-63 characters from
      `/a-z-/`. * Must end with a number or letter.
    googleCloudBeyondcorpSecuritygatewaysV1alphaApplication: A
      GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication resource to be
      passed as the request body.
    parent: Required. The resource name of the parent SecurityGateway using
      the form: `projects/{project_id}/locations/global/securityGateways/{secu
      rity_gateway_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore request if it has already been completed. The server
      will guarantee that for at least 60 minutes since the first request.
  """

  applicationId = _messages.StringField(1)
  googleCloudBeyondcorpSecuritygatewaysV1alphaApplication = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsPatchRequest(_messages.Message):
  r"""A
  BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsPatchRequest
  object.

  Fields:
    googleCloudBeyondcorpSecuritygatewaysV1alphaApplication: A
      GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication resource to be
      passed as the request body.
    name: Identifier. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request timed out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Mutable fields include: display_name.
  """

  googleCloudBeyondcorpSecuritygatewaysV1alphaApplication = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsGlobalSecurityGatewaysApplicationsTestIamPe
  rmissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest object.

  Enums:
    AggregationValueValuesEnum: Required. Aggregation type. Available
      aggregation could be fetched by calling insight list and get APIs in
      `BASIC` view.

  Fields:
    aggregation: Required. Aggregation type. Available aggregation could be
      fetched by calling insight list and get APIs in `BASIC` view.
    customGrouping_fieldFilter: Optional. Filterable parameters to be added to
      the grouping clause. Available fields could be fetched by calling
      insight list and get APIs in `BASIC` view. `=` is the only comparison
      operator supported. `AND` is the only logical operator supported. Usage:
      field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only
      `AND` conditions are allowed. NOTE: Use the `filter_alias` from
      `Insight.Metadata.Field` message for the filtering the corresponding
      fields in this filter field. (These expressions are based on the filter
      language described at https://google.aip.dev/160).
    customGrouping_groupFields: Required. Fields to be used for grouping.
      NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for
      declaring the fields to be grouped-by here.
    endTime: Required. Ending time for the duration for which insight is to be
      pulled.
    fieldFilter: Optional. Other filterable/configurable parameters as
      applicable to the selected insight. Available fields could be fetched by
      calling insight list and get APIs in `BASIC` view. `=` is the only
      comparison operator supported. `AND` is the only logical operator
      supported. Usage: field_filter="fieldName1=fieldVal1 AND
      fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE:
      Use the `filter_alias` from `Insight.Metadata.Field` message for the
      filtering the corresponding fields in this filter field. (These
      expressions are based on the filter language described at
      https://google.aip.dev/160).
    group: Optional. Group id of the available groupings for the insight.
      Available groupings could be fetched by calling insight list and get
      APIs in `BASIC` view.
    insight: Required. The resource name of the insight using the form: `organ
      izations/{organization_id}/locations/{location_id}/insights/{insight_id}
      ` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. Used to fetch the page represented by the token.
      Fetches the first page when not set.
    startTime: Required. Starting time for the duration for which insight is
      to be pulled.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Required. Aggregation type. Available aggregation could be fetched by
    calling insight list and get APIs in `BASIC` view.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  customGrouping_fieldFilter = _messages.StringField(2)
  customGrouping_groupFields = _messages.StringField(3, repeated=True)
  endTime = _messages.StringField(4)
  fieldFilter = _messages.StringField(5)
  group = _messages.StringField(6)
  insight = _messages.StringField(7, required=True)
  pageSize = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(9)
  startTime = _messages.StringField(10)


class BeyondcorpProjectsLocationsInsightsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsInsightsGetRequest object.

  Enums:
    ViewValueValuesEnum: Required. Metadata only or full data view.

  Fields:
    name: Required. The resource name of the insight using the form: `organiza
      tions/{organization_id}/locations/{location_id}/insights/{insight_id}`
      `projects/{project_id}/locations/{location_id}/insights/{insight_id}`
    view: Required. Metadata only or full data view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Required. Metadata only or full data view.

    Values:
      INSIGHT_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Include basic metadata about the insight, but not the insight
        data. This is the default value (for both ListInsights and
        GetInsight).
      FULL: Include everything.
    """
    INSIGHT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BeyondcorpProjectsLocationsInsightsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsInsightsListRequest object.

  Enums:
    AggregationValueValuesEnum: Optional. Aggregation type. The default is
      'DAILY'.
    ViewValueValuesEnum: Required. List only metadata or full data.

  Fields:
    aggregation: Optional. Aggregation type. The default is 'DAILY'.
    endTime: Optional. Ending time for the duration for which insights are to
      be pulled. The default is the current time.
    filter: Optional. Filter expression to restrict the insights returned.
      Supported filter fields: * `type` * `category` * `subCategory` Examples:
      * "category = application AND type = count" * "category = application
      AND subCategory = iap" * "type = status" Allowed values: * type: [count,
      latency, status, list] * category: [application, device, request,
      security] * subCategory: [iap, caa, webprotect] NOTE: Only equality
      based comparison is allowed. Only `AND` conjunction is allowed. NOTE:
      The 'AND' in the filter field needs to be in capital letters only. NOTE:
      Just filtering on `subCategory` is not allowed. It should be passed in
      with the parent `category` too. (These expressions are based on the
      filter language described at https://google.aip.dev/160).
    orderBy: Optional. Hint for how to order the results. This is currently
      ignored.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      NOTE: Default page size is 50.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The resource name of InsightMetadata using the form:
      `organizations/{organization_id}/locations/{location}`
      `projects/{project_id}/locations/{location_id}`
    startTime: Optional. Starting time for the duration for which insights are
      to be pulled. The default is 7 days before the current time.
    view: Required. List only metadata or full data.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Optional. Aggregation type. The default is 'DAILY'.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  class ViewValueValuesEnum(_messages.Enum):
    r"""Required. List only metadata or full data.

    Values:
      INSIGHT_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      BASIC: Include basic metadata about the insight, but not the insight
        data. This is the default value (for both ListInsights and
        GetInsight).
      FULL: Include everything.
    """
    INSIGHT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  endTime = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  startTime = _messages.StringField(8)
  view = _messages.EnumField('ViewValueValuesEnum', 9)


class BeyondcorpProjectsLocationsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class BeyondcorpProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BeyondcorpProjectsLocationsSecurityGatewaysApplicationsDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysApplicationsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsSecurityGatewaysApplicationsGetIamPolicyRequest(_messages.Message):
  r"""A
  BeyondcorpProjectsLocationsSecurityGatewaysApplicationsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysApplicationsGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysApplicationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Application using the form: `proj
      ects/{project_id}/locations/global/securityGateway/{security_gateway_id}
      /applications/{application_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysApplicationsListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysApplicationsListRequest
  object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation. All
      fields in the Application message are supported. For example, the
      following query will return the Application with displayName "test-
      application" For more information, please refer to
      https://google.aip.dev/160.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListApplicationsRequest, if any.
    parent: Required. The parent location to which the resources belong. `proj
      ects/{project_id}/locations/global/securityGateways/{security_gateway_id
      }`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysApplicationsSetIamPolicyRequest(_messages.Message):
  r"""A
  BeyondcorpProjectsLocationsSecurityGatewaysApplicationsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysCreateRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysCreateRequest object.

  Fields:
    googleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway: A
      GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway resource to
      be passed as the request body.
    parent: Required. The resource project name of the SecurityGateway
      location using the form: `projects/{project_id}/locations/{location_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore request if it has already been completed. The server
      will guarantee that for at least 60 minutes since the first request.
    securityGatewayId: Optional. User-settable SecurityGateway resource ID. *
      Must start with a letter. * Must contain between 4-63 characters from
      `/a-z-/`. * Must end with a number or letter.
  """

  googleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  securityGatewayId = _messages.StringField(4)


class BeyondcorpProjectsLocationsSecurityGatewaysDeleteRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysDeleteRequest object.

  Fields:
    name: Required. BeyondCorp SecurityGateway name using the form: `projects/
      {project_id}/locations/{location_id}/securityGateways/{security_gateway_
      id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class BeyondcorpProjectsLocationsSecurityGatewaysGetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysGetRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysGetRequest object.

  Fields:
    name: Required. The resource name of the PartnerTenant using the form: `pr
      ojects/{project_id}/locations/{location_id}/securityGateway/{security_ga
      teway_id}`
  """

  name = _messages.StringField(1, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysListRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysListRequest object.

  Fields:
    filter: Optional. A filter specifying constraints of a list operation. All
      fields in the SecurityGateway message are supported. For example, the
      following query will return the SecurityGateway with displayName "test-
      security-gateway" For more information, please refer to
      https://google.aip.dev/160.
    orderBy: Optional. Specifies the ordering of results. See [Sorting
      order](https://cloud.google.com/apis/design/design_patterns#sorting_orde
      r) for more information.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 50 will be used by the service. Regardless
      of the page_size value, the response may include a partial list and a
      caller should only rely on response's next_page_token to determine if
      there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      ListSecurityGatewayRequest, if any.
    parent: Required. The parent location to which the resources belong.
      `projects/{project_id}/locations/{location_id}/`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysPatchRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysPatchRequest object.

  Fields:
    googleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway: A
      GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway resource to
      be passed as the request body.
    name: Identifier. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request timed out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Mutable fields include: display_name, hubs.
  """

  googleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BeyondcorpProjectsLocationsSecurityGatewaysSetIamPolicyRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BeyondcorpProjectsLocationsSecurityGatewaysTestIamPermissionsRequest(_messages.Message):
  r"""A BeyondcorpProjectsLocationsSecurityGatewaysTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudPubSubNotificationConfig(_messages.Message):
  r"""The configuration for Pub/Sub messaging for the connector.

  Fields:
    pubsubSubscription: The Pub/Sub subscription the connector uses to receive
      notifications.
  """

  pubsubSubscription = _messages.StringField(1)


class CloudSecurityZerotrustApplinkAppConnectorProtoConnectionConfig(_messages.Message):
  r"""ConnectionConfig represents a Connection Configuration object.

  Fields:
    applicationEndpoint: application_endpoint is the endpoint of the
      application the form of host:port. For example, "localhost:80".
    applicationName: application_name represents the given name of the
      application the connection is connecting with.
    gateway: gateway lists all instances running a gateway in GCP. They all
      connect to a connector on the host.
    name: name is the unique ID for each connection. TODO(b/190732451) returns
      connection name from user-specified name in config. Now, name =
      ${application_name}:${application_endpoint}
    project: project represents the consumer project the connection belongs
      to.
    tunnelsPerGateway: tunnels_per_gateway reflects the number of tunnels
      between a connector and a gateway.
    userPort: user_port specifies the reserved port on gateways for user
      connections.
  """

  applicationEndpoint = _messages.StringField(1)
  applicationName = _messages.StringField(2)
  gateway = _messages.MessageField('CloudSecurityZerotrustApplinkAppConnectorProtoGateway', 3, repeated=True)
  name = _messages.StringField(4)
  project = _messages.StringField(5)
  tunnelsPerGateway = _messages.IntegerField(6, variant=_messages.Variant.UINT32)
  userPort = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class CloudSecurityZerotrustApplinkAppConnectorProtoConnectorDetails(_messages.Message):
  r"""ConnectorDetails reflects the details of a connector."""


class CloudSecurityZerotrustApplinkAppConnectorProtoGateway(_messages.Message):
  r"""Gateway represents a GCE VM Instance endpoint for use by IAP TCP.

  Fields:
    interface: interface specifies the network interface of the gateway to
      connect to.
    name: name is the name of an instance running a gateway. It is the unique
      ID for a gateway. All gateways under the same connection have the same
      prefix. It is derived from the gateway URL. For example,
      name=${instance} assuming a gateway URL. https://www.googleapis.com/comp
      ute/${version}/projects/${project}/zones/${zone}/instances/${instance}
    port: port specifies the port of the gateway for tunnel connections from
      the connectors.
    project: project is the tenant project the gateway belongs to. Different
      from the project in the connection, it is a BeyondCorpAPI internally
      created project to manage all the gateways. It is sharing the same
      network with the consumer project user owned. It is derived from the
      gateway URL. For example, project=${project} assuming a gateway URL. htt
      ps://www.googleapis.com/compute/${version}/projects/${project}/zones/${z
      one}/instances/${instance}
    selfLink: self_link is the gateway URL in the form https://www.googleapis.
      com/compute/${version}/projects/${project}/zones/${zone}/instances/${ins
      tance}
    zone: zone represents the zone the instance belongs. It is derived from
      the gateway URL. For example, zone=${zone} assuming a gateway URL. https
      ://www.googleapis.com/compute/${version}/projects/${project}/zones/${zon
      e}/instances/${instance}
  """

  interface = _messages.StringField(1)
  name = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  project = _messages.StringField(4)
  selfLink = _messages.StringField(5)
  zone = _messages.StringField(6)


class CloudSecurityZerotrustApplinkLogagentProtoLogAgentDetails(_messages.Message):
  r"""LogAgentDetails reflects the details of a log agent."""


class Connection(_messages.Message):
  r"""A BeyondCorp Connection resource represents a BeyondCorp protected
  connection to a remote application. It creates all the necessary GCP
  components needed for creating a BeyondCorp protected connection. Multiple
  connectors can be authorised for a single Connection.

  Enums:
    StateValueValuesEnum: Output only. The current state of the connection.
    TypeValueValuesEnum: Required. The type of network connectivity used by
      the connection.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    applicationEndpoint: Required. Address of the remote application endpoint
      for the BeyondCorp Connection.
    connectors: Optional. List of
      [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to
      be associated with this Connection.
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the connection.
      Cannot exceed 64 characters.
    gateway: Optional. Gateway used by the connection.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Required. Unique resource name of the connection. The name is
      ignored when creating a connection.
    state: Output only. The current state of the connection.
    type: Required. The type of network connectivity used by the connection.
    uid: Output only. A unique identifier for the instance generated by the
      system.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the connection.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: Connection is being created.
      CREATED: Connection has been created.
      UPDATING: Connection's configuration is being updated.
      DELETING: Connection is being deleted.
      DOWN: Connection is down and may be restored in the future. This happens
        when CCFE sends ProjectState = OFF.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    UPDATING = 3
    DELETING = 4
    DOWN = 5

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of network connectivity used by the connection.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      TCP_PROXY: TCP Proxy based BeyondCorp Connection. API will default to
        this if unset.
    """
    TYPE_UNSPECIFIED = 0
    TCP_PROXY = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  applicationEndpoint = _messages.MessageField('ApplicationEndpoint', 1)
  connectors = _messages.StringField(2, repeated=True)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  gateway = _messages.MessageField('Gateway', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class ConnectionDetails(_messages.Message):
  r"""Details of the Connection.

  Fields:
    connection: A BeyondCorp Connection in the project.
    recentMigVms: If type=GCP_REGIONAL_MIG, contains most recent VM instances,
      like "https://www.googleapis.com/compute/v1/projects/{project_id}/zones/
      {zone_id}/instances/{instance_id}".
  """

  connection = _messages.MessageField('Connection', 1)
  recentMigVms = _messages.StringField(2, repeated=True)


class ConnectionOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Connector(_messages.Message):
  r"""A BeyondCorp connector resource that represents an application facing
  component deployed proximal to and with direct access to the application
  instances. It is used to establish connectivity between the remote
  enterprise environment and GCP. It initiates connections to the applications
  and can proxy the data from users over the connection.

  Enums:
    StateValueValuesEnum: Output only. The current state of the connector.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the connector.
      Cannot exceed 64 characters.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Required. Unique resource name of the connector. The name is ignored
      when creating a connector.
    principalInfo: Required. Principal information about the Identity of the
      connector.
    resourceInfo: Optional. Resource info of the connector.
    state: Output only. The current state of the connector.
    uid: Output only. A unique identifier for the instance generated by the
      system.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the connector.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: Connector is being created.
      CREATED: Connector has been created.
      UPDATING: Connector's configuration is being updated.
      DELETING: Connector is being deleted.
      DOWN: Connector is down and may be restored in the future. This happens
        when CCFE sends ProjectState = OFF.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    UPDATING = 3
    DELETING = 4
    DOWN = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  principalInfo = _messages.MessageField('PrincipalInfo', 5)
  resourceInfo = _messages.MessageField('ResourceInfo', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ConnectorInstanceConfig(_messages.Message):
  r"""ConnectorInstanceConfig defines the instance config of a connector.

  Messages:
    InstanceConfigValue: The SLM instance agent configuration.

  Fields:
    imageConfig: ImageConfig defines the GCR images to run for the remote
      agent's control plane.
    instanceConfig: The SLM instance agent configuration.
    notificationConfig: NotificationConfig defines the notification mechanism
      that the remote instance should subscribe to in order to receive
      notification.
    sequenceNumber: Required. A monotonically increasing number generated and
      maintained by the API provider. Every time a config changes in the
      backend, the sequenceNumber should be bumped up to reflect the change.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InstanceConfigValue(_messages.Message):
    r"""The SLM instance agent configuration.

    Messages:
      AdditionalProperty: An additional property for a InstanceConfigValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InstanceConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  imageConfig = _messages.MessageField('ImageConfig', 1)
  instanceConfig = _messages.MessageField('InstanceConfigValue', 2)
  notificationConfig = _messages.MessageField('NotificationConfig', 3)
  sequenceNumber = _messages.IntegerField(4)


class ConnectorOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ContainerHealthDetails(_messages.Message):
  r"""ContainerHealthDetails reflects the health details of a container.

  Messages:
    ExtendedStatusValue: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.

  Fields:
    currentConfigVersion: The version of the current config.
    errorMsg: The latest error message.
    expectedConfigVersion: The version of the expected config.
    extendedStatus: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtendedStatusValue(_messages.Message):
    r"""The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.

    Messages:
      AdditionalProperty: An additional property for a ExtendedStatusValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExtendedStatusValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtendedStatusValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  currentConfigVersion = _messages.StringField(1)
  errorMsg = _messages.StringField(2)
  expectedConfigVersion = _messages.StringField(3)
  extendedStatus = _messages.MessageField('ExtendedStatusValue', 4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Gateway(_messages.Message):
  r"""Gateway represents a user facing component that serves as an entrance to
  enable connectivity.

  Enums:
    TypeValueValuesEnum: Required. The type of hosting used by the gateway.

  Fields:
    type: Required. The type of hosting used by the gateway.
    uri: Output only. Server-defined URI for this resource.
    userPort: Output only. User port reserved on the gateways for this
      connection, if not specified or zero, the default port is 19443.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of hosting used by the gateway.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      GCP_REGIONAL_MIG: Gateway hosted in a GCP regional managed instance
        group.
    """
    TYPE_UNSPECIFIED = 0
    GCP_REGIONAL_MIG = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  uri = _messages.StringField(2)
  userPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudBeyondcorpAppconnectionsV1AppConnectionOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection(_messages.Message):
  r"""A BeyondCorp AppConnection resource represents a BeyondCorp protected
  AppConnection to a remote application. It creates all the necessary GCP
  components needed for creating a BeyondCorp protected AppConnection.
  Multiple connectors can be authorised for a single AppConnection.

  Enums:
    StateValueValuesEnum: Output only. The current state of the AppConnection.
    TypeValueValuesEnum: Required. The type of network connectivity used by
      the AppConnection.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    applicationEndpoint: Required. Address of the remote application endpoint
      for the BeyondCorp AppConnection.
    connectors: Optional. List of
      [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to
      be associated with this AppConnection.
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the
      AppConnection. Cannot exceed 64 characters.
    gateway: Optional. Gateway used by the AppConnection.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Required. Unique resource name of the AppConnection. The name is
      ignored when creating a AppConnection.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current state of the AppConnection.
    type: Required. The type of network connectivity used by the
      AppConnection.
    uid: Output only. A unique identifier for the instance generated by the
      system.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the AppConnection.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: AppConnection is being created.
      CREATED: AppConnection has been created.
      UPDATING: AppConnection's configuration is being updated.
      DELETING: AppConnection is being deleted.
      DOWN: AppConnection is down and may be restored in the future. This
        happens when CCFE sends ProjectState = OFF.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    UPDATING = 3
    DELETING = 4
    DOWN = 5

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of network connectivity used by the AppConnection.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      TCP_PROXY: TCP Proxy based BeyondCorp AppConnection. API will default to
        this if unset.
    """
    TYPE_UNSPECIFIED = 0
    TCP_PROXY = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  applicationEndpoint = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint', 1)
  connectors = _messages.StringField(2, repeated=True)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  gateway = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  satisfiesPzi = _messages.BooleanField(8)
  satisfiesPzs = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint(_messages.Message):
  r"""ApplicationEndpoint represents a remote application endpoint.

  Fields:
    host: Required. Hostname or IP address of the remote application endpoint.
    port: Required. Port of the remote application endpoint.
  """

  host = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway(_messages.Message):
  r"""Gateway represents a user facing component that serves as an entrance to
  enable connectivity.

  Enums:
    TypeValueValuesEnum: Required. The type of hosting used by the gateway.

  Fields:
    appGateway: Required. AppGateway name in following format:
      `projects/{project_id}/locations/{location_id}/appgateways/{gateway_id}`
    ingressPort: Output only. Ingress port reserved on the gateways for this
      AppConnection, if not specified or zero, the default port is 19443.
    l7psc: Output only. L7 private service connection for this resource.
    type: Required. The type of hosting used by the gateway.
    uri: Output only. Server-defined URI for this resource.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of hosting used by the gateway.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      GCP_REGIONAL_MIG: Gateway hosted in a GCP regional managed instance
        group.
    """
    TYPE_UNSPECIFIED = 0
    GCP_REGIONAL_MIG = 1

  appGateway = _messages.StringField(1)
  ingressPort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  l7psc = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  uri = _messages.StringField(5)


class GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListAppConnections.

  Fields:
    appConnections: A list of BeyondCorp AppConnections in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  appConnections = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ResolveAppConnections.

  Fields:
    appConnectionDetails: A list of BeyondCorp AppConnections with details in
      the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  appConnectionDetails = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails(_messages.Message):
  r"""Details of the AppConnection.

  Fields:
    appConnection: A BeyondCorp AppConnection in the project.
    recentMigVms: If type=GCP_REGIONAL_MIG, contains most recent VM instances,
      like `https://www.googleapis.com/compute/v1/projects/{project_id}/zones/
      {zone_id}/instances/{instance_id}`.
  """

  appConnection = _messages.MessageField('GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection', 1)
  recentMigVms = _messages.StringField(2, repeated=True)


class GoogleCloudBeyondcorpAppconnectorsV1AppConnectorOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of `1`, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpAppconnectorsV1ContainerHealthDetails(_messages.Message):
  r"""ContainerHealthDetails reflects the health details of a container.

  Messages:
    ExtendedStatusValue: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.

  Fields:
    currentConfigVersion: The version of the current config.
    errorMsg: The latest error message.
    expectedConfigVersion: The version of the expected config.
    extendedStatus: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtendedStatusValue(_messages.Message):
    r"""The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.

    Messages:
      AdditionalProperty: An additional property for a ExtendedStatusValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExtendedStatusValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtendedStatusValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  currentConfigVersion = _messages.StringField(1)
  errorMsg = _messages.StringField(2)
  expectedConfigVersion = _messages.StringField(3)
  extendedStatus = _messages.MessageField('ExtendedStatusValue', 4)


class GoogleCloudBeyondcorpAppconnectorsV1RemoteAgentDetails(_messages.Message):
  r"""RemoteAgentDetails reflects the details of a remote agent."""


class GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector(_messages.Message):
  r"""A BeyondCorp connector resource that represents an application facing
  component deployed proximal to and with direct access to the application
  instances. It is used to establish connectivity between the remote
  enterprise environment and GCP. It initiates connections to the applications
  and can proxy the data from users over the connection.

  Enums:
    StateValueValuesEnum: Output only. The current state of the AppConnector.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the
      AppConnector. Cannot exceed 64 characters.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Required. Unique resource name of the AppConnector. The name is
      ignored when creating a AppConnector.
    principalInfo: Required. Principal information about the Identity of the
      AppConnector.
    resourceInfo: Optional. Resource info of the connector.
    state: Output only. The current state of the AppConnector.
    uid: Output only. A unique identifier for the instance generated by the
      system.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the AppConnector.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: AppConnector is being created.
      CREATED: AppConnector has been created.
      UPDATING: AppConnector's configuration is being updated.
      DELETING: AppConnector is being deleted.
      DOWN: AppConnector is down and may be restored in the future. This
        happens when CCFE sends ProjectState = OFF.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    UPDATING = 3
    DELETING = 4
    DOWN = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  principalInfo = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo', 5)
  resourceInfo = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig(_messages.Message):
  r"""AppConnectorInstanceConfig defines the instance config of a
  AppConnector.

  Messages:
    InstanceConfigValue: The SLM instance agent configuration.

  Fields:
    imageConfig: ImageConfig defines the GCR images to run for the remote
      agent's control plane.
    instanceConfig: The SLM instance agent configuration.
    notificationConfig: NotificationConfig defines the notification mechanism
      that the remote instance should subscribe to in order to receive
      notification.
    sequenceNumber: Required. A monotonically increasing number generated and
      maintained by the API provider. Every time a config changes in the
      backend, the sequenceNumber should be bumped up to reflect the change.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InstanceConfigValue(_messages.Message):
    r"""The SLM instance agent configuration.

    Messages:
      AdditionalProperty: An additional property for a InstanceConfigValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InstanceConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  imageConfig = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig', 1)
  instanceConfig = _messages.MessageField('InstanceConfigValue', 2)
  notificationConfig = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig', 3)
  sequenceNumber = _messages.IntegerField(4)


class GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of `1`, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo(_messages.Message):
  r"""PrincipalInfo represents an Identity oneof.

  Fields:
    serviceAccount: A GCP service account.
  """

  serviceAccount = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount', 1)


class GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount(_messages.Message):
  r"""ServiceAccount represents a GCP service account.

  Fields:
    email: Email address of the service account.
  """

  email = _messages.StringField(1)


class GoogleCloudBeyondcorpAppconnectorsV1alphaContainerHealthDetails(_messages.Message):
  r"""ContainerHealthDetails reflects the health details of a container.

  Messages:
    ExtendedStatusValue: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.

  Fields:
    currentConfigVersion: The version of the current config.
    errorMsg: The latest error message.
    expectedConfigVersion: The version of the expected config.
    extendedStatus: The extended status. Such as ExitCode, StartedAt,
      FinishedAt, etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtendedStatusValue(_messages.Message):
    r"""The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.

    Messages:
      AdditionalProperty: An additional property for a ExtendedStatusValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExtendedStatusValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtendedStatusValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  currentConfigVersion = _messages.StringField(1)
  errorMsg = _messages.StringField(2)
  expectedConfigVersion = _messages.StringField(3)
  extendedStatus = _messages.MessageField('ExtendedStatusValue', 4)


class GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig(_messages.Message):
  r"""ImageConfig defines the control plane images to run.

  Fields:
    stableImage: The stable image that the remote agent will fallback to if
      the target image fails. Format would be a gcr image path, e.g.:
      gcr.io/PROJECT-ID/my-image:tag1
    targetImage: The initial image the remote agent will attempt to run for
      the control plane. Format would be a gcr image path, e.g.:
      gcr.io/PROJECT-ID/my-image:tag1
  """

  stableImage = _messages.StringField(1)
  targetImage = _messages.StringField(2)


class GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListAppConnectors.

  Fields:
    appConnectors: A list of BeyondCorp AppConnectors in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  appConnectors = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig(_messages.Message):
  r"""NotificationConfig defines the mechanisms to notify instance agent.

  Fields:
    pubsubNotification: Cloud Pub/Sub Configuration to receive notifications.
  """

  pubsubNotification = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig', 1)


class GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig(_messages.Message):
  r"""The configuration for Pub/Sub messaging for the AppConnector.

  Fields:
    pubsubSubscription: The Pub/Sub subscription the AppConnector uses to
      receive notifications.
  """

  pubsubSubscription = _messages.StringField(1)


class GoogleCloudBeyondcorpAppconnectorsV1alphaRemoteAgentDetails(_messages.Message):
  r"""RemoteAgentDetails reflects the details of a remote agent."""


class GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest(_messages.Message):
  r"""Request report the connector status.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    resourceInfo: Required. Resource info of the connector.
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  requestId = _messages.StringField(1)
  resourceInfo = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo', 2)
  validateOnly = _messages.BooleanField(3)


class GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse(_messages.Message):
  r"""Response message for BeyondCorp.ResolveInstanceConfig.

  Fields:
    instanceConfig: AppConnectorInstanceConfig.
  """

  instanceConfig = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig', 1)


class GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo(_messages.Message):
  r"""ResourceInfo represents the information/status of an app connector
  resource. Such as: - remote_agent - container - runtime - appgateway -
  appconnector - appconnection - tunnel - logagent

  Enums:
    StatusValueValuesEnum: Overall health status. Overall status is derived
      based on the status of each sub level resources.

  Messages:
    ResourceValue: Specific details for the resource. This is for internal use
      only.

  Fields:
    id: Required. Unique Id for the resource.
    resource: Specific details for the resource. This is for internal use
      only.
    status: Overall health status. Overall status is derived based on the
      status of each sub level resources.
    sub: List of Info for the sub level resources.
    time: The timestamp to collect the info. It is suggested to be set by the
      topmost level resource only.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Overall health status. Overall status is derived based on the status
    of each sub level resources.

    Values:
      HEALTH_STATUS_UNSPECIFIED: Health status is unknown: not initialized or
        failed to retrieve.
      HEALTHY: The resource is healthy.
      UNHEALTHY: The resource is unhealthy.
      UNRESPONSIVE: The resource is unresponsive.
      DEGRADED: Some sub-resources are UNHEALTHY.
    """
    HEALTH_STATUS_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    UNRESPONSIVE = 3
    DEGRADED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceValue(_messages.Message):
    r"""Specific details for the resource. This is for internal use only.

    Messages:
      AdditionalProperty: An additional property for a ResourceValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  resource = _messages.MessageField('ResourceValue', 2)
  status = _messages.EnumField('StatusValueValuesEnum', 3)
  sub = _messages.MessageField('GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo', 4, repeated=True)
  time = _messages.StringField(5)


class GoogleCloudBeyondcorpAppgatewaysV1AppGatewayOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of `1`, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpPartnerservicesV1alphaGroup(_messages.Message):
  r"""Message to capture group information

  Fields:
    email: The group email id
    id: Google group id
  """

  email = _messages.StringField(1)
  id = _messages.StringField(2)


class GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse(_messages.Message):
  r"""Message for response to listing PartnerTenants.

  Fields:
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    partnerTenants: The list of PartnerTenant objects.
  """

  nextPageToken = _messages.StringField(1)
  partnerTenants = _messages.MessageField('GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant', 2, repeated=True)


class GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata(_messages.Message):
  r"""Metadata associated with PartnerTenant and is provided by the Partner.

  Fields:
    internalTenantId: Optional. UUID used by the Partner to refer to the
      PartnerTenant in their internal systems.
    partnerTenantId: Optional. UUID used by the Partner to refer to the
      PartnerTenant in their internal systems.
  """

  internalTenantId = _messages.StringField(1)
  partnerTenantId = _messages.StringField(2)


class GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerServiceOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the caller has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant(_messages.Message):
  r"""Information about a BeyoncCorp Enterprise PartnerTenant.

  Fields:
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary caller-provided name for the
      PartnerTenant. Cannot exceed 64 characters.
    group: Optional. Group information for the users enabled to use the
      partnerTenant. If the group information is not provided then the
      partnerTenant will be enabled for all users.
    name: Output only. Unique resource name of the PartnerTenant. The name is
      ignored when creating PartnerTenant.
    partnerMetadata: Optional. Metadata provided by the Partner associated
      with PartnerTenant.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  group = _messages.MessageField('GoogleCloudBeyondcorpPartnerservicesV1alphaGroup', 3)
  name = _messages.StringField(4)
  partnerMetadata = _messages.MessageField('GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata', 5)
  updateTime = _messages.StringField(6)


class GoogleCloudBeyondcorpPartnerservicesV1mainPartnerServiceOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the caller has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig(_messages.Message):
  r"""The configuration that was applied to generate the result.

  Enums:
    AggregationValueValuesEnum: Output only. Aggregation type applied.

  Fields:
    aggregation: Output only. Aggregation type applied.
    customGrouping: Output only. Customised grouping applied.
    endTime: Output only. Ending time for the duration for which insight was
      pulled.
    fieldFilter: Output only. Filters applied.
    group: Output only. Group id of the grouping applied.
    startTime: Output only. Starting time for the duration for which insight
      was pulled.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Output only. Aggregation type applied.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  customGrouping = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping', 2)
  endTime = _messages.StringField(3)
  fieldFilter = _messages.StringField(4)
  group = _messages.StringField(5)
  startTime = _messages.StringField(6)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse(_messages.Message):
  r"""The response for the configured insight.

  Fields:
    appliedConfig: Output only. Applied insight config to generate the result
      data rows.
    nextPageToken: Output only. Next page token to be fetched. Set to empty or
      NULL if there are no more pages available.
    rows: Output only. Result rows returned containing the required value(s)
      for configured insight.
  """

  appliedConfig = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig', 1)
  nextPageToken = _messages.StringField(2)
  rows = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow', 3, repeated=True)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping(_messages.Message):
  r"""Customised grouping option that allows setting the group_by fields and
  also the filters togather for a configured insight request.

  Fields:
    fieldFilter: Optional. Filterable parameters to be added to the grouping
      clause. Available fields could be fetched by calling insight list and
      get APIs in `BASIC` view. `=` is the only comparison operator supported.
      `AND` is the only logical operator supported. Usage:
      field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only
      `AND` conditions are allowed. NOTE: Use the `filter_alias` from
      `Insight.Metadata.Field` message for the filtering the corresponding
      fields in this filter field. (These expressions are based on the filter
      language described at https://google.aip.dev/160).
    groupFields: Required. Fields to be used for grouping. NOTE: Use the
      `filter_alias` from `Insight.Metadata.Field` message for declaring the
      fields to be grouped-by here.
  """

  fieldFilter = _messages.StringField(1)
  groupFields = _messages.StringField(2, repeated=True)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight(_messages.Message):
  r"""The Insight object with configuration that was returned and actual list
  of records.

  Fields:
    appliedConfig: Output only. Applied insight config to generate the result
      data rows.
    metadata: Output only. Metadata for the Insight.
    name: Output only. The insight resource name. e.g. `organizations/{organiz
      ation_id}/locations/{location_id}/insights/{insight_id}` OR
      `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.
    rows: Output only. Result rows returned containing the required value(s).
  """

  appliedConfig = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig', 1)
  metadata = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata', 2)
  name = _messages.StringField(3)
  rows = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow', 4, repeated=True)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata(_messages.Message):
  r"""Insight filters, groupings and aggregations that can be applied for the
  insight. Examples: aggregations, groups, field filters.

  Enums:
    AggregationsValueListEntryValuesEnum:

  Fields:
    aggregations: Output only. List of aggregation types available for
      insight.
    category: Output only. Category of the insight.
    displayName: Output only. Common name of the insight.
    fields: Output only. List of fields available for insight.
    groups: Output only. List of groupings available for insight.
    subCategory: Output only. Sub-Category of the insight.
    type: Output only. Type of the insight. It is metadata describing whether
      the insight is a metric (e.g. count) or a report (e.g. list, status).
  """

  class AggregationsValueListEntryValuesEnum(_messages.Enum):
    r"""AggregationsValueListEntryValuesEnum enum type.

    Values:
      AGGREGATION_UNSPECIFIED: Unspecified.
      HOURLY: Insight should be aggregated at hourly level.
      DAILY: Insight should be aggregated at daily level.
      WEEKLY: Insight should be aggregated at weekly level.
      MONTHLY: Insight should be aggregated at monthly level.
      CUSTOM_DATE_RANGE: Insight should be aggregated at the custom date range
        passed in as the start and end time in the request.
    """
    AGGREGATION_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    CUSTOM_DATE_RANGE = 5

  aggregations = _messages.EnumField('AggregationsValueListEntryValuesEnum', 1, repeated=True)
  category = _messages.StringField(2)
  displayName = _messages.StringField(3)
  fields = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField', 4, repeated=True)
  groups = _messages.StringField(5, repeated=True)
  subCategory = _messages.StringField(6)
  type = _messages.StringField(7)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField(_messages.Message):
  r"""Field metadata. Commonly understandable name and description for the
  field. Multiple such fields constitute the Insight.

  Fields:
    description: Output only. Description of the field.
    displayName: Output only. Name of the field.
    filterAlias: Output only. Field name to be used in filter while requesting
      configured insight filtered on this field.
    filterable: Output only. Indicates whether the field can be used for
      filtering.
    groupable: Output only. Indicates whether the field can be used for
      grouping in custom grouping request.
    id: Output only. Field id for which this is the metadata.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  filterAlias = _messages.StringField(3)
  filterable = _messages.BooleanField(4)
  groupable = _messages.BooleanField(5)
  id = _messages.StringField(6)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse(_messages.Message):
  r"""The response for the list of insights.

  Fields:
    insights: Output only. List of all insights.
    nextPageToken: Output only. Next page token to be fetched. Set to empty or
      NULL if there are no more pages available.
  """

  insights = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow(_messages.Message):
  r"""Row of the fetch response consisting of a set of entries.

  Fields:
    fieldValues: Output only. Columns/entries/key-vals in the result.
  """

  fieldValues = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal', 1, repeated=True)


class GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal(_messages.Message):
  r"""Column or key value pair from the request as part of key to use in query
  or a single pair of the fetch response.

  Fields:
    displayName: Output only. Name of the field.
    filterAlias: Output only. Field name to be used in filter while requesting
      configured insight filtered on this field.
    id: Output only. Field id.
    value: Output only. Value of the field in string format. Acceptable values
      are strings or numbers.
  """

  displayName = _messages.StringField(1)
  filterAlias = _messages.StringField(2)
  id = _messages.StringField(3)
  value = _messages.StringField(4)


class GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse(_messages.Message):
  r"""Response message for BeyondCorp.CancelSubscription

  Fields:
    effectiveCancellationTime: Time when the cancellation will become
      effective
  """

  effectiveCancellationTime = _messages.StringField(1)


class GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListSubscriptions.

  Fields:
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    subscriptions: A list of BeyondCorp Subscriptions in the organization.
  """

  nextPageToken = _messages.StringField(1)
  subscriptions = _messages.MessageField('GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription', 2, repeated=True)


class GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse(_messages.Message):
  r"""Response message for BeyondCorp.RestartSubscription"""


class GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription(_messages.Message):
  r"""A BeyondCorp Subscription resource represents BeyondCorp Enterprise
  Subscription. BeyondCorp Enterprise Subscription enables BeyondCorp
  Enterprise permium features for an organization.

  Enums:
    SkuValueValuesEnum: Required. SKU of subscription.
    StateValueValuesEnum: Output only. The current state of the subscription.
    SubscriberTypeValueValuesEnum: Output only. Type of subscriber.
    TypeValueValuesEnum: Required. Type of subscription.

  Fields:
    autoRenewEnabled: Output only. Represents that, if subscription will renew
      or end when the term ends.
    billingAccount: Optional. Name of the billing account in the format. e.g.
      billingAccounts/123456-123456-123456 Required if Subscription is of Paid
      type.
    createTime: Output only. Create time of the subscription.
    csgCustomer: Optional. Whether the subscription is being created as part
      of the Citrix flow. If this field is set to true, the subscription
      should have both the start_time and end_time set in the request and the
      billing account used will be the Citrix master billing account
      regardless of what its set to in the request. This field can only be set
      to true in create requests.
    endTime: Optional. End time of the subscription.
    name: Identifier. Unique resource name of the Subscription. The name is
      ignored when creating a subscription.
    seatCount: Optional. Number of seats in the subscription.
    sku: Required. SKU of subscription.
    startTime: Optional. Start time of the subscription.
    state: Output only. The current state of the subscription.
    subscriberType: Output only. Type of subscriber.
    type: Required. Type of subscription.
  """

  class SkuValueValuesEnum(_messages.Enum):
    r"""Required. SKU of subscription.

    Values:
      SKU_UNSPECIFIED: Default value. This value is unused.
      BCE_STANDARD_SKU: Represents BeyondCorp Standard SKU.
    """
    SKU_UNSPECIFIED = 0
    BCE_STANDARD_SKU = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the subscription.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: Represents an active subscription.
      INACTIVE: Represents an upcomming subscription.
      COMPLETED: Represents a completed subscription.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2
    COMPLETED = 3

  class SubscriberTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of subscriber.

    Values:
      SUBSCRIBER_TYPE_UNSPECIFIED: Default value. This value is unused.
      ONLINE: Represents an online subscription.
      OFFLINE: Represents an offline subscription.
    """
    SUBSCRIBER_TYPE_UNSPECIFIED = 0
    ONLINE = 1
    OFFLINE = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of subscription.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      TRIAL: Represents a trial subscription.
      PAID: Represents a paid subscription.
      ALLOWLIST: Reresents an allowlisted subscription.
    """
    TYPE_UNSPECIFIED = 0
    TRIAL = 1
    PAID = 2
    ALLOWLIST = 3

  autoRenewEnabled = _messages.BooleanField(1)
  billingAccount = _messages.StringField(2)
  createTime = _messages.StringField(3)
  csgCustomer = _messages.BooleanField(4)
  endTime = _messages.StringField(5)
  name = _messages.StringField(6)
  seatCount = _messages.IntegerField(7)
  sku = _messages.EnumField('SkuValueValuesEnum', 8)
  startTime = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  subscriberType = _messages.EnumField('SubscriberTypeValueValuesEnum', 11)
  type = _messages.EnumField('TypeValueValuesEnum', 12)


class GoogleCloudBeyondcorpSecuritygatewaysV1SecurityGatewayOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication(_messages.Message):
  r"""A Beyondcorp Application resource information.

  Fields:
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the Application
      resource. Cannot exceed 64 characters.
    endpointMatchers: Required. Endpoint matchers associated with an
      application. A combination of hostname and ports as endpoint matcher is
      used to match the application. Match conditions for OR logic. An array
      of match conditions to allow for multiple matching criteria. The rule is
      considered a match if one the conditions are met. The conditions can be
      one of the following combination (Hostname), (Hostname & Ports)
      EXAMPLES: Hostname - ("*.abc.com"), ("xyz.abc.com") Hostname and Ports -
      ("abc.com" and "22"), ("abc.com" and "22,33") etc
    name: Identifier. Name of the resource.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  endpointMatchers = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher', 3, repeated=True)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher(_messages.Message):
  r"""EndpointMatcher contains the information of the endpoint that will match
  the application.

  Fields:
    hostname: Required. Hostname of the application.
    ports: Optional. Ports of the application.
  """

  hostname = _messages.StringField(1)
  ports = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.INT32)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub(_messages.Message):
  r"""The Hub message contains information pertaining to the regional data
  path deployments.

  Fields:
    internetGateway: Optional. Internet Gateway configuration.
  """

  internetGateway = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway', 1)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway(_messages.Message):
  r"""Represents the Internet Gateway configuration.

  Fields:
    assignedIps: Output only. List of IP addresses assigned to the Cloud NAT.
  """

  assignedIps = _messages.StringField(1, repeated=True)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse(_messages.Message):
  r"""Message for response to listing Applications.

  Fields:
    applications: A list of BeyondCorp Application in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  applications = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse(_messages.Message):
  r"""Message for response to listing SecurityGateways.

  Fields:
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    securityGateways: A list of BeyondCorp SecurityGateway in the project.
    unreachable: A list of locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  securityGateways = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway(_messages.Message):
  r"""Information about a BeyoncCorp SecurityGateway resource.

  Enums:
    StateValueValuesEnum: Output only. The operational state of the
      SecurityGateway.

  Messages:
    HubsValue: Optional. Map of Hubs that represents regional data path
      deployment with GCP region as a key.

  Fields:
    createTime: Output only. Timestamp when the resource was created.
    displayName: Optional. An arbitrary user-provided name for the
      SecurityGateway. Cannot exceed 64 characters.
    externalIps: Output only. IP addresses that will be used for establishing
      connection to the endpoints.
    hubs: Optional. Map of Hubs that represents regional data path deployment
      with GCP region as a key.
    name: Identifier. Name of the resource.
    state: Output only. The operational state of the SecurityGateway.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The operational state of the SecurityGateway.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: SecurityGateway is being created.
      UPDATING: SecurityGateway is being updated.
      DELETING: SecurityGateway is being deleted.
      RUNNING: SecurityGateway is running.
      DOWN: SecurityGateway is down and may be restored in the future. This
        happens when CCFE sends ProjectState = OFF.
      ERROR: SecurityGateway encountered an error and is in an indeterministic
        state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    UPDATING = 2
    DELETING = 3
    RUNNING = 4
    DOWN = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HubsValue(_messages.Message):
    r"""Optional. Map of Hubs that represents regional data path deployment
    with GCP region as a key.

    Messages:
      AdditionalProperty: An additional property for a HubsValue object.

    Fields:
      additionalProperties: Additional properties of type HubsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HubsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  externalIps = _messages.StringField(3, repeated=True)
  hubs = _messages.MessageField('HubsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGatewayOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ImageConfig(_messages.Message):
  r"""ImageConfig defines the control plane images to run.

  Fields:
    stableImage: The stable image that the remote agent will fallback to if
      the target image fails.
    targetImage: The initial image the remote agent will attempt to run for
      the control plane.
  """

  stableImage = _messages.StringField(1)
  targetImage = _messages.StringField(2)


class ListAppGatewaysResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListAppGateways.

  Fields:
    appGateways: A list of BeyondCorp AppGateways in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  appGateways = _messages.MessageField('AppGateway', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectionsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListConnections.

  Fields:
    connections: A list of BeyondCorp Connections in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectorsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ListConnectors.

  Fields:
    connectors: A list of BeyondCorp Connectors in the project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  connectors = _messages.MessageField('Connector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class NotificationConfig(_messages.Message):
  r"""NotificationConfig defines the mechanisms to notify instance agent.

  Fields:
    pubsubNotification: Pub/Sub topic for Connector to subscribe and receive
      notifications from `projects/{project}/topics/{pubsub_topic}`
  """

  pubsubNotification = _messages.MessageField('CloudPubSubNotificationConfig', 1)


class PrincipalInfo(_messages.Message):
  r"""PrincipalInfo represents an Identity oneof.

  Fields:
    serviceAccount: A GCP service account.
  """

  serviceAccount = _messages.MessageField('ServiceAccount', 1)


class RemoteAgentDetails(_messages.Message):
  r"""RemoteAgentDetails reflects the details of a remote agent."""


class ReportStatusRequest(_messages.Message):
  r"""Request report the connector status.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    resourceInfo: Required. Resource info of the connector.
    validateOnly: Optional. If set, validates request by executing a dry-run
      which would not alter the resource in any way.
  """

  requestId = _messages.StringField(1)
  resourceInfo = _messages.MessageField('ResourceInfo', 2)
  validateOnly = _messages.BooleanField(3)


class ResolveConnectionsResponse(_messages.Message):
  r"""Response message for BeyondCorp.ResolveConnections.

  Fields:
    connectionDetails: A list of BeyondCorp Connections with details in the
      project.
    nextPageToken: A token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: A list of locations that could not be reached.
  """

  connectionDetails = _messages.MessageField('ConnectionDetails', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ResolveInstanceConfigResponse(_messages.Message):
  r"""Response message for BeyondCorp.ResolveInstanceConfig.

  Fields:
    instanceConfig: ConnectorInstanceConfig.
  """

  instanceConfig = _messages.MessageField('ConnectorInstanceConfig', 1)


class ResourceInfo(_messages.Message):
  r"""ResourceInfo represents the information/status of the associated
  resource.

  Enums:
    StatusValueValuesEnum: Overall health status. Overall status is derived
      based on the status of each sub level resources.

  Messages:
    ResourceValue: Specific details for the resource.

  Fields:
    id: Required. Unique Id for the resource.
    resource: Specific details for the resource.
    status: Overall health status. Overall status is derived based on the
      status of each sub level resources.
    sub: List of Info for the sub level resources.
    time: The timestamp to collect the info. It is suggested to be set by the
      topmost level resource only.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Overall health status. Overall status is derived based on the status
    of each sub level resources.

    Values:
      HEALTH_STATUS_UNSPECIFIED: Health status is unknown: not initialized or
        failed to retrieve.
      HEALTHY: The resource is healthy.
      UNHEALTHY: The resource is unhealthy.
      UNRESPONSIVE: The resource is unresponsive.
      DEGRADED: Some sub-resources are UNHEALTHY.
    """
    HEALTH_STATUS_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    UNRESPONSIVE = 3
    DEGRADED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceValue(_messages.Message):
    r"""Specific details for the resource.

    Messages:
      AdditionalProperty: An additional property for a ResourceValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  resource = _messages.MessageField('ResourceValue', 2)
  status = _messages.EnumField('StatusValueValuesEnum', 3)
  sub = _messages.MessageField('ResourceInfo', 4, repeated=True)
  time = _messages.StringField(5)


class ServiceAccount(_messages.Message):
  r"""ServiceAccount represents a GCP service account.

  Fields:
    email: Email address of the service account.
  """

  email = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Tunnelv1ProtoTunnelerError(_messages.Message):
  r"""TunnelerError is an error proto for errors returned by the connection
  manager.

  Fields:
    err: Original raw error
    retryable: retryable isn't used for now, but we may want to reuse it in
      the future.
  """

  err = _messages.StringField(1)
  retryable = _messages.BooleanField(2)


class Tunnelv1ProtoTunnelerInfo(_messages.Message):
  r"""TunnelerInfo contains metadata about tunneler launched by connection
  manager.

  Fields:
    backoffRetryCount: backoff_retry_count stores the number of times the
      tunneler has been retried by tunManager for current backoff sequence.
      Gets reset to 0 if time difference between 2 consecutive retries exceeds
      backoffRetryResetTime.
    id: id is the unique id of a tunneler.
    latestErr: latest_err stores the Error for the latest tunneler failure.
      Gets reset everytime the tunneler is retried by tunManager.
    latestRetryTime: latest_retry_time stores the time when the tunneler was
      last restarted.
    totalRetryCount: total_retry_count stores the total number of times the
      tunneler has been retried by tunManager.
  """

  backoffRetryCount = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  id = _messages.StringField(2)
  latestErr = _messages.MessageField('Tunnelv1ProtoTunnelerError', 3)
  latestRetryTime = _messages.StringField(4)
  totalRetryCount = _messages.IntegerField(5, variant=_messages.Variant.UINT32)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest, 'customGrouping_fieldFilter', 'customGrouping.fieldFilter')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest, 'customGrouping_groupFields', 'customGrouping.groupFields')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsAppConnectionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsAppConnectorsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsAppGatewaysGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsApplicationDomainsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsApplicationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsClientConnectorServicesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsClientGatewaysGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsConnectionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsConnectorsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest, 'customGrouping_fieldFilter', 'customGrouping.fieldFilter')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest, 'customGrouping_groupFields', 'customGrouping.groupFields')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsSecurityGatewaysGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    BeyondcorpProjectsLocationsSecurityGatewaysApplicationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
