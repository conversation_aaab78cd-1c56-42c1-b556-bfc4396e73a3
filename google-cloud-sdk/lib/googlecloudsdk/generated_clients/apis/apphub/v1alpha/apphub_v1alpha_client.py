"""Generated client library for apphub version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.apphub.v1alpha import apphub_v1alpha_messages as messages


class ApphubV1alpha(base_api.BaseApiClient):
  """Generated client library for service apphub version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://apphub.googleapis.com/'
  MTLS_BASE_URL = 'https://apphub.mtls.googleapis.com/'

  _PACKAGE = 'apphub'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ApphubV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new apphub handle."""
    url = url or self.BASE_URL
    super(ApphubV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_applications_services = self.ProjectsLocationsApplicationsServicesService(self)
    self.projects_locations_applications_workloads = self.ProjectsLocationsApplicationsWorkloadsService(self)
    self.projects_locations_applications = self.ProjectsLocationsApplicationsService(self)
    self.projects_locations_discoveredServices = self.ProjectsLocationsDiscoveredServicesService(self)
    self.projects_locations_discoveredWorkloads = self.ProjectsLocationsDiscoveredWorkloadsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_serviceProjectAttachments = self.ProjectsLocationsServiceProjectAttachmentsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsApplicationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_applications_services resource."""

    _NAME = 'projects_locations_applications_services'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsApplicationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Service in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services',
        http_method='POST',
        method_id='apphub.projects.locations.applications.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceId'],
        relative_path='v1alpha/{+parent}/services',
        request_field='service',
        request_type_name='ApphubProjectsLocationsApplicationsServicesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Service from an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}',
        http_method='DELETE',
        method_id='apphub.projects.locations.applications.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsServicesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Service in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}',
        http_method='GET',
        method_id='apphub.projects.locations.applications.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Services in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services',
        http_method='GET',
        method_id='apphub.projects.locations.applications.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/services',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Service in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}',
        http_method='PATCH',
        method_id='apphub.projects.locations.applications.services.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='service',
        request_type_name='ApphubProjectsLocationsApplicationsServicesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsApplicationsWorkloadsService(base_api.BaseApiService):
    """Service class for the projects_locations_applications_workloads resource."""

    _NAME = 'projects_locations_applications_workloads'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsApplicationsWorkloadsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Workload in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsWorkloadsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads',
        http_method='POST',
        method_id='apphub.projects.locations.applications.workloads.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'workloadId'],
        relative_path='v1alpha/{+parent}/workloads',
        request_field='workload',
        request_type_name='ApphubProjectsLocationsApplicationsWorkloadsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Workload from an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsWorkloadsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}',
        http_method='DELETE',
        method_id='apphub.projects.locations.applications.workloads.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsWorkloadsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Workload in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsWorkloadsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Workload) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}',
        http_method='GET',
        method_id='apphub.projects.locations.applications.workloads.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsWorkloadsGetRequest',
        response_type_name='Workload',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Workloads in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsWorkloadsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads',
        http_method='GET',
        method_id='apphub.projects.locations.applications.workloads.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/workloads',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsWorkloadsListRequest',
        response_type_name='ListWorkloadsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Workload in an Application.

      Args:
        request: (ApphubProjectsLocationsApplicationsWorkloadsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}',
        http_method='PATCH',
        method_id='apphub.projects.locations.applications.workloads.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='workload',
        request_type_name='ApphubProjectsLocationsApplicationsWorkloadsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsApplicationsService(base_api.BaseApiService):
    """Service class for the projects_locations_applications resource."""

    _NAME = 'projects_locations_applications'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsApplicationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsApplicationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications',
        http_method='POST',
        method_id='apphub.projects.locations.applications.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['applicationId', 'requestId'],
        relative_path='v1alpha/{+parent}/applications',
        request_field='application',
        request_type_name='ApphubProjectsLocationsApplicationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsApplicationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}',
        http_method='DELETE',
        method_id='apphub.projects.locations.applications.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsApplicationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Application) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}',
        http_method='GET',
        method_id='apphub.projects.locations.applications.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsGetRequest',
        response_type_name='Application',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ApphubProjectsLocationsApplicationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:getIamPolicy',
        http_method='GET',
        method_id='apphub.projects.locations.applications.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Applications in a host project and location.

      Args:
        request: (ApphubProjectsLocationsApplicationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListApplicationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications',
        http_method='GET',
        method_id='apphub.projects.locations.applications.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/applications',
        request_field='',
        request_type_name='ApphubProjectsLocationsApplicationsListRequest',
        response_type_name='ListApplicationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsApplicationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}',
        http_method='PATCH',
        method_id='apphub.projects.locations.applications.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='application',
        request_type_name='ApphubProjectsLocationsApplicationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ApphubProjectsLocationsApplicationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:setIamPolicy',
        http_method='POST',
        method_id='apphub.projects.locations.applications.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ApphubProjectsLocationsApplicationsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ApphubProjectsLocationsApplicationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:testIamPermissions',
        http_method='POST',
        method_id='apphub.projects.locations.applications.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ApphubProjectsLocationsApplicationsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDiscoveredServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_discoveredServices resource."""

    _NAME = 'projects_locations_discoveredServices'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsDiscoveredServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def FindUnregistered(self, request, global_params=None):
      r"""Finds unregistered services in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredServicesFindUnregisteredRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FindUnregisteredServicesResponse) The response message.
      """
      config = self.GetMethodConfig('FindUnregistered')
      return self._RunMethod(
          config, request, global_params=global_params)

    FindUnregistered.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredServices:findUnregistered',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredServices.findUnregistered',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/discoveredServices:findUnregistered',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredServicesFindUnregisteredRequest',
        response_type_name='FindUnregisteredServicesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Discovered Service in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiscoveredService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredServices/{discoveredServicesId}',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredServicesGetRequest',
        response_type_name='DiscoveredService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Discovered Services that can be added to an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDiscoveredServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredServices',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/discoveredServices',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredServicesListRequest',
        response_type_name='ListDiscoveredServicesResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Lists a Discovered Service in a host project and location, with a given resource URI.

      Args:
        request: (ApphubProjectsLocationsDiscoveredServicesLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupDiscoveredServiceResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredServices:lookup',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredServices.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['uri'],
        relative_path='v1alpha/{+parent}/discoveredServices:lookup',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredServicesLookupRequest',
        response_type_name='LookupDiscoveredServiceResponse',
        supports_download=False,
    )

  class ProjectsLocationsDiscoveredWorkloadsService(base_api.BaseApiService):
    """Service class for the projects_locations_discoveredWorkloads resource."""

    _NAME = 'projects_locations_discoveredWorkloads'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsDiscoveredWorkloadsService, self).__init__(client)
      self._upload_configs = {
          }

    def FindUnregistered(self, request, global_params=None):
      r"""Finds unregistered workloads in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredWorkloadsFindUnregisteredRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FindUnregisteredWorkloadsResponse) The response message.
      """
      config = self.GetMethodConfig('FindUnregistered')
      return self._RunMethod(
          config, request, global_params=global_params)

    FindUnregistered.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads:findUnregistered',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredWorkloads.findUnregistered',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/discoveredWorkloads:findUnregistered',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredWorkloadsFindUnregisteredRequest',
        response_type_name='FindUnregisteredWorkloadsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Discovered Workload in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredWorkloadsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiscoveredWorkload) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads/{discoveredWorkloadsId}',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredWorkloads.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredWorkloadsGetRequest',
        response_type_name='DiscoveredWorkload',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Discovered Workloads that can be added to an Application in a host project and location.

      Args:
        request: (ApphubProjectsLocationsDiscoveredWorkloadsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDiscoveredWorkloadsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredWorkloads.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/discoveredWorkloads',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredWorkloadsListRequest',
        response_type_name='ListDiscoveredWorkloadsResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Lists a Discovered Workload in a host project and location, with a given resource URI.

      Args:
        request: (ApphubProjectsLocationsDiscoveredWorkloadsLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupDiscoveredWorkloadResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads:lookup',
        http_method='GET',
        method_id='apphub.projects.locations.discoveredWorkloads.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['uri'],
        relative_path='v1alpha/{+parent}/discoveredWorkloads:lookup',
        request_field='',
        request_type_name='ApphubProjectsLocationsDiscoveredWorkloadsLookupRequest',
        response_type_name='LookupDiscoveredWorkloadResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ApphubProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='apphub.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='ApphubProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ApphubProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='apphub.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ApphubProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='apphub.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ApphubProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='apphub.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='ApphubProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceProjectAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceProjectAttachments resource."""

    _NAME = 'projects_locations_serviceProjectAttachments'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsServiceProjectAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Attaches a service project to the host project.

      Args:
        request: (ApphubProjectsLocationsServiceProjectAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments',
        http_method='POST',
        method_id='apphub.projects.locations.serviceProjectAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceProjectAttachmentId'],
        relative_path='v1alpha/{+parent}/serviceProjectAttachments',
        request_field='serviceProjectAttachment',
        request_type_name='ApphubProjectsLocationsServiceProjectAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a service project attachment.

      Args:
        request: (ApphubProjectsLocationsServiceProjectAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments/{serviceProjectAttachmentsId}',
        http_method='DELETE',
        method_id='apphub.projects.locations.serviceProjectAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsServiceProjectAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a service project attachment.

      Args:
        request: (ApphubProjectsLocationsServiceProjectAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceProjectAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments/{serviceProjectAttachmentsId}',
        http_method='GET',
        method_id='apphub.projects.locations.serviceProjectAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsServiceProjectAttachmentsGetRequest',
        response_type_name='ServiceProjectAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists service projects attached to the host project.

      Args:
        request: (ApphubProjectsLocationsServiceProjectAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceProjectAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments',
        http_method='GET',
        method_id='apphub.projects.locations.serviceProjectAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/serviceProjectAttachments',
        request_field='',
        request_type_name='ApphubProjectsLocationsServiceProjectAttachmentsListRequest',
        response_type_name='ListServiceProjectAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def DetachServiceProjectAttachment(self, request, global_params=None):
      r"""Detaches a service project from a host project. You can call this API from any service project without needing access to the host project that it is attached to.

      Args:
        request: (ApphubProjectsLocationsDetachServiceProjectAttachmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DetachServiceProjectAttachmentResponse) The response message.
      """
      config = self.GetMethodConfig('DetachServiceProjectAttachment')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetachServiceProjectAttachment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}:detachServiceProjectAttachment',
        http_method='POST',
        method_id='apphub.projects.locations.detachServiceProjectAttachment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:detachServiceProjectAttachment',
        request_field='detachServiceProjectAttachmentRequest',
        request_type_name='ApphubProjectsLocationsDetachServiceProjectAttachmentRequest',
        response_type_name='DetachServiceProjectAttachmentResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ApphubProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='apphub.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ApphubProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ApphubProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='apphub.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='ApphubProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def LookupServiceProjectAttachment(self, request, global_params=None):
      r"""Lists a service project attachment for a given service project. You can call this API from any project to find if it is attached to a host project.

      Args:
        request: (ApphubProjectsLocationsLookupServiceProjectAttachmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupServiceProjectAttachmentResponse) The response message.
      """
      config = self.GetMethodConfig('LookupServiceProjectAttachment')
      return self._RunMethod(
          config, request, global_params=global_params)

    LookupServiceProjectAttachment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}:lookupServiceProjectAttachment',
        http_method='GET',
        method_id='apphub.projects.locations.lookupServiceProjectAttachment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:lookupServiceProjectAttachment',
        request_field='',
        request_type_name='ApphubProjectsLocationsLookupServiceProjectAttachmentRequest',
        response_type_name='LookupServiceProjectAttachmentResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ApphubV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
