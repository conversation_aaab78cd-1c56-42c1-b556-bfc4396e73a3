"""Generated message classes for apphub version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'apphub'


class ApphubProjectsLocationsApplicationsCreateRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsCreateRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    applicationId: Required. The Application identifier. Must contain only
      lowercase letters, numbers or hyphens, with the first character a
      letter, the last a letter or a number, and a 63 character maximum.
    parent: Required. Project and location to create Application in. Expected
      format: `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  application = _messages.MessageField('Application', 1)
  applicationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ApphubProjectsLocationsApplicationsDeleteRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsDeleteRequest object.

  Fields:
    name: Required. Fully qualified name of the Application to delete.
      Expected format:
      `projects/{project}/locations/{location}/applications/{application}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ApphubProjectsLocationsApplicationsGetIamPolicyRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ApphubProjectsLocationsApplicationsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsGetRequest object.

  Fields:
    name: Required. Fully qualified name of the Application to fetch. Expected
      format:
      `projects/{project}/locations/{location}/applications/{application}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsApplicationsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Project and location to list Applications on. Expected
      format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsApplicationsPatchRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsPatchRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    name: Identifier. The resource name of an Application. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-id}"`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Application resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. The API changes the values of the fields as specified in the
      update_mask. The API ignores the values of all fields not covered by the
      update_mask. You can also unset a field by not specifying it in the
      updated message, but adding the field to the mask. This clears whatever
      value the field previously had.
  """

  application = _messages.MessageField('Application', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ApphubProjectsLocationsApplicationsServicesCreateRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsServicesCreateRequest object.

  Fields:
    parent: Required. Fully qualified name of the parent Application to create
      the Service in. Expected format:
      `projects/{project}/locations/{location}/applications/{application}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    service: A Service resource to be passed as the request body.
    serviceId: Required. The Service identifier. Must contain only lowercase
      letters, numbers or hyphens, with the first character a letter, the last
      a letter or a number, and a 63 character maximum.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  service = _messages.MessageField('Service', 3)
  serviceId = _messages.StringField(4)


class ApphubProjectsLocationsApplicationsServicesDeleteRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsServicesDeleteRequest object.

  Fields:
    name: Required. Fully qualified name of the Service to delete from an
      Application. Expected format: `projects/{project}/locations/{location}/a
      pplications/{application}/services/{service}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ApphubProjectsLocationsApplicationsServicesGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsServicesGetRequest object.

  Fields:
    name: Required. Fully qualified name of the Service to fetch. Expected
      format: `projects/{project}/locations/{location}/applications/{applicati
      on}/services/{service}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsApplicationsServicesListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsServicesListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Fully qualified name of the parent Application to list
      Services for. Expected format:
      `projects/{project}/locations/{location}/applications/{application}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsApplicationsServicesPatchRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsServicesPatchRequest object.

  Fields:
    name: Identifier. The resource name of a Service. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-
      id}/services/{service-id}"`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    service: A Service resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Service resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request.
      The API changes the values of the fields as specified in the
      update_mask. The API ignores the values of all fields not covered by the
      update_mask. You can also unset a field by not specifying it in the
      updated message, but adding the field to the mask. This clears whatever
      value the field previously had.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  service = _messages.MessageField('Service', 3)
  updateMask = _messages.StringField(4)


class ApphubProjectsLocationsApplicationsSetIamPolicyRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ApphubProjectsLocationsApplicationsTestIamPermissionsRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ApphubProjectsLocationsApplicationsWorkloadsCreateRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsWorkloadsCreateRequest object.

  Fields:
    parent: Required. Fully qualified name of the Application to create
      Workload in. Expected format:
      `projects/{project}/locations/{location}/applications/{application}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    workload: A Workload resource to be passed as the request body.
    workloadId: Required. The Workload identifier. Must contain only lowercase
      letters, numbers or hyphens, with the first character a letter, the last
      a letter or a number, and a 63 character maximum.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  workload = _messages.MessageField('Workload', 3)
  workloadId = _messages.StringField(4)


class ApphubProjectsLocationsApplicationsWorkloadsDeleteRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsWorkloadsDeleteRequest object.

  Fields:
    name: Required. Fully qualified name of the Workload to delete from an
      Application. Expected format: `projects/{project}/locations/{location}/a
      pplications/{application}/workloads/{workload}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ApphubProjectsLocationsApplicationsWorkloadsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsWorkloadsGetRequest object.

  Fields:
    name: Required. Fully qualified name of the Workload to fetch. Expected
      format: `projects/{project}/locations/{location}/applications/{applicati
      on}/workloads/{workload}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsApplicationsWorkloadsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsWorkloadsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Fully qualified name of the parent Application to list
      Workloads for. Expected format:
      `projects/{project}/locations/{location}/applications/{application}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsApplicationsWorkloadsPatchRequest(_messages.Message):
  r"""A ApphubProjectsLocationsApplicationsWorkloadsPatchRequest object.

  Fields:
    name: Identifier. The resource name of the Workload. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-
      id}/workloads/{workload-id}"`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Workload resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request.
      The API changes the values of the fields as specified in the
      update_mask. The API ignores the values of all fields not covered by the
      update_mask. You can also unset a field by not specifying it in the
      updated message, but adding the field to the mask. This clears whatever
      value the field previously had.
    workload: A Workload resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  updateMask = _messages.StringField(3)
  workload = _messages.MessageField('Workload', 4)


class ApphubProjectsLocationsDetachServiceProjectAttachmentRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDetachServiceProjectAttachmentRequest object.

  Fields:
    detachServiceProjectAttachmentRequest: A
      DetachServiceProjectAttachmentRequest resource to be passed as the
      request body.
    name: Required. Service project id and location to detach from a host
      project. Only global location is supported. Expected format:
      `projects/{project}/locations/{location}`.
  """

  detachServiceProjectAttachmentRequest = _messages.MessageField('DetachServiceProjectAttachmentRequest', 1)
  name = _messages.StringField(2, required=True)


class ApphubProjectsLocationsDiscoveredServicesFindUnregisteredRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredServicesFindUnregisteredRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Project and location to find unregistered Discovered
      Services on. Expected format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsDiscoveredServicesGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredServicesGetRequest object.

  Fields:
    name: Required. Fully qualified name of the Discovered Service to fetch.
      Expected format: `projects/{project}/locations/{location}/discoveredServ
      ices/{discoveredService}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsDiscoveredServicesListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredServicesListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Project and location to list Discovered Services on.
      Expected format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsDiscoveredServicesLookupRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredServicesLookupRequest object.

  Fields:
    parent: Required. Host project ID and location to lookup Discovered
      Service in. Expected format: `projects/{project}/locations/{location}`.
    uri: Required. Resource URI to find DiscoveredService for. Accepts both
      project number and project ID and does translation when needed.
  """

  parent = _messages.StringField(1, required=True)
  uri = _messages.StringField(2)


class ApphubProjectsLocationsDiscoveredWorkloadsFindUnregisteredRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredWorkloadsFindUnregisteredRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Project and location to find unregistered Discovered
      Workloads on. Expected format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsDiscoveredWorkloadsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredWorkloadsGetRequest object.

  Fields:
    name: Required. Fully qualified name of the Discovered Workload to fetch.
      Expected format: `projects/{project}/locations/{location}/discoveredWork
      loads/{discoveredWorkload}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsDiscoveredWorkloadsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredWorkloadsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Project and location to list Discovered Workloads on.
      Expected format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApphubProjectsLocationsDiscoveredWorkloadsLookupRequest(_messages.Message):
  r"""A ApphubProjectsLocationsDiscoveredWorkloadsLookupRequest object.

  Fields:
    parent: Required. Host project ID and location to lookup Discovered
      Workload in. Expected format: `projects/{project}/locations/{location}`.
    uri: Required. Resource URI to find Discovered Workload for. Accepts both
      project number and project ID and does translation when needed.
  """

  parent = _messages.StringField(1, required=True)
  uri = _messages.StringField(2)


class ApphubProjectsLocationsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ApphubProjectsLocationsLookupServiceProjectAttachmentRequest(_messages.Message):
  r"""A ApphubProjectsLocationsLookupServiceProjectAttachmentRequest object.

  Fields:
    name: Required. Service project ID and location to lookup service project
      attachment for. Only global location is supported. Expected format:
      `projects/{project}/locations/{location}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ApphubProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ApphubProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ApphubProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ApphubProjectsLocationsServiceProjectAttachmentsCreateRequest(_messages.Message):
  r"""A ApphubProjectsLocationsServiceProjectAttachmentsCreateRequest object.

  Fields:
    parent: Required. Host project ID and location to which service project is
      being attached. Only global location is supported. Expected format:
      `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    serviceProjectAttachment: A ServiceProjectAttachment resource to be passed
      as the request body.
    serviceProjectAttachmentId: Required. The service project attachment
      identifier must contain the project id of the service project specified
      in the service_project_attachment.service_project field.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  serviceProjectAttachment = _messages.MessageField('ServiceProjectAttachment', 3)
  serviceProjectAttachmentId = _messages.StringField(4)


class ApphubProjectsLocationsServiceProjectAttachmentsDeleteRequest(_messages.Message):
  r"""A ApphubProjectsLocationsServiceProjectAttachmentsDeleteRequest object.

  Fields:
    name: Required. Fully qualified name of the service project attachment to
      delete. Expected format: `projects/{project}/locations/{location}/servic
      eProjectAttachments/{serviceProjectAttachment}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ApphubProjectsLocationsServiceProjectAttachmentsGetRequest(_messages.Message):
  r"""A ApphubProjectsLocationsServiceProjectAttachmentsGetRequest object.

  Fields:
    name: Required. Fully qualified name of the service project attachment to
      retrieve. Expected format: `projects/{project}/locations/{location}/serv
      iceProjectAttachments/{serviceProjectAttachment}`.
  """

  name = _messages.StringField(1, required=True)


class ApphubProjectsLocationsServiceProjectAttachmentsListRequest(_messages.Message):
  r"""A ApphubProjectsLocationsServiceProjectAttachmentsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Host project ID and location to list service project
      attachments. Only global location is supported. Expected format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class Application(_messages.Message):
  r"""Application defines the governance boundary for App Hub entities that
  perform a logical end-to-end business function. App Hub supports application
  level IAM permission to align with governance requirements.

  Enums:
    StateValueValuesEnum: Output only. Application state.

  Fields:
    attributes: Optional. Consumer provided attributes.
    createTime: Output only. Create time.
    description: Optional. User-defined description of an Application. Can
      have a maximum length of 2048 characters.
    displayName: Optional. User-defined name for the Application. Can have a
      maximum length of 63 characters.
    name: Identifier. The resource name of an Application. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-id}"`
    scope: Required. Immutable. Defines what data can be included into this
      Application. Limits which Services and Workloads can be registered.
    state: Output only. Application state.
    uid: Output only. A universally unique identifier (in UUID4 format) for
      the `Application`.
    updateTime: Output only. Update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Application state.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: The Application is being created.
      ACTIVE: The Application is ready to register Services and Workloads.
      DELETING: The Application is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  attributes = _messages.MessageField('Attributes', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  scope = _messages.MessageField('Scope', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class Attributes(_messages.Message):
  r"""Consumer provided attributes.

  Fields:
    businessOwners: Optional. Business team that ensures user needs are met
      and value is delivered
    criticality: Optional. User-defined criticality information.
    developerOwners: Optional. Developer team that owns development and
      coding.
    environment: Optional. User-defined environment information.
    operatorOwners: Optional. Operator team that ensures runtime and
      operations.
  """

  businessOwners = _messages.MessageField('ContactInfo', 1, repeated=True)
  criticality = _messages.MessageField('Criticality', 2)
  developerOwners = _messages.MessageField('ContactInfo', 3, repeated=True)
  environment = _messages.MessageField('Environment', 4)
  operatorOwners = _messages.MessageField('ContactInfo', 5, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Channel(_messages.Message):
  r"""Separate message to accommodate custom formats across IRC and Slack.

  Fields:
    uri: Required. URI of the channel.
  """

  uri = _messages.StringField(1)


class ContactInfo(_messages.Message):
  r"""Contact information of stakeholders.

  Fields:
    channel: Optional. Communication channel of the contacts.
    displayName: Optional. Contact's name. Can have a maximum length of 63
      characters.
    email: Required. Email address of the contacts.
  """

  channel = _messages.MessageField('Channel', 1)
  displayName = _messages.StringField(2)
  email = _messages.StringField(3)


class Criticality(_messages.Message):
  r"""Criticality of the Application, Service, or Workload

  Enums:
    TypeValueValuesEnum: Required. Criticality Type.

  Fields:
    level: Optional. Criticality level. Can contain only lowercase letters,
      numeric characters, underscores, and dashes. Can have a maximum length
      of 63 characters. Deprecated: Please refer to type instead.
    missionCritical: Optional. Indicates mission-critical Application,
      Service, or Workload. Deprecated: Please refer to type instead.
    type: Required. Criticality Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Criticality Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      MISSION_CRITICAL: Mission critical service, application or workload.
      HIGH: High impact.
      MEDIUM: Medium impact.
      LOW: Low impact.
    """
    TYPE_UNSPECIFIED = 0
    MISSION_CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  level = _messages.StringField(1)
  missionCritical = _messages.BooleanField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class DetachServiceProjectAttachmentRequest(_messages.Message):
  r"""Request for DetachServiceProjectAttachment."""


class DetachServiceProjectAttachmentResponse(_messages.Message):
  r"""Response for DetachServiceProjectAttachment."""


class DiscoveredService(_messages.Message):
  r"""DiscoveredService is a network or API interface that exposes some
  functionality to clients for consumption over the network. A discovered
  service can be registered to a App Hub service.

  Fields:
    name: Identifier. The resource name of the discovered service. Format:
      `"projects/{host-project-
      id}/locations/{location}/discoveredServices/{uuid}"`
    serviceProperties: Output only. Properties of an underlying compute
      resource that can comprise a Service. These are immutable.
    serviceReference: Output only. Reference to an underlying networking
      resource that can comprise a Service. These are immutable.
  """

  name = _messages.StringField(1)
  serviceProperties = _messages.MessageField('ServiceProperties', 2)
  serviceReference = _messages.MessageField('ServiceReference', 3)


class DiscoveredWorkload(_messages.Message):
  r"""DiscoveredWorkload is a binary deployment (such as managed instance
  groups (MIGs) and GKE deployments) that performs the smallest logical subset
  of business functionality. A discovered workload can be registered to an App
  Hub Workload.

  Fields:
    name: Identifier. The resource name of the discovered workload. Format:
      `"projects/{host-project-
      id}/locations/{location}/discoveredWorkloads/{uuid}"`
    workloadProperties: Output only. Properties of an underlying compute
      resource represented by the Workload. These are immutable.
    workloadReference: Output only. Reference of an underlying compute
      resource represented by the Workload. These are immutable.
  """

  name = _messages.StringField(1)
  workloadProperties = _messages.MessageField('WorkloadProperties', 2)
  workloadReference = _messages.MessageField('WorkloadReference', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Environment(_messages.Message):
  r"""Environment of the Application, Service, or Workload

  Enums:
    TypeValueValuesEnum: Required. Environment Type.

  Fields:
    environment: Optional. Environment name. Can contain only lowercase
      letters, numeric characters, underscores, and dashes. Can have a maximum
      length of 63 characters. Deprecated: Please refer to type instead.
    type: Required. Environment Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Environment Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      PRODUCTION: Production environment.
      STAGING: Staging environment.
      TEST: Test environment.
      DEVELOPMENT: Development environment.
    """
    TYPE_UNSPECIFIED = 0
    PRODUCTION = 1
    STAGING = 2
    TEST = 3
    DEVELOPMENT = 4

  environment = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FindUnregisteredServicesResponse(_messages.Message):
  r"""Response for FindUnregisteredServices.

  Fields:
    discoveredServices: List of Discovered Services.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  discoveredServices = _messages.MessageField('DiscoveredService', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class FindUnregisteredWorkloadsResponse(_messages.Message):
  r"""Response for FindUnregisteredWorkloads.

  Fields:
    discoveredWorkloads: List of Discovered Workloads.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  discoveredWorkloads = _messages.MessageField('DiscoveredWorkload', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListApplicationsResponse(_messages.Message):
  r"""Response for ListApplications.

  Fields:
    applications: List of Applications.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  applications = _messages.MessageField('Application', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDiscoveredServicesResponse(_messages.Message):
  r"""Response for ListDiscoveredServices.

  Fields:
    discoveredServices: List of Discovered Services.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  discoveredServices = _messages.MessageField('DiscoveredService', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDiscoveredWorkloadsResponse(_messages.Message):
  r"""Response for ListDiscoveredWorkloads.

  Fields:
    discoveredWorkloads: List of Discovered Workloads.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  discoveredWorkloads = _messages.MessageField('DiscoveredWorkload', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListServiceProjectAttachmentsResponse(_messages.Message):
  r"""Response for ListServiceProjectAttachments.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    serviceProjectAttachments: List of service project attachments.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  serviceProjectAttachments = _messages.MessageField('ServiceProjectAttachment', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListServicesResponse(_messages.Message):
  r"""Response for ListServices.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    services: List of Services.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('Service', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListWorkloadsResponse(_messages.Message):
  r"""Response for ListWorkloads.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    workloads: List of Workloads.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workloads = _messages.MessageField('Workload', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LookupDiscoveredServiceResponse(_messages.Message):
  r"""Response for LookupDiscoveredService.

  Fields:
    discoveredService: Discovered Service if exists, empty otherwise.
  """

  discoveredService = _messages.MessageField('DiscoveredService', 1)


class LookupDiscoveredWorkloadResponse(_messages.Message):
  r"""Response for LookupDiscoveredWorkload.

  Fields:
    discoveredWorkload: Discovered Workload if exists, empty otherwise.
  """

  discoveredWorkload = _messages.MessageField('DiscoveredWorkload', 1)


class LookupServiceProjectAttachmentResponse(_messages.Message):
  r"""Response for LookupServiceProjectAttachment.

  Fields:
    serviceProjectAttachment: Service project attachment for a project if
      exists, empty otherwise.
  """

  serviceProjectAttachment = _messages.MessageField('ServiceProjectAttachment', 1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have google.longrunning.Operation.error value
      with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Scope(_messages.Message):
  r"""Scope of an application.

  Enums:
    TypeValueValuesEnum: Required. Scope Type.

  Fields:
    type: Required. Scope Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Scope Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      REGIONAL: Regional type.
      GLOBAL: Global type.
    """
    TYPE_UNSPECIFIED = 0
    REGIONAL = 1
    GLOBAL = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Service(_messages.Message):
  r"""Service is an App Hub data model that contains a discovered service,
  which represents a network or API interface that exposes some functionality
  to clients for consumption over the network.

  Enums:
    StateValueValuesEnum: Output only. Service state.

  Fields:
    attributes: Optional. Consumer provided attributes.
    createTime: Output only. Create time.
    description: Optional. User-defined description of a Service. Can have a
      maximum length of 2048 characters.
    discoveredService: Required. Immutable. The resource name of the original
      discovered service.
    displayName: Optional. User-defined name for the Service. Can have a
      maximum length of 63 characters.
    name: Identifier. The resource name of a Service. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-
      id}/services/{service-id}"`
    serviceProperties: Output only. Properties of an underlying compute
      resource that can comprise a Service. These are immutable.
    serviceReference: Output only. Reference to an underlying networking
      resource that can comprise a Service. These are immutable.
    state: Output only. Service state.
    uid: Output only. A universally unique identifier (UUID) for the `Service`
      in the UUID4 format.
    updateTime: Output only. Update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Service state.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: The service is being created.
      ACTIVE: The service is ready.
      DELETING: The service is being deleted.
      DETACHED: The underlying networking resources have been deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    DETACHED = 4

  attributes = _messages.MessageField('Attributes', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  discoveredService = _messages.StringField(4)
  displayName = _messages.StringField(5)
  name = _messages.StringField(6)
  serviceProperties = _messages.MessageField('ServiceProperties', 7)
  serviceReference = _messages.MessageField('ServiceReference', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class ServiceProjectAttachment(_messages.Message):
  r"""ServiceProjectAttachment represents an attachment from a service project
  to a host project. Service projects contain the underlying cloud
  infrastructure resources, and expose these resources to the host project
  through a ServiceProjectAttachment. With the attachments, the host project
  can provide an aggregated view of resources across all service projects.

  Enums:
    StateValueValuesEnum: Output only. ServiceProjectAttachment state.

  Fields:
    createTime: Output only. Create time.
    name: Identifier. The resource name of a ServiceProjectAttachment. Format:
      `"projects/{host-project-
      id}/locations/global/serviceProjectAttachments/{service-project-id}."`
    serviceProject: Required. Immutable. Service project name in the format:
      `"projects/abc"` or `"projects/123"`. As input, project name with either
      project id or number are accepted. As output, this field will contain
      project number.
    state: Output only. ServiceProjectAttachment state.
    uid: Output only. A globally unique identifier (in UUID4 format) for the
      `ServiceProjectAttachment`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. ServiceProjectAttachment state.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: The ServiceProjectAttachment is being created.
      ACTIVE: The ServiceProjectAttachment is ready. This means Services and
        Workloads under the corresponding ServiceProjectAttachment is ready
        for registration.
      DELETING: The ServiceProjectAttachment is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  serviceProject = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  uid = _messages.StringField(5)


class ServiceProperties(_messages.Message):
  r"""Properties of an underlying cloud resource that can comprise a Service.

  Fields:
    gcpProject: Output only. The service project identifier that the
      underlying cloud resource resides in.
    location: Output only. The location that the underlying resource resides
      in, for example, us-west1.
    zone: Output only. The location that the underlying resource resides in if
      it is zonal, for example, us-west1-a).
  """

  gcpProject = _messages.StringField(1)
  location = _messages.StringField(2)
  zone = _messages.StringField(3)


class ServiceReference(_messages.Message):
  r"""Reference to an underlying networking resource that can comprise a
  Service.

  Fields:
    path: Output only. Additional path under the resource URI (demultiplexing
      one resource URI into multiple entries). Smallest unit a policy can be
      attached to. Examples: URL Map path entry.
    uri: Output only. The underlying resource URI. For example, URI of
      Forwarding Rule, URL Map, and Backend Service.
  """

  path = _messages.StringField(1)
  uri = _messages.StringField(2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Workload(_messages.Message):
  r"""Workload is an App Hub data model that contains a discovered workload,
  which represents a binary deployment (such as managed instance groups (MIGs)
  and GKE deployments) that performs the smallest logical subset of business
  functionality.

  Enums:
    StateValueValuesEnum: Output only. Workload state.

  Fields:
    attributes: Optional. Consumer provided attributes.
    createTime: Output only. Create time.
    description: Optional. User-defined description of a Workload. Can have a
      maximum length of 2048 characters.
    discoveredWorkload: Required. Immutable. The resource name of the original
      discovered workload.
    displayName: Optional. User-defined name for the Workload. Can have a
      maximum length of 63 characters.
    name: Identifier. The resource name of the Workload. Format:
      `"projects/{host-project-
      id}/locations/{location}/applications/{application-
      id}/workloads/{workload-id}"`
    state: Output only. Workload state.
    uid: Output only. A universally unique identifier (UUID) for the
      `Workload` in the UUID4 format.
    updateTime: Output only. Update time.
    workloadProperties: Output only. Properties of an underlying compute
      resource represented by the Workload. These are immutable.
    workloadReference: Output only. Reference of an underlying compute
      resource represented by the Workload. These are immutable.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Workload state.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: The Workload is being created.
      ACTIVE: The Workload is ready.
      DELETING: The Workload is being deleted.
      DETACHED: The underlying compute resources have been deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    DETACHED = 4

  attributes = _messages.MessageField('Attributes', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  discoveredWorkload = _messages.StringField(4)
  displayName = _messages.StringField(5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  workloadProperties = _messages.MessageField('WorkloadProperties', 10)
  workloadReference = _messages.MessageField('WorkloadReference', 11)


class WorkloadProperties(_messages.Message):
  r"""Properties of an underlying compute resource represented by the
  Workload.

  Fields:
    gcpProject: Output only. The service project identifier that the
      underlying cloud resource resides in. Empty for non-cloud resources.
    location: Output only. The location that the underlying compute resource
      resides in (for example, us-west1).
    zone: Output only. The location that the underlying compute resource
      resides in if it is zonal (for example, us-west1-a).
  """

  gcpProject = _messages.StringField(1)
  location = _messages.StringField(2)
  zone = _messages.StringField(3)


class WorkloadReference(_messages.Message):
  r"""Reference of an underlying compute resource represented by the Workload.

  Fields:
    uri: Output only. The underlying compute resource uri.
  """

  uri = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ApphubProjectsLocationsApplicationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
