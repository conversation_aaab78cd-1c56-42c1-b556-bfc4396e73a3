"""Generated message classes for cloudquotas version v1beta.

Cloud Quotas API provides Google Cloud service consumers with management and
observability for resource usage, quotas, and restrictions of the services
they consume.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'cloudquotas'


class CloudquotasFoldersLocationsQuotaAdjusterSettingsGetQuotaAdjusterSettingsRequest(
    _messages.Message
):
  r"""A CloudquotasFoldersLocationsQuotaAdjusterSettingsGetQuotaAdjusterSettin

  gsRequest object.

  Fields:
    name: Required. Name of the `quotaAdjusterSettings` configuration. Only a
      single setting per project is supported.
  """

  name = _messages.StringField(1, required=True)


class CloudquotasFoldersLocationsQuotaAdjusterSettingsUpdateQuotaAdjusterSettingsRequest(
    _messages.Message
):
  r"""A CloudquotasFoldersLocationsQuotaAdjusterSettingsUpdateQuotaAdjusterSet

  tingsRequest object.

  Fields:
    name: Identifier. Name of the config would be of the format:
      projects/PROJECT_NUMBER/locations/global/quotaAdjusterSettings
      folders/FOLDER_NUMBER/locations/global/quotaAdjusterSettings
      organizations/ORGANIZATION_NUMBER/locations/global/quotaAdjusterSettings
    quotaAdjusterSettings: A QuotaAdjusterSettings resource to be passed as
      the request body.
    updateMask: Optional. The list of fields to update.
    validateOnly: Optional. If set to true, checks the syntax of the request
      but doesn't update the quota adjuster settings value. Note that although
      a request can be valid, that doesn't guarantee that the request will be
      fulfilled.
  """

  name = _messages.StringField(1, required=True)
  quotaAdjusterSettings = _messages.MessageField('QuotaAdjusterSettings', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudquotasFoldersLocationsQuotaPreferencesCreateRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsQuotaPreferencesCreateRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    parent: Required. Value for parent. Example:
      `projects/123/locations/global`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    quotaPreferenceId: Optional. Id of the requesting object, must be unique
      under its parent. If client does not set this field, the service will
      generate one.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 1, repeated=True)
  parent = _messages.StringField(2, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 3)
  quotaPreferenceId = _messages.StringField(4)


class CloudquotasFoldersLocationsQuotaPreferencesGetRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsQuotaPreferencesGetRequest object.

  Fields:
    name: Required. Name of the resource Example name:
      `projects/123/locations/global/quota_preferences/my-config-for-us-east1`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasFoldersLocationsQuotaPreferencesListRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsQuotaPreferencesListRequest object.

  Fields:
    filter: Optional. Filter result QuotaPreferences by their state, type,
      create/update time range. Example filters: `reconciling=true AND
      request_type=CLOUD_CONSOLE`, `reconciling=true OR
      creation_time>2022-12-03T10:30:00`
    orderBy: Optional. How to order of the results. By default, the results
      are ordered by create time. Example orders: `quota_id`, `service,
      create_time`
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaPreference resources. Listing
      across different resource containers (such as 'projects/-') is not
      allowed. When the value starts with 'folders' or 'organizations', it
      lists the QuotaPreferences for org quotas in the container. It does not
      list the QuotaPreferences in the descendant projects of the container.
      Example parents: `projects/123/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudquotasFoldersLocationsQuotaPreferencesPatchRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsQuotaPreferencesPatchRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    allowMissing: Optional. If set to true, and the quota preference is not
      found, a new one will be created. In this situation, `update_mask` is
      ignored.
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    name: Required except in the CREATE requests. The resource name of the
      quota preference. The path that follows `/locations` must be `/global`.
      For example: `projects/123/locations/global/quotaPreferences/my-config-
      for-us-east1`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the QuotaPreference resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, validate the request, but do not
      actually update. Note that a request being valid does not mean that the
      request is guaranteed to be fulfilled.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  allowMissing = _messages.BooleanField(1)
  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 2, repeated=True)
  name = _messages.StringField(3, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class CloudquotasFoldersLocationsServicesQuotaInfosGetRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsServicesQuotaInfosGetRequest object.

  Fields:
    name: Required. The resource name of the quota info. An example name: `pro
      jects/123/locations/global/services/compute.googleapis.com/quotaInfos/Cp
      usPerProjectPerRegion`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasFoldersLocationsServicesQuotaInfosListRequest(_messages.Message):
  r"""A CloudquotasFoldersLocationsServicesQuotaInfosListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaInfo resources. Listing across
      different resource containers (such as 'projects/-') is not allowed.
      Example names:
      `projects/123/locations/global/services/compute.googleapis.com`
      `folders/234/locations/global/services/compute.googleapis.com`
      `organizations/345/locations/global/services/compute.googleapis.com`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudquotasOrganizationsLocationsQuotaAdjusterSettingsGetQuotaAdjusterSettingsRequest(
    _messages.Message
):
  r"""A CloudquotasOrganizationsLocationsQuotaAdjusterSettingsGetQuotaAdjuster

  SettingsRequest object.

  Fields:
    name: Required. Name of the `quotaAdjusterSettings` configuration. Only a
      single setting per project is supported.
  """

  name = _messages.StringField(1, required=True)


class CloudquotasOrganizationsLocationsQuotaAdjusterSettingsUpdateQuotaAdjusterSettingsRequest(
    _messages.Message
):
  r"""A CloudquotasOrganizationsLocationsQuotaAdjusterSettingsUpdateQuotaAdjus

  terSettingsRequest object.

  Fields:
    name: Identifier. Name of the config would be of the format:
      projects/PROJECT_NUMBER/locations/global/quotaAdjusterSettings
      folders/FOLDER_NUMBER/locations/global/quotaAdjusterSettings
      organizations/ORGANIZATION_NUMBER/locations/global/quotaAdjusterSettings
    quotaAdjusterSettings: A QuotaAdjusterSettings resource to be passed as
      the request body.
    updateMask: Optional. The list of fields to update.
    validateOnly: Optional. If set to true, checks the syntax of the request
      but doesn't update the quota adjuster settings value. Note that although
      a request can be valid, that doesn't guarantee that the request will be
      fulfilled.
  """

  name = _messages.StringField(1, required=True)
  quotaAdjusterSettings = _messages.MessageField('QuotaAdjusterSettings', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudquotasOrganizationsLocationsQuotaPreferencesCreateRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsQuotaPreferencesCreateRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    parent: Required. Value for parent. Example:
      `projects/123/locations/global`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    quotaPreferenceId: Optional. Id of the requesting object, must be unique
      under its parent. If client does not set this field, the service will
      generate one.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 1, repeated=True)
  parent = _messages.StringField(2, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 3)
  quotaPreferenceId = _messages.StringField(4)


class CloudquotasOrganizationsLocationsQuotaPreferencesGetRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsQuotaPreferencesGetRequest object.

  Fields:
    name: Required. Name of the resource Example name:
      `projects/123/locations/global/quota_preferences/my-config-for-us-east1`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasOrganizationsLocationsQuotaPreferencesListRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsQuotaPreferencesListRequest object.

  Fields:
    filter: Optional. Filter result QuotaPreferences by their state, type,
      create/update time range. Example filters: `reconciling=true AND
      request_type=CLOUD_CONSOLE`, `reconciling=true OR
      creation_time>2022-12-03T10:30:00`
    orderBy: Optional. How to order of the results. By default, the results
      are ordered by create time. Example orders: `quota_id`, `service,
      create_time`
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaPreference resources. Listing
      across different resource containers (such as 'projects/-') is not
      allowed. When the value starts with 'folders' or 'organizations', it
      lists the QuotaPreferences for org quotas in the container. It does not
      list the QuotaPreferences in the descendant projects of the container.
      Example parents: `projects/123/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudquotasOrganizationsLocationsQuotaPreferencesPatchRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsQuotaPreferencesPatchRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    allowMissing: Optional. If set to true, and the quota preference is not
      found, a new one will be created. In this situation, `update_mask` is
      ignored.
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    name: Required except in the CREATE requests. The resource name of the
      quota preference. The path that follows `/locations` must be `/global`.
      For example: `projects/123/locations/global/quotaPreferences/my-config-
      for-us-east1`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the QuotaPreference resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, validate the request, but do not
      actually update. Note that a request being valid does not mean that the
      request is guaranteed to be fulfilled.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  allowMissing = _messages.BooleanField(1)
  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 2, repeated=True)
  name = _messages.StringField(3, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class CloudquotasOrganizationsLocationsServicesQuotaInfosGetRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsServicesQuotaInfosGetRequest object.

  Fields:
    name: Required. The resource name of the quota info. An example name: `pro
      jects/123/locations/global/services/compute.googleapis.com/quotaInfos/Cp
      usPerProjectPerRegion`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasOrganizationsLocationsServicesQuotaInfosListRequest(_messages.Message):
  r"""A CloudquotasOrganizationsLocationsServicesQuotaInfosListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaInfo resources. Listing across
      different resource containers (such as 'projects/-') is not allowed.
      Example names:
      `projects/123/locations/global/services/compute.googleapis.com`
      `folders/234/locations/global/services/compute.googleapis.com`
      `organizations/345/locations/global/services/compute.googleapis.com`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudquotasProjectsLocationsQuotaAdjusterSettingsGetQuotaAdjusterSettingsRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaAdjusterSettingsGetQuotaAdjusterSetti
  ngsRequest object.

  Fields:
    name: Required. Name of the `quotaAdjusterSettings` configuration. Only a
      single setting per project is supported.
  """

  name = _messages.StringField(1, required=True)


class CloudquotasProjectsLocationsQuotaAdjusterSettingsUpdateQuotaAdjusterSettingsRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaAdjusterSettingsUpdateQuotaAdjusterSe
  ttingsRequest object.

  Fields:
    name: Identifier. Name of the config would be of the format:
      projects/PROJECT_NUMBER/locations/global/quotaAdjusterSettings
      folders/FOLDER_NUMBER/locations/global/quotaAdjusterSettings
      organizations/ORGANIZATION_NUMBER/locations/global/quotaAdjusterSettings
    quotaAdjusterSettings: A QuotaAdjusterSettings resource to be passed as
      the request body.
    updateMask: Optional. The list of fields to update.
    validateOnly: Optional. If set to true, checks the syntax of the request
      but doesn't update the quota adjuster settings value. Note that although
      a request can be valid, that doesn't guarantee that the request will be
      fulfilled.
  """

  name = _messages.StringField(1, required=True)
  quotaAdjusterSettings = _messages.MessageField('QuotaAdjusterSettings', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudquotasProjectsLocationsQuotaPreferencesCreateRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaPreferencesCreateRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    parent: Required. Value for parent. Example:
      `projects/123/locations/global`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    quotaPreferenceId: Optional. Id of the requesting object, must be unique
      under its parent. If client does not set this field, the service will
      generate one.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 1, repeated=True)
  parent = _messages.StringField(2, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 3)
  quotaPreferenceId = _messages.StringField(4)


class CloudquotasProjectsLocationsQuotaPreferencesGetRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaPreferencesGetRequest object.

  Fields:
    name: Required. Name of the resource Example name:
      `projects/123/locations/global/quota_preferences/my-config-for-us-east1`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasProjectsLocationsQuotaPreferencesListRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaPreferencesListRequest object.

  Fields:
    filter: Optional. Filter result QuotaPreferences by their state, type,
      create/update time range. Example filters: `reconciling=true AND
      request_type=CLOUD_CONSOLE`, `reconciling=true OR
      creation_time>2022-12-03T10:30:00`
    orderBy: Optional. How to order of the results. By default, the results
      are ordered by create time. Example orders: `quota_id`, `service,
      create_time`
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaPreference resources. Listing
      across different resource containers (such as 'projects/-') is not
      allowed. When the value starts with 'folders' or 'organizations', it
      lists the QuotaPreferences for org quotas in the container. It does not
      list the QuotaPreferences in the descendant projects of the container.
      Example parents: `projects/123/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudquotasProjectsLocationsQuotaPreferencesPatchRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsQuotaPreferencesPatchRequest object.

  Enums:
    IgnoreSafetyChecksValueValuesEnum: The list of quota safety checks to be
      ignored.

  Fields:
    allowMissing: Optional. If set to true, and the quota preference is not
      found, a new one will be created. In this situation, `update_mask` is
      ignored.
    ignoreSafetyChecks: The list of quota safety checks to be ignored.
    name: Required except in the CREATE requests. The resource name of the
      quota preference. The path that follows `/locations` must be `/global`.
      For example: `projects/123/locations/global/quotaPreferences/my-config-
      for-us-east1`
    quotaPreference: A QuotaPreference resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the QuotaPreference resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, validate the request, but do not
      actually update. Note that a request being valid does not mean that the
      request is guaranteed to be fulfilled.
  """

  class IgnoreSafetyChecksValueValuesEnum(_messages.Enum):
    r"""The list of quota safety checks to be ignored.

    Values:
      QUOTA_SAFETY_CHECK_UNSPECIFIED: Unspecified quota safety check.
      QUOTA_DECREASE_BELOW_USAGE: Validates that a quota mutation would not
        cause the consumer's effective limit to be lower than the consumer's
        quota usage.
      QUOTA_DECREASE_PERCENTAGE_TOO_HIGH: Validates that a quota mutation
        would not cause the consumer's effective limit to decrease by more
        than 10 percent.
    """
    QUOTA_SAFETY_CHECK_UNSPECIFIED = 0
    QUOTA_DECREASE_BELOW_USAGE = 1
    QUOTA_DECREASE_PERCENTAGE_TOO_HIGH = 2

  allowMissing = _messages.BooleanField(1)
  ignoreSafetyChecks = _messages.EnumField('IgnoreSafetyChecksValueValuesEnum', 2, repeated=True)
  name = _messages.StringField(3, required=True)
  quotaPreference = _messages.MessageField('QuotaPreference', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class CloudquotasProjectsLocationsServicesQuotaInfosGetRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsServicesQuotaInfosGetRequest object.

  Fields:
    name: Required. The resource name of the quota info. An example name: `pro
      jects/123/locations/global/services/compute.googleapis.com/quotaInfos/Cp
      usPerProjectPerRegion`
  """

  name = _messages.StringField(1, required=True)


class CloudquotasProjectsLocationsServicesQuotaInfosListRequest(_messages.Message):
  r"""A CloudquotasProjectsLocationsServicesQuotaInfosListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value of QuotaInfo resources. Listing across
      different resource containers (such as 'projects/-') is not allowed.
      Example names:
      `projects/123/locations/global/services/compute.googleapis.com`
      `folders/234/locations/global/services/compute.googleapis.com`
      `organizations/345/locations/global/services/compute.googleapis.com`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DimensionsInfo(_messages.Message):
  r"""The detailed quota information such as effective quota value for a
  combination of dimensions.

  Messages:
    DimensionsValue: The map of dimensions in key-value pairs. The key of a
      map entry is "region", "zone", or the name of a service-specific
      dimension, and the value of a map entry is the value of the dimension.
      If a dimension does not appear in the map of dimensions, the dimensions
      info applies to all the dimension values except for those that have
      another DimensionInfo instance configured for the specific value. For
      example: `{"provider" : "Example Organization"}` where `provider` is a
      service-specific quota dimension and `Example Organization` is the
      provider name.

  Fields:
    applicableLocations: The applicable regions or zones of this dimension.
      The field is set to ['global'] for quotas that are not per region or per
      zone. Otherwise, it will be set to the list of locations this dimension
      info is applicable to.
    details: Quota details for the specified dimensions.
    dimensions: The map of dimensions in key-value pairs. The key of a map
      entry is "region", "zone", or the name of a service-specific dimension,
      and the value of a map entry is the value of the dimension. If a
      dimension does not appear in the map of dimensions, the dimensions info
      applies to all the dimension values except for those that have another
      DimensionInfo instance configured for the specific value. For example:
      `{"provider" : "Example Organization"}` where `provider` is a service-
      specific quota dimension and `Example Organization` is the provider
      name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DimensionsValue(_messages.Message):
    r"""The map of dimensions in key-value pairs. The key of a map entry is
    "region", "zone", or the name of a service-specific dimension, and the
    value of a map entry is the value of the dimension. If a dimension does
    not appear in the map of dimensions, the dimensions info applies to all
    the dimension values except for those that have another DimensionInfo
    instance configured for the specific value. For example: `{"provider" :
    "Example Organization"}` where `provider` is a service-specific quota
    dimension and `Example Organization` is the provider name.

    Messages:
      AdditionalProperty: An additional property for a DimensionsValue object.

    Fields:
      additionalProperties: Additional properties of type DimensionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DimensionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  applicableLocations = _messages.StringField(1, repeated=True)
  details = _messages.MessageField('QuotaDetails', 2)
  dimensions = _messages.MessageField('DimensionsValue', 3)


class ListQuotaInfosResponse(_messages.Message):
  r"""Message for response to listing QuotaInfos

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    quotaInfos: The list of QuotaInfo
  """

  nextPageToken = _messages.StringField(1)
  quotaInfos = _messages.MessageField('QuotaInfo', 2, repeated=True)


class ListQuotaPreferencesResponse(_messages.Message):
  r"""Message for response to listing QuotaPreferences

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    quotaPreferences: The list of QuotaPreference
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  quotaPreferences = _messages.MessageField('QuotaPreference', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class QuotaAdjusterSettings(_messages.Message):
  r"""The QuotaAdjusterSettings resource defines the settings for the Quota

  Adjuster.

  Enums:
    EnablementValueValuesEnum: Optional. The configured value of the
      enablement at the given resource.

  Fields:
    enablement: Optional. The configured value of the enablement at the given
      resource.
    etag: Optional. The current ETag of the QuotaAdjusterSettings. If an ETag
      is provided on update and does not match the current server's ETag in
      the QuotaAdjusterSettings, the request is blocked and returns an ABORTED
      error. See https://google.aip.dev/134#etags for more details on ETags.
    inherited: Optional. Indicates whether the setting is inherited or
      explicitly specified.
    inheritedFrom: Output only. The resource container from which the setting
      is inherited. This refers to the nearest ancestor with enablement set
      (either ENABLED or DISABLED). The value can be an
      organizations/{organization_id}, folders/{folder_id}, or can be
      'default' if no ancestor exists with enablement set. The value will be
      empty when enablement is directly set on this container.
    name: Identifier. Name of the config would be of the format:
      projects/PROJECT_NUMBER/locations/global/quotaAdjusterSettings
      folders/FOLDER_NUMBER/locations/global/quotaAdjusterSettings
      organizations/ORGANIZATION_NUMBER/locations/global/quotaAdjusterSettings
    updateTime: Output only. The timestamp when the QuotaAdjusterSettings
      resource was last updated.
  """

  class EnablementValueValuesEnum(_messages.Enum):
    r"""Optional. The configured value of the enablement at the given
    resource.

    Values:
      ENABLEMENT_UNSPECIFIED: The quota adjuster is in an unknown state.
      ENABLED: The quota adjuster is enabled.
      DISABLED: The quota adjuster is disabled.
    """
    ENABLEMENT_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  enablement = _messages.EnumField('EnablementValueValuesEnum', 1)
  etag = _messages.StringField(2)
  inherited = _messages.BooleanField(3)
  inheritedFrom = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class QuotaConfig(_messages.Message):
  r"""The preferred quota configuration.

  Enums:
    RequestOriginValueValuesEnum: Output only. The origin of the quota
      preference request.

  Messages:
    AnnotationsValue: Optional. The annotations map for clients to store small
      amounts of arbitrary data. Do not put PII or other sensitive information
      here. See https://google.aip.dev/128#annotations

  Fields:
    annotations: Optional. The annotations map for clients to store small
      amounts of arbitrary data. Do not put PII or other sensitive information
      here. See https://google.aip.dev/128#annotations
    grantedValue: Output only. Granted quota value.
    preferredValue: Required. The preferred value. Must be greater than or
      equal to -1. If set to -1, it means the value is "unlimited".
    requestOrigin: Output only. The origin of the quota preference request.
    stateDetail: Output only. Optional details about the state of this quota
      preference.
    traceId: Output only. The trace id that the Google Cloud uses to provision
      the requested quota. This trace id may be used by the client to contact
      Cloud support to track the state of a quota preference request. The
      trace id is only produced for increase requests and is unique for each
      request. The quota decrease requests do not have a trace id.
  """

  class RequestOriginValueValuesEnum(_messages.Enum):
    r"""Output only. The origin of the quota preference request.

    Values:
      ORIGIN_UNSPECIFIED: The unspecified value.
      CLOUD_CONSOLE: Created through Cloud Console.
      AUTO_ADJUSTER: Generated by automatic quota adjustment.
    """
    ORIGIN_UNSPECIFIED = 0
    CLOUD_CONSOLE = 1
    AUTO_ADJUSTER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. The annotations map for clients to store small amounts of
    arbitrary data. Do not put PII or other sensitive information here. See
    https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  grantedValue = _messages.IntegerField(2)
  preferredValue = _messages.IntegerField(3)
  requestOrigin = _messages.EnumField('RequestOriginValueValuesEnum', 4)
  stateDetail = _messages.StringField(5)
  traceId = _messages.StringField(6)


class QuotaDetails(_messages.Message):
  r"""The quota details for a map of dimensions.

  Fields:
    rolloutInfo: Rollout information of this quota. This field is present only
      if the effective limit will change due to the ongoing rollout of the
      service config.
    value: The value currently in effect and being enforced.
  """

  rolloutInfo = _messages.MessageField('RolloutInfo', 1)
  value = _messages.IntegerField(2)


class QuotaIncreaseEligibility(_messages.Message):
  r"""Eligibility information regarding requesting increase adjustment of a
  quota.

  Enums:
    IneligibilityReasonValueValuesEnum: The reason of why it is ineligible to
      request increased value of the quota. If the is_eligible field is true,
      it defaults to INELIGIBILITY_REASON_UNSPECIFIED.

  Fields:
    ineligibilityReason: The reason of why it is ineligible to request
      increased value of the quota. If the is_eligible field is true, it
      defaults to INELIGIBILITY_REASON_UNSPECIFIED.
    isEligible: Whether a higher quota value can be requested for the quota.
  """

  class IneligibilityReasonValueValuesEnum(_messages.Enum):
    r"""The reason of why it is ineligible to request increased value of the
    quota. If the is_eligible field is true, it defaults to
    INELIGIBILITY_REASON_UNSPECIFIED.

    Values:
      INELIGIBILITY_REASON_UNSPECIFIED: Default value when is_eligible is
        true.
      NO_VALID_BILLING_ACCOUNT: The container is not linked with a valid
        billing account.
      NOT_SUPPORTED: Quota increase is not supported for the quota.
      NOT_ENOUGH_USAGE_HISTORY: There is not enough usage history to determine
        the eligibility.
      OTHER: Other reasons.
    """
    INELIGIBILITY_REASON_UNSPECIFIED = 0
    NO_VALID_BILLING_ACCOUNT = 1
    NOT_SUPPORTED = 2
    NOT_ENOUGH_USAGE_HISTORY = 3
    OTHER = 4

  ineligibilityReason = _messages.EnumField('IneligibilityReasonValueValuesEnum', 1)
  isEligible = _messages.BooleanField(2)


class QuotaInfo(_messages.Message):
  r"""QuotaInfo represents information about a particular quota for a given
  project, folder or organization.

  Enums:
    ContainerTypeValueValuesEnum: The container type of the QuotaInfo.

  Fields:
    containerType: The container type of the QuotaInfo.
    dimensions: The dimensions the quota is defined on.
    dimensionsInfos: The collection of dimensions info ordered by their
      dimensions from more specific ones to less specific ones.
    isConcurrent: Whether the quota is a concurrent quota. Concurrent quotas
      are enforced on the total number of concurrent operations in flight at
      any given time.
    isFixed: Whether the quota value is fixed or adjustable
    isPrecise: Whether this is a precise quota. A precise quota is tracked
      with absolute precision. In contrast, an imprecise quota is not tracked
      with precision.
    metric: The metric of the quota. It specifies the resources consumption
      the quota is defined for. For example, `compute.googleapis.com/cpus`
    metricDisplayName: The display name of the quota metric
    metricUnit: The unit in which the metric value is reported, e.g., "MByte".
    name: Resource name of this QuotaInfo. The ID component following
      "locations/" must be "global". For example, `projects/123/locations/glob
      al/services/compute.googleapis.com/quotaInfos/CpusPerProjectPerRegion`
    quotaDisplayName: The display name of the quota.
    quotaId: The id of the quota, which is unquie within the service. For
      example, `CpusPerProjectPerRegion`
    quotaIncreaseEligibility: Whether it is eligible to request a higher quota
      value for this quota.
    refreshInterval: The reset time interval for the quota. Refresh interval
      applies to rate quota only. For example, "minute" for per minute, "day"
      for per day, or "10 seconds" for every 10 seconds.
    service: The name of the service in which the quota is defined. For
      example, `compute.googleapis.com`
    serviceRequestQuotaUri: URI to the page where users can request more quota
      for the cloud service-for example, https://console.cloud.google.com/iam-
      admin/quotas.
  """

  class ContainerTypeValueValuesEnum(_messages.Enum):
    r"""The container type of the QuotaInfo.

    Values:
      CONTAINER_TYPE_UNSPECIFIED: Unspecified container type.
      PROJECT: consumer project
      FOLDER: folder
      ORGANIZATION: organization
    """
    CONTAINER_TYPE_UNSPECIFIED = 0
    PROJECT = 1
    FOLDER = 2
    ORGANIZATION = 3

  containerType = _messages.EnumField('ContainerTypeValueValuesEnum', 1)
  dimensions = _messages.StringField(2, repeated=True)
  dimensionsInfos = _messages.MessageField('DimensionsInfo', 3, repeated=True)
  isConcurrent = _messages.BooleanField(4)
  isFixed = _messages.BooleanField(5)
  isPrecise = _messages.BooleanField(6)
  metric = _messages.StringField(7)
  metricDisplayName = _messages.StringField(8)
  metricUnit = _messages.StringField(9)
  name = _messages.StringField(10)
  quotaDisplayName = _messages.StringField(11)
  quotaId = _messages.StringField(12)
  quotaIncreaseEligibility = _messages.MessageField('QuotaIncreaseEligibility', 13)
  refreshInterval = _messages.StringField(14)
  service = _messages.StringField(15)
  serviceRequestQuotaUri = _messages.StringField(16)


class QuotaPreference(_messages.Message):
  r"""QuotaPreference represents the preferred quota configuration specified
  for a project, folder or organization. There is only one QuotaPreference
  resource for a quota value targeting a unique set of dimensions.

  Messages:
    DimensionsValue: Immutable. The dimensions that this quota preference
      applies to. The key of the map entry is the name of a dimension, such as
      `region`, `zone`, `network_id`, and the value of the map entry is the
      dimension value. If a dimension is missing from the map of dimensions,
      the quota preference applies to all the dimension values except for
      those that have other quota preferences configured for the specific
      value. Note: QuotaPreferences can only be applied across all values of
      `user` and `resource` dimension. Do not set values for `user` or
      `resource` in the dimension map. For example: `{"provider" : "Example
      Organization"}` where `provider` is a service-specific quota dimension
      and `Example Organization` is the provider name.

  Fields:
    contactEmail: Input only. An email address that can be used to contact the
      user, in case Google Cloud needs more information to make a decision
      before additional quota can be granted. When requesting a quota
      increase, the email address is required. When requesting a quota
      decrease, the email address is optional. For example, the email address
      is optional when the `QuotaConfig.preferred_value` is smaller than the
      `QuotaDetails.reset_value`.
    createTime: Output only. Create time stamp
    dimensions: Immutable. The dimensions that this quota preference applies
      to. The key of the map entry is the name of a dimension, such as
      `region`, `zone`, `network_id`, and the value of the map entry is the
      dimension value. If a dimension is missing from the map of dimensions,
      the quota preference applies to all the dimension values except for
      those that have other quota preferences configured for the specific
      value. Note: QuotaPreferences can only be applied across all values of
      `user` and `resource` dimension. Do not set values for `user` or
      `resource` in the dimension map. For example: `{"provider" : "Example
      Organization"}` where `provider` is a service-specific quota dimension
      and `Example Organization` is the provider name.
    etag: Optional. The current etag of the quota preference. If an etag is
      provided on update and does not match the current server's etag of the
      quota preference, the request will be blocked and an ABORTED error will
      be returned. See https://google.aip.dev/134#etags for more details on
      etags.
    justification: The reason / justification for this quota preference.
    name: Required except in the CREATE requests. The resource name of the
      quota preference. The path that follows `/locations` must be `/global`.
      For example: `projects/123/locations/global/quotaPreferences/my-config-
      for-us-east1`
    quotaConfig: Required. Preferred quota configuration.
    quotaId: Required. The id of the quota to which the quota preference is
      applied. A quota name is unique in the service. For example,
      `CpusPerProjectPerRegion`
    reconciling: Output only. Is the quota preference pending Google Cloud
      approval and fulfillment.
    service: Required. The name of the service to which the quota preference
      is applied.
    updateTime: Output only. Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DimensionsValue(_messages.Message):
    r"""Immutable. The dimensions that this quota preference applies to. The
    key of the map entry is the name of a dimension, such as `region`, `zone`,
    `network_id`, and the value of the map entry is the dimension value. If a
    dimension is missing from the map of dimensions, the quota preference
    applies to all the dimension values except for those that have other quota
    preferences configured for the specific value. Note: QuotaPreferences can
    only be applied across all values of `user` and `resource` dimension. Do
    not set values for `user` or `resource` in the dimension map. For example:
    `{"provider" : "Example Organization"}` where `provider` is a service-
    specific quota dimension and `Example Organization` is the provider name.

    Messages:
      AdditionalProperty: An additional property for a DimensionsValue object.

    Fields:
      additionalProperties: Additional properties of type DimensionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DimensionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contactEmail = _messages.StringField(1)
  createTime = _messages.StringField(2)
  dimensions = _messages.MessageField('DimensionsValue', 3)
  etag = _messages.StringField(4)
  justification = _messages.StringField(5)
  name = _messages.StringField(6)
  quotaConfig = _messages.MessageField('QuotaConfig', 7)
  quotaId = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  service = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class RolloutInfo(_messages.Message):
  r"""[Output only] Rollout information of a quota.

  Fields:
    ongoingRollout: Whether there is an ongoing rollout for a quota or not.
  """

  ongoingRollout = _messages.BooleanField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
