"""Generated client library for cloudbuild version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudbuild.v1 import cloudbuild_v1_messages as messages


class CloudbuildV1(base_api.BaseApiClient):
  """Generated client library for service cloudbuild version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudbuild.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudbuild.mtls.googleapis.com/'

  _PACKAGE = 'cloudbuild'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudbuildV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudbuild handle."""
    url = url or self.BASE_URL
    super(CloudbuildV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.github_installations_installations = self.GithubInstallationsInstallationsService(self)
    self.github_installations_projects = self.GithubInstallationsProjectsService(self)
    self.github_installations = self.GithubInstallationsService(self)
    self.github = self.GithubService(self)
    self.githubDotComWebhook = self.GithubDotComWebhookService(self)
    self.installations_installations = self.InstallationsInstallationsService(self)
    self.installations = self.InstallationsService(self)
    self.locations = self.LocationsService(self)
    self.oauth = self.OauthService(self)
    self.operations = self.OperationsService(self)
    self.projects_builds = self.ProjectsBuildsService(self)
    self.projects_github_installations = self.ProjectsGithubInstallationsService(self)
    self.projects_github = self.ProjectsGithubService(self)
    self.projects_githubEnterpriseConfigs = self.ProjectsGithubEnterpriseConfigsService(self)
    self.projects_installations = self.ProjectsInstallationsService(self)
    self.projects_locations_bitbucketServerConfigs_connectedRepositories = self.ProjectsLocationsBitbucketServerConfigsConnectedRepositoriesService(self)
    self.projects_locations_bitbucketServerConfigs_repos = self.ProjectsLocationsBitbucketServerConfigsReposService(self)
    self.projects_locations_bitbucketServerConfigs = self.ProjectsLocationsBitbucketServerConfigsService(self)
    self.projects_locations_builds = self.ProjectsLocationsBuildsService(self)
    self.projects_locations_gitLabConfigs_connectedRepositories = self.ProjectsLocationsGitLabConfigsConnectedRepositoriesService(self)
    self.projects_locations_gitLabConfigs_repos = self.ProjectsLocationsGitLabConfigsReposService(self)
    self.projects_locations_gitLabConfigs = self.ProjectsLocationsGitLabConfigsService(self)
    self.projects_locations_github_installations = self.ProjectsLocationsGithubInstallationsService(self)
    self.projects_locations_github = self.ProjectsLocationsGithubService(self)
    self.projects_locations_githubEnterpriseConfigs = self.ProjectsLocationsGithubEnterpriseConfigsService(self)
    self.projects_locations_installations = self.ProjectsLocationsInstallationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_triggers = self.ProjectsLocationsTriggersService(self)
    self.projects_locations_workerPools = self.ProjectsLocationsWorkerPoolsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_triggers = self.ProjectsTriggersService(self)
    self.projects = self.ProjectsService(self)
    self.v1 = self.V1Service(self)

  class GithubInstallationsInstallationsService(base_api.BaseApiService):
    """Service class for the github_installations_installations resource."""

    _NAME = 'github_installations_installations'

    def __init__(self, client):
      super(CloudbuildV1.GithubInstallationsInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""For given installation id, list project-installation mappings across all GCB projects visible to the caller. This API is experimental.

      Args:
        request: (CloudbuildGithubInstallationsInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.github.installations.installations.list',
        ordered_params=['installationId'],
        path_params=['installationId'],
        query_params=[],
        relative_path='v1/github/installations/{installationId}/installations',
        request_field='',
        request_type_name='CloudbuildGithubInstallationsInstallationsListRequest',
        response_type_name='ListGitHubInstallationsResponse',
        supports_download=False,
    )

  class GithubInstallationsProjectsService(base_api.BaseApiService):
    """Service class for the github_installations_projects resource."""

    _NAME = 'github_installations_projects'

    def __init__(self, client):
      super(CloudbuildV1.GithubInstallationsProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""For given installation id, list project-installation mappings across all GCB projects visible to the caller. This API is experimental.

      Args:
        request: (CloudbuildGithubInstallationsProjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.github.installations.projects.list',
        ordered_params=['installationId'],
        path_params=['installationId'],
        query_params=[],
        relative_path='v1/github/installations/{installationId}/projects',
        request_field='',
        request_type_name='CloudbuildGithubInstallationsProjectsListRequest',
        response_type_name='ListGitHubInstallationsResponse',
        supports_download=False,
    )

  class GithubInstallationsService(base_api.BaseApiService):
    """Service class for the github_installations resource."""

    _NAME = 'github_installations'

    def __init__(self, client):
      super(CloudbuildV1.GithubInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

  class GithubService(base_api.BaseApiService):
    """Service class for the github resource."""

    _NAME = 'github'

    def __init__(self, client):
      super(CloudbuildV1.GithubService, self).__init__(client)
      self._upload_configs = {
          }

  class GithubDotComWebhookService(base_api.BaseApiService):
    """Service class for the githubDotComWebhook resource."""

    _NAME = 'githubDotComWebhook'

    def __init__(self, client):
      super(CloudbuildV1.GithubDotComWebhookService, self).__init__(client)
      self._upload_configs = {
          }

    def Receive(self, request, global_params=None):
      r"""ReceiveGitHubDotComWebhook is called when the API receives a github.com webhook.

      Args:
        request: (CloudbuildGithubDotComWebhookReceiveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Receive')
      return self._RunMethod(
          config, request, global_params=global_params)

    Receive.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.githubDotComWebhook.receive',
        ordered_params=[],
        path_params=[],
        query_params=['webhookKey'],
        relative_path='v1/githubDotComWebhook:receive',
        request_field='httpBody',
        request_type_name='CloudbuildGithubDotComWebhookReceiveRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class InstallationsInstallationsService(base_api.BaseApiService):
    """Service class for the installations_installations resource."""

    _NAME = 'installations_installations'

    def __init__(self, client):
      super(CloudbuildV1.InstallationsInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""For given installation id, list project-installation mappings across all GCB projects visible to the caller. This API is experimental.

      Args:
        request: (CloudbuildInstallationsInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.installations.installations.list',
        ordered_params=['installationId'],
        path_params=['installationId'],
        query_params=[],
        relative_path='v1/installations/{installationId}/installations',
        request_field='',
        request_type_name='CloudbuildInstallationsInstallationsListRequest',
        response_type_name='ListGitHubInstallationsResponse',
        supports_download=False,
    )

  class InstallationsService(base_api.BaseApiService):
    """Service class for the installations resource."""

    _NAME = 'installations'

    def __init__(self, client):
      super(CloudbuildV1.InstallationsService, self).__init__(client)
      self._upload_configs = {
          }

  class LocationsService(base_api.BaseApiService):
    """Service class for the locations resource."""

    _NAME = 'locations'

    def __init__(self, client):
      super(CloudbuildV1.LocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def RegionalWebhook(self, request, global_params=None):
      r"""ReceiveRegionalWebhook is called when the API receives a regional GitHub webhook.

      Args:
        request: (CloudbuildLocationsRegionalWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('RegionalWebhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    RegionalWebhook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/regionalWebhook',
        http_method='POST',
        method_id='cloudbuild.locations.regionalWebhook',
        ordered_params=['location'],
        path_params=['location'],
        query_params=['webhookKey'],
        relative_path='v1/{+location}/regionalWebhook',
        request_field='httpBody',
        request_type_name='CloudbuildLocationsRegionalWebhookRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class OauthService(base_api.BaseApiService):
    """Service class for the oauth resource."""

    _NAME = 'oauth'

    def __init__(self, client):
      super(CloudbuildV1.OauthService, self).__init__(client)
      self._upload_configs = {
          }

    def GetRegistration(self, request, global_params=None):
      r"""Get a URL that a customer should use to initiate an OAuth flow on an external source provider. This API is experimental.

      Args:
        request: (CloudbuildOauthGetRegistrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OAuthRegistrationURI) The response message.
      """
      config = self.GetMethodConfig('GetRegistration')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetRegistration.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.oauth.getRegistration',
        ordered_params=[],
        path_params=[],
        query_params=['authUser', 'csesidx', 'githubEnterpriseConfig', 'hostUrl', 'namespace'],
        relative_path='v1/oauth/registration',
        request_field='',
        request_type_name='CloudbuildOauthGetRegistrationRequest',
        response_type_name='OAuthRegistrationURI',
        supports_download=False,
    )

    def ProcessOAuthCallback(self, request, global_params=None):
      r"""ProcessOAuthCallback fulfills the last leg of the OAuth dance with a source provider. For GitHub this is as defined by https://developer.github.com/apps/building-oauth-apps/authorizing-oauth-apps/#2-users-are-redirected-back-to-your-site-by-github Users will not be able to call this in any meaningful way since they don't have access to the OAuth code used in the exchange. For now, this rpc only supports GitHubEnterprise, but will eventually replace GenerateGitHubAccessToken.

      Args:
        request: (CloudbuildOauthProcessOAuthCallbackRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('ProcessOAuthCallback')
      return self._RunMethod(
          config, request, global_params=global_params)

    ProcessOAuthCallback.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.oauth.processOAuthCallback',
        ordered_params=[],
        path_params=[],
        query_params=['code', 'githubEnterpriseConfig', 'hostUrl', 'namespace', 'state'],
        relative_path='v1/oauth:processOAuthCallback',
        request_field='',
        request_type_name='CloudbuildOauthProcessOAuthCallbackRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(CloudbuildV1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (CloudbuildOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='cloudbuild.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='CloudbuildOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudbuildOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}',
        http_method='GET',
        method_id='cloudbuild.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsBuildsService(base_api.BaseApiService):
    """Service class for the projects_builds resource."""

    _NAME = 'projects_builds'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsBuildsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""Approves or rejects a pending build. If approved, the returned LRO will be analogous to the LRO returned from a CreateBuild call. If rejected, the returned LRO will be immediately done.

      Args:
        request: (CloudbuildProjectsBuildsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/builds/{buildsId}:approve',
        http_method='POST',
        method_id='cloudbuild.projects.builds.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:approve',
        request_field='approveBuildRequest',
        request_type_name='CloudbuildProjectsBuildsApproveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Cancels a build in progress.

      Args:
        request: (CloudbuildProjectsBuildsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Build) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.builds.cancel',
        ordered_params=['projectId', 'id'],
        path_params=['id', 'projectId'],
        query_params=[],
        relative_path='v1/projects/{projectId}/builds/{id}:cancel',
        request_field='cancelBuildRequest',
        request_type_name='CloudbuildProjectsBuildsCancelRequest',
        response_type_name='Build',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Starts a build with the specified configuration. This method returns a long-running `Operation`, which includes the build ID. Pass the build ID to `GetBuild` to determine the build status (such as `SUCCESS` or `FAILURE`).

      Args:
        request: (CloudbuildProjectsBuildsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.builds.create',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent'],
        relative_path='v1/projects/{projectId}/builds',
        request_field='build',
        request_type_name='CloudbuildProjectsBuildsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns information about a previously requested build. The `Build` that is returned includes its status (such as `SUCCESS`, `FAILURE`, or `WORKING`), and timing information.

      Args:
        request: (CloudbuildProjectsBuildsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Build) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.builds.get',
        ordered_params=['projectId', 'id'],
        path_params=['id', 'projectId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/builds/{id}',
        request_field='',
        request_type_name='CloudbuildProjectsBuildsGetRequest',
        response_type_name='Build',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists previously requested builds. Previously requested builds may still be in-progress, or may have finished successfully or unsuccessfully.

      Args:
        request: (CloudbuildProjectsBuildsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBuildsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.builds.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['filter', 'pageSize', 'pageToken', 'parent'],
        relative_path='v1/projects/{projectId}/builds',
        request_field='',
        request_type_name='CloudbuildProjectsBuildsListRequest',
        response_type_name='ListBuildsResponse',
        supports_download=False,
    )

    def Retry(self, request, global_params=None):
      r"""Creates a new build based on the specified build. This method creates a new build using the original build request, which may or may not result in an identical build. For triggered builds: * Triggered builds resolve to a precise revision; therefore a retry of a triggered build will result in a build that uses the same revision. For non-triggered builds that specify `RepoSource`: * If the original build built from the tip of a branch, the retried build will build from the tip of that branch, which may not be the same revision as the original build. * If the original build specified a commit sha or revision ID, the retried build will use the identical source. For builds that specify `StorageSource`: * If the original build pulled source from Cloud Storage without specifying the generation of the object, the new build will use the current object, which may be different from the original build source. * If the original build pulled source from Cloud Storage and specified the generation of the object, the new build will attempt to use the same object, which may or may not be available depending on the bucket's lifecycle management settings.

      Args:
        request: (RetryBuildRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Retry')
      return self._RunMethod(
          config, request, global_params=global_params)

    Retry.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.builds.retry',
        ordered_params=['projectId', 'id'],
        path_params=['id', 'projectId'],
        query_params=[],
        relative_path='v1/projects/{projectId}/builds/{id}:retry',
        request_field='<request>',
        request_type_name='RetryBuildRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsGithubInstallationsService(base_api.BaseApiService):
    """Service class for the projects_github_installations resource."""

    _NAME = 'projects_github_installations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsGithubInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsGithubInstallationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.github.installations.create',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent', 'projectId1', 'userOauthCode'],
        relative_path='v1/projects/{projectId}/github/installations',
        request_field='installation',
        request_type_name='CloudbuildProjectsGithubInstallationsCreateRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsGithubInstallationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='cloudbuild.projects.github.installations.delete',
        ordered_params=['projectId', 'installationId'],
        path_params=['installationId', 'projectId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/github/installations/{installationId}',
        request_field='',
        request_type_name='CloudbuildProjectsGithubInstallationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all Installations for a given project id. This API is experimental.

      Args:
        request: (CloudbuildProjectsGithubInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsForProjectResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.github.installations.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent'],
        relative_path='v1/projects/{projectId}/github/installations',
        request_field='',
        request_type_name='CloudbuildProjectsGithubInstallationsListRequest',
        response_type_name='ListGitHubInstallationsForProjectResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update settings for a GCP project to GitHub installation mapping. This API is experimental.

      Args:
        request: (CloudbuildProjectsGithubInstallationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='cloudbuild.projects.github.installations.patch',
        ordered_params=['projectId', 'id'],
        path_params=['id', 'projectId'],
        query_params=['installationId', 'name', 'projectId1', 'updateMask'],
        relative_path='v1/projects/{projectId}/github/installations/{id}',
        request_field='installation',
        request_type_name='CloudbuildProjectsGithubInstallationsPatchRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsGithubService(base_api.BaseApiService):
    """Service class for the projects_github resource."""

    _NAME = 'projects_github'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsGithubService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsGithubEnterpriseConfigsService(base_api.BaseApiService):
    """Service class for the projects_githubEnterpriseConfigs resource."""

    _NAME = 'projects_githubEnterpriseConfigs'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsGithubEnterpriseConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs',
        http_method='POST',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gheConfigId', 'projectId'],
        relative_path='v1/{+parent}/githubEnterpriseConfigs',
        request_field='gitHubEnterpriseConfig',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['configId', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a GitHubEnterpriseConfig.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GitHubEnterpriseConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='GET',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['configId', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsGetRequest',
        response_type_name='GitHubEnterpriseConfig',
        supports_download=False,
    )

    def GetApp(self, request, global_params=None):
      r"""Get the GitHub App associated with a GitHub Enterprise Config. Uses the GitHub API: https://developer.github.com/enterprise/2.21/v3/apps/#get-an-app This API is experimental.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsGetAppRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GitHubEnterpriseApp) The response message.
      """
      config = self.GetMethodConfig('GetApp')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetApp.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}/app',
        http_method='GET',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.getApp',
        ordered_params=['enterpriseConfigResource'],
        path_params=['enterpriseConfigResource'],
        query_params=[],
        relative_path='v1/{+enterpriseConfigResource}/app',
        request_field='',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsGetAppRequest',
        response_type_name='GitHubEnterpriseApp',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all GitHubEnterpriseConfigs for a given project.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGithubEnterpriseConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs',
        http_method='GET',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/githubEnterpriseConfigs',
        request_field='',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsListRequest',
        response_type_name='ListGithubEnterpriseConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsGithubEnterpriseConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.githubEnterpriseConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='gitHubEnterpriseConfig',
        request_type_name='CloudbuildProjectsGithubEnterpriseConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsInstallationsService(base_api.BaseApiService):
    """Service class for the projects_installations resource."""

    _NAME = 'projects_installations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsInstallationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.installations.create',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent', 'userOauthCode'],
        relative_path='v1/projects/{projectId}/installations',
        request_field='installation',
        request_type_name='CloudbuildProjectsInstallationsCreateRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsInstallationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='cloudbuild.projects.installations.delete',
        ordered_params=['projectId', 'installationId'],
        path_params=['installationId', 'projectId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/installations/{installationId}',
        request_field='',
        request_type_name='CloudbuildProjectsInstallationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all Installations for a given project id. This API is experimental.

      Args:
        request: (CloudbuildProjectsInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsForProjectResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.installations.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent'],
        relative_path='v1/projects/{projectId}/installations',
        request_field='',
        request_type_name='CloudbuildProjectsInstallationsListRequest',
        response_type_name='ListGitHubInstallationsForProjectResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update settings for a GCP project to GitHub installation mapping. This API is experimental.

      Args:
        request: (CloudbuildProjectsInstallationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='cloudbuild.projects.installations.patch',
        ordered_params=['projectNum', 'id'],
        path_params=['id', 'projectNum'],
        query_params=['installationId', 'name', 'projectId', 'updateMask'],
        relative_path='v1/projects/{projectNum}/installations/{id}',
        request_field='installation',
        request_type_name='CloudbuildProjectsInstallationsPatchRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsLocationsBitbucketServerConfigsConnectedRepositoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_bitbucketServerConfigs_connectedRepositories resource."""

    _NAME = 'projects_locations_bitbucketServerConfigs_connectedRepositories'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsBitbucketServerConfigsConnectedRepositoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch connecting Bitbucket Server repositories to Cloud Build.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsConnectedRepositoriesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}/connectedRepositories:batchCreate',
        http_method='POST',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.connectedRepositories.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/connectedRepositories:batchCreate',
        request_field='batchCreateBitbucketServerConnectedRepositoriesRequest',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsConnectedRepositoriesBatchCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBitbucketServerConfigsReposService(base_api.BaseApiService):
    """Service class for the projects_locations_bitbucketServerConfigs_repos resource."""

    _NAME = 'projects_locations_bitbucketServerConfigs_repos'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsBitbucketServerConfigsReposService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List all repositories for a given `BitbucketServerConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsReposListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBitbucketServerRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}/repos',
        http_method='GET',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.repos.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/repos',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsReposListRequest',
        response_type_name='ListBitbucketServerRepositoriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsBitbucketServerConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_bitbucketServerConfigs resource."""

    _NAME = 'projects_locations_bitbucketServerConfigs'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsBitbucketServerConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `BitbucketServerConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs',
        http_method='POST',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bitbucketServerConfigId'],
        relative_path='v1/{+parent}/bitbucketServerConfigs',
        request_field='bitbucketServerConfig',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a `BitbucketServerConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a `BitbucketServerConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BitbucketServerConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsGetRequest',
        response_type_name='BitbucketServerConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all `BitbucketServerConfigs` for a given project. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBitbucketServerConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs',
        http_method='GET',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bitbucketServerConfigs',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsListRequest',
        response_type_name='ListBitbucketServerConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing `BitbucketServerConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='bitbucketServerConfig',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveBitbucketServerConnectedRepository(self, request, global_params=None):
      r"""Remove a Bitbucket Server repository from a given BitbucketServerConfig's connected repositories. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsBitbucketServerConfigsRemoveBitbucketServerConnectedRepositoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('RemoveBitbucketServerConnectedRepository')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveBitbucketServerConnectedRepository.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bitbucketServerConfigs/{bitbucketServerConfigsId}:removeBitbucketServerConnectedRepository',
        http_method='POST',
        method_id='cloudbuild.projects.locations.bitbucketServerConfigs.removeBitbucketServerConnectedRepository',
        ordered_params=['config'],
        path_params=['config'],
        query_params=[],
        relative_path='v1/{+config}:removeBitbucketServerConnectedRepository',
        request_field='removeBitbucketServerConnectedRepositoryRequest',
        request_type_name='CloudbuildProjectsLocationsBitbucketServerConfigsRemoveBitbucketServerConnectedRepositoryRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsLocationsBuildsService(base_api.BaseApiService):
    """Service class for the projects_locations_builds resource."""

    _NAME = 'projects_locations_builds'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsBuildsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""Approves or rejects a pending build. If approved, the returned LRO will be analogous to the LRO returned from a CreateBuild call. If rejected, the returned LRO will be immediately done.

      Args:
        request: (CloudbuildProjectsLocationsBuildsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds/{buildsId}:approve',
        http_method='POST',
        method_id='cloudbuild.projects.locations.builds.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:approve',
        request_field='approveBuildRequest',
        request_type_name='CloudbuildProjectsLocationsBuildsApproveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Cancels a build in progress.

      Args:
        request: (CancelBuildRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Build) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds/{buildsId}:cancel',
        http_method='POST',
        method_id='cloudbuild.projects.locations.builds.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='<request>',
        request_type_name='CancelBuildRequest',
        response_type_name='Build',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Starts a build with the specified configuration. This method returns a long-running `Operation`, which includes the build ID. Pass the build ID to `GetBuild` to determine the build status (such as `SUCCESS` or `FAILURE`).

      Args:
        request: (CloudbuildProjectsLocationsBuildsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds',
        http_method='POST',
        method_id='cloudbuild.projects.locations.builds.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/builds',
        request_field='build',
        request_type_name='CloudbuildProjectsLocationsBuildsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns information about a previously requested build. The `Build` that is returned includes its status (such as `SUCCESS`, `FAILURE`, or `WORKING`), and timing information.

      Args:
        request: (CloudbuildProjectsLocationsBuildsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Build) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds/{buildsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.builds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['id', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBuildsGetRequest',
        response_type_name='Build',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists previously requested builds. Previously requested builds may still be in-progress, or may have finished successfully or unsuccessfully.

      Args:
        request: (CloudbuildProjectsLocationsBuildsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBuildsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds',
        http_method='GET',
        method_id='cloudbuild.projects.locations.builds.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'projectId'],
        relative_path='v1/{+parent}/builds',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsBuildsListRequest',
        response_type_name='ListBuildsResponse',
        supports_download=False,
    )

    def Retry(self, request, global_params=None):
      r"""Creates a new build based on the specified build. This method creates a new build using the original build request, which may or may not result in an identical build. For triggered builds: * Triggered builds resolve to a precise revision; therefore a retry of a triggered build will result in a build that uses the same revision. For non-triggered builds that specify `RepoSource`: * If the original build built from the tip of a branch, the retried build will build from the tip of that branch, which may not be the same revision as the original build. * If the original build specified a commit sha or revision ID, the retried build will use the identical source. For builds that specify `StorageSource`: * If the original build pulled source from Cloud Storage without specifying the generation of the object, the new build will use the current object, which may be different from the original build source. * If the original build pulled source from Cloud Storage and specified the generation of the object, the new build will attempt to use the same object, which may or may not be available depending on the bucket's lifecycle management settings.

      Args:
        request: (RetryBuildRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Retry')
      return self._RunMethod(
          config, request, global_params=global_params)

    Retry.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/builds/{buildsId}:retry',
        http_method='POST',
        method_id='cloudbuild.projects.locations.builds.retry',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:retry',
        request_field='<request>',
        request_type_name='RetryBuildRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGitLabConfigsConnectedRepositoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_gitLabConfigs_connectedRepositories resource."""

    _NAME = 'projects_locations_gitLabConfigs_connectedRepositories'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGitLabConfigsConnectedRepositoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch connecting GitLab repositories to Cloud Build. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsConnectedRepositoriesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}/connectedRepositories:batchCreate',
        http_method='POST',
        method_id='cloudbuild.projects.locations.gitLabConfigs.connectedRepositories.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/connectedRepositories:batchCreate',
        request_field='batchCreateGitLabConnectedRepositoriesRequest',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsConnectedRepositoriesBatchCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGitLabConfigsReposService(base_api.BaseApiService):
    """Service class for the projects_locations_gitLabConfigs_repos resource."""

    _NAME = 'projects_locations_gitLabConfigs_repos'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGitLabConfigsReposService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List all repositories for a given `GitLabConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsReposListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitLabRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}/repos',
        http_method='GET',
        method_id='cloudbuild.projects.locations.gitLabConfigs.repos.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/repos',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsReposListRequest',
        response_type_name='ListGitLabRepositoriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsGitLabConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_gitLabConfigs resource."""

    _NAME = 'projects_locations_gitLabConfigs'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGitLabConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `GitLabConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs',
        http_method='POST',
        method_id='cloudbuild.projects.locations.gitLabConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gitlabConfigId'],
        relative_path='v1/{+parent}/gitLabConfigs',
        request_field='gitLabConfig',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a `GitLabConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.gitLabConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `GitLabConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GitLabConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.gitLabConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsGetRequest',
        response_type_name='GitLabConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all `GitLabConfigs` for a given project. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitLabConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs',
        http_method='GET',
        method_id='cloudbuild.projects.locations.gitLabConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/gitLabConfigs',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsListRequest',
        response_type_name='ListGitLabConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing `GitLabConfig`. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.gitLabConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='gitLabConfig',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveGitLabConnectedRepository(self, request, global_params=None):
      r"""Remove a GitLab repository from a given GitLabConfig's connected repositories. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGitLabConfigsRemoveGitLabConnectedRepositoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('RemoveGitLabConnectedRepository')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveGitLabConnectedRepository.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/gitLabConfigs/{gitLabConfigsId}:removeGitLabConnectedRepository',
        http_method='POST',
        method_id='cloudbuild.projects.locations.gitLabConfigs.removeGitLabConnectedRepository',
        ordered_params=['config'],
        path_params=['config'],
        query_params=[],
        relative_path='v1/{+config}:removeGitLabConnectedRepository',
        request_field='removeGitLabConnectedRepositoryRequest',
        request_type_name='CloudbuildProjectsLocationsGitLabConfigsRemoveGitLabConnectedRepositoryRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsLocationsGithubInstallationsService(base_api.BaseApiService):
    """Service class for the projects_locations_github_installations resource."""

    _NAME = 'projects_locations_github_installations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGithubInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGithubInstallationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/github/installations',
        http_method='POST',
        method_id='cloudbuild.projects.locations.github.installations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId', 'userOauthCode'],
        relative_path='v1/{+parent}/github/installations',
        request_field='installation',
        request_type_name='CloudbuildProjectsLocationsGithubInstallationsCreateRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGithubInstallationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/github/installations/{installationsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.github.installations.delete',
        ordered_params=['name', 'installationsId'],
        path_params=['installationsId', 'name'],
        query_params=['installationId', 'projectId'],
        relative_path='v1/{+name}/github/installations/{installationsId}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubInstallationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all Installations for a given project id. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGithubInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsForProjectResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/github/installations',
        http_method='GET',
        method_id='cloudbuild.projects.locations.github.installations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/github/installations',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubInstallationsListRequest',
        response_type_name='ListGitHubInstallationsForProjectResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update settings for a GCP project to GitHub installation mapping. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGithubInstallationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/github/installations/{installationsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.github.installations.patch',
        ordered_params=['name', 'installationsId'],
        path_params=['installationsId', 'name'],
        query_params=['installationId', 'name1', 'projectId', 'updateMask'],
        relative_path='v1/{+name}/github/installations/{installationsId}',
        request_field='installation',
        request_type_name='CloudbuildProjectsLocationsGithubInstallationsPatchRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsLocationsGithubService(base_api.BaseApiService):
    """Service class for the projects_locations_github resource."""

    _NAME = 'projects_locations_github'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGithubService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsGithubEnterpriseConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_githubEnterpriseConfigs resource."""

    _NAME = 'projects_locations_githubEnterpriseConfigs'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsGithubEnterpriseConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs',
        http_method='POST',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gheConfigId', 'projectId'],
        relative_path='v1/{+parent}/githubEnterpriseConfigs',
        request_field='gitHubEnterpriseConfig',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['configId', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a GitHubEnterpriseConfig.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GitHubEnterpriseConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['configId', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsGetRequest',
        response_type_name='GitHubEnterpriseConfig',
        supports_download=False,
    )

    def GetApp(self, request, global_params=None):
      r"""Get the GitHub App associated with a GitHub Enterprise Config. Uses the GitHub API: https://developer.github.com/enterprise/2.21/v3/apps/#get-an-app This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsGetAppRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GitHubEnterpriseApp) The response message.
      """
      config = self.GetMethodConfig('GetApp')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetApp.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}/app',
        http_method='GET',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.getApp',
        ordered_params=['enterpriseConfigResource'],
        path_params=['enterpriseConfigResource'],
        query_params=[],
        relative_path='v1/{+enterpriseConfigResource}/app',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsGetAppRequest',
        response_type_name='GitHubEnterpriseApp',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all GitHubEnterpriseConfigs for a given project.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGithubEnterpriseConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs',
        http_method='GET',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/githubEnterpriseConfigs',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsListRequest',
        response_type_name='ListGithubEnterpriseConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an association between a GCP project and a GitHub Enterprise server.

      Args:
        request: (CloudbuildProjectsLocationsGithubEnterpriseConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/githubEnterpriseConfigs/{githubEnterpriseConfigsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.githubEnterpriseConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='gitHubEnterpriseConfig',
        request_type_name='CloudbuildProjectsLocationsGithubEnterpriseConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsInstallationsService(base_api.BaseApiService):
    """Service class for the projects_locations_installations resource."""

    _NAME = 'projects_locations_installations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsInstallationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete an association between a GCP project and a GitHub installation. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsInstallationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/installations/{installationsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.installations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['installationId', 'projectId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsInstallationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all Installations for a given project id. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsInstallationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGitHubInstallationsForProjectResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/installations',
        http_method='GET',
        method_id='cloudbuild.projects.locations.installations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/installations',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsInstallationsListRequest',
        response_type_name='ListGitHubInstallationsForProjectResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update settings for a GCP project to GitHub installation mapping. This API is experimental.

      Args:
        request: (CloudbuildProjectsLocationsInstallationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/installations/{installationsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.installations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['installationId', 'name1', 'projectId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='installation',
        request_type_name='CloudbuildProjectsLocationsInstallationsPatchRequest',
        response_type_name='Empty',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (CloudbuildProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='cloudbuild.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='CloudbuildProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudbuildProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsTriggersService(base_api.BaseApiService):
    """Service class for the projects_locations_triggers resource."""

    _NAME = 'projects_locations_triggers'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `BuildTrigger`.

      Args:
        request: (CloudbuildProjectsLocationsTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers',
        http_method='POST',
        method_id='cloudbuild.projects.locations.triggers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['projectId'],
        relative_path='v1/{+parent}/triggers',
        request_field='buildTrigger',
        request_type_name='CloudbuildProjectsLocationsTriggersCreateRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `BuildTrigger` by its project ID and trigger ID.

      Args:
        request: (CloudbuildProjectsLocationsTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.triggers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['projectId', 'triggerId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsTriggersDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns information about a `BuildTrigger`.

      Args:
        request: (CloudbuildProjectsLocationsTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.triggers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['projectId', 'triggerId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsTriggersGetRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists existing `BuildTrigger`s.

      Args:
        request: (CloudbuildProjectsLocationsTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBuildTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers',
        http_method='GET',
        method_id='cloudbuild.projects.locations.triggers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'projectId'],
        relative_path='v1/{+parent}/triggers',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsTriggersListRequest',
        response_type_name='ListBuildTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `BuildTrigger` by its project ID and trigger ID.

      Args:
        request: (CloudbuildProjectsLocationsTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.triggers.patch',
        ordered_params=['resourceName'],
        path_params=['resourceName'],
        query_params=['projectId', 'triggerId', 'updateMask'],
        relative_path='v1/{+resourceName}',
        request_field='buildTrigger',
        request_type_name='CloudbuildProjectsLocationsTriggersPatchRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Runs a `BuildTrigger` at a particular source revision. To run a regional or global trigger, use the POST request that includes the location endpoint in the path (ex. v1/projects/{projectId}/locations/{region}/triggers/{triggerId}:run). The POST request that does not include the location endpoint in the path can only be used when running global triggers.

      Args:
        request: (CloudbuildProjectsLocationsTriggersRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:run',
        http_method='POST',
        method_id='cloudbuild.projects.locations.triggers.run',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:run',
        request_field='runBuildTriggerRequest',
        request_type_name='CloudbuildProjectsLocationsTriggersRunRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Webhook(self, request, global_params=None):
      r"""ReceiveTriggerWebhook [Experimental] is called when the API receives a webhook request targeted at a specific trigger.

      Args:
        request: (CloudbuildProjectsLocationsTriggersWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ReceiveTriggerWebhookResponse) The response message.
      """
      config = self.GetMethodConfig('Webhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    Webhook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:webhook',
        http_method='POST',
        method_id='cloudbuild.projects.locations.triggers.webhook',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['projectId', 'secret', 'trigger'],
        relative_path='v1/{+name}:webhook',
        request_field='httpBody',
        request_type_name='CloudbuildProjectsLocationsTriggersWebhookRequest',
        response_type_name='ReceiveTriggerWebhookResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkerPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_workerPools resource."""

    _NAME = 'projects_locations_workerPools'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsWorkerPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a `WorkerPool`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerPools',
        http_method='POST',
        method_id='cloudbuild.projects.locations.workerPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workerPoolId'],
        relative_path='v1/{+parent}/workerPools',
        request_field='workerPool',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `WorkerPool`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.workerPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns details of a `WorkerPool`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkerPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workerPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolsGetRequest',
        response_type_name='WorkerPool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WorkerPool`s.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkerPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerPools',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workerPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/workerPools',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolsListRequest',
        response_type_name='ListWorkerPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `WorkerPool`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.workerPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='workerPool',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetDefaultServiceAccount(self, request, global_params=None):
      r"""Returns the `DefaultServiceAccount` used by the project.

      Args:
        request: (CloudbuildProjectsLocationsGetDefaultServiceAccountRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DefaultServiceAccount) The response message.
      """
      config = self.GetMethodConfig('GetDefaultServiceAccount')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDefaultServiceAccount.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/defaultServiceAccount',
        http_method='GET',
        method_id='cloudbuild.projects.locations.getDefaultServiceAccount',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGetDefaultServiceAccountRequest',
        response_type_name='DefaultServiceAccount',
        supports_download=False,
    )

  class ProjectsTriggersService(base_api.BaseApiService):
    """Service class for the projects_triggers resource."""

    _NAME = 'projects_triggers'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `BuildTrigger`.

      Args:
        request: (CloudbuildProjectsTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.triggers.create',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['parent'],
        relative_path='v1/projects/{projectId}/triggers',
        request_field='buildTrigger',
        request_type_name='CloudbuildProjectsTriggersCreateRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `BuildTrigger` by its project ID and trigger ID.

      Args:
        request: (CloudbuildProjectsTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='cloudbuild.projects.triggers.delete',
        ordered_params=['projectId', 'triggerId'],
        path_params=['projectId', 'triggerId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/triggers/{triggerId}',
        request_field='',
        request_type_name='CloudbuildProjectsTriggersDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns information about a `BuildTrigger`.

      Args:
        request: (CloudbuildProjectsTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.triggers.get',
        ordered_params=['projectId', 'triggerId'],
        path_params=['projectId', 'triggerId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/triggers/{triggerId}',
        request_field='',
        request_type_name='CloudbuildProjectsTriggersGetRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists existing `BuildTrigger`s.

      Args:
        request: (CloudbuildProjectsTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBuildTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudbuild.projects.triggers.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['pageSize', 'pageToken', 'parent'],
        relative_path='v1/projects/{projectId}/triggers',
        request_field='',
        request_type_name='CloudbuildProjectsTriggersListRequest',
        response_type_name='ListBuildTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `BuildTrigger` by its project ID and trigger ID.

      Args:
        request: (CloudbuildProjectsTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BuildTrigger) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='cloudbuild.projects.triggers.patch',
        ordered_params=['projectId', 'triggerId'],
        path_params=['projectId', 'triggerId'],
        query_params=['updateMask'],
        relative_path='v1/projects/{projectId}/triggers/{triggerId}',
        request_field='buildTrigger',
        request_type_name='CloudbuildProjectsTriggersPatchRequest',
        response_type_name='BuildTrigger',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Runs a `BuildTrigger` at a particular source revision. To run a regional or global trigger, use the POST request that includes the location endpoint in the path (ex. v1/projects/{projectId}/locations/{region}/triggers/{triggerId}:run). The POST request that does not include the location endpoint in the path can only be used when running global triggers.

      Args:
        request: (CloudbuildProjectsTriggersRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.triggers.run',
        ordered_params=['projectId', 'triggerId'],
        path_params=['projectId', 'triggerId'],
        query_params=['name'],
        relative_path='v1/projects/{projectId}/triggers/{triggerId}:run',
        request_field='repoSource',
        request_type_name='CloudbuildProjectsTriggersRunRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Webhook(self, request, global_params=None):
      r"""ReceiveTriggerWebhook [Experimental] is called when the API receives a webhook request targeted at a specific trigger.

      Args:
        request: (CloudbuildProjectsTriggersWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ReceiveTriggerWebhookResponse) The response message.
      """
      config = self.GetMethodConfig('Webhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    Webhook.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.projects.triggers.webhook',
        ordered_params=['projectId', 'trigger'],
        path_params=['projectId', 'trigger'],
        query_params=['name', 'secret'],
        relative_path='v1/projects/{projectId}/triggers/{trigger}:webhook',
        request_field='httpBody',
        request_type_name='CloudbuildProjectsTriggersWebhookRequest',
        response_type_name='ReceiveTriggerWebhookResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(CloudbuildV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class V1Service(base_api.BaseApiService):
    """Service class for the v1 resource."""

    _NAME = 'v1'

    def __init__(self, client):
      super(CloudbuildV1.V1Service, self).__init__(client)
      self._upload_configs = {
          }

    def Webhook(self, request, global_params=None):
      r"""ReceiveWebhook is called when the API receives a GitHub webhook.

      Args:
        request: (CloudbuildWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Webhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    Webhook.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudbuild.webhook',
        ordered_params=[],
        path_params=[],
        query_params=['webhookKey'],
        relative_path='v1/webhook',
        request_field='httpBody',
        request_type_name='CloudbuildWebhookRequest',
        response_type_name='Empty',
        supports_download=False,
    )
