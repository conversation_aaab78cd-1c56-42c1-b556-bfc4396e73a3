"""Generated message classes for cloudbuild version v2.

Creates and manages builds on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudbuild'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BatchCreateRepositoriesRequest(_messages.Message):
  r"""Message for creating repositoritories in batch.

  Fields:
    requests: Required. The request messages specifying the repositories to
      create.
  """

  requests = _messages.MessageField('CreateRepositoryRequest', 1, repeated=True)


class BatchCreateRepositoriesResponse(_messages.Message):
  r"""Message for response of creating repositories in batch.

  Fields:
    repositories: Repository resources created.
  """

  repositories = _messages.MessageField('Repository', 1, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BitbucketCloudConfig(_messages.Message):
  r"""Configuration for connections to Bitbucket Cloud.

  Fields:
    authorizerCredential: Required. An access token with the `webhook`,
      `repository`, `repository:admin` and `pullrequest` scope access. It can
      be either a workspace, project or repository access token. It's
      recommended to use a system account to generate these credentials.
    readAuthorizerCredential: Required. An access token with the `repository`
      access. It can be either a workspace, project or repository access
      token. It's recommended to use a system account to generate the
      credentials.
    webhookSecretSecretVersion: Required. SecretManager resource containing
      the webhook secret used to verify webhook events, formatted as
      `projects/*/secrets/*/versions/*`.
    workspace: Required. The Bitbucket Cloud Workspace ID to be connected to
      Google Cloud Platform.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 2)
  webhookSecretSecretVersion = _messages.StringField(3)
  workspace = _messages.StringField(4)


class BitbucketDataCenterConfig(_messages.Message):
  r"""Configuration for connections to Bitbucket Data Center.

  Fields:
    authorizerCredential: Required. A http access token with the `REPO_ADMIN`
      scope access.
    hostUri: Required. The URI of the Bitbucket Data Center instance or
      cluster this connection is for.
    readAuthorizerCredential: Required. A http access token with the
      `REPO_READ` access.
    serverVersion: Output only. Version of the Bitbucket Data Center running
      on the `host_uri`.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a Bitbucket Data Center. This should
      only be set if the Bitbucket Data Center is hosted on-premises and not
      reachable by public internet. If this field is left empty, calls to the
      Bitbucket Data Center will be made over the public internet.
    sslCa: Optional. SSL certificate to use for requests to the Bitbucket Data
      Center.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret used to verify webhook events, formatted
      as `projects/*/secrets/*/versions/*`.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  hostUri = _messages.StringField(2)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 3)
  serverVersion = _messages.StringField(4)
  serviceDirectoryConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV2ServiceDirectoryConfig', 5)
  sslCa = _messages.StringField(6)
  webhookSecretSecretVersion = _messages.StringField(7)


class CEL(_messages.Message):
  r"""Filters in CEL.

  Fields:
    cel: The filter logic in CEL.
    notification: A notification sent back to SCM if the cel program fails.
  """

  cel = _messages.StringField(1)
  notification = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Capabilities(_messages.Message):
  r"""Capabilities adds and removes POSIX capabilities from running
  containers.

  Fields:
    add: Optional. Added capabilities +optional
    drop: Optional. Removed capabilities +optional
  """

  add = _messages.StringField(1, repeated=True)
  drop = _messages.StringField(2, repeated=True)


class ChildStatusReference(_messages.Message):
  r"""ChildStatusReference is used to point to the statuses of individual
  TaskRuns and Runs within this PipelineRun.

  Enums:
    TypeValueValuesEnum: Output only. Type of the child reference.

  Fields:
    name: Name is the name of the TaskRun or Run this is referencing.
    pipelineTaskName: PipelineTaskName is the name of the PipelineTask this is
      referencing.
    type: Output only. Type of the child reference.
    whenExpressions: WhenExpressions is the list of checks guarding the
      execution of the PipelineTask
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the child reference.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      TASK_RUN: TaskRun.
    """
    TYPE_UNSPECIFIED = 0
    TASK_RUN = 1

  name = _messages.StringField(1)
  pipelineTaskName = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  whenExpressions = _messages.MessageField('WhenExpression', 4, repeated=True)


class CloudbuildProjectsLocationsConnectionsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    connectionId: Required. The ID to use for the Connection, which will
      become the final component of the Connection's resource name. Names must
      be unique per-project per-location. Allows alphanumeric characters and
      any of -._~%!$&'()*+,;=@.
    parent: Required. Project and location where the connection will be
      created. Format: `projects/*/locations/*`.
  """

  connection = _messages.MessageField('Connection', 1)
  connectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsConnectionsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsDeleteRequest object.

  Fields:
    etag: The current etag of the connection. If an etag is provided and does
      not match the current etag of the connection, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. The name of the Connection to delete. Format:
      `projects/*/locations/*/connections/*`.
    validateOnly: If set, validate the request, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class CloudbuildProjectsLocationsConnectionsFetchLinkableRepositoriesRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsFetchLinkableRepositoriesRequest
  object.

  Fields:
    connection: Required. The name of the Connection. Format:
      `projects/*/locations/*/connections/*`.
    pageSize: Number of results to return in the list. Default to 20.
    pageToken: Page start.
  """

  connection = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class CloudbuildProjectsLocationsConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsConnectionsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsGetRequest object.

  Fields:
    name: Required. The name of the Connection to retrieve. Format:
      `projects/*/locations/*/connections/*`.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsConnectionsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsListRequest object.

  Fields:
    pageSize: Number of results to return in the list.
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of Connections.
      Format: `projects/*/locations/*`.
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable. If set to false, the
      response will fail if any region is unreachable.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class CloudbuildProjectsLocationsConnectionsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsPatchRequest object.

  Fields:
    allowMissing: If set to true, and the connection is not found a new
      connection will be created. In this situation `update_mask` is ignored.
      The creation will succeed only if the input connection has all the
      necessary information (e.g a github_config with both user_oauth_token
      and installation_id properties).
    connection: A Connection resource to be passed as the request body.
    etag: The current etag of the connection. If an etag is provided and does
      not match the current etag of the connection, update will be blocked and
      an ABORTED error will be returned.
    name: Immutable. The resource name of the connection, in the format
      `projects/{project}/locations/{location}/connections/{connection_id}`.
    updateMask: The list of fields to be updated.
  """

  allowMissing = _messages.BooleanField(1)
  connection = _messages.MessageField('Connection', 2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  updateMask = _messages.StringField(5)


class CloudbuildProjectsLocationsConnectionsProcessWebhookRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsProcessWebhookRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: Required. Project and location where the webhook will be received.
      Format: `projects/*/locations/*`.
    webhookKey: Arbitrary additional key to find the matching repository for a
      webhook event if needed.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)
  webhookKey = _messages.StringField(3)


class CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadTokenRequest(_messages.Message):
  r"""A
  CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadTokenRequest
  object.

  Fields:
    fetchReadTokenRequest: A FetchReadTokenRequest resource to be passed as
      the request body.
    repository: Required. The resource name of the repository in the format
      `projects/*/locations/*/connections/*/repositories/*`.
  """

  fetchReadTokenRequest = _messages.MessageField('FetchReadTokenRequest', 1)
  repository = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadWriteTokenRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadWriteToken
  Request object.

  Fields:
    fetchReadWriteTokenRequest: A FetchReadWriteTokenRequest resource to be
      passed as the request body.
    repository: Required. The resource name of the repository in the format
      `projects/*/locations/*/connections/*/repositories/*`.
  """

  fetchReadWriteTokenRequest = _messages.MessageField('FetchReadWriteTokenRequest', 1)
  repository = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsConnectionsRepositoriesBatchCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesBatchCreateRequest
  object.

  Fields:
    batchCreateRepositoriesRequest: A BatchCreateRepositoriesRequest resource
      to be passed as the request body.
    parent: Required. The connection to contain all the repositories being
      created. Format: projects/*/locations/*/connections/* The parent field
      in the CreateRepositoryRequest messages must either be empty or match
      this field.
  """

  batchCreateRepositoriesRequest = _messages.MessageField('BatchCreateRepositoriesRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsConnectionsRepositoriesCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesCreateRequest
  object.

  Fields:
    parent: Required. The connection to contain the repository. If the request
      is part of a BatchCreateRepositoriesRequest, this field should be empty
      or match the parent specified there.
    repository: A Repository resource to be passed as the request body.
    repositoryId: Required. The ID to use for the repository, which will
      become the final component of the repository's resource name. This ID
      should be unique in the connection. Allows alphanumeric characters and
      any of -._~%!$&'()*+,;=@.
  """

  parent = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class CloudbuildProjectsLocationsConnectionsRepositoriesDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesDeleteRequest
  object.

  Fields:
    etag: The current etag of the repository. If an etag is provided and does
      not match the current etag of the repository, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. The name of the Repository to delete. Format:
      `projects/*/locations/*/connections/*/repositories/*`.
    validateOnly: If set, validate the request, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class CloudbuildProjectsLocationsConnectionsRepositoriesFetchGitRefsRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesFetchGitRefsRequest
  object.

  Enums:
    RefTypeValueValuesEnum: Type of refs to fetch

  Fields:
    pageSize: Optional. Number of results to return in the list. Default to
      20.
    pageToken: Optional. Page start.
    refType: Type of refs to fetch
    repository: Required. The resource name of the repository in the format
      `projects/*/locations/*/connections/*/repositories/*`.
  """

  class RefTypeValueValuesEnum(_messages.Enum):
    r"""Type of refs to fetch

    Values:
      REF_TYPE_UNSPECIFIED: No type specified.
      TAG: To fetch tags.
      BRANCH: To fetch branches.
    """
    REF_TYPE_UNSPECIFIED = 0
    TAG = 1
    BRANCH = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  refType = _messages.EnumField('RefTypeValueValuesEnum', 3)
  repository = _messages.StringField(4, required=True)


class CloudbuildProjectsLocationsConnectionsRepositoriesGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesGetRequest object.

  Fields:
    name: Required. The name of the Repository to retrieve. Format:
      `projects/*/locations/*/connections/*/repositories/*`.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsConnectionsRepositoriesListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsRepositoriesListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Expressions must follow API improvement proposal
      [AIP-160](https://google.aip.dev/160). e.g.
      `remote_uri:"https://github.com*"`.
    pageSize: Number of results to return in the list.
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of Repositories.
      Format: `projects/*/locations/*/connections/*`.
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable. If set to false, the
      response will fail if any region is unreachable.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  returnPartialSuccess = _messages.BooleanField(5)


class CloudbuildProjectsLocationsConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudbuildProjectsLocationsConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsConnectionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudbuildProjectsLocationsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class CloudbuildProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsPipelineRunsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsPipelineRunsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    pipelineRun: A PipelineRun resource to be passed as the request body.
    pipelineRunId: Required. The ID to use for the PipelineRun, which will
      become the final component of the PipelineRun's resource name. Names
      must be unique per-project per-location. This value should be 4-63
      characters, and valid characters are /a-z-/.
    validateOnly: Optional. When true, the query is validated only, but not
      executed.
  """

  parent = _messages.StringField(1, required=True)
  pipelineRun = _messages.MessageField('PipelineRun', 2)
  pipelineRunId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudbuildProjectsLocationsPipelineRunsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsPipelineRunsGetRequest object.

  Fields:
    name: Required. The name of the PipelineRun to retrieve. Format:
      projects/{project}/locations/{location}/pipelineRuns/{pipelineRun}
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsPipelineRunsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsPipelineRunsListRequest object.

  Fields:
    filter: Filter for the results.
    pageSize: Number of results to return in the list.
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of PipelineRuns.
      Format: projects/{project}/locations/{location}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudbuildProjectsLocationsPipelineRunsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsPipelineRunsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the PipelineRun is not found,
      a new PipelineRun will be created. In this situation, `update_mask` is
      ignored.
    name: Output only. The `PipelineRun` name with format
      `projects/{project}/locations/{location}/pipelineRuns/{pipeline_run}`
    pipelineRun: A PipelineRun resource to be passed as the request body.
    updateMask: Required. The list of fields to be updated.
    validateOnly: Optional. When true, the query is validated only, but not
      executed.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  pipelineRun = _messages.MessageField('PipelineRun', 3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class CloudbuildProjectsLocationsResultsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsResultsGetRequest object.

  Fields:
    name: Required. The name of the Result to retrieve. Format:
      projects/{project}/locations/{location}/results/{result}
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsResultsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsResultsListRequest object.

  Fields:
    filter: Filter for the Records.
    pageSize: Size of the page to return. Default page_size = 50 Maximum
      page_size = 1000
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of Results.
      Format: projects/{project}/locations/{location}/
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudbuildProjectsLocationsResultsRecordsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsResultsRecordsGetRequest object.

  Fields:
    name: Required. The name of the Record to retrieve. Format:
      projects/{project}/locations/{location}/results/{result}/records/{record
      }
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsResultsRecordsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsResultsRecordsListRequest object.

  Fields:
    filter: Filter for the Records.
    pageSize: Size of the page to return. Default page_size = 50 Maximum
      page_size = 1000
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of Records.
      Format: projects/{project}/locations/{location}/results/{result}/
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudbuildProjectsLocationsTaskRunsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTaskRunsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    taskRun: A TaskRun resource to be passed as the request body.
    taskRunId: Required. The ID to use for the TaskRun, which will become the
      final component of the TaskRun's resource name. Names must be unique
      per-project per-location. This value should be 4-63 characters, and
      valid characters are /a-z-/.
    validateOnly: Optional. When true, the query is validated only, but not
      executed.
  """

  parent = _messages.StringField(1, required=True)
  taskRun = _messages.MessageField('TaskRun', 2)
  taskRunId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudbuildProjectsLocationsTaskRunsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTaskRunsGetRequest object.

  Fields:
    name: Required. The name of the TaskRun to retrieve. Format:
      projects/{project}/locations/{location}/taskRuns/{taskRun}
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsTaskRunsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTaskRunsListRequest object.

  Fields:
    filter: Filter for the results.
    pageSize: Number of results to return in the list.
    pageToken: Page start.
    parent: Required. The parent, which owns this collection of TaskRuns.
      Format: projects/{project}/locations/{location}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudbuildProjectsLocationsTaskRunsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTaskRunsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the TaskRun is not found, a
      new TaskRun will be created. In this situation, `update_mask` is
      ignored.
    name: Output only. The 'TaskRun' name with format:
      `projects/{project}/locations/{location}/taskRuns/{task_run}`
    taskRun: A TaskRun resource to be passed as the request body.
    updateMask: Required. The list of fields to be updated.
    validateOnly: Optional. When true, the query is validated only, but not
      executed.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  taskRun = _messages.MessageField('TaskRun', 3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class CloudbuildProjectsLocationsWorkerPoolSecondGenCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolSecondGenCreateRequest object.

  Fields:
    parent: Required. The parent resource where this worker pool will be
      created. Format: `projects/{project}/locations/{location}`.
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
    workerPoolSecondGen: A WorkerPoolSecondGen resource to be passed as the
      request body.
    workerPoolSecondGenId: Required. Immutable. The ID to use for the
      `WorkerPoolSecondGen`, which will become the final component of the
      resource name. This value should be 1-63 characters, and valid
      characters are /a-z-/.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workerPoolSecondGen = _messages.MessageField('WorkerPoolSecondGen', 3)
  workerPoolSecondGenId = _messages.StringField(4)


class CloudbuildProjectsLocationsWorkerPoolSecondGenDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolSecondGenDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the `WorkerPoolSecondGen` is
      not found, the request will succeed but no action will be taken on the
      server.
    etag: Optional. If provided, it must match the server's etag on the
      WorkerPoolSecondGen for the request to be processed.
    name: Required. The name of the `WorkerPoolSecondGen` to delete. Format: `
      projects/{project}/locations/{location}/workerPoolSecondGen/{workerPoolS
      econdGen}`.
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class CloudbuildProjectsLocationsWorkerPoolSecondGenGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolSecondGenGetRequest object.

  Fields:
    name: Required. The name of the `WorkerPoolSecondGen` to retrieve. Format:
      `projects/{project}/locations/{location}/workerPoolSecondGen/{workerPool
      SecondGen}`.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsWorkerPoolSecondGenListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolSecondGenListRequest object.

  Fields:
    pageSize: Optional. The maximum number of `WorkerPoolSecondGen`s to
      return. The service may return fewer than this value. If omitted, the
      server will use a sensible default.
    pageToken: Optional. A page token, received from a previous
      `ListWorkerPoolSecondGen` call. Provide this to retrieve the subsequent
      page.
    parent: Required. The parent of the collection of `WorkerPoolSecondGen`.
      Format: `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsWorkerPoolSecondGenPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolSecondGenPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the `WorkerPoolSecondGen` is
      not found, a new `WorkerPoolSecondGen` will be created. In this
      situation, `update_mask` is ignored.
    name: Output only. Identifier. The resource name of the
      `WorkerPoolSecondGen`, with format `projects/{project}/locations/{locati
      on}/workerPoolSecondGen/{worker_pool_second_gen}`.
    updateMask: Optional. A mask specifying which fields in
      `worker_pool_second_gen` to update.
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
    workerPoolSecondGen: A WorkerPoolSecondGen resource to be passed as the
      request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  workerPoolSecondGen = _messages.MessageField('WorkerPoolSecondGen', 5)


class CloudbuildProjectsLocationsWorkflowsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsCreateRequest object.

  Fields:
    parent: Required. Format: `projects/{project}/locations/{location}`
    validateOnly: When true, the query is validated only, but not executed.
    workflow: A Workflow resource to be passed as the request body.
    workflowId: Required. The ID to use for the Workflow, which will become
      the final component of the Workflow's resource name.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workflow = _messages.MessageField('Workflow', 3)
  workflowId = _messages.StringField(4)


class CloudbuildProjectsLocationsWorkflowsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsDeleteRequest object.

  Fields:
    etag: The etag of the workflow. If this is provided, it must match the
      server's etag.
    name: Required. Format:
      `projects/{project}/locations/{location}/workflow/{workflow}`
    validateOnly: When true, the query is validated only, but not executed.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class CloudbuildProjectsLocationsWorkflowsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project}/locations/{location}/workflow/{workflow}`
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsWorkflowsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsListRequest object.

  Fields:
    filter: Filter for the results.
    orderBy: The order to sort results by.
    pageSize: Number of results to return in the list.
    pageToken: Page start.
    parent: Required. Format: `projects/{project}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudbuildProjectsLocationsWorkflowsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsPatchRequest object.

  Fields:
    allowMissing: If set to true, and the workflow is not found, a new
      workflow will be created. In this situation, `update_mask` is ignored.
    name: Output only. Format:
      `projects/{project}/locations/{location}/workflows/{workflow}`
    updateMask: The list of fields to be updated.
    validateOnly: When true, the query is validated only, but not executed.
    workflow: A Workflow resource to be passed as the request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  workflow = _messages.MessageField('Workflow', 5)


class CloudbuildProjectsLocationsWorkflowsRunRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsRunRequest object.

  Fields:
    name: Required. Format:
      `projects/{project}/locations/{location}/workflow/{workflow}`
    runWorkflowRequest: A RunWorkflowRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  runWorkflowRequest = _messages.MessageField('RunWorkflowRequest', 2)


class CloudbuildProjectsLocationsWorkflowsWebhookRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkflowsWebhookRequest object.

  Fields:
    processWorkflowTriggerWebhookRequest: A
      ProcessWorkflowTriggerWebhookRequest resource to be passed as the
      request body.
    workflow: Required. Format:
      `projects/{project}/locations/{location}/workflow/{workflow}`
  """

  processWorkflowTriggerWebhookRequest = _messages.MessageField('ProcessWorkflowTriggerWebhookRequest', 1)
  workflow = _messages.StringField(2, required=True)


class Connection(_messages.Message):
  r"""A connection to a SCM like GitHub, GitHub Enterprise, Bitbucket Data
  Center, Bitbucket Cloud or GitLab.

  Messages:
    AnnotationsValue: Optional. Allows clients to store small amounts of
      arbitrary data.

  Fields:
    annotations: Optional. Allows clients to store small amounts of arbitrary
      data.
    bitbucketCloudConfig: Configuration for connections to Bitbucket Cloud.
    bitbucketDataCenterConfig: Configuration for connections to Bitbucket Data
      Center.
    createTime: Output only. Server assigned timestamp for when the connection
      was created.
    disabled: Optional. If disabled is set to true, functionality is disabled
      for this connection. Repository based API methods and webhooks
      processing for repositories in this connection will be disabled.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    githubConfig: Configuration for connections to github.com.
    githubEnterpriseConfig: Configuration for connections to an instance of
      GitHub Enterprise.
    gitlabConfig: Configuration for connections to gitlab.com or an instance
      of GitLab Enterprise.
    installationState: Output only. Installation state of the Connection.
    name: Immutable. The resource name of the connection, in the format
      `projects/{project}/locations/{location}/connections/{connection_id}`.
    reconciling: Output only. Set to true when the connection is being set up
      or updated in the background.
    updateTime: Output only. Server assigned timestamp for when the connection
      was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows clients to store small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  bitbucketCloudConfig = _messages.MessageField('BitbucketCloudConfig', 2)
  bitbucketDataCenterConfig = _messages.MessageField('BitbucketDataCenterConfig', 3)
  createTime = _messages.StringField(4)
  disabled = _messages.BooleanField(5)
  etag = _messages.StringField(6)
  githubConfig = _messages.MessageField('GitHubConfig', 7)
  githubEnterpriseConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV2GitHubEnterpriseConfig', 8)
  gitlabConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV2GitLabConfig', 9)
  installationState = _messages.MessageField('InstallationState', 10)
  name = _messages.StringField(11)
  reconciling = _messages.BooleanField(12)
  updateTime = _messages.StringField(13)


class ContainerStateRunning(_messages.Message):
  r"""ContainerStateWaiting is a running state of a container.

  Fields:
    startedAt: Time at which the container was last (re-)started.
  """

  startedAt = _messages.StringField(1)


class ContainerStateTerminated(_messages.Message):
  r"""ContainerStateWaiting is a terminated state of a container.

  Fields:
    exitCode: Exit status from the last termination of the container.
    finishedAt: Time at which the container last terminated
    message: Message regarding the last termination of the container
    reason: Reason from the last termination of the container
    startedAt: Time at which previous execution of the container started
  """

  exitCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  finishedAt = _messages.StringField(2)
  message = _messages.StringField(3)
  reason = _messages.StringField(4)
  startedAt = _messages.StringField(5)


class ContainerStateWaiting(_messages.Message):
  r"""ContainerStateWaiting is a waiting state of a container.

  Fields:
    message: Message regarding why the container is not yet running.
    reason: Reason the container is not yet running.
  """

  message = _messages.StringField(1)
  reason = _messages.StringField(2)


class CreateRepositoryRequest(_messages.Message):
  r"""Message for creating a Repository.

  Fields:
    parent: Required. The connection to contain the repository. If the request
      is part of a BatchCreateRepositoriesRequest, this field should be empty
      or match the parent specified there.
    repository: Required. The repository to create.
    repositoryId: Required. The ID to use for the repository, which will
      become the final component of the repository's resource name. This ID
      should be unique in the connection. Allows alphanumeric characters and
      any of -._~%!$&'()*+,;=@.
  """

  parent = _messages.StringField(1)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class EmbeddedTask(_messages.Message):
  r"""EmbeddedTask defines a Task that is embedded in a Pipeline.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations

  Fields:
    annotations: User annotations. See https://google.aip.dev/128#annotations
    taskSpec: Spec to instantiate this TaskRun.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  taskSpec = _messages.MessageField('TaskSpec', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EmptyDirVolumeSource(_messages.Message):
  r"""Represents an empty Volume source."""


class EnvVar(_messages.Message):
  r"""Environment variable.

  Fields:
    name: Name of the environment variable.
    value: Value of the environment variable.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class EventSource(_messages.Message):
  r"""Event Source referenceable within a WorkflowTrigger.

  Fields:
    eventSource: Output only. The fully qualified resource name for the event
      source.
    gitRepoLink: Resource name of Developer Connect GitRepositoryLink.
    gitRepositoryLink: Output only. Resource name of Developer Connect
      GitRepositoryLink.
    id: identification to Resource.
    repository: Output only. Resource name of GCB v2 repo.
    subscription: Output only. Resource name of Pub/Sub subscription.
    topic: Resource name of Pub/Sub topic.
    url: SCM Repo URL.
  """

  eventSource = _messages.StringField(1)
  gitRepoLink = _messages.StringField(2)
  gitRepositoryLink = _messages.StringField(3)
  id = _messages.StringField(4)
  repository = _messages.StringField(5)
  subscription = _messages.StringField(6)
  topic = _messages.StringField(7)
  url = _messages.StringField(8)


class ExecAction(_messages.Message):
  r"""ExecAction describes a "run in container" action.

  Fields:
    command: Optional. Command is the command line to execute inside the
      container, the working directory for the command is root ('/') in the
      container's filesystem. The command is simply exec'd, it is not run
      inside a shell, so traditional shell instructions ('|', etc) won't work.
      To use a shell, you need to explicitly call out to that shell. Exit
      status of 0 is treated as live/healthy and non-zero is unhealthy.
      +optional
  """

  command = _messages.StringField(1, repeated=True)


class ExecutionEnvironment(_messages.Message):
  r"""Contains the workerpool.

  Fields:
    workerPool: Required. The workerpool used to run the PipelineRun.
      Deprecated; please use workflow_options.worker_pool instead.
  """

  workerPool = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FetchGitRefsResponse(_messages.Message):
  r"""Response for fetching git refs

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    refNames: Name of the refs fetched.
  """

  nextPageToken = _messages.StringField(1)
  refNames = _messages.StringField(2, repeated=True)


class FetchLinkableRepositoriesResponse(_messages.Message):
  r"""Response message for FetchLinkableRepositories.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    repositories: repositories ready to be created.
  """

  nextPageToken = _messages.StringField(1)
  repositories = _messages.MessageField('Repository', 2, repeated=True)


class FetchReadTokenRequest(_messages.Message):
  r"""Message for fetching SCM read token."""


class FetchReadTokenResponse(_messages.Message):
  r"""Message for responding to get read token.

  Fields:
    expirationTime: Expiration timestamp. Can be empty if unknown or non-
      expiring.
    token: The token content.
  """

  expirationTime = _messages.StringField(1)
  token = _messages.StringField(2)


class FetchReadWriteTokenRequest(_messages.Message):
  r"""Message for fetching SCM read/write token."""


class FetchReadWriteTokenResponse(_messages.Message):
  r"""Message for responding to get read/write token.

  Fields:
    expirationTime: Expiration timestamp. Can be empty if unknown or non-
      expiring.
    token: The token content.
  """

  expirationTime = _messages.StringField(1)
  token = _messages.StringField(2)


class GitHubConfig(_messages.Message):
  r"""Configuration for connections to github.com.

  Fields:
    appInstallationId: Optional. GitHub App installation id.
    authorizerCredential: Optional. OAuth credential of the account that
      authorized the Cloud Build GitHub App. It is recommended to use a robot
      account instead of a human user account. The OAuth token must be tied to
      the Cloud Build GitHub App.
  """

  appInstallationId = _messages.IntegerField(1)
  authorizerCredential = _messages.MessageField('OAuthCredential', 2)


class GitRef(_messages.Message):
  r"""Git ref configuration for filters.

  Fields:
    inverse: If true, the regex matching result is inversed.
    nameRegex: Regex to match the branch or tag of SCM.
  """

  inverse = _messages.BooleanField(1)
  nameRegex = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV2Condition(_messages.Message):
  r"""Conditions defines a readiness condition for a Knative resource.

  Enums:
    SeverityValueValuesEnum: Severity with which to treat failures of this
      type of condition.
    StatusValueValuesEnum: Status of the condition.

  Fields:
    lastTransitionTime: LastTransitionTime is the last time the condition
      transitioned from one status to another.
    message: A human readable message indicating details about the transition.
    reason: The reason for the condition's last transition.
    severity: Severity with which to treat failures of this type of condition.
    status: Status of the condition.
    type: Type of condition.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity with which to treat failures of this type of condition.

    Values:
      SEVERITY_UNSPECIFIED: Default enum type; should not be used.
      WARNING: Severity is warning.
      INFO: Severity is informational only.
    """
    SEVERITY_UNSPECIFIED = 0
    WARNING = 1
    INFO = 2

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the condition.

    Values:
      UNKNOWN: Default enum type indicating execution is still ongoing.
      TRUE: Success
      FALSE: Failure
    """
    UNKNOWN = 0
    TRUE = 1
    FALSE = 2

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)
  status = _messages.EnumField('StatusValueValuesEnum', 5)
  type = _messages.StringField(6)


class GoogleDevtoolsCloudbuildV2GitHubEnterpriseConfig(_messages.Message):
  r"""Configuration for connections to an instance of GitHub Enterprise.

  Fields:
    apiKey: Required. API Key used for authentication of webhook events.
    appId: Optional. Id of the GitHub App created from the manifest.
    appInstallationId: Optional. ID of the installation of the GitHub App.
    appSlug: Optional. The URL-friendly name of the GitHub App.
    authorizerCredential: Optional. OAuth credential of the account that
      authorized the Cloud Build GitHub App. It is recommended to use a robot
      account instead of a human user account The OAuth token must be tied to
      the Cloud Build GitHub App.
    hostUri: Required. The URI of the GitHub Enterprise host this connection
      is for.
    oauthClientIdSecretVersion: Optional. SecretManager resource containing
      the OAuth client_id of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
    oauthSecretSecretVersion: Optional. SecretManager resource containing the
      OAuth secret of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
    privateKeySecretVersion: Optional. SecretManager resource containing the
      private key of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
    serverVersion: Output only. GitHub Enterprise version installed at the
      host_uri.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a GitHub Enterprise server. This
      should only be set if the GitHub Enterprise server is hosted on-premises
      and not reachable by public internet. If this field is left empty, calls
      to the GitHub Enterprise server will be made over the public internet.
    sslCa: Optional. SSL certificate to use for requests to GitHub Enterprise.
    webhookSecretSecretVersion: Optional. SecretManager resource containing
      the webhook secret of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
  """

  apiKey = _messages.StringField(1)
  appId = _messages.IntegerField(2)
  appInstallationId = _messages.IntegerField(3)
  appSlug = _messages.StringField(4)
  authorizerCredential = _messages.MessageField('OAuthCredential', 5)
  hostUri = _messages.StringField(6)
  oauthClientIdSecretVersion = _messages.StringField(7)
  oauthSecretSecretVersion = _messages.StringField(8)
  privateKeySecretVersion = _messages.StringField(9)
  serverVersion = _messages.StringField(10)
  serviceDirectoryConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV2ServiceDirectoryConfig', 11)
  sslCa = _messages.StringField(12)
  webhookSecretSecretVersion = _messages.StringField(13)


class GoogleDevtoolsCloudbuildV2GitLabConfig(_messages.Message):
  r"""Configuration for connections to gitlab.com or an instance of GitLab
  Enterprise.

  Fields:
    authorizerCredential: Required. A GitLab personal access token with the
      `api` scope access.
    hostUri: Optional. The URI of the GitLab Enterprise host this connection
      is for. If not specified, the default value is https://gitlab.com.
    readAuthorizerCredential: Required. A GitLab personal access token with
      the minimum `read_api` scope access.
    serverVersion: Output only. Version of the GitLab Enterprise server
      running on the `host_uri`.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a GitLab Enterprise server. This
      should only be set if the GitLab Enterprise server is hosted on-premises
      and not reachable by public internet. If this field is left empty, calls
      to the GitLab Enterprise server will be made over the public internet.
    sslCa: Optional. SSL certificate to use for requests to GitLab Enterprise.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret of a GitLab Enterprise project, formatted
      as `projects/*/secrets/*/versions/*`.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  hostUri = _messages.StringField(2)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 3)
  serverVersion = _messages.StringField(4)
  serviceDirectoryConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV2ServiceDirectoryConfig', 5)
  sslCa = _messages.StringField(6)
  webhookSecretSecretVersion = _messages.StringField(7)


class GoogleDevtoolsCloudbuildV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleDevtoolsCloudbuildV2ServiceDirectoryConfig(_messages.Message):
  r"""ServiceDirectoryConfig represents Service Directory configuration for a
  connection.

  Fields:
    service: Required. The Service Directory service name. Format: projects/{p
      roject}/locations/{location}/namespaces/{namespace}/services/{service}.
  """

  service = _messages.StringField(1)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class InstallationState(_messages.Message):
  r"""Describes stage and necessary actions to be taken by the user to
  complete the installation. Used for GitHub and GitHub Enterprise based
  connections.

  Enums:
    StageValueValuesEnum: Output only. Current step of the installation
      process.

  Fields:
    actionUri: Output only. Link to follow for next action. Empty string if
      the installation is already complete.
    message: Output only. Message of what the user should do next to continue
      the installation. Empty string if the installation is already complete.
    stage: Output only. Current step of the installation process.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Output only. Current step of the installation process.

    Values:
      STAGE_UNSPECIFIED: No stage specified.
      PENDING_CREATE_APP: Only for GitHub Enterprise. An App creation has been
        requested. The user needs to confirm the creation in their GitHub
        enterprise host.
      PENDING_USER_OAUTH: User needs to authorize the GitHub (or Enterprise)
        App via OAuth.
      PENDING_INSTALL_APP: User needs to follow the link to install the GitHub
        (or Enterprise) App.
      COMPLETE: Installation process has been completed.
    """
    STAGE_UNSPECIFIED = 0
    PENDING_CREATE_APP = 1
    PENDING_USER_OAUTH = 2
    PENDING_INSTALL_APP = 3
    COMPLETE = 4

  actionUri = _messages.StringField(1)
  message = _messages.StringField(2)
  stage = _messages.EnumField('StageValueValuesEnum', 3)


class JsonAny(_messages.Message):
  r"""JSON serialized data.

  Fields:
    type: Identifier of underlying data. e.g.
      `cloudbuild.googleapis.com/PipelineRun`
    value: JSON-serialized data of above type.
  """

  type = _messages.StringField(1)
  value = _messages.BytesField(2)


class ListConnectionsResponse(_messages.Message):
  r"""Message for response to listing Connections.

  Fields:
    connections: The list of Connections.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPipelineRunsResponse(_messages.Message):
  r"""Message for response to listing PipelineRuns

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    pipelineRuns: The list of PipelineRun
  """

  nextPageToken = _messages.StringField(1)
  pipelineRuns = _messages.MessageField('PipelineRun', 2, repeated=True)


class ListRecordsResponse(_messages.Message):
  r"""Message for response to listing Records.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    records: The list of Records.
  """

  nextPageToken = _messages.StringField(1)
  records = _messages.MessageField('Record', 2, repeated=True)


class ListRepositoriesResponse(_messages.Message):
  r"""Message for response to listing Repositories.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    repositories: The list of Repositories.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  repositories = _messages.MessageField('Repository', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListResultsResponse(_messages.Message):
  r"""Message for response to listing Results.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    results: The list of Results.
  """

  nextPageToken = _messages.StringField(1)
  results = _messages.MessageField('Result', 2, repeated=True)


class ListTaskRunsResponse(_messages.Message):
  r"""Message for response to listing TaskRuns

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    taskRuns: The list of TaskRun
  """

  nextPageToken = _messages.StringField(1)
  taskRuns = _messages.MessageField('TaskRun', 2, repeated=True)


class ListWorkerPoolSecondGenResponse(_messages.Message):
  r"""Response containing existing `WorkerPoolSecondGen`.

  Fields:
    nextPageToken: Continuation token used to page through large result sets.
      Provide this value in a subsequent ListWorkerPoolSecondGenRequest to
      return the next page of results.
    workerPoolSecondGen: `WorkerPoolSecondGen` for the specified project.
  """

  nextPageToken = _messages.StringField(1)
  workerPoolSecondGen = _messages.MessageField('WorkerPoolSecondGen', 2, repeated=True)


class ListWorkflowsResponse(_messages.Message):
  r"""Message for response to listing Workflows.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    workflows: The list of Workflows.
  """

  nextPageToken = _messages.StringField(1)
  workflows = _messages.MessageField('Workflow', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NetworkConfig(_messages.Message):
  r"""Defines the network configuration for the WorkerPoolSecondGen.

  Fields:
    privateServiceConnect: Connect to peered network through Private Service
      Connect.
    publicIpAddressDisabled: Required. Immutable. Disable public IP on the
      primary network interface. If true, workers are created without any
      public address, which prevents network egress to public IPs unless a
      network proxy is configured. If false, workers are created with a public
      address which allows for public Internet egress. The public address only
      applies to traffic through the primary network interface.
  """

  privateServiceConnect = _messages.MessageField('PrivateServiceConnect', 1)
  publicIpAddressDisabled = _messages.BooleanField(2)


class OAuthCredential(_messages.Message):
  r"""Represents an OAuth token of the account that authorized the Connection,
  and associated metadata.

  Fields:
    oauthTokenSecretVersion: Optional. A SecretManager resource containing the
      OAuth token that authorizes the Cloud Build connection. Format:
      `projects/*/secrets/*/versions/*`.
    username: Output only. The username associated to this token.
  """

  oauthTokenSecretVersion = _messages.StringField(1)
  username = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Param(_messages.Message):
  r"""Param defined with name and value. PipelineRef can be used to refer to a
  specific instance of a Pipeline.

  Fields:
    name: Name of the parameter.
    value: Value of the parameter.
  """

  name = _messages.StringField(1)
  value = _messages.MessageField('ParamValue', 2)


class ParamSpec(_messages.Message):
  r"""ParamSpec defines parameters needed beyond typed inputs (such as
  resources). Parameter values are provided by users as inputs on a TaskRun or
  PipelineRun.

  Enums:
    TypeValueValuesEnum: Type of ParamSpec

  Fields:
    default: The default value a parameter takes if no input value is supplied
    description: Description of the ParamSpec
    name: Name of the ParamSpec
    type: Type of ParamSpec
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of ParamSpec

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type.
      OBJECT: Object type.
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  default = _messages.MessageField('ParamValue', 1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class ParamValue(_messages.Message):
  r"""Parameter value.

  Enums:
    TypeValueValuesEnum: Type of parameter.

  Messages:
    ObjectValValue: Optional. Value of the parameter if type is object.

  Fields:
    arrayVal: Value of the parameter if type is array.
    objectVal: Optional. Value of the parameter if type is object.
    stringVal: Value of the parameter if type is string.
    type: Type of parameter.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of parameter.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type
      OBJECT: Object type
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ObjectValValue(_messages.Message):
    r"""Optional. Value of the parameter if type is object.

    Messages:
      AdditionalProperty: An additional property for a ObjectValValue object.

    Fields:
      additionalProperties: Additional properties of type ObjectValValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ObjectValValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arrayVal = _messages.StringField(1, repeated=True)
  objectVal = _messages.MessageField('ObjectValValue', 2)
  stringVal = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class PipelineRef(_messages.Message):
  r"""PipelineRef can be used to refer to a specific instance of a Pipeline.

  Enums:
    ResolverValueValuesEnum: Resolver is the name of the resolver that should
      perform resolution of the referenced Tekton resource.

  Fields:
    name: Optional. Name of the Pipeline.
    params: Params contains the parameters used to identify the referenced
      Tekton resource. Example entries might include "repo" or "path" but the
      set of params ultimately depends on the chosen resolver.
    resolver: Resolver is the name of the resolver that should perform
      resolution of the referenced Tekton resource.
  """

  class ResolverValueValuesEnum(_messages.Enum):
    r"""Resolver is the name of the resolver that should perform resolution of
    the referenced Tekton resource.

    Values:
      RESOLVER_NAME_UNSPECIFIED: Default enum type; should not be used.
      BUNDLES: Bundles resolver. https://tekton.dev/docs/pipelines/bundle-
        resolver/
      GCB_REPO: GCB repo resolver.
      GIT: Simple Git resolver. https://tekton.dev/docs/pipelines/git-
        resolver/
      DEVELOPER_CONNECT: Developer Connect resolver.
      DEFAULT: Default resolver.
    """
    RESOLVER_NAME_UNSPECIFIED = 0
    BUNDLES = 1
    GCB_REPO = 2
    GIT = 3
    DEVELOPER_CONNECT = 4
    DEFAULT = 5

  name = _messages.StringField(1)
  params = _messages.MessageField('Param', 2, repeated=True)
  resolver = _messages.EnumField('ResolverValueValuesEnum', 3)


class PipelineResult(_messages.Message):
  r"""A value produced by a Pipeline.

  Enums:
    TypeValueValuesEnum: Output only. The type of data that the result holds.

  Fields:
    description: Output only. Description of the result.
    name: Output only. Name of the result.
    type: Output only. The type of data that the result holds.
    value: Output only. Value of the result.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of data that the result holds.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type
      OBJECT: Object type
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  value = _messages.MessageField('ResultValue', 4)


class PipelineRun(_messages.Message):
  r"""Message describing PipelineRun object

  Enums:
    PipelineRunStatusValueValuesEnum: Pipelinerun status the user can provide.
      Used for cancellation.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations
    GcbParamsValue: Output only. GCB default params.

  Fields:
    annotations: User annotations. See https://google.aip.dev/128#annotations
    childReferences: Output only. List of TaskRun and Run names and
      PipelineTask names for children of this PipelineRun.
    completionTime: Output only. Time the pipeline completed.
    conditions: Output only. Kubernetes Conditions convention for PipelineRun
      status and error.
    createTime: Output only. Time at which the request to create the
      `PipelineRun` was received.
    etag: Needed for declarative-friendly resources.
    finallyStartTime: Output only. FinallyStartTime is when all non-finally
      tasks have been completed and only finally tasks are being executed.
      +optional
    gcbParams: Output only. GCB default params.
    name: Output only. The `PipelineRun` name with format
      `projects/{project}/locations/{location}/pipelineRuns/{pipeline_run}`
    params: Params is a list of parameter names and values.
    pipelineRef: PipelineRef refer to a specific instance of a Pipeline.
    pipelineRunStatus: Pipelinerun status the user can provide. Used for
      cancellation.
    pipelineSpec: PipelineSpec defines the desired state of Pipeline.
    pipelineSpecYaml: Output only. Inline pipelineSpec yaml string, used by
      workflow run requests.
    provenance: Optional. Provenance configuration.
    record: Output only. The `Record` of this `PipelineRun`. Format: `projects
      /{project}/locations/{location}/results/{result_id}/records/{record_id}`
    resolvedPipelineSpec: Output only. The exact PipelineSpec used to
      instantiate the run.
    results: Optional. Output only. List of results written out by the
      pipeline's containers
    security: Optional. Security configuration.
    serviceAccount: Service account used in the Pipeline. Deprecated; please
      use security.service_account instead.
    skippedTasks: Output only. List of tasks that were skipped due to when
      expressions evaluating to false.
    startTime: Output only. Time the pipeline is actually started.
    timeouts: Time after which the Pipeline times out. Currently three keys
      are accepted in the map pipeline, tasks and finally with
      Timeouts.pipeline >= Timeouts.tasks + Timeouts.finally
    uid: Output only. A unique identifier for the `PipelineRun`.
    updateTime: Output only. Time at which the request to update the
      `PipelineRun` was received.
    worker: Optional. Worker configuration.
    workerPool: Output only. The WorkerPool used to run this PipelineRun.
    workflow: Output only. The Workflow used to create this PipelineRun.
    workspaces: Workspaces is a list of WorkspaceBindings from volumes to
      workspaces.
  """

  class PipelineRunStatusValueValuesEnum(_messages.Enum):
    r"""Pipelinerun status the user can provide. Used for cancellation.

    Values:
      PIPELINE_RUN_STATUS_UNSPECIFIED: Default enum type; should not be used.
      PIPELINE_RUN_CANCELLED: Cancelled status.
    """
    PIPELINE_RUN_STATUS_UNSPECIFIED = 0
    PIPELINE_RUN_CANCELLED = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GcbParamsValue(_messages.Message):
    r"""Output only. GCB default params.

    Messages:
      AdditionalProperty: An additional property for a GcbParamsValue object.

    Fields:
      additionalProperties: Additional properties of type GcbParamsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GcbParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  childReferences = _messages.MessageField('ChildStatusReference', 2, repeated=True)
  completionTime = _messages.StringField(3)
  conditions = _messages.MessageField('GoogleDevtoolsCloudbuildV2Condition', 4, repeated=True)
  createTime = _messages.StringField(5)
  etag = _messages.StringField(6)
  finallyStartTime = _messages.StringField(7)
  gcbParams = _messages.MessageField('GcbParamsValue', 8)
  name = _messages.StringField(9)
  params = _messages.MessageField('Param', 10, repeated=True)
  pipelineRef = _messages.MessageField('PipelineRef', 11)
  pipelineRunStatus = _messages.EnumField('PipelineRunStatusValueValuesEnum', 12)
  pipelineSpec = _messages.MessageField('PipelineSpec', 13)
  pipelineSpecYaml = _messages.StringField(14)
  provenance = _messages.MessageField('Provenance', 15)
  record = _messages.StringField(16)
  resolvedPipelineSpec = _messages.MessageField('PipelineSpec', 17)
  results = _messages.MessageField('PipelineRunResult', 18, repeated=True)
  security = _messages.MessageField('Security', 19)
  serviceAccount = _messages.StringField(20)
  skippedTasks = _messages.MessageField('SkippedTask', 21, repeated=True)
  startTime = _messages.StringField(22)
  timeouts = _messages.MessageField('TimeoutFields', 23)
  uid = _messages.StringField(24)
  updateTime = _messages.StringField(25)
  worker = _messages.MessageField('Worker', 26)
  workerPool = _messages.StringField(27)
  workflow = _messages.StringField(28)
  workspaces = _messages.MessageField('WorkspaceBinding', 29, repeated=True)


class PipelineRunResult(_messages.Message):
  r"""PipelineRunResult used to describe the results of a pipeline

  Fields:
    name: Output only. Name of the TaskRun
    value: Output only. Value of the result.
  """

  name = _messages.StringField(1)
  value = _messages.MessageField('ResultValue', 2)


class PipelineSpec(_messages.Message):
  r"""PipelineSpec defines the desired state of Pipeline.

  Fields:
    finallyTasks: List of Tasks that execute just before leaving the Pipeline
      i.e. either after all Tasks are finished executing successfully or after
      a failure which would result in ending the Pipeline.
    generatedYaml: Output only. auto-generated yaml that is output only for
      display purpose for workflows using pipeline_spec, used by UI/gcloud cli
      for Workflows.
    params: List of parameters.
    results: Optional. Output only. List of results written out by the
      pipeline's containers
    tasks: List of Tasks that execute when this Pipeline is run.
    workspaces: Workspaces declares a set of named workspaces that are
      expected to be provided by a PipelineRun.
  """

  finallyTasks = _messages.MessageField('PipelineTask', 1, repeated=True)
  generatedYaml = _messages.StringField(2)
  params = _messages.MessageField('ParamSpec', 3, repeated=True)
  results = _messages.MessageField('PipelineResult', 4, repeated=True)
  tasks = _messages.MessageField('PipelineTask', 5, repeated=True)
  workspaces = _messages.MessageField('PipelineWorkspaceDeclaration', 6, repeated=True)


class PipelineTask(_messages.Message):
  r"""PipelineTask defines a task in a Pipeline.

  Fields:
    name: Name of the task.
    params: Params is a list of parameter names and values.
    retries: Retries represents how many times this task should be retried in
      case of task failure.
    runAfter: RunAfter is the list of PipelineTask names that should be
      executed before this Task executes. (Used to force a specific ordering
      in graph execution.)
    taskRef: Reference to a specific instance of a task.
    taskSpec: Spec to instantiate this TaskRun.
    timeout: Time after which the TaskRun times out. Defaults to 1 hour.
      Specified TaskRun timeout should be less than 24h.
    whenExpressions: Conditions that need to be true for the task to run.
    workspaces: Workspaces maps workspaces from the pipeline spec to the
      workspaces declared in the Task.
  """

  name = _messages.StringField(1)
  params = _messages.MessageField('Param', 2, repeated=True)
  retries = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  runAfter = _messages.StringField(4, repeated=True)
  taskRef = _messages.MessageField('TaskRef', 5)
  taskSpec = _messages.MessageField('EmbeddedTask', 6)
  timeout = _messages.StringField(7)
  whenExpressions = _messages.MessageField('WhenExpression', 8, repeated=True)
  workspaces = _messages.MessageField('WorkspacePipelineTaskBinding', 9, repeated=True)


class PipelineWorkspaceDeclaration(_messages.Message):
  r"""Workspaces declares a set of named workspaces that are expected to be
  provided by a PipelineRun.

  Fields:
    description: Description is a human readable string describing how the
      workspace will be used in the Pipeline.
    name: Name is the name of a workspace to be provided by a PipelineRun.
    optional: Optional marks a Workspace as not being required in
      PipelineRuns. By default this field is false and so declared workspaces
      are required.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  optional = _messages.BooleanField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PrivateServiceConnect(_messages.Message):
  r"""Defines the Private Service Connect network configuration for the
  WorkerPoolSecondGen.

  Fields:
    networkAttachment: Required. Immutable. The network attachment that the
      worker is peered to. Must be in the format `projects/{project}/regions/{
      region}/networkAttachments/{networkAttachment}`. The region of network
      attachment must be the same as the worker pool. See [Network
      Attachments](https://cloud.google.com/vpc/docs/about-network-
      attachments)
    routeAllTraffic: Immutable. Route all traffic through PSC interface.
      Enable this if you want full control of traffic in the private pool.
      Configure Cloud NAT for the subnet of network attachment if you need to
      access public Internet. If true, all traffic will go through the non-
      primary network interface, the boolean `public_ip_address_disabled` in
      Network Config has no effect. If false, Only route private IPs,
      including 10.0.0.0/8, **********/12, and ***********/16 through PSC
      interface.
  """

  networkAttachment = _messages.StringField(1)
  routeAllTraffic = _messages.BooleanField(2)


class Probe(_messages.Message):
  r"""Probe describes a health check to be performed against a container to
  determine whether it is alive or ready to receive traffic.

  Fields:
    exec_: Optional. Exec specifies the action to take. +optional
    periodSeconds: Optional. How often (in seconds) to perform the probe.
      Default to 10 seconds. Minimum value is 1. +optional
  """

  exec_ = _messages.MessageField('ExecAction', 1)
  periodSeconds = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ProcessWorkflowTriggerWebhookRequest(_messages.Message):
  r"""Message for processing webhooks posted to WorkflowTrigger.

  Fields:
    body: Required. The webhook body in JSON.
    secretToken: Required. The secret token used for authorization based on
      the matching result between this and the secret stored in
      WorkflowTrigger.
    triggerId: Required. The WorkflowTrigger id.
  """

  body = _messages.MessageField('HttpBody', 1)
  secretToken = _messages.StringField(2)
  triggerId = _messages.StringField(3)


class ProcessWorkflowTriggerWebhookResponse(_messages.Message):
  r"""message for processing webhooks posted to WorkflowTrigger."""


class PropertySpec(_messages.Message):
  r"""PropertySpec holds information about a property in an object.

  Enums:
    TypeValueValuesEnum: A type for the object.

  Fields:
    type: A type for the object.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""A type for the object.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Provenance(_messages.Message):
  r"""Provenance configuration.

  Enums:
    EnabledValueValuesEnum: Optional. Provenance push mode.
    RegionValueValuesEnum: Optional. Provenance region.
    StorageValueValuesEnum: Optional. Where provenance is stored.

  Fields:
    enabled: Optional. Provenance push mode.
    region: Optional. Provenance region.
    storage: Optional. Where provenance is stored.
  """

  class EnabledValueValuesEnum(_messages.Enum):
    r"""Optional. Provenance push mode.

    Values:
      ENABLED_UNSPECIFIED: Default to disabled (before AA regionalization),
        optimistic after
      REQUIRED: Provenance failures would fail the run
      OPTIMISTIC: GCB will attempt to push to artifact analaysis and build
        state would not be impacted by the push failures.
      DISABLED: Disable the provenance push entirely.
    """
    ENABLED_UNSPECIFIED = 0
    REQUIRED = 1
    OPTIMISTIC = 2
    DISABLED = 3

  class RegionValueValuesEnum(_messages.Enum):
    r"""Optional. Provenance region.

    Values:
      REGION_UNSPECIFIED: The PipelineRun/TaskRun/Workflow will be rejected.
        Update this comment to push to the same region as the run in Artifact
        Analysis when it's regionalized.
      GLOBAL: Push provenance to Artifact Analysis in global region.
    """
    REGION_UNSPECIFIED = 0
    GLOBAL = 1

  class StorageValueValuesEnum(_messages.Enum):
    r"""Optional. Where provenance is stored.

    Values:
      STORAGE_UNSPECIFIED: Default PREFER_ARTIFACT_PROJECT.
      PREFER_ARTIFACT_PROJECT: GCB will attempt to push provenance to the
        artifact project. If it is not available, fallback to build project.
      ARTIFACT_PROJECT_ONLY: Only push to artifact project.
      BUILD_PROJECT_ONLY: Only push to build project.
    """
    STORAGE_UNSPECIFIED = 0
    PREFER_ARTIFACT_PROJECT = 1
    ARTIFACT_PROJECT_ONLY = 2
    BUILD_PROJECT_ONLY = 3

  enabled = _messages.EnumField('EnabledValueValuesEnum', 1)
  region = _messages.EnumField('RegionValueValuesEnum', 2)
  storage = _messages.EnumField('StorageValueValuesEnum', 3)


class PullRequest(_messages.Message):
  r"""Pull request configuration for filters.

  Enums:
    PusherValueValuesEnum: Allowed PR role that triggers a Workflow.

  Fields:
    comment: Comment that should be included to trigger a Workflow.
    pusher: Allowed PR role that triggers a Workflow.
  """

  class PusherValueValuesEnum(_messages.Enum):
    r"""Allowed PR role that triggers a Workflow.

    Values:
      PUSHER_UNSPECIFIED: Default to OWNER_AND_COLLABORATOR.
      OWNER_AND_COLLABORATORS: PR author are ownes and/or collaborators of the
        SCM repo.
      OWNER: PR author is the owner of the SCM repo.
      ALL_USERS: PR author can be everyone.
    """
    PUSHER_UNSPECIFIED = 0
    OWNER_AND_COLLABORATORS = 1
    OWNER = 2
    ALL_USERS = 3

  comment = _messages.StringField(1)
  pusher = _messages.EnumField('PusherValueValuesEnum', 2)


class ReadyWorkers(_messages.Message):
  r"""Defines the configuration for ready workers in the WorkerPoolSecondGen.

  Fields:
    count: Optional. Amount of ready workers for the WorkerPoolSecondGen.
  """

  count = _messages.IntegerField(1)


class Record(_messages.Message):
  r"""Record belonging to a Result.

  Fields:
    createTime: Output only. Server assigned timestamp for when the record was
      created.
    data: Output only. JSON serialized Record data.
    name: Output only. The name of Record.
    uid: Output only. Server assigned uid.
    updateTime: Output only. Server assigned timestamp for when the record was
      updated.
  """

  createTime = _messages.StringField(1)
  data = _messages.MessageField('JsonAny', 2)
  name = _messages.StringField(3)
  uid = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class RecordSummary(_messages.Message):
  r"""LINT.IfChange(RecordSummary) Summary of the underlying Record.

  Enums:
    StatusValueValuesEnum: Output only. Status of the underlying Run of this
      Record

  Messages:
    RecordDataValue: Output only. Key-value pairs representing underlying
      record data, e.g. "status", "SUCCESS"

  Fields:
    createTime: Output only. The time the Record was created.
    record: Output only. Summarized record.
    recordData: Output only. Key-value pairs representing underlying record
      data, e.g. "status", "SUCCESS"
    status: Output only. Status of the underlying Run of this Record
    type: Output only. Identifier of underlying data. e.g.
      `cloudbuild.googleapis.com/PipelineRun`
    updateTime: Output only. The time the Record was updated.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the underlying Run of this Record

    Values:
      STATUS_UNSPECIFIED: Default enum type; should not be used.
      SUCCESS: Run was successful
      FAILURE: Run failed
      TIMEOUT: Run timed out
      CANCELLED: Run got cancelled
      IN_PROGRESS: Run is in progress
      QUEUED: Run is queued
    """
    STATUS_UNSPECIFIED = 0
    SUCCESS = 1
    FAILURE = 2
    TIMEOUT = 3
    CANCELLED = 4
    IN_PROGRESS = 5
    QUEUED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RecordDataValue(_messages.Message):
    r"""Output only. Key-value pairs representing underlying record data, e.g.
    "status", "SUCCESS"

    Messages:
      AdditionalProperty: An additional property for a RecordDataValue object.

    Fields:
      additionalProperties: Additional properties of type RecordDataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RecordDataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  record = _messages.StringField(2)
  recordData = _messages.MessageField('RecordDataValue', 3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)
  type = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class Repository(_messages.Message):
  r"""A repository associated to a parent connection.

  Messages:
    AnnotationsValue: Optional. Allows clients to store small amounts of
      arbitrary data.

  Fields:
    annotations: Optional. Allows clients to store small amounts of arbitrary
      data.
    createTime: Output only. Server assigned timestamp for when the connection
      was created.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    name: Immutable. Resource name of the repository, in the format
      `projects/*/locations/*/connections/*/repositories/*`.
    remoteUri: Required. Git Clone HTTPS URI.
    updateTime: Output only. Server assigned timestamp for when the connection
      was updated.
    webhookId: Output only. External ID of the webhook created for the
      repository.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows clients to store small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  remoteUri = _messages.StringField(5)
  updateTime = _messages.StringField(6)
  webhookId = _messages.StringField(7)


class Resource(_messages.Message):
  r"""Resource referenceable within a workflow.

  Fields:
    gitRepoLink: Developer Connect GitRepositoryLink.
    repo: Resource name of v2 GCB repo.
    secretVersion: Secret manager secret.
    topic: Resource name of PubSub topic.
    url: SCM Repo URL.
  """

  gitRepoLink = _messages.StringField(1)
  repo = _messages.StringField(2)
  secretVersion = _messages.StringField(3)
  topic = _messages.StringField(4)
  url = _messages.StringField(5)


class Result(_messages.Message):
  r"""Result of a single event/execution.

  Fields:
    createTime: Output only. Server assigned timestamp for when the result was
      created.
    name: Output only. The name of Result.
    recordSummaries: Output only. Summary of the underlying Record. GCB only
      returns a summary for the primary Record, e.g. a PipelineRun but not its
      TaskRuns.
    uid: Output only. Server assigned uid.
    updateTime: Output only. Server assigned timestamp for when the result was
      updated.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  recordSummaries = _messages.MessageField('RecordSummary', 3, repeated=True)
  uid = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class ResultValue(_messages.Message):
  r"""ResultValue holds different types of data for a single result.

  Enums:
    TypeValueValuesEnum: Output only. The type of data that the result holds.

  Messages:
    ObjectValValue: Value of the result if type is object.

  Fields:
    arrayVal: Value of the result if type is array.
    objectVal: Value of the result if type is object.
    stringVal: Value of the result if type is string.
    type: Output only. The type of data that the result holds.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of data that the result holds.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type
      OBJECT: Object type
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ObjectValValue(_messages.Message):
    r"""Value of the result if type is object.

    Messages:
      AdditionalProperty: An additional property for a ObjectValValue object.

    Fields:
      additionalProperties: Additional properties of type ObjectValValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ObjectValValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arrayVal = _messages.StringField(1, repeated=True)
  objectVal = _messages.MessageField('ObjectValValue', 2)
  stringVal = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class RunWorkflowCustomOperationMetadata(_messages.Message):
  r"""Represents the custom metadata of the RunWorkflow long-running
  operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    pipelineRunId: Output only. ID of the pipeline run created by RunWorkflow.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  pipelineRunId = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class RunWorkflowRequest(_messages.Message):
  r"""Message for running a Workflow.

  Fields:
    etag: Needed for declarative-friendly resources.
    params: Run-time params.
    validateOnly: When true, the query is validated only, but not executed.
  """

  etag = _messages.StringField(1)
  params = _messages.MessageField('Param', 2, repeated=True)
  validateOnly = _messages.BooleanField(3)


class SecretVolumeSource(_messages.Message):
  r"""Secret Volume Source.

  Fields:
    secretName: Name of the secret referenced by the WorkspaceBinding.
    secretVersion: Optional. Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  secretName = _messages.StringField(1)
  secretVersion = _messages.StringField(2)


class Security(_messages.Message):
  r"""Security configuration.

  Enums:
    PrivilegeModeValueValuesEnum: Optional. Privilege mode.

  Fields:
    privilegeMode: Optional. Privilege mode.
    serviceAccount: IAM service account whose credentials will be used at
      runtime.
  """

  class PrivilegeModeValueValuesEnum(_messages.Enum):
    r"""Optional. Privilege mode.

    Values:
      PRIVILEGE_MODE_UNSPECIFIED: Default to PRIVILEGED.
      PRIVILEGED: Privileged mode.
      UNPRIVILEGED: Unprivileged mode.
    """
    PRIVILEGE_MODE_UNSPECIFIED = 0
    PRIVILEGED = 1
    UNPRIVILEGED = 2

  privilegeMode = _messages.EnumField('PrivilegeModeValueValuesEnum', 1)
  serviceAccount = _messages.StringField(2)


class SecurityContext(_messages.Message):
  r"""Security options the container should be run with.

  Fields:
    allowPrivilegeEscalation: Optional. AllowPrivilegeEscalation controls
      whether a process can gain more privileges than its parent process. This
      bool directly controls if the no_new_privs flag will be set on the
      container process. AllowPrivilegeEscalation is true always when the
      container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this
      field cannot be set when spec.os.name is windows. +optional
    capabilities: Optional. Adds and removes POSIX capabilities from running
      containers.
    privileged: Run container in privileged mode.
    runAsGroup: Optional. The GID to run the entrypoint of the container
      process. Uses runtime default if unset. May also be set in
      PodSecurityContext. If set in both SecurityContext and
      PodSecurityContext, the value specified in SecurityContext takes
      precedence. Note that this field cannot be set when spec.os.name is
      windows. +optional
    runAsNonRoot: Optional. Indicates that the container must run as a non-
      root user. If true, the Kubelet will validate the image at runtime to
      ensure that it does not run as UID 0 (root) and fail to start the
      container if it does. If unset or false, no such validation will be
      performed. May also be set in PodSecurityContext. If set in both
      SecurityContext and PodSecurityContext, the value specified in
      SecurityContext takes precedence. +optional
    runAsUser: Optional. The UID to run the entrypoint of the container
      process. Defaults to user specified in image metadata if unspecified.
      May also be set in PodSecurityContext. If set in both SecurityContext
      and PodSecurityContext, the value specified in SecurityContext takes
      precedence. Note that this field cannot be set when spec.os.name is
      windows. +optional
  """

  allowPrivilegeEscalation = _messages.BooleanField(1)
  capabilities = _messages.MessageField('Capabilities', 2)
  privileged = _messages.BooleanField(3)
  runAsGroup = _messages.IntegerField(4)
  runAsNonRoot = _messages.BooleanField(5)
  runAsUser = _messages.IntegerField(6)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Sidecar(_messages.Message):
  r"""Sidecars run alongside the Task's step containers.

  Fields:
    args: Arguments to the entrypoint.
    command: Entrypoint array.
    env: List of environment variables to set in the container.
    image: Docker image name.
    name: Name of the Sidecar.
    readinessProbe: Optional. Periodic probe of Sidecar service readiness.
      Container will be removed from service endpoints if the probe fails.
      Cannot be updated. More info:
      https://kubernetes.io/docs/concepts/workloads/pods/pod-
      lifecycle#container-probes +optional
    script: The contents of an executable file to execute.
    securityContext: Optional. Security options the container should be run
      with.
    volumeMounts: Pod volumes to mount into the container's filesystem.
    workingDir: Container's working directory.
  """

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('EnvVar', 3, repeated=True)
  image = _messages.StringField(4)
  name = _messages.StringField(5)
  readinessProbe = _messages.MessageField('Probe', 6)
  script = _messages.StringField(7)
  securityContext = _messages.MessageField('SecurityContext', 8)
  volumeMounts = _messages.MessageField('VolumeMount', 9, repeated=True)
  workingDir = _messages.StringField(10)


class SidecarState(_messages.Message):
  r"""The state of a sidecar.

  Fields:
    containerName: Name of the container.
    imageId: ID of the image.
    name: Name of the Sidecar.
    running: Details about a running container.
    terminated: Details about a terminated container.
    waiting: Details about a waiting container.
  """

  containerName = _messages.StringField(1)
  imageId = _messages.StringField(2)
  name = _messages.StringField(3)
  running = _messages.MessageField('ContainerStateRunning', 4)
  terminated = _messages.MessageField('ContainerStateTerminated', 5)
  waiting = _messages.MessageField('ContainerStateWaiting', 6)


class SkippedTask(_messages.Message):
  r"""SkippedTask is used to describe the Tasks that were skipped due to their
  When Expressions evaluating to False.

  Fields:
    name: Name is the Pipeline Task name
    reason: Output only. Reason is the cause of the PipelineTask being
      skipped.
    whenExpressions: WhenExpressions is the list of checks guarding the
      execution of the PipelineTask
  """

  name = _messages.StringField(1)
  reason = _messages.StringField(2)
  whenExpressions = _messages.MessageField('WhenExpression', 3, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""Step embeds the Container type, which allows it to include fields not
  provided by Container.

  Enums:
    OnErrorValueValuesEnum: Optional. OnError defines the exiting behavior on
      error can be set to [ continue | stopAndFail ]

  Fields:
    args: Arguments to the entrypoint.
    command: Entrypoint array.
    env: List of environment variables to set in the container.
    image: Docker image name.
    name: Name of the container specified as a DNS_LABEL.
    onError: Optional. OnError defines the exiting behavior on error can be
      set to [ continue | stopAndFail ]
    params: Optional. Optional parameters passed to the StepAction.
    ref: Optional. Optional reference to a remote StepAction.
    script: The contents of an executable file to execute.
    securityContext: Optional. SecurityContext defines the security options
      the Step should be run with. If set, the fields of SecurityContext
      override the equivalent fields of PodSecurityContext. More info:
      https://kubernetes.io/docs/tasks/configure-pod-container/security-
      context/ +optional
    timeout: Time after which the Step times out. Defaults to never.
    volumeMounts: Pod volumes to mount into the container's filesystem.
    workingDir: Container's working directory.
  """

  class OnErrorValueValuesEnum(_messages.Enum):
    r"""Optional. OnError defines the exiting behavior on error can be set to
    [ continue | stopAndFail ]

    Values:
      ON_ERROR_TYPE_UNSPECIFIED: Default enum type; should not be used.
      STOP_AND_FAIL: StopAndFail indicates exit if the step/task exits with
        non-zero exit code
      CONTINUE: Continue indicates continue executing the rest of the
        steps/tasks irrespective of the exit code
    """
    ON_ERROR_TYPE_UNSPECIFIED = 0
    STOP_AND_FAIL = 1
    CONTINUE = 2

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('EnvVar', 3, repeated=True)
  image = _messages.StringField(4)
  name = _messages.StringField(5)
  onError = _messages.EnumField('OnErrorValueValuesEnum', 6)
  params = _messages.MessageField('Param', 7, repeated=True)
  ref = _messages.MessageField('StepRef', 8)
  script = _messages.StringField(9)
  securityContext = _messages.MessageField('SecurityContext', 10)
  timeout = _messages.StringField(11)
  volumeMounts = _messages.MessageField('VolumeMount', 12, repeated=True)
  workingDir = _messages.StringField(13)


class StepRef(_messages.Message):
  r"""A reference to a remote Step, i.e. a StepAction.

  Enums:
    ResolverValueValuesEnum: Optional. Type of the resolver.

  Fields:
    name: Optional. Name of the step.
    params: Optional. Parameters used to control the resolution.
    resolver: Optional. Type of the resolver.
  """

  class ResolverValueValuesEnum(_messages.Enum):
    r"""Optional. Type of the resolver.

    Values:
      RESOLVER_NAME_UNSPECIFIED: Default enum type; should not be used.
      BUNDLES: Bundles resolver. https://tekton.dev/docs/pipelines/bundle-
        resolver/
      GCB_REPO: GCB repo resolver.
      GIT: Simple Git resolver. https://tekton.dev/docs/pipelines/git-
        resolver/
      DEVELOPER_CONNECT: Developer Connect resolver.
      DEFAULT: Default resolver.
    """
    RESOLVER_NAME_UNSPECIFIED = 0
    BUNDLES = 1
    GCB_REPO = 2
    GIT = 3
    DEVELOPER_CONNECT = 4
    DEFAULT = 5

  name = _messages.StringField(1)
  params = _messages.MessageField('Param', 2, repeated=True)
  resolver = _messages.EnumField('ResolverValueValuesEnum', 3)


class StepState(_messages.Message):
  r"""StepState reports the results of running a step in a Task.

  Fields:
    imageId: Image ID of the StepState.
    name: Name of the StepState.
    results: Output only. Holds optional results produced by a StepAction.
    running: Details about a running container
    terminated: Details about a terminated container
    terminationReason: Output only. Describes the final status of a Step.
    waiting: Details about a waiting container
  """

  imageId = _messages.StringField(1)
  name = _messages.StringField(2)
  results = _messages.MessageField('TaskRunResult', 3, repeated=True)
  running = _messages.MessageField('ContainerStateRunning', 4)
  terminated = _messages.MessageField('ContainerStateTerminated', 5)
  terminationReason = _messages.StringField(6)
  waiting = _messages.MessageField('ContainerStateWaiting', 7)


class StepTemplate(_messages.Message):
  r"""StepTemplate can be used as the basis for all step containers within the
  Task, so that the steps inherit settings on the base container.

  Fields:
    env: Optional. List of environment variables to set in the Step. Cannot be
      updated.
    volumeMounts: Optional. Pod volumes to mount into the container's
      filesystem.
  """

  env = _messages.MessageField('EnvVar', 1, repeated=True)
  volumeMounts = _messages.MessageField('VolumeMount', 2, repeated=True)


class TaskRef(_messages.Message):
  r"""TaskRef can be used to refer to a specific instance of a task.
  PipelineRef can be used to refer to a specific instance of a Pipeline.

  Enums:
    ResolverValueValuesEnum: Resolver is the name of the resolver that should
      perform resolution of the referenced Tekton resource.

  Fields:
    name: Optional. Name of the task.
    params: Params contains the parameters used to identify the referenced
      Tekton resource. Example entries might include "repo" or "path" but the
      set of params ultimately depends on the chosen resolver.
    resolver: Resolver is the name of the resolver that should perform
      resolution of the referenced Tekton resource.
  """

  class ResolverValueValuesEnum(_messages.Enum):
    r"""Resolver is the name of the resolver that should perform resolution of
    the referenced Tekton resource.

    Values:
      RESOLVER_NAME_UNSPECIFIED: Default enum type; should not be used.
      BUNDLES: Bundles resolver. https://tekton.dev/docs/pipelines/bundle-
        resolver/
      GCB_REPO: GCB repo resolver.
      GIT: Simple Git resolver. https://tekton.dev/docs/pipelines/git-
        resolver/
      DEVELOPER_CONNECT: Developer Connect resolver.
      DEFAULT: Default resolver.
    """
    RESOLVER_NAME_UNSPECIFIED = 0
    BUNDLES = 1
    GCB_REPO = 2
    GIT = 3
    DEVELOPER_CONNECT = 4
    DEFAULT = 5

  name = _messages.StringField(1)
  params = _messages.MessageField('Param', 2, repeated=True)
  resolver = _messages.EnumField('ResolverValueValuesEnum', 3)


class TaskResult(_messages.Message):
  r"""TaskResult is used to describe the results of a task.

  Enums:
    TypeValueValuesEnum: The type of data that the result holds.

  Messages:
    PropertiesValue: When type is OBJECT, this map holds the names of fields
      inside that object along with the type of data each field holds.

  Fields:
    description: Description of the result.
    name: Name of the result.
    properties: When type is OBJECT, this map holds the names of fields inside
      that object along with the type of data each field holds.
    type: The type of data that the result holds.
    value: Optional. Optionally used to initialize a Task's result with a
      Step's result.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of data that the result holds.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type
      OBJECT: Object type
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""When type is OBJECT, this map holds the names of fields inside that
    object along with the type of data each field holds.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A PropertySpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PropertySpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  properties = _messages.MessageField('PropertiesValue', 3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  value = _messages.MessageField('ParamValue', 5)


class TaskRun(_messages.Message):
  r"""Message describing TaskRun object

  Enums:
    TaskRunStatusValueValuesEnum: Taskrun status the user can provide. Used
      for cancellation.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations
    GcbParamsValue: Output only. GCB default params.

  Fields:
    annotations: User annotations. See https://google.aip.dev/128#annotations
    completionTime: Output only. Time the task completed.
    conditions: Output only. Kubernetes Conditions convention for PipelineRun
      status and error.
    createTime: Output only. Time at which the request to create the `TaskRun`
      was received.
    etag: Needed for declarative-friendly resources.
    gcbParams: Output only. GCB default params.
    name: Output only. The 'TaskRun' name with format:
      `projects/{project}/locations/{location}/taskRuns/{task_run}`
    params: Params is a list of parameter names and values.
    pipelineRun: Output only. Name of the parent PipelineRun. If it is a
      standalone TaskRun (no parent), this field will not be set.
    provenance: Optional. Provenance configuration.
    record: Output only. The `Record` of this `TaskRun`. Format: `projects/{pr
      oject}/locations/{location}/results/{result_id}/records/{record_id}`
    resolvedTaskSpec: Output only. The exact TaskSpec used to instantiate the
      run.
    results: Output only. List of results written out by the task's containers
    security: Optional. Security configuration.
    serviceAccount: Required. Service account used in the task. Deprecated;
      please use security.service_account instead.
    sidecars: Output only. State of each Sidecar in the TaskSpec.
    startTime: Output only. Time the task is actually started.
    statusMessage: Optional. Output only. Status message for cancellation.
      +optional
    steps: Output only. Steps describes the state of each build step
      container.
    taskRef: TaskRef refer to a specific instance of a task.
    taskRunStatus: Taskrun status the user can provide. Used for cancellation.
    taskSpec: TaskSpec contains the Spec to instantiate a TaskRun.
    timeout: Time after which the task times out. Defaults to 1 hour. If you
      set the timeout to 0, the TaskRun will have no timeout and will run
      until it completes successfully or fails from an error.
    uid: Output only. A unique identifier for the `TaskRun`.
    updateTime: Output only. Time at which the request to update the `TaskRun`
      was received.
    worker: Optional. Worker configuration.
    workerPool: Output only. The WorkerPool used to run this TaskRun.
    workspaces: Workspaces is a list of WorkspaceBindings from volumes to
      workspaces.
  """

  class TaskRunStatusValueValuesEnum(_messages.Enum):
    r"""Taskrun status the user can provide. Used for cancellation.

    Values:
      TASK_RUN_STATUS_UNSPECIFIED: Default enum type; should not be used.
      TASK_RUN_CANCELLED: Cancelled status.
    """
    TASK_RUN_STATUS_UNSPECIFIED = 0
    TASK_RUN_CANCELLED = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GcbParamsValue(_messages.Message):
    r"""Output only. GCB default params.

    Messages:
      AdditionalProperty: An additional property for a GcbParamsValue object.

    Fields:
      additionalProperties: Additional properties of type GcbParamsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GcbParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  completionTime = _messages.StringField(2)
  conditions = _messages.MessageField('GoogleDevtoolsCloudbuildV2Condition', 3, repeated=True)
  createTime = _messages.StringField(4)
  etag = _messages.StringField(5)
  gcbParams = _messages.MessageField('GcbParamsValue', 6)
  name = _messages.StringField(7)
  params = _messages.MessageField('Param', 8, repeated=True)
  pipelineRun = _messages.StringField(9)
  provenance = _messages.MessageField('Provenance', 10)
  record = _messages.StringField(11)
  resolvedTaskSpec = _messages.MessageField('TaskSpec', 12)
  results = _messages.MessageField('TaskRunResult', 13, repeated=True)
  security = _messages.MessageField('Security', 14)
  serviceAccount = _messages.StringField(15)
  sidecars = _messages.MessageField('SidecarState', 16, repeated=True)
  startTime = _messages.StringField(17)
  statusMessage = _messages.StringField(18)
  steps = _messages.MessageField('StepState', 19, repeated=True)
  taskRef = _messages.MessageField('TaskRef', 20)
  taskRunStatus = _messages.EnumField('TaskRunStatusValueValuesEnum', 21)
  taskSpec = _messages.MessageField('TaskSpec', 22)
  timeout = _messages.StringField(23)
  uid = _messages.StringField(24)
  updateTime = _messages.StringField(25)
  worker = _messages.MessageField('Worker', 26)
  workerPool = _messages.StringField(27)
  workspaces = _messages.MessageField('WorkspaceBinding', 28, repeated=True)


class TaskRunResult(_messages.Message):
  r"""TaskRunResult used to describe the results of a task

  Enums:
    TypeValueValuesEnum: The type of data that the result holds.

  Fields:
    name: Name of the TaskRun
    resultValue: Value of the result.
    type: The type of data that the result holds.
    value: Value of the result. Deprecated; please use result_value instead.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of data that the result holds.

    Values:
      TYPE_UNSPECIFIED: Default enum type; should not be used.
      STRING: Default
      ARRAY: Array type
      OBJECT: Object type
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    ARRAY = 2
    OBJECT = 3

  name = _messages.StringField(1)
  resultValue = _messages.MessageField('ResultValue', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  value = _messages.StringField(4)


class TaskSpec(_messages.Message):
  r"""TaskSpec contains the Spec to instantiate a TaskRun.

  Enums:
    ManagedSidecarsValueListEntryValuesEnum:

  Fields:
    description: Description of the task.
    managedSidecars: Sidecars that run alongside the Task's step containers
      that should be added to this Task.
    params: List of parameters.
    results: Values that this Task can output.
    sidecars: Sidecars that run alongside the Task's step containers.
    stepTemplate: Optional. StepTemplate can be used as the basis for all step
      containers within the Task, so that the steps inherit settings on the
      base container.
    steps: Steps of the task.
    volumes: A collection of volumes that are available to mount into steps.
    workspaces: The volumes that this Task requires.
  """

  class ManagedSidecarsValueListEntryValuesEnum(_messages.Enum):
    r"""ManagedSidecarsValueListEntryValuesEnum enum type.

    Values:
      MANAGED_SIDECAR_UNSPECIFIED: Default enum type; should not be used.
      PRIVILEGED_DOCKER_DAEMON: Sidecar for a privileged docker daemon.
    """
    MANAGED_SIDECAR_UNSPECIFIED = 0
    PRIVILEGED_DOCKER_DAEMON = 1

  description = _messages.StringField(1)
  managedSidecars = _messages.EnumField('ManagedSidecarsValueListEntryValuesEnum', 2, repeated=True)
  params = _messages.MessageField('ParamSpec', 3, repeated=True)
  results = _messages.MessageField('TaskResult', 4, repeated=True)
  sidecars = _messages.MessageField('Sidecar', 5, repeated=True)
  stepTemplate = _messages.MessageField('StepTemplate', 6)
  steps = _messages.MessageField('Step', 7, repeated=True)
  volumes = _messages.MessageField('VolumeSource', 8, repeated=True)
  workspaces = _messages.MessageField('WorkspaceDeclaration', 9, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeoutFields(_messages.Message):
  r"""TimeoutFields allows granular specification of pipeline, task, and
  finally timeouts

  Fields:
    finally_: Finally sets the maximum allowed duration of this pipeline's
      finally
    pipeline: Pipeline sets the maximum allowed duration for execution of the
      entire pipeline. The sum of individual timeouts for tasks and finally
      must not exceed this value.
    tasks: Tasks sets the maximum allowed duration of this pipeline's tasks
  """

  finally_ = _messages.StringField(1)
  pipeline = _messages.StringField(2)
  tasks = _messages.StringField(3)


class UserCredential(_messages.Message):
  r"""Represents a personal access token that authorized the Connection, and
  associated metadata.

  Fields:
    userTokenSecretVersion: Required. A SecretManager resource containing the
      user token that authorizes the Cloud Build connection. Format:
      `projects/*/secrets/*/versions/*`.
    username: Output only. The username associated to this token.
  """

  userTokenSecretVersion = _messages.StringField(1)
  username = _messages.StringField(2)


class VolumeClaim(_messages.Message):
  r"""VolumeClaim is a user's request for a volume.

  Fields:
    storage: Volume size, e.g. 1gb.
  """

  storage = _messages.StringField(1)


class VolumeMount(_messages.Message):
  r"""Pod volumes to mount into the container's filesystem.

  Fields:
    mountPath: Path within the container at which the volume should be
      mounted. Must not contain ':'.
    name: Name of the volume.
    readOnly: Mounted read-only if true, read-write otherwise (false or
      unspecified).
    subPath: Path within the volume from which the container's volume should
      be mounted. Defaults to "" (volume's root).
    subPathExpr: Expanded path within the volume from which the container's
      volume should be mounted. Behaves similarly to SubPath but environment
      variable references $(VAR_NAME) are expanded using the container's
      environment. Defaults to "" (volume's root).
  """

  mountPath = _messages.StringField(1)
  name = _messages.StringField(2)
  readOnly = _messages.BooleanField(3)
  subPath = _messages.StringField(4)
  subPathExpr = _messages.StringField(5)


class VolumeSource(_messages.Message):
  r"""Volumes available to mount.

  Fields:
    emptyDir: A temporary directory that shares a pod's lifetime.
    name: Name of the Volume. Must be a DNS_LABEL and unique within the pod.
      More info: https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/#names
  """

  emptyDir = _messages.MessageField('EmptyDirVolumeSource', 1)
  name = _messages.StringField(2)


class WebhookSecret(_messages.Message):
  r"""Webhook secret referenceable within a WorkflowTrigger.

  Fields:
    id: identification to secret Resource.
    secretVersion: Output only. Secret Manager version.
  """

  id = _messages.StringField(1)
  secretVersion = _messages.StringField(2)


class WhenExpression(_messages.Message):
  r"""Conditions that need to be true for the task to run.

  Enums:
    ExpressionOperatorValueValuesEnum: Operator that represents an Input's
      relationship to the values

  Fields:
    expressionOperator: Operator that represents an Input's relationship to
      the values
    input: Input is the string for guard checking which can be a static input
      or an output from a parent Task.
    values: Values is an array of strings, which is compared against the
      input, for guard checking.
  """

  class ExpressionOperatorValueValuesEnum(_messages.Enum):
    r"""Operator that represents an Input's relationship to the values

    Values:
      EXPRESSION_OPERATOR_UNSPECIFIED: Default enum type; should not be used.
      IN: Input is in values.
      NOT_IN: Input is not in values.
    """
    EXPRESSION_OPERATOR_UNSPECIFIED = 0
    IN = 1
    NOT_IN = 2

  expressionOperator = _messages.EnumField('ExpressionOperatorValueValuesEnum', 1)
  input = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class Worker(_messages.Message):
  r"""Configuration for the worker.

  Fields:
    machineType: Optional. Machine type of a worker, default is
      "e2-standard-2".
  """

  machineType = _messages.StringField(1)


class WorkerConfig(_messages.Message):
  r"""Defines the configuration to be used for creating workers in the
  WorkerPoolSecondGen.

  Fields:
    diskStorage: Optional. Disk space for user workloads, in GB. Specify a
      value of up to 2000. If `0` is specified, Cloud Build will use a default
      disk size of 100GB.
    machineType: Optional. Machine type of a worker in the pool, such as
      `e2-standard-2`. If left blank, Cloud Build will use a sensible default.
  """

  diskStorage = _messages.IntegerField(1)
  machineType = _messages.StringField(2)


class WorkerPoolSecondGen(_messages.Message):
  r"""Configuration for a `WorkerPoolSecondGen`. If your workload needs access
  to resources on a private network, create and use a `WorkerPoolSecondGen` to
  run your workloads. `WorkerPoolSecondGen`s give your workloads access to any
  single VPC network that you administer, including any on-prem resources
  connected to that VPC network.

  Enums:
    StateValueValuesEnum: Output only. `WorkerPoolSecondGen` state.

  Messages:
    AnnotationsValue: Optional. User specified annotations. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.

  Fields:
    annotations: Optional. User specified annotations. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    createTime: Output only. Time at which the request to create the
      `WorkerPoolSecondGen` was received.
    deleteTime: Output only. Time at which the request to delete the
      `WorkerPoolSecondGen` was received.
    displayName: Optional. A user-specified, human-readable name for the
      `WorkerPoolSecondGen`. If provided, this value must be 1-63 characters.
    etag: Output only. Checksum computed by the server. May be sent on update
      and delete requests to ensure that the client has an up-to-date value
      before proceeding.
    name: Output only. Identifier. The resource name of the
      `WorkerPoolSecondGen`, with format `projects/{project}/locations/{locati
      on}/workerPoolSecondGen/{worker_pool_second_gen}`.
    network: Optional. Network configuration for the `WorkerPoolSecondGen`.
    readyWorkers: Optional. Configuration for ready workers in the
      WorkerPoolSecondGen.
    reconciling: Output only. If true, this WorkerPoolSecondGen is being
      updated. If false, this WorkerPoolSecondGen matches the user's intent.
    state: Output only. `WorkerPoolSecondGen` state.
    uid: Output only. A unique identifier for the `WorkerPoolSecondGen`.
    updateTime: Output only. Time at which the request to update the
      `WorkerPoolSecondGen` was received.
    worker: Optional. Worker configuration for the `WorkerPoolSecondGen`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. `WorkerPoolSecondGen` state.

    Values:
      STATE_UNSPECIFIED: State of the `WorkerPoolSecondGen` is unknown.
      CREATING: `WorkerPoolSecondGen` is being created.
      RUNNING: `WorkerPoolSecondGen` is running.
      DELETING: `WorkerPoolSecondGen` is being deleted: cancelling runs and
        draining workers.
      DELETED: `WorkerPoolSecondGen` is deleted.
      UPDATING: `WorkerPoolSecondGen` is being updated; new runs cannot be
        performed.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    DELETING = 3
    DELETED = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User specified annotations. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  network = _messages.MessageField('NetworkConfig', 7)
  readyWorkers = _messages.MessageField('ReadyWorkers', 8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  worker = _messages.MessageField('WorkerConfig', 13)


class Workflow(_messages.Message):
  r"""Message describing Workflow object.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations
    ResourcesValue: Resources referenceable within a workflow.

  Fields:
    annotations: User annotations. See https://google.aip.dev/128#annotations
    createTime: Output only. Server assigned timestamp for when the workflow
      was created.
    deleteTime: Output only. Server assigned timestamp for when the workflow
      was deleted. Deprecated; will be removed soon.
    etag: Needed for declarative-friendly resources.
    name: Output only. Format:
      `projects/{project}/locations/{location}/workflows/{workflow}`
    options: Workflow runs can be modified through several Workflow options.
    params: List of parameters.
    pipelineRef: PipelineRef refer to a specific instance of a Pipeline.
    pipelineSpec: Fields from both the Workflow and the PipelineSpec will be
      used to form the full PipelineRun.
    pipelineSpecYaml: PipelineSpec in yaml format.
    ref: PipelineRef refer to a specific instance of a Pipeline. Deprecated;
      please use pipeline_ref instead.
    resources: Resources referenceable within a workflow.
    secrets: Optional. Secrets referenceable within a workflow.
    serviceAccount: If omitted, the default Cloud Build Service Account is
      used instead. Format:
      `projects/{project}/serviceAccounts/{serviceAccount}` Deprecated; please
      use options.security.service_account instead.
    uid: Output only. A unique identifier for the `Workflow`.
    updateTime: Output only. Server assigned timestamp for when the workflow
      was last updated.
    workflowTriggers: The Workflow triggers that can fire the workflow.
    workspaces: Workspaces is a list of WorkspaceBindings from volumes to
      workspaces.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcesValue(_messages.Message):
    r"""Resources referenceable within a workflow.

    Messages:
      AdditionalProperty: An additional property for a ResourcesValue object.

    Fields:
      additionalProperties: Additional properties of type ResourcesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A Resource attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Resource', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  options = _messages.MessageField('WorkflowOptions', 6)
  params = _messages.MessageField('ParamSpec', 7, repeated=True)
  pipelineRef = _messages.MessageField('PipelineRef', 8)
  pipelineSpec = _messages.MessageField('PipelineSpec', 9)
  pipelineSpecYaml = _messages.StringField(10)
  ref = _messages.MessageField('PipelineRef', 11)
  resources = _messages.MessageField('ResourcesValue', 12)
  secrets = _messages.MessageField('WorkflowSecret', 13, repeated=True)
  serviceAccount = _messages.StringField(14)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)
  workflowTriggers = _messages.MessageField('WorkflowTrigger', 17, repeated=True)
  workspaces = _messages.MessageField('WorkspaceBinding', 18, repeated=True)


class WorkflowOptions(_messages.Message):
  r"""Workflow runs can be modified through several Workflow options.

  Fields:
    executionEnvironment: Contains the workerpool.
    provenance: Optional. Provenance configuration.
    security: Optional. Security configuration.
    statusUpdateOptions: How/where status on the workflow is posted.
    timeouts: Time after which the Pipeline times out. Currently three keys
      are accepted in the map pipeline, tasks and finally with
      Timeouts.pipeline >= Timeouts.tasks + Timeouts.finally
    worker: Optional. Worker config.
    workerPool: Optional. The workerpool used to run the Workflow.
  """

  executionEnvironment = _messages.MessageField('ExecutionEnvironment', 1)
  provenance = _messages.MessageField('Provenance', 2)
  security = _messages.MessageField('Security', 3)
  statusUpdateOptions = _messages.MessageField('WorkflowStatusUpdateOptions', 4)
  timeouts = _messages.MessageField('TimeoutFields', 5)
  worker = _messages.MessageField('Worker', 6)
  workerPool = _messages.StringField(7)


class WorkflowSecret(_messages.Message):
  r"""Secret referenceable within a workflow.

  Fields:
    name: Immutable. The name of the secret.
    secretVersion: Required. The version of the secret.
  """

  name = _messages.StringField(1)
  secretVersion = _messages.StringField(2)


class WorkflowStatusUpdateOptions(_messages.Message):
  r"""Configure how/where status is posted.

  Enums:
    RepositoryStatusValueValuesEnum: Options that specify the level of details
      related to the PipelineRun that was created by a triggered workflow sent
      back to the GitHub CheckRun.

  Fields:
    pubsubTopic: Controls which Pub/Sub topic is used to send status updates
      as a build progresses and terminates. Default: projects//pub-
      sub/topics/cloud-build
    repositoryStatus: Options that specify the level of details related to the
      PipelineRun that was created by a triggered workflow sent back to the
      GitHub CheckRun.
  """

  class RepositoryStatusValueValuesEnum(_messages.Enum):
    r"""Options that specify the level of details related to the PipelineRun
    that was created by a triggered workflow sent back to the GitHub CheckRun.

    Values:
      REPOSITORY_STATUS_UNSPECIFIED: This value is unused.
      REPOSITORY_STATUS_NAME: Include the status of the PipelineRun. This is
        the default value.
      REPOSITORY_STATUS_NAME_LOG: Include the status of the PipelineRun and
        the GCL log url of it.
    """
    REPOSITORY_STATUS_UNSPECIFIED = 0
    REPOSITORY_STATUS_NAME = 1
    REPOSITORY_STATUS_NAME_LOG = 2

  pubsubTopic = _messages.StringField(1)
  repositoryStatus = _messages.EnumField('RepositoryStatusValueValuesEnum', 2)


class WorkflowTrigger(_messages.Message):
  r"""Workflow trigger within a Workflow.

  Enums:
    EventTypeValueValuesEnum: Optional. The type of the events the
      WorkflowTrigger accepts.
    StatusValueValuesEnum: Output only. The status of the WorkflowTrigger.

  Fields:
    createTime: Output only. Creation time of the WorkflowTrigger.
    custom: The CEL filters that triggers the Workflow.
    eventType: Optional. The type of the events the WorkflowTrigger accepts.
    gitRef: Optional. The Git ref matching the SCM repo branch/tag.
    id: Immutable. id given by the users to the Workflow.
    params: List of parameters associated with the WorkflowTrigger.
    pullRequest: Optional. The Pull request role and comment that triggers the
      Workflow.
    source: The event source the WorkflowTrigger listens to.
    status: Output only. The status of the WorkflowTrigger.
    statusMessage: Output only. The reason why WorkflowTrigger is deactivated.
    updateTime: Output only. Update time of the WorkflowTrigger.
    uuid: Output only. The internal id of the WorkflowTrigger.
    webhookSecret: The webhook secret resource.
    webhookValidationSecret: Resource name of SecretManagerSecret version
      validating webhook triggers.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the events the WorkflowTrigger accepts.

    Values:
      EVENTTYPE_UNSPECIFIED: Default to ALL.
      ALL: All events.
      PULL_REQUEST: PR events.
      PUSH_BRANCH: Push to branch events.
      PUSH_TAG: Push to tag events.
    """
    EVENTTYPE_UNSPECIFIED = 0
    ALL = 1
    PULL_REQUEST = 2
    PUSH_BRANCH = 3
    PUSH_TAG = 4

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the WorkflowTrigger.

    Values:
      STATUS_UNSPECIFIED: Defaults to ACTIVE.
      ACTIVE: WorkflowTrigger is active.
      DEACTIVATED: WorkflowTrigger is deactivated.
    """
    STATUS_UNSPECIFIED = 0
    ACTIVE = 1
    DEACTIVATED = 2

  createTime = _messages.StringField(1)
  custom = _messages.MessageField('CEL', 2, repeated=True)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 3)
  gitRef = _messages.MessageField('GitRef', 4)
  id = _messages.StringField(5)
  params = _messages.MessageField('Param', 6, repeated=True)
  pullRequest = _messages.MessageField('PullRequest', 7)
  source = _messages.MessageField('EventSource', 8)
  status = _messages.EnumField('StatusValueValuesEnum', 9)
  statusMessage = _messages.StringField(10)
  updateTime = _messages.StringField(11)
  uuid = _messages.StringField(12)
  webhookSecret = _messages.MessageField('WebhookSecret', 13)
  webhookValidationSecret = _messages.StringField(14)


class WorkspaceBinding(_messages.Message):
  r"""WorkspaceBinding maps a workspace to a Volume. PipelineRef can be used
  to refer to a specific instance of a Pipeline.

  Fields:
    name: Name of the workspace.
    secret: Secret Volume Source.
    subPath: Optional. SubPath is optionally a directory on the volume which
      should be used for this binding (i.e. the volume will be mounted at this
      sub directory). +optional
    volumeClaim: Volume claim that will be created in the same namespace.
      Deprecated, do not use for workloads that don't use workerpools.
  """

  name = _messages.StringField(1)
  secret = _messages.MessageField('SecretVolumeSource', 2)
  subPath = _messages.StringField(3)
  volumeClaim = _messages.MessageField('VolumeClaim', 4)


class WorkspaceDeclaration(_messages.Message):
  r"""WorkspaceDeclaration is a declaration of a volume that a Task requires.

  Fields:
    description: Description is a human readable description of this volume.
    mountPath: MountPath overrides the directory that the volume will be made
      available at.
    name: Name is the name by which you can bind the volume at runtime.
    optional: Optional. Optional marks a Workspace as not being required in
      TaskRuns. By default this field is false and so declared workspaces are
      required.
    readOnly: ReadOnly dictates whether a mounted volume is writable.
  """

  description = _messages.StringField(1)
  mountPath = _messages.StringField(2)
  name = _messages.StringField(3)
  optional = _messages.BooleanField(4)
  readOnly = _messages.BooleanField(5)


class WorkspacePipelineTaskBinding(_messages.Message):
  r"""WorkspacePipelineTaskBinding maps workspaces from the PipelineSpec to
  the workspaces declared in the Task.

  Fields:
    name: Name of the workspace as declared by the task.
    subPath: Optional. SubPath is optionally a directory on the volume which
      should be used for this binding (i.e. the volume will be mounted at this
      sub directory). +optional
    workspace: Name of the workspace declared by the pipeline.
  """

  name = _messages.StringField(1)
  subPath = _messages.StringField(2)
  workspace = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    Probe, 'exec_', 'exec')
encoding.AddCustomJsonFieldMapping(
    TimeoutFields, 'finally_', 'finally')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    CloudbuildProjectsLocationsConnectionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
