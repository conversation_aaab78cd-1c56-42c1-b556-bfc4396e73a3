"""Generated client library for cloudbuild version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudbuild.v2 import cloudbuild_v2_messages as messages


class CloudbuildV2(base_api.BaseApiClient):
  """Generated client library for service cloudbuild version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudbuild.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudbuild.mtls.googleapis.com/'

  _PACKAGE = 'cloudbuild'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudbuildV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudbuild handle."""
    url = url or self.BASE_URL
    super(CloudbuildV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_connections_repositories = self.ProjectsLocationsConnectionsRepositoriesService(self)
    self.projects_locations_connections = self.ProjectsLocationsConnectionsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_pipelineRuns = self.ProjectsLocationsPipelineRunsService(self)
    self.projects_locations_results_records = self.ProjectsLocationsResultsRecordsService(self)
    self.projects_locations_results = self.ProjectsLocationsResultsService(self)
    self.projects_locations_taskRuns = self.ProjectsLocationsTaskRunsService(self)
    self.projects_locations_workerPoolSecondGen = self.ProjectsLocationsWorkerPoolSecondGenService(self)
    self.projects_locations_workflows = self.ProjectsLocationsWorkflowsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsConnectionsRepositoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_connections_repositories resource."""

    _NAME = 'projects_locations_connections_repositories'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsConnectionsRepositoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def AccessReadToken(self, request, global_params=None):
      r"""Fetches read token of a given repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchReadTokenResponse) The response message.
      """
      config = self.GetMethodConfig('AccessReadToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    AccessReadToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories/{repositoriesId}:accessReadToken',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.repositories.accessReadToken',
        ordered_params=['repository'],
        path_params=['repository'],
        query_params=[],
        relative_path='v2/{+repository}:accessReadToken',
        request_field='fetchReadTokenRequest',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadTokenRequest',
        response_type_name='FetchReadTokenResponse',
        supports_download=False,
    )

    def AccessReadWriteToken(self, request, global_params=None):
      r"""Fetches read/write token of a given repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadWriteTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchReadWriteTokenResponse) The response message.
      """
      config = self.GetMethodConfig('AccessReadWriteToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    AccessReadWriteToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories/{repositoriesId}:accessReadWriteToken',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.repositories.accessReadWriteToken',
        ordered_params=['repository'],
        path_params=['repository'],
        query_params=[],
        relative_path='v2/{+repository}:accessReadWriteToken',
        request_field='fetchReadWriteTokenRequest',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesAccessReadWriteTokenRequest',
        response_type_name='FetchReadWriteTokenResponse',
        supports_download=False,
    )

    def BatchCreate(self, request, global_params=None):
      r"""Creates multiple repositories inside a connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories:batchCreate',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.repositories.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/repositories:batchCreate',
        request_field='batchCreateRepositoriesRequest',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesBatchCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.repositories.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['repositoryId'],
        relative_path='v2/{+parent}/repositories',
        request_field='repository',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories/{repositoriesId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.connections.repositories.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FetchGitRefs(self, request, global_params=None):
      r"""Fetch the list of branches or tags for a given repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesFetchGitRefsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchGitRefsResponse) The response message.
      """
      config = self.GetMethodConfig('FetchGitRefs')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchGitRefs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories/{repositoriesId}:fetchGitRefs',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.repositories.fetchGitRefs',
        ordered_params=['repository'],
        path_params=['repository'],
        query_params=['pageSize', 'pageToken', 'refType'],
        relative_path='v2/{+repository}:fetchGitRefs',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesFetchGitRefsRequest',
        response_type_name='FetchGitRefsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single repository.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Repository) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories/{repositoriesId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.repositories.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesGetRequest',
        response_type_name='Repository',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Repositories in a given connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsRepositoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/repositories',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.repositories.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v2/{+parent}/repositories',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsRepositoriesListRequest',
        response_type_name='ListRepositoriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_connections resource."""

    _NAME = 'projects_locations_connections'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectionId'],
        relative_path='v2/{+parent}/connections',
        request_field='connection',
        request_type_name='CloudbuildProjectsLocationsConnectionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.connections.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FetchLinkableRepositories(self, request, global_params=None):
      r"""FetchLinkableRepositories get repositories from SCM that are accessible and could be added to the connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsFetchLinkableRepositoriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchLinkableRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('FetchLinkableRepositories')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchLinkableRepositories.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:fetchLinkableRepositories',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.fetchLinkableRepositories',
        ordered_params=['connection'],
        path_params=['connection'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+connection}:fetchLinkableRepositories',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsFetchLinkableRepositoriesRequest',
        response_type_name='FetchLinkableRepositoriesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsGetRequest',
        response_type_name='Connection',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:getIamPolicy',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Connections in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections',
        http_method='GET',
        method_id='cloudbuild.projects.locations.connections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v2/{+parent}/connections',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsConnectionsListRequest',
        response_type_name='ListConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single connection.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.connections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='connection',
        request_type_name='CloudbuildProjectsLocationsConnectionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ProcessWebhook(self, request, global_params=None):
      r"""ProcessWebhook is called by the external SCM for notifying of events.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsProcessWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('ProcessWebhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    ProcessWebhook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections:processWebhook',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.processWebhook',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['webhookKey'],
        relative_path='v2/{+parent}/connections:processWebhook',
        request_field='httpBody',
        request_type_name='CloudbuildProjectsLocationsConnectionsProcessWebhookRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:setIamPolicy',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudbuildProjectsLocationsConnectionsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudbuildProjectsLocationsConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:testIamPermissions',
        http_method='POST',
        method_id='cloudbuild.projects.locations.connections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudbuildProjectsLocationsConnectionsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (CloudbuildProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='cloudbuild.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='CloudbuildProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudbuildProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPipelineRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelineRuns resource."""

    _NAME = 'projects_locations_pipelineRuns'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsPipelineRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PipelineRun in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsPipelineRunsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/pipelineRuns',
        http_method='POST',
        method_id='cloudbuild.projects.locations.pipelineRuns.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pipelineRunId', 'validateOnly'],
        relative_path='v2/{+parent}/pipelineRuns',
        request_field='pipelineRun',
        request_type_name='CloudbuildProjectsLocationsPipelineRunsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PipelineRun.

      Args:
        request: (CloudbuildProjectsLocationsPipelineRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PipelineRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/pipelineRuns/{pipelineRunsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.pipelineRuns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsPipelineRunsGetRequest',
        response_type_name='PipelineRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PipelineRuns in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsPipelineRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPipelineRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/pipelineRuns',
        http_method='GET',
        method_id='cloudbuild.projects.locations.pipelineRuns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/pipelineRuns',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsPipelineRunsListRequest',
        response_type_name='ListPipelineRunsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single PipelineRun.

      Args:
        request: (CloudbuildProjectsLocationsPipelineRunsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/pipelineRuns/{pipelineRunsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.pipelineRuns.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='pipelineRun',
        request_type_name='CloudbuildProjectsLocationsPipelineRunsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsResultsRecordsService(base_api.BaseApiService):
    """Service class for the projects_locations_results_records resource."""

    _NAME = 'projects_locations_results_records'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsResultsRecordsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets Records of a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsResultsRecordsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Record) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/results/{resultsId}/records/{recordsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.results.records.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsResultsRecordsGetRequest',
        response_type_name='Record',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Records of a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsResultsRecordsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRecordsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/results/{resultsId}/records',
        http_method='GET',
        method_id='cloudbuild.projects.locations.results.records.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/records',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsResultsRecordsListRequest',
        response_type_name='ListRecordsResponse',
        supports_download=False,
    )

  class ProjectsLocationsResultsService(base_api.BaseApiService):
    """Service class for the projects_locations_results resource."""

    _NAME = 'projects_locations_results'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets Results of a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsResultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Result) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/results/{resultsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.results.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsResultsGetRequest',
        response_type_name='Result',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Results of a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/results',
        http_method='GET',
        method_id='cloudbuild.projects.locations.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/results',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsResultsListRequest',
        response_type_name='ListResultsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTaskRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_taskRuns resource."""

    _NAME = 'projects_locations_taskRuns'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsTaskRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TaskRun in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsTaskRunsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/taskRuns',
        http_method='POST',
        method_id='cloudbuild.projects.locations.taskRuns.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['taskRunId', 'validateOnly'],
        relative_path='v2/{+parent}/taskRuns',
        request_field='taskRun',
        request_type_name='CloudbuildProjectsLocationsTaskRunsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TaskRun.

      Args:
        request: (CloudbuildProjectsLocationsTaskRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TaskRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/taskRuns/{taskRunsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.taskRuns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsTaskRunsGetRequest',
        response_type_name='TaskRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TaskRuns in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsTaskRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTaskRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/taskRuns',
        http_method='GET',
        method_id='cloudbuild.projects.locations.taskRuns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/taskRuns',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsTaskRunsListRequest',
        response_type_name='ListTaskRunsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TaskRun.

      Args:
        request: (CloudbuildProjectsLocationsTaskRunsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/taskRuns/{taskRunsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.taskRuns.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='taskRun',
        request_type_name='CloudbuildProjectsLocationsTaskRunsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkerPoolSecondGenService(base_api.BaseApiService):
    """Service class for the projects_locations_workerPoolSecondGen resource."""

    _NAME = 'projects_locations_workerPoolSecondGen'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsWorkerPoolSecondGenService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a `WorkerPoolSecondGen`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolSecondGenCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPoolSecondGen',
        http_method='POST',
        method_id='cloudbuild.projects.locations.workerPoolSecondGen.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workerPoolSecondGenId'],
        relative_path='v2/{+parent}/workerPoolSecondGen',
        request_field='workerPoolSecondGen',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolSecondGenCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `WorkerPoolSecondGen`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolSecondGenDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPoolSecondGen/{workerPoolSecondGenId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.workerPoolSecondGen.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolSecondGenDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns details of a `WorkerPoolSecondGen`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolSecondGenGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkerPoolSecondGen) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPoolSecondGen/{workerPoolSecondGenId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workerPoolSecondGen.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolSecondGenGetRequest',
        response_type_name='WorkerPoolSecondGen',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WorkerPoolSecondGen`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolSecondGenListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkerPoolSecondGenResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPoolSecondGen',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workerPoolSecondGen.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/workerPoolSecondGen',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolSecondGenListRequest',
        response_type_name='ListWorkerPoolSecondGenResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `WorkerPoolSecondGen`.

      Args:
        request: (CloudbuildProjectsLocationsWorkerPoolSecondGenPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPoolSecondGen/{workerPoolSecondGenId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.workerPoolSecondGen.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='workerPoolSecondGen',
        request_type_name='CloudbuildProjectsLocationsWorkerPoolSecondGenPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkflowsService(base_api.BaseApiService):
    """Service class for the projects_locations_workflows resource."""

    _NAME = 'projects_locations_workflows'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsWorkflowsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Workflow in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows',
        http_method='POST',
        method_id='cloudbuild.projects.locations.workflows.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workflowId'],
        relative_path='v2/{+parent}/workflows',
        request_field='workflow',
        request_type_name='CloudbuildProjectsLocationsWorkflowsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Workflow.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows/{workflowsId}',
        http_method='DELETE',
        method_id='cloudbuild.projects.locations.workflows.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkflowsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Workflow.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Workflow) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows/{workflowsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workflows.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkflowsGetRequest',
        response_type_name='Workflow',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Workflows in a given project and location.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkflowsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows',
        http_method='GET',
        method_id='cloudbuild.projects.locations.workflows.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/workflows',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsWorkflowsListRequest',
        response_type_name='ListWorkflowsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Workflow.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows/{workflowsId}',
        http_method='PATCH',
        method_id='cloudbuild.projects.locations.workflows.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='workflow',
        request_type_name='CloudbuildProjectsLocationsWorkflowsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Runs a Workflow.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows/{workflowsId}:run',
        http_method='POST',
        method_id='cloudbuild.projects.locations.workflows.run',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:run',
        request_field='runWorkflowRequest',
        request_type_name='CloudbuildProjectsLocationsWorkflowsRunRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Webhook(self, request, global_params=None):
      r"""Processes webhooks posted towards a WorkflowTrigger.

      Args:
        request: (CloudbuildProjectsLocationsWorkflowsWebhookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProcessWorkflowTriggerWebhookResponse) The response message.
      """
      config = self.GetMethodConfig('Webhook')
      return self._RunMethod(
          config, request, global_params=global_params)

    Webhook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workflows/{workflowsId}:webhook',
        http_method='POST',
        method_id='cloudbuild.projects.locations.workflows.webhook',
        ordered_params=['workflow'],
        path_params=['workflow'],
        query_params=[],
        relative_path='v2/{+workflow}:webhook',
        request_field='processWorkflowTriggerWebhookRequest',
        request_type_name='CloudbuildProjectsLocationsWorkflowsWebhookRequest',
        response_type_name='ProcessWorkflowTriggerWebhookResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (CloudbuildProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='cloudbuild.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (CloudbuildProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='cloudbuild.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='CloudbuildProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(CloudbuildV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
