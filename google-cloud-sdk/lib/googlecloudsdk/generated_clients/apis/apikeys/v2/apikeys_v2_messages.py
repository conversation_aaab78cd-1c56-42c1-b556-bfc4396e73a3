"""Generated message classes for apikeys version v2.

Manages the API keys associated with developer projects.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'apikeys'


class ApikeysKeysLookupKeyRequest(_messages.Message):
  r"""A ApikeysKeysLookupKeyRequest object.

  Fields:
    keyString: Required. Finds the project that owns the key string value.
  """

  keyString = _messages.StringField(1)


class ApikeysOperationsGetRequest(_messages.Message):
  r"""A ApikeysOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ApikeysProjectsLocationsKeysCreateRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysCreateRequest object.

  Fields:
    keyId: User specified key id (optional). If specified, it will become the
      final component of the key resource name. The id must be unique within
      the project, must conform with RFC-1034, is restricted to lower-cased
      letters, and has a maximum length of 63 characters. In another word, the
      id must match the regular expression: `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
      The id must NOT be a UUID-like string.
    parent: Required. The project in which the API key is created.
    v2Key: A V2Key resource to be passed as the request body.
  """

  keyId = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  v2Key = _messages.MessageField('V2Key', 3)


class ApikeysProjectsLocationsKeysDeleteRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysDeleteRequest object.

  Fields:
    etag: Optional. The etag known to the client for the expected state of the
      key. This is to be used for optimistic concurrency.
    name: Required. The resource name of the API key to be deleted.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApikeysProjectsLocationsKeysGetKeyStringRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysGetKeyStringRequest object.

  Fields:
    name: Required. The resource name of the API key to be retrieved.
  """

  name = _messages.StringField(1, required=True)


class ApikeysProjectsLocationsKeysGetRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysGetRequest object.

  Fields:
    name: Required. The resource name of the API key to get.
  """

  name = _messages.StringField(1, required=True)


class ApikeysProjectsLocationsKeysListRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysListRequest object.

  Fields:
    pageSize: Optional. Specifies the maximum number of results to be returned
      at a time.
    pageToken: Optional. Requests a specific page of results.
    parent: Required. Lists all API keys associated with this project.
    showDeleted: Optional. Indicate that keys deleted in the past 30 days
      should also be returned.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class ApikeysProjectsLocationsKeysPatchRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysPatchRequest object.

  Fields:
    name: Output only. The resource name of the key. The `name` has the form:
      `projects//locations/global/keys/`. For example: `projects/************/
      locations/global/keys/b7ff1f9f-8275-410a-94dd-3855ee9b5dd2` NOTE: Key is
      a global resource; hence the only supported value for location is
      `global`.
    updateMask: The field mask specifies which fields to be updated as part of
      this request. All other fields are ignored. Mutable fields are:
      `display_name`, `restrictions`, and `annotations`. If an update mask is
      not provided, the service treats it as an implied mask equivalent to all
      allowed fields that are set on the wire. If the field mask has a special
      value "*", the service treats it equivalent to replace all allowed
      mutable fields.
    v2Key: A V2Key resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  v2Key = _messages.MessageField('V2Key', 3)


class ApikeysProjectsLocationsKeysUndeleteRequest(_messages.Message):
  r"""A ApikeysProjectsLocationsKeysUndeleteRequest object.

  Fields:
    name: Required. The resource name of the API key to be undeleted.
    v2UndeleteKeyRequest: A V2UndeleteKeyRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  v2UndeleteKeyRequest = _messages.MessageField('V2UndeleteKeyRequest', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class V2AndroidApplication(_messages.Message):
  r"""Identifier of an Android application for key use.

  Fields:
    packageName: The package name of the application.
    sha1Fingerprint: The SHA1 fingerprint of the application. For example,
      both sha1 formats are acceptable :
      DA:39:A3:EE:5E:6B:4B:0D:32:55:BF:EF:95:60:18:90:AF:D8:07:09 or
      DA39A3EE5E6B4B0D3255BFEF95601890AFD80709. Output format is the latter.
  """

  packageName = _messages.StringField(1)
  sha1Fingerprint = _messages.StringField(2)


class V2AndroidKeyRestrictions(_messages.Message):
  r"""The Android apps that are allowed to use the key.

  Fields:
    allowedApplications: A list of Android applications that are allowed to
      make API calls with this key.
  """

  allowedApplications = _messages.MessageField('V2AndroidApplication', 1, repeated=True)


class V2ApiTarget(_messages.Message):
  r"""A restriction for a specific service and optionally one or multiple
  specific methods. Both fields are case insensitive.

  Fields:
    methods: Optional. List of one or more methods that can be called. If
      empty, all methods for the service are allowed. A wildcard (*) can be
      used as the last symbol. Valid examples:
      `google.cloud.translate.v2.TranslateService.GetSupportedLanguage`
      `TranslateText` `Get*` `translate.googleapis.com.Get*`
    service: The service for this restriction. It should be the canonical
      service name, for example: `translate.googleapis.com`. You can use
      [`gcloud services
      list`](https://cloud.google.com/sdk/gcloud/reference/services/list) to
      get a list of services that are enabled in the project.
  """

  methods = _messages.StringField(1, repeated=True)
  service = _messages.StringField(2)


class V2BrowserKeyRestrictions(_messages.Message):
  r"""The HTTP referrers (websites) that are allowed to use the key.

  Fields:
    allowedReferrers: A list of regular expressions for the referrer URLs that
      are allowed to make API calls with this key.
  """

  allowedReferrers = _messages.StringField(1, repeated=True)


class V2GetKeyStringResponse(_messages.Message):
  r"""Response message for `GetKeyString` method.

  Fields:
    keyString: An encrypted and signed value of the key.
  """

  keyString = _messages.StringField(1)


class V2IosKeyRestrictions(_messages.Message):
  r"""The iOS apps that are allowed to use the key.

  Fields:
    allowedBundleIds: A list of bundle IDs that are allowed when making API
      calls with this key.
  """

  allowedBundleIds = _messages.StringField(1, repeated=True)


class V2Key(_messages.Message):
  r"""The representation of a key managed by the API Keys API.

  Messages:
    AnnotationsValue: Annotations is an unstructured key-value map stored with
      a policy that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects.

  Fields:
    annotations: Annotations is an unstructured key-value map stored with a
      policy that may be set by external tools to store and retrieve arbitrary
      metadata. They are not queryable and should be preserved when modifying
      objects.
    createTime: Output only. A timestamp identifying the time this key was
      originally created.
    deleteTime: Output only. A timestamp when this key was deleted. If the
      resource is not deleted, this must be empty.
    displayName: Human-readable display name of this key that you can modify.
      The maximum length is 63 characters.
    etag: Output only. A checksum computed by the server based on the current
      value of the Key resource. This may be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
      See https://google.aip.dev/154.
    keyString: Output only. An encrypted and signed value held by this key.
      This field can be accessed only through the `GetKeyString` method.
    name: Output only. The resource name of the key. The `name` has the form:
      `projects//locations/global/keys/`. For example: `projects/************/
      locations/global/keys/b7ff1f9f-8275-410a-94dd-3855ee9b5dd2` NOTE: Key is
      a global resource; hence the only supported value for location is
      `global`.
    restrictions: Key restrictions.
    serviceAccountEmail: Optional. The email address of [the service
      account](https://cloud.google.com/iam/docs/service-accounts) the key is
      bound to.
    uid: Output only. Unique id in UUID4 format.
    updateTime: Output only. A timestamp identifying the time this key was
      last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is an unstructured key-value map stored with a policy that
    may be set by external tools to store and retrieve arbitrary metadata.
    They are not queryable and should be preserved when modifying objects.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  keyString = _messages.StringField(6)
  name = _messages.StringField(7)
  restrictions = _messages.MessageField('V2Restrictions', 8)
  serviceAccountEmail = _messages.StringField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class V2ListKeysResponse(_messages.Message):
  r"""Response message for `ListKeys` method.

  Fields:
    keys: A list of API keys.
    nextPageToken: The pagination token for the next page of results.
  """

  keys = _messages.MessageField('V2Key', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class V2LookupKeyResponse(_messages.Message):
  r"""Response message for `LookupKey` method.

  Fields:
    name: The resource name of the API key. If the API key has been purged,
      resource name is empty.
    parent: The project that owns the key with the value specified in the
      request.
  """

  name = _messages.StringField(1)
  parent = _messages.StringField(2)


class V2Restrictions(_messages.Message):
  r"""Describes the restrictions on the key.

  Fields:
    androidKeyRestrictions: The Android apps that are allowed to use the key.
    apiTargets: A restriction for a specific service and optionally one or
      more specific methods. Requests are allowed if they match any of these
      restrictions. If no restrictions are specified, all targets are allowed.
    browserKeyRestrictions: The HTTP referrers (websites) that are allowed to
      use the key.
    iosKeyRestrictions: The iOS apps that are allowed to use the key.
    serverKeyRestrictions: The IP addresses of callers that are allowed to use
      the key.
  """

  androidKeyRestrictions = _messages.MessageField('V2AndroidKeyRestrictions', 1)
  apiTargets = _messages.MessageField('V2ApiTarget', 2, repeated=True)
  browserKeyRestrictions = _messages.MessageField('V2BrowserKeyRestrictions', 3)
  iosKeyRestrictions = _messages.MessageField('V2IosKeyRestrictions', 4)
  serverKeyRestrictions = _messages.MessageField('V2ServerKeyRestrictions', 5)


class V2ServerKeyRestrictions(_messages.Message):
  r"""The IP addresses of callers that are allowed to use the key.

  Fields:
    allowedIps: A list of the caller IP addresses that are allowed to make API
      calls with this key.
  """

  allowedIps = _messages.StringField(1, repeated=True)


class V2UndeleteKeyRequest(_messages.Message):
  r"""Request message for `UndeleteKey` method."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
