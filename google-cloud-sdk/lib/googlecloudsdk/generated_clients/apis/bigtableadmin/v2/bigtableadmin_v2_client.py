"""Generated client library for bigtableadmin version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.bigtableadmin.v2 import bigtableadmin_v2_messages as messages


class BigtableadminV2(base_api.BaseApiClient):
  """Generated client library for service bigtableadmin version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://bigtableadmin.googleapis.com/'
  MTLS_BASE_URL = 'https://bigtableadmin.mtls.googleapis.com/'

  _PACKAGE = 'bigtableadmin'
  _SCOPES = ['https://www.googleapis.com/auth/bigtable.admin', 'https://www.googleapis.com/auth/bigtable.admin.cluster', 'https://www.googleapis.com/auth/bigtable.admin.instance', 'https://www.googleapis.com/auth/bigtable.admin.table', 'https://www.googleapis.com/auth/cloud-bigtable.admin', 'https://www.googleapis.com/auth/cloud-bigtable.admin.cluster', 'https://www.googleapis.com/auth/cloud-bigtable.admin.table', 'https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'BigtableadminV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new bigtableadmin handle."""
    url = url or self.BASE_URL
    super(BigtableadminV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations_projects_operations = self.OperationsProjectsOperationsService(self)
    self.operations_projects = self.OperationsProjectsService(self)
    self.operations = self.OperationsService(self)
    self.projects_instances_appProfiles = self.ProjectsInstancesAppProfilesService(self)
    self.projects_instances_clusters_backups = self.ProjectsInstancesClustersBackupsService(self)
    self.projects_instances_clusters_hotTablets = self.ProjectsInstancesClustersHotTabletsService(self)
    self.projects_instances_clusters = self.ProjectsInstancesClustersService(self)
    self.projects_instances_logicalViews = self.ProjectsInstancesLogicalViewsService(self)
    self.projects_instances_materializedViews = self.ProjectsInstancesMaterializedViewsService(self)
    self.projects_instances_tables_authorizedViews = self.ProjectsInstancesTablesAuthorizedViewsService(self)
    self.projects_instances_tables_schemaBundles = self.ProjectsInstancesTablesSchemaBundlesService(self)
    self.projects_instances_tables = self.ProjectsInstancesTablesService(self)
    self.projects_instances = self.ProjectsInstancesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OperationsProjectsOperationsService(base_api.BaseApiService):
    """Service class for the operations_projects_operations resource."""

    _NAME = 'operations_projects_operations'

    def __init__(self, client):
      super(BigtableadminV2.OperationsProjectsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (BigtableadminOperationsProjectsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/operations/projects/{projectsId}/operations',
        http_method='GET',
        method_id='bigtableadmin.operations.projects.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='BigtableadminOperationsProjectsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OperationsProjectsService(base_api.BaseApiService):
    """Service class for the operations_projects resource."""

    _NAME = 'operations_projects'

    def __init__(self, client):
      super(BigtableadminV2.OperationsProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(BigtableadminV2.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (BigtableadminOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='bigtableadmin.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='',
        request_type_name='BigtableadminOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (BigtableadminOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/operations/{operationsId}',
        http_method='DELETE',
        method_id='bigtableadmin.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (BigtableadminOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/operations/{operationsId}',
        http_method='GET',
        method_id='bigtableadmin.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsInstancesAppProfilesService(base_api.BaseApiService):
    """Service class for the projects_instances_appProfiles resource."""

    _NAME = 'projects_instances_appProfiles'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesAppProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an app profile within an instance.

      Args:
        request: (BigtableadminProjectsInstancesAppProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AppProfile) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/appProfiles',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.appProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['appProfileId', 'ignoreWarnings'],
        relative_path='v2/{+parent}/appProfiles',
        request_field='appProfile',
        request_type_name='BigtableadminProjectsInstancesAppProfilesCreateRequest',
        response_type_name='AppProfile',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an app profile from an instance.

      Args:
        request: (BigtableadminProjectsInstancesAppProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/appProfiles/{appProfilesId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.appProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreWarnings'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesAppProfilesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about an app profile.

      Args:
        request: (BigtableadminProjectsInstancesAppProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AppProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/appProfiles/{appProfilesId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.appProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesAppProfilesGetRequest',
        response_type_name='AppProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about app profiles in an instance.

      Args:
        request: (BigtableadminProjectsInstancesAppProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAppProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/appProfiles',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.appProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/appProfiles',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesAppProfilesListRequest',
        response_type_name='ListAppProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an app profile within an instance.

      Args:
        request: (BigtableadminProjectsInstancesAppProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/appProfiles/{appProfilesId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.appProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreWarnings', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='appProfile',
        request_type_name='BigtableadminProjectsInstancesAppProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsInstancesClustersBackupsService(base_api.BaseApiService):
    """Service class for the projects_instances_clusters_backups resource."""

    _NAME = 'projects_instances_clusters_backups'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesClustersBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Copy(self, request, global_params=None):
      r"""Copy a Cloud Bigtable backup to a new backup in the destination cluster located in the destination instance and project.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsCopyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Copy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Copy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups:copy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.backups.copy',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/backups:copy',
        request_field='copyBackupRequest',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsCopyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Starts creating a new Cloud Bigtable Backup. The returned backup long-running operation can be used to track creation of the backup. The metadata field type is CreateBackupMetadata. The response field type is Backup, if successful. Cancelling the returned operation will stop the creation and delete the backup.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId'],
        relative_path='v2/{+parent}/backups',
        request_field='backup',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a pending or completed Cloud Bigtable backup.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.clusters.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets metadata on a pending or completed Cloud Bigtable Backup.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.clusters.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a Bigtable resource. Returns an empty policy if the resource exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.backups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Cloud Bigtable backups. Returns both completed and pending backups.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.clusters.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/backups',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a pending or completed Cloud Bigtable Backup.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.clusters.backups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='backup',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsPatchRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on a Bigtable resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.backups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified Bigtable resource.

      Args:
        request: (BigtableadminProjectsInstancesClustersBackupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/backups/{backupsId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.backups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesClustersBackupsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsInstancesClustersHotTabletsService(base_api.BaseApiService):
    """Service class for the projects_instances_clusters_hotTablets resource."""

    _NAME = 'projects_instances_clusters_hotTablets'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesClustersHotTabletsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists hot tablets in a cluster, within the time range provided. Hot tablets are ordered based on CPU usage.

      Args:
        request: (BigtableadminProjectsInstancesClustersHotTabletsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHotTabletsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}/hotTablets',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.clusters.hotTablets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endTime', 'pageSize', 'pageToken', 'startTime'],
        relative_path='v2/{+parent}/hotTablets',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersHotTabletsListRequest',
        response_type_name='ListHotTabletsResponse',
        supports_download=False,
    )

  class ProjectsInstancesClustersService(base_api.BaseApiService):
    """Service class for the projects_instances_clusters resource."""

    _NAME = 'projects_instances_clusters'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a cluster within an instance. Note that exactly one of Cluster.serve_nodes and Cluster.cluster_config.cluster_autoscaling_config can be set. If serve_nodes is set to non-zero, then the cluster is manually scaled. If cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is enabled.

      Args:
        request: (BigtableadminProjectsInstancesClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.clusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clusterId'],
        relative_path='v2/{+parent}/clusters',
        request_field='cluster',
        request_type_name='BigtableadminProjectsInstancesClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a cluster from an instance.

      Args:
        request: (BigtableadminProjectsInstancesClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.clusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a cluster.

      Args:
        request: (BigtableadminProjectsInstancesClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.clusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersGetRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about clusters in an instance.

      Args:
        request: (BigtableadminProjectsInstancesClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.clusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageToken'],
        relative_path='v2/{+parent}/clusters',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesClustersListRequest',
        response_type_name='ListClustersResponse',
        supports_download=False,
    )

    def PartialUpdateCluster(self, request, global_params=None):
      r"""Partially updates a cluster within a project. This method is the preferred way to update a Cluster. To enable and update autoscaling, set cluster_config.cluster_autoscaling_config. When autoscaling is enabled, serve_nodes is treated as an OUTPUT_ONLY field, meaning that updates to it are ignored. Note that an update cannot simultaneously set serve_nodes to non-zero and cluster_config.cluster_autoscaling_config to non-empty, and also specify both in the update_mask. To disable autoscaling, clear cluster_config.cluster_autoscaling_config, and explicitly set a serve_node count via the update_mask.

      Args:
        request: (BigtableadminProjectsInstancesClustersPartialUpdateClusterRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PartialUpdateCluster')
      return self._RunMethod(
          config, request, global_params=global_params)

    PartialUpdateCluster.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.clusters.partialUpdateCluster',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='cluster',
        request_type_name='BigtableadminProjectsInstancesClustersPartialUpdateClusterRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a cluster within an instance. Note that UpdateCluster does not support updating cluster_config.cluster_autoscaling_config. In order to update it, you must use PartialUpdateCluster.

      Args:
        request: (Cluster) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/clusters/{clustersId}',
        http_method='PUT',
        method_id='bigtableadmin.projects.instances.clusters.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='<request>',
        request_type_name='Cluster',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsInstancesLogicalViewsService(base_api.BaseApiService):
    """Service class for the projects_instances_logicalViews resource."""

    _NAME = 'projects_instances_logicalViews'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesLogicalViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a logical view within an instance.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.logicalViews.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['logicalViewId'],
        relative_path='v2/{+parent}/logicalViews',
        request_field='logicalView',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a logical view from an instance.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.logicalViews.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a logical view.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogicalView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.logicalViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsGetRequest',
        response_type_name='LogicalView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an instance resource. Returns an empty policy if an instance exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.logicalViews.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about logical views in an instance.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogicalViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.logicalViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/logicalViews',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsListRequest',
        response_type_name='ListLogicalViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a logical view within an instance.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.logicalViews.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logicalView',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on an instance resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.logicalViews.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified instance resource.

      Args:
        request: (BigtableadminProjectsInstancesLogicalViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/logicalViews/{logicalViewsId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.logicalViews.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesLogicalViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsInstancesMaterializedViewsService(base_api.BaseApiService):
    """Service class for the projects_instances_materializedViews resource."""

    _NAME = 'projects_instances_materializedViews'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesMaterializedViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a materialized view within an instance.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.materializedViews.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['materializedViewId'],
        relative_path='v2/{+parent}/materializedViews',
        request_field='materializedView',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a materialized view from an instance.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.materializedViews.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a materialized view.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MaterializedView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.materializedViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsGetRequest',
        response_type_name='MaterializedView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an instance resource. Returns an empty policy if an instance exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.materializedViews.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about materialized views in an instance.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMaterializedViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.materializedViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/materializedViews',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsListRequest',
        response_type_name='ListMaterializedViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a materialized view within an instance.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.materializedViews.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='materializedView',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on an instance resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.materializedViews.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified instance resource.

      Args:
        request: (BigtableadminProjectsInstancesMaterializedViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/materializedViews/{materializedViewsId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.materializedViews.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesMaterializedViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsInstancesTablesAuthorizedViewsService(base_api.BaseApiService):
    """Service class for the projects_instances_tables_authorizedViews resource."""

    _NAME = 'projects_instances_tables_authorizedViews'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesTablesAuthorizedViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AuthorizedView in a table.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['authorizedViewId'],
        relative_path='v2/{+parent}/authorizedViews',
        request_field='authorizedView',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Permanently deletes a specified AuthorizedView.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information from a specified AuthorizedView.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthorizedView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsGetRequest',
        response_type_name='AuthorizedView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a Bigtable resource. Returns an empty policy if the resource exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AuthorizedViews from a specific table.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizedViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v2/{+parent}/authorizedViews',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsListRequest',
        response_type_name='ListAuthorizedViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AuthorizedView in a table.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreWarnings', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='authorizedView',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on a Bigtable resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified Bigtable resource.

      Args:
        request: (BigtableadminProjectsInstancesTablesAuthorizedViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/authorizedViews/{authorizedViewsId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.authorizedViews.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesTablesAuthorizedViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsInstancesTablesSchemaBundlesService(base_api.BaseApiService):
    """Service class for the projects_instances_tables_schemaBundles resource."""

    _NAME = 'projects_instances_tables_schemaBundles'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesTablesSchemaBundlesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new schema bundle in the specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['schemaBundleId'],
        relative_path='v2/{+parent}/schemaBundles',
        request_field='schemaBundle',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a schema bundle in the specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets metadata information about the specified schema bundle.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaBundle) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesGetRequest',
        response_type_name='SchemaBundle',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a Bigtable resource. Returns an empty policy if the resource exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all schema bundles associated with the specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSchemaBundlesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/schemaBundles',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesListRequest',
        response_type_name='ListSchemaBundlesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a schema bundle in the specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreWarnings', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='schemaBundle',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on a Bigtable resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified Bigtable resource.

      Args:
        request: (BigtableadminProjectsInstancesTablesSchemaBundlesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}/schemaBundles/{schemaBundlesId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.schemaBundles.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesTablesSchemaBundlesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsInstancesTablesService(base_api.BaseApiService):
    """Service class for the projects_instances_tables resource."""

    _NAME = 'projects_instances_tables'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesTablesService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckConsistency(self, request, global_params=None):
      r"""Checks replication consistency based on a consistency token, that is, if replication has caught up based on the conditions specified in the token and the check request.

      Args:
        request: (BigtableadminProjectsInstancesTablesCheckConsistencyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckConsistencyResponse) The response message.
      """
      config = self.GetMethodConfig('CheckConsistency')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckConsistency.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:checkConsistency',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.checkConsistency',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:checkConsistency',
        request_field='checkConsistencyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesCheckConsistencyRequest',
        response_type_name='CheckConsistencyResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new table in the specified instance. The table can be created with a full set of initial column families, specified in the request.

      Args:
        request: (BigtableadminProjectsInstancesTablesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/tables',
        request_field='createTableRequest',
        request_type_name='BigtableadminProjectsInstancesTablesCreateRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Permanently deletes a specified table and all of its data.

      Args:
        request: (BigtableadminProjectsInstancesTablesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.tables.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def DropRowRange(self, request, global_params=None):
      r"""Permanently drop/delete a row range from a specified table. The request can specify whether to delete all rows in a table, or only those that match a particular prefix. Note that row key prefixes used here are treated as service data. For more information about how service data is handled, see the [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice).

      Args:
        request: (BigtableadminProjectsInstancesTablesDropRowRangeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('DropRowRange')
      return self._RunMethod(
          config, request, global_params=global_params)

    DropRowRange.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:dropRowRange',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.dropRowRange',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:dropRowRange',
        request_field='dropRowRangeRequest',
        request_type_name='BigtableadminProjectsInstancesTablesDropRowRangeRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def GenerateConsistencyToken(self, request, global_params=None):
      r"""Generates a consistency token for a Table, which can be used in CheckConsistency to check whether mutations to the table that finished before this call started have been replicated. The tokens will be available for 90 days.

      Args:
        request: (BigtableadminProjectsInstancesTablesGenerateConsistencyTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateConsistencyTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateConsistencyToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateConsistencyToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:generateConsistencyToken',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.generateConsistencyToken',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:generateConsistencyToken',
        request_field='generateConsistencyTokenRequest',
        request_type_name='BigtableadminProjectsInstancesTablesGenerateConsistencyTokenRequest',
        response_type_name='GenerateConsistencyTokenResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets metadata information about the specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesGetRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a Bigtable resource. Returns an empty policy if the resource exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesTablesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all tables served from a specified instance.

      Args:
        request: (BigtableadminProjectsInstancesTablesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTablesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.tables.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v2/{+parent}/tables',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesTablesListRequest',
        response_type_name='ListTablesResponse',
        supports_download=False,
    )

    def ModifyColumnFamilies(self, request, global_params=None):
      r"""Performs a series of column family modifications on the specified table. Either all or none of the modifications will occur before this method returns, but data requests received prior to that point may see a table where only some modifications have taken effect.

      Args:
        request: (BigtableadminProjectsInstancesTablesModifyColumnFamiliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Table) The response message.
      """
      config = self.GetMethodConfig('ModifyColumnFamilies')
      return self._RunMethod(
          config, request, global_params=global_params)

    ModifyColumnFamilies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:modifyColumnFamilies',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.modifyColumnFamilies',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:modifyColumnFamilies',
        request_field='modifyColumnFamiliesRequest',
        request_type_name='BigtableadminProjectsInstancesTablesModifyColumnFamiliesRequest',
        response_type_name='Table',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a specified table.

      Args:
        request: (BigtableadminProjectsInstancesTablesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.tables.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreWarnings', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='table',
        request_type_name='BigtableadminProjectsInstancesTablesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Create a new table by restoring from a completed backup. The returned table long-running operation can be used to track the progress of the operation, and to cancel it. The metadata field type is RestoreTableMetadata. The response type is Table, if successful.

      Args:
        request: (BigtableadminProjectsInstancesTablesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables:restore',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.restore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/tables:restore',
        request_field='restoreTableRequest',
        request_type_name='BigtableadminProjectsInstancesTablesRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on a Bigtable resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesTablesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesTablesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified Bigtable resource.

      Args:
        request: (BigtableadminProjectsInstancesTablesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesTablesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Restores a specified table which was accidentally deleted.

      Args:
        request: (BigtableadminProjectsInstancesTablesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}/tables/{tablesId}:undelete',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.tables.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteTableRequest',
        request_type_name='BigtableadminProjectsInstancesTablesUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsInstancesService(base_api.BaseApiService):
    """Service class for the projects_instances resource."""

    _NAME = 'projects_instances'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an instance within a project. Note that exactly one of Cluster.serve_nodes and Cluster.cluster_config.cluster_autoscaling_config can be set. If serve_nodes is set to non-zero, then the cluster is manually scaled. If cluster_config.cluster_autoscaling_config is non-empty, then autoscaling is enabled.

      Args:
        request: (CreateInstanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/instances',
        request_field='<request>',
        request_type_name='CreateInstanceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an instance from a project.

      Args:
        request: (BigtableadminProjectsInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='bigtableadmin.projects.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about an instance.

      Args:
        request: (BigtableadminProjectsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesGetRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an instance resource. Returns an empty policy if an instance exists but does not have a policy set.

      Args:
        request: (BigtableadminProjectsInstancesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}:getIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about instances in a project.

      Args:
        request: (BigtableadminProjectsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances',
        http_method='GET',
        method_id='bigtableadmin.projects.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageToken'],
        relative_path='v2/{+parent}/instances',
        request_field='',
        request_type_name='BigtableadminProjectsInstancesListRequest',
        response_type_name='ListInstancesResponse',
        supports_download=False,
    )

    def PartialUpdateInstance(self, request, global_params=None):
      r"""Partially updates an instance within a project. This method can modify all fields of an Instance and is the preferred way to update an Instance.

      Args:
        request: (BigtableadminProjectsInstancesPartialUpdateInstanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PartialUpdateInstance')
      return self._RunMethod(
          config, request, global_params=global_params)

    PartialUpdateInstance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}',
        http_method='PATCH',
        method_id='bigtableadmin.projects.instances.partialUpdateInstance',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='instance',
        request_type_name='BigtableadminProjectsInstancesPartialUpdateInstanceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on an instance resource. Replaces any existing policy.

      Args:
        request: (BigtableadminProjectsInstancesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}:setIamPolicy',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='BigtableadminProjectsInstancesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that the caller has on the specified instance resource.

      Args:
        request: (BigtableadminProjectsInstancesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}:testIamPermissions',
        http_method='POST',
        method_id='bigtableadmin.projects.instances.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='BigtableadminProjectsInstancesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an instance within a project. This method updates only the display name and type for an Instance. To update other Instance properties, such as labels, use PartialUpdateInstance.

      Args:
        request: (Instance) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/instances/{instancesId}',
        http_method='PUT',
        method_id='bigtableadmin.projects.instances.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='<request>',
        request_type_name='Instance',
        response_type_name='Instance',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (BigtableadminProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='bigtableadmin.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BigtableadminProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (BigtableadminProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='bigtableadmin.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='BigtableadminProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(BigtableadminV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
