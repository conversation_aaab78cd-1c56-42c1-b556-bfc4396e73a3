"""Generated message classes for backupdr version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'backupdr'


class AbandonBackupRequest(_messages.Message):
  r"""request message for AbandonBackup.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class AcceleratorConfig(_messages.Message):
  r"""A specification of the type and number of accelerator cards attached to
  the instance.

  Fields:
    acceleratorCount: Optional. The number of the guest accelerator cards
      exposed to this instance.
    acceleratorType: Optional. Full or partial URL of the accelerator type
      resource to attach to this instance.
  """

  acceleratorCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  acceleratorType = _messages.StringField(2)


class AccessConfig(_messages.Message):
  r"""An access configuration attached to an instance's network interface.
  Only one access config per instance is supported.

  Enums:
    NetworkTierValueValuesEnum: Optional. This signifies the networking tier
      used for configuring this access
    TypeValueValuesEnum: Optional. In accessConfigs (IPv4), the default and
      only option is ONE_TO_ONE_NAT. In ipv6AccessConfigs, the default and
      only option is DIRECT_IPV6.

  Fields:
    externalIpv6: Optional. The external IPv6 address of this access
      configuration.
    externalIpv6PrefixLength: Optional. The prefix length of the external IPv6
      range.
    name: Optional. The name of this access configuration.
    natIP: Optional. The external IP address of this access configuration.
    networkTier: Optional. This signifies the networking tier used for
      configuring this access
    publicPtrDomainName: Optional. The DNS domain name for the public PTR
      record.
    setPublicPtr: Optional. Specifies whether a public DNS 'PTR' record should
      be created to map the external IP address of the instance to a DNS
      domain name.
    type: Optional. In accessConfigs (IPv4), the default and only option is
      ONE_TO_ONE_NAT. In ipv6AccessConfigs, the default and only option is
      DIRECT_IPV6.
  """

  class NetworkTierValueValuesEnum(_messages.Enum):
    r"""Optional. This signifies the networking tier used for configuring this
    access

    Values:
      NETWORK_TIER_UNSPECIFIED: Default value. This value is unused.
      PREMIUM: High quality, Google-grade network tier, support for all
        networking products.
      STANDARD: Public internet quality, only limited support for other
        networking products.
    """
    NETWORK_TIER_UNSPECIFIED = 0
    PREMIUM = 1
    STANDARD = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. In accessConfigs (IPv4), the default and only option is
    ONE_TO_ONE_NAT. In ipv6AccessConfigs, the default and only option is
    DIRECT_IPV6.

    Values:
      ACCESS_TYPE_UNSPECIFIED: Default value. This value is unused.
      ONE_TO_ONE_NAT: ONE_TO_ONE_NAT
      DIRECT_IPV6: Direct IPv6 access.
    """
    ACCESS_TYPE_UNSPECIFIED = 0
    ONE_TO_ONE_NAT = 1
    DIRECT_IPV6 = 2

  externalIpv6 = _messages.StringField(1)
  externalIpv6PrefixLength = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  name = _messages.StringField(3)
  natIP = _messages.StringField(4)
  networkTier = _messages.EnumField('NetworkTierValueValuesEnum', 5)
  publicPtrDomainName = _messages.StringField(6)
  setPublicPtr = _messages.BooleanField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)


class AdvancedMachineFeatures(_messages.Message):
  r"""Specifies options for controlling advanced machine features.

  Fields:
    enableNestedVirtualization: Optional. Whether to enable nested
      virtualization or not (default is false).
    enableUefiNetworking: Optional. Whether to enable UEFI networking for
      instance creation.
    threadsPerCore: Optional. The number of threads per physical core. To
      disable simultaneous multithreading (SMT) set this to 1. If unset, the
      maximum number of threads supported per core by the underlying processor
      is assumed.
    visibleCoreCount: Optional. The number of physical cores to expose to an
      instance. Multiply by the number of threads per core to compute the
      total number of virtual CPUs to expose to the instance. If unset, the
      number of cores is inferred from the instance's nominal CPU count and
      the underlying platform's SMT width.
  """

  enableNestedVirtualization = _messages.BooleanField(1)
  enableUefiNetworking = _messages.BooleanField(2)
  threadsPerCore = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  visibleCoreCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class AliasIpRange(_messages.Message):
  r"""An alias IP range attached to an instance's network interface.

  Fields:
    ipCidrRange: Optional. The IP alias ranges to allocate for this interface.
    subnetworkRangeName: Optional. The name of a subnetwork secondary IP range
      from which to allocate an IP alias range. If not specified, the primary
      range of the subnetwork is used.
  """

  ipCidrRange = _messages.StringField(1)
  subnetworkRangeName = _messages.StringField(2)


class AllocationAffinity(_messages.Message):
  r"""Specifies the reservations that this instance can consume from.

  Enums:
    ConsumeReservationTypeValueValuesEnum: Optional. Specifies the type of
      reservation from which this instance can consume

  Fields:
    consumeReservationType: Optional. Specifies the type of reservation from
      which this instance can consume
    key: Optional. Corresponds to the label key of a reservation resource.
    values: Optional. Corresponds to the label values of a reservation
      resource.
  """

  class ConsumeReservationTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the type of reservation from which this instance
    can consume

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      NO_RESERVATION: Do not consume from any allocated capacity.
      ANY_RESERVATION: Consume any allocation available.
      SPECIFIC_RESERVATION: Must consume from a specific reservation. Must
        specify key value fields for specifying the reservations.
    """
    TYPE_UNSPECIFIED = 0
    NO_RESERVATION = 1
    ANY_RESERVATION = 2
    SPECIFIC_RESERVATION = 3

  consumeReservationType = _messages.EnumField('ConsumeReservationTypeValueValuesEnum', 1)
  key = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class AttachedDisk(_messages.Message):
  r"""An instance-attached disk resource.

  Enums:
    DiskInterfaceValueValuesEnum: Optional. Specifies the disk interface to
      use for attaching this disk.
    DiskTypeDeprecatedValueValuesEnum: Specifies the type of the disk.
    ModeValueValuesEnum: Optional. The mode in which to attach this disk.
    SavedStateValueValuesEnum: Optional. Output only. The state of the disk.
    TypeValueValuesEnum: Optional. Specifies the type of the disk.

  Fields:
    autoDelete: Optional. Specifies whether the disk will be auto-deleted when
      the instance is deleted (but not when the disk is detached from the
      instance).
    boot: Optional. Indicates that this is a boot disk. The virtual machine
      will use the first partition of the disk for its root filesystem.
    deviceName: Optional. This is used as an identifier for the disks. This is
      the unique name has to provided to modify disk parameters like disk_name
      and replica_zones (in case of RePDs)
    diskEncryptionKey: Optional. Encrypts or decrypts a disk using a customer-
      supplied encryption key.
    diskInterface: Optional. Specifies the disk interface to use for attaching
      this disk.
    diskSizeGb: Optional. The size of the disk in GB.
    diskType: Optional. Output only. The URI of the disk type resource. For
      example: projects/project/zones/zone/diskTypes/pd-standard or pd-ssd
    diskTypeDeprecated: Specifies the type of the disk.
    guestOsFeature: Optional. A list of features to enable on the guest
      operating system. Applicable only for bootable images.
    index: Optional. A zero-based index to this disk, where 0 is reserved for
      the boot disk.
    initializeParams: Optional. Specifies the parameters to initialize this
      disk.
    kind: Optional. Type of the resource.
    license: Optional. Any valid publicly visible licenses.
    mode: Optional. The mode in which to attach this disk.
    savedState: Optional. Output only. The state of the disk.
    source: Optional. Specifies a valid partial or full URL to an existing
      Persistent Disk resource.
    type: Optional. Specifies the type of the disk.
  """

  class DiskInterfaceValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the disk interface to use for attaching this disk.

    Values:
      DISK_INTERFACE_UNSPECIFIED: Default value, which is unused.
      SCSI: SCSI Disk Interface.
      NVME: NVME Disk Interface.
      NVDIMM: NVDIMM Disk Interface.
      ISCSI: ISCSI Disk Interface.
    """
    DISK_INTERFACE_UNSPECIFIED = 0
    SCSI = 1
    NVME = 2
    NVDIMM = 3
    ISCSI = 4

  class DiskTypeDeprecatedValueValuesEnum(_messages.Enum):
    r"""Specifies the type of the disk.

    Values:
      DISK_TYPE_UNSPECIFIED: Default value, which is unused.
      SCRATCH: A scratch disk type.
      PERSISTENT: A persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    SCRATCH = 1
    PERSISTENT = 2

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode in which to attach this disk.

    Values:
      DISK_MODE_UNSPECIFIED: Default value, which is unused.
      READ_WRITE: Attaches this disk in read-write mode. Only one virtual
        machine at a time can be attached to a disk in read-write mode.
      READ_ONLY: Attaches this disk in read-only mode. Multiple virtual
        machines can use a disk in read-only mode at a time.
      LOCKED: The disk is locked for administrative reasons. Nobody else can
        use the disk. This mode is used (for example) when taking a snapshot
        of a disk to prevent mounting the disk while it is being snapshotted.
    """
    DISK_MODE_UNSPECIFIED = 0
    READ_WRITE = 1
    READ_ONLY = 2
    LOCKED = 3

  class SavedStateValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. The state of the disk.

    Values:
      DISK_SAVED_STATE_UNSPECIFIED: Default Disk state has not been preserved.
      PRESERVED: Disk state has been preserved.
    """
    DISK_SAVED_STATE_UNSPECIFIED = 0
    PRESERVED = 1

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the type of the disk.

    Values:
      DISK_TYPE_UNSPECIFIED: Default value, which is unused.
      SCRATCH: A scratch disk type.
      PERSISTENT: A persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    SCRATCH = 1
    PERSISTENT = 2

  autoDelete = _messages.BooleanField(1)
  boot = _messages.BooleanField(2)
  deviceName = _messages.StringField(3)
  diskEncryptionKey = _messages.MessageField('CustomerEncryptionKey', 4)
  diskInterface = _messages.EnumField('DiskInterfaceValueValuesEnum', 5)
  diskSizeGb = _messages.IntegerField(6)
  diskType = _messages.StringField(7)
  diskTypeDeprecated = _messages.EnumField('DiskTypeDeprecatedValueValuesEnum', 8)
  guestOsFeature = _messages.MessageField('GuestOsFeature', 9, repeated=True)
  index = _messages.IntegerField(10)
  initializeParams = _messages.MessageField('InitializeParams', 11)
  kind = _messages.StringField(12)
  license = _messages.StringField(13, repeated=True)
  mode = _messages.EnumField('ModeValueValuesEnum', 14)
  savedState = _messages.EnumField('SavedStateValueValuesEnum', 15)
  source = _messages.StringField(16)
  type = _messages.EnumField('TypeValueValuesEnum', 17)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Backup(_messages.Message):
  r"""Message describing a Backup object.

  Enums:
    BackupTypeValueValuesEnum: Output only. Type of the backup, unspecified,
      scheduled or ondemand.
    StateValueValuesEnum: Output only. The Backup resource instance state.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. No labels currently defined.

  Fields:
    backupApplianceBackupProperties: Output only. Backup Appliance specific
      backup properties.
    backupApplianceLocks: Optional. The list of BackupLocks taken by the
      accessor Backup Appliance.
    backupType: Output only. Type of the backup, unspecified, scheduled or
      ondemand.
    cloudSqlInstanceBackupProperties: Output only. Cloud SQL specific backup
      properties.
    computeInstanceBackupProperties: Output only. Compute Engine specific
      backup properties.
    consistencyTime: Output only. The point in time when this backup was
      captured from the source.
    createTime: Output only. The time when the instance was created.
    description: Output only. The description of the Backup instance (2048
      characters or less).
    diskBackupProperties: Output only. Disk specific backup properties.
    enforcedRetentionEndTime: Optional. The backup can not be deleted before
      this time.
    etag: Optional. Server specified ETag to prevent updates from overwriting
      each other.
    expireTime: Optional. When this backup is automatically expired.
    gcpBackupPlanInfo: Output only. Configuration for a Google Cloud resource.
    labels: Optional. Resource labels to represent user provided metadata. No
      labels currently defined.
    name: Output only. Identifier. Name of the backup to create. It must have
      the format`"projects//locations//backupVaults//dataSources/{datasource}/
      backups/{backup}"`. `{backup}` cannot be changed after creation. It must
      be between 3-63 characters long and must be unique within the
      datasource.
    resourceSizeBytes: Output only. source resource size in bytes at the time
      of the backup.
    satisfiesPzi: Optional. Output only. Reserved for future use.
    satisfiesPzs: Optional. Output only. Reserved for future use.
    serviceLocks: Output only. The list of BackupLocks taken by the service to
      prevent the deletion of the backup.
    state: Output only. The Backup resource instance state.
    updateTime: Output only. The time when the instance was updated.
  """

  class BackupTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the backup, unspecified, scheduled or ondemand.

    Values:
      BACKUP_TYPE_UNSPECIFIED: Backup type is unspecified.
      SCHEDULED: Scheduled backup.
      ON_DEMAND: On demand backup.
      ON_DEMAND_OPERATIONAL: Operational backup.
    """
    BACKUP_TYPE_UNSPECIFIED = 0
    SCHEDULED = 1
    ON_DEMAND = 2
    ON_DEMAND_OPERATIONAL = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The Backup resource instance state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The backup is being created.
      ACTIVE: The backup has been created and is fully usable.
      DELETING: The backup is being deleted.
      ERROR: The backup is experiencing an issue and might be unusable.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ERROR = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. No
    labels currently defined.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupApplianceBackupProperties = _messages.MessageField('BackupApplianceBackupProperties', 1)
  backupApplianceLocks = _messages.MessageField('BackupLock', 2, repeated=True)
  backupType = _messages.EnumField('BackupTypeValueValuesEnum', 3)
  cloudSqlInstanceBackupProperties = _messages.MessageField('CloudSqlInstanceBackupProperties', 4)
  computeInstanceBackupProperties = _messages.MessageField('ComputeInstanceBackupProperties', 5)
  consistencyTime = _messages.StringField(6)
  createTime = _messages.StringField(7)
  description = _messages.StringField(8)
  diskBackupProperties = _messages.MessageField('DiskBackupProperties', 9)
  enforcedRetentionEndTime = _messages.StringField(10)
  etag = _messages.StringField(11)
  expireTime = _messages.StringField(12)
  gcpBackupPlanInfo = _messages.MessageField('GCPBackupPlanInfo', 13)
  labels = _messages.MessageField('LabelsValue', 14)
  name = _messages.StringField(15)
  resourceSizeBytes = _messages.IntegerField(16)
  satisfiesPzi = _messages.BooleanField(17)
  satisfiesPzs = _messages.BooleanField(18)
  serviceLocks = _messages.MessageField('BackupLock', 19, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  updateTime = _messages.StringField(21)


class BackupApplianceBackupConfig(_messages.Message):
  r"""BackupApplianceBackupConfig captures the backup configuration for
  applications that are protected by Backup Appliances.

  Fields:
    applicationName: The name of the application.
    backupApplianceId: The ID of the backup appliance.
    backupApplianceName: The name of the backup appliance.
    hostName: The name of the host where the application is running.
    slaId: The ID of the SLA of this application.
    slpName: The name of the SLP associated with the application.
    sltName: The name of the SLT associated with the application.
  """

  applicationName = _messages.StringField(1)
  backupApplianceId = _messages.IntegerField(2)
  backupApplianceName = _messages.StringField(3)
  hostName = _messages.StringField(4)
  slaId = _messages.IntegerField(5)
  slpName = _messages.StringField(6)
  sltName = _messages.StringField(7)


class BackupApplianceBackupProperties(_messages.Message):
  r"""BackupApplianceBackupProperties represents BackupDR backup appliance's
  properties.

  Fields:
    finalizeTime: Output only. The time when this backup object was finalized
      (if none, backup is not finalized).
    generationId: Output only. The numeric generation ID of the backup
      (monotonically increasing).
    recoveryRangeEndTime: Optional. The latest timestamp of data available in
      this Backup.
    recoveryRangeStartTime: Optional. The earliest timestamp of data available
      in this Backup.
  """

  finalizeTime = _messages.StringField(1)
  generationId = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  recoveryRangeEndTime = _messages.StringField(3)
  recoveryRangeStartTime = _messages.StringField(4)


class BackupApplianceLockInfo(_messages.Message):
  r"""BackupApplianceLockInfo contains metadata about the backupappliance that
  created the lock.

  Fields:
    backupApplianceId: Required. The ID of the backup/recovery appliance that
      created this lock.
    backupApplianceName: Required. The name of the backup/recovery appliance
      that created this lock.
    backupImage: The image name that depends on this Backup.
    jobName: The job name on the backup/recovery appliance that created this
      lock.
    lockReason: Required. The reason for the lock: e.g.
      MOUNT/RESTORE/BACKUP/etc. The value of this string is only meaningful to
      the client and it is not interpreted by the BackupVault service.
    slaId: The SLA on the backup/recovery appliance that owns the lock.
  """

  backupApplianceId = _messages.IntegerField(1)
  backupApplianceName = _messages.StringField(2)
  backupImage = _messages.StringField(3)
  jobName = _messages.StringField(4)
  lockReason = _messages.StringField(5)
  slaId = _messages.IntegerField(6)


class BackupConfigDetails(_messages.Message):
  r"""BackupConfigDetails has information about how the resource is configured
  for backups and about the most recent backup taken for this configuration.

  Enums:
    StateValueValuesEnum: Output only. The state of the backup config
      resource.
    TypeValueValuesEnum: Output only. The type of the backup config resource.

  Fields:
    applicableResource: Output only. The [full resource
      name](https://cloud.google.com/asset-inventory/docs/resource-name-
      format) of the resource that is applicable for the backup configuration.
      Example: "//compute.googleapis.com/projects/{project}/zones/{zone}/insta
      nces/{instance}"
    backupConfigSource: Output only. The full resource name of the backup
      config source resource. For example, "//backupdr.googleapis.com/v1/proje
      cts/{project}/locations/{region}/backupPlans/{backupplanId}" or "//compu
      te.googleapis.com/projects/{project}/locations/{region}/resourcePolicies
      /{resourcePolicyId}".
    backupConfigSourceDisplayName: Output only. The display name of the backup
      config source resource.
    backupDrPlanConfig: Backup and DR's Backup Plan specific data.
    backupDrTemplateConfig: Backup and DR's Template specific data.
    backupLocations: The locations where the backups are to be stored.
    backupVault: Output only. The [full resource
      name](https://cloud.google.com/asset-inventory/docs/resource-name-
      format) of the backup vault that will store the backups generated
      through this backup configuration. Example: "//backupdr.googleapis.com/v
      1/projects/{project}/locations/{region}/backupVaults/{backupvaultId}"
    latestSuccessfulBackupTime: Output only. Timestamp of the latest
      successful backup created via this backup configuration.
    pitrSettings: Output only. Point in time recovery settings of the backup
      configuration resource.
    state: Output only. The state of the backup config resource.
    type: Output only. The type of the backup config resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the backup config resource.

    Values:
      STATE_UNSPECIFIED: Backup config state not set.
      ACTIVE: The config is in an active state protecting the resource
      INACTIVE: The config is currently not protecting the resource. Either
        because it is disabled or the owning project has been deleted without
        cleanup of the actual resource.
      ERROR: The config still exists but because of some error state it is not
        protecting the resource. Like the source project is deleted. For eg.
        PlanAssociation, BackupPlan is deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2
    ERROR = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the backup config resource.

    Values:
      TYPE_UNSPECIFIED: Backup config type is unspecified.
      CLOUD_SQL_INSTANCE_BACKUP_CONFIG: Backup config is Cloud SQL instance's
        automated backup config.
      COMPUTE_ENGINE_RESOURCE_POLICY: Backup config is Compute Engine Resource
        Policy.
      BACKUPDR_BACKUP_PLAN: Backup config is Backup and DR's Backup Plan.
      BACKUPDR_TEMPLATE: Backup config is Backup and DR's Template.
    """
    TYPE_UNSPECIFIED = 0
    CLOUD_SQL_INSTANCE_BACKUP_CONFIG = 1
    COMPUTE_ENGINE_RESOURCE_POLICY = 2
    BACKUPDR_BACKUP_PLAN = 3
    BACKUPDR_TEMPLATE = 4

  applicableResource = _messages.StringField(1)
  backupConfigSource = _messages.StringField(2)
  backupConfigSourceDisplayName = _messages.StringField(3)
  backupDrPlanConfig = _messages.MessageField('BackupDrPlanConfig', 4)
  backupDrTemplateConfig = _messages.MessageField('BackupDrTemplateConfig', 5)
  backupLocations = _messages.MessageField('BackupLocation', 6, repeated=True)
  backupVault = _messages.StringField(7)
  latestSuccessfulBackupTime = _messages.StringField(8)
  pitrSettings = _messages.MessageField('PitrSettings', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)


class BackupConfigInfo(_messages.Message):
  r"""BackupConfigInfo has information about how the resource is configured
  for Backup and about the most recent backup to this vault.

  Enums:
    LastBackupStateValueValuesEnum: Output only. The status of the last backup
      to this BackupVault

  Fields:
    backupApplianceBackupConfig: Configuration for an application backed up by
      a Backup Appliance.
    gcpBackupConfig: Configuration for a Google Cloud resource.
    lastBackupError: Output only. If the last backup failed, this field has
      the error message.
    lastBackupState: Output only. The status of the last backup to this
      BackupVault
    lastSuccessfulBackupConsistencyTime: Output only. If the last backup were
      successful, this field has the consistency date.
    lastSuccessfulLogBackupConsistencyTime: Output only. If the last log
      backup were successful, this field has the consistency date.
  """

  class LastBackupStateValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the last backup to this BackupVault

    Values:
      LAST_BACKUP_STATE_UNSPECIFIED: Status not set.
      FIRST_BACKUP_PENDING: The first backup has not yet completed
      SUCCEEDED: The most recent backup was successful
      FAILED: The most recent backup failed
      PERMISSION_DENIED: The most recent backup could not be run/failed
        because of the lack of permissions
    """
    LAST_BACKUP_STATE_UNSPECIFIED = 0
    FIRST_BACKUP_PENDING = 1
    SUCCEEDED = 2
    FAILED = 3
    PERMISSION_DENIED = 4

  backupApplianceBackupConfig = _messages.MessageField('BackupApplianceBackupConfig', 1)
  gcpBackupConfig = _messages.MessageField('GcpBackupConfig', 2)
  lastBackupError = _messages.MessageField('Status', 3)
  lastBackupState = _messages.EnumField('LastBackupStateValueValuesEnum', 4)
  lastSuccessfulBackupConsistencyTime = _messages.StringField(5)
  lastSuccessfulLogBackupConsistencyTime = _messages.StringField(6)


class BackupDrPlanConfig(_messages.Message):
  r"""BackupDrPlanConfig has additional information about Backup and DR's Plan
  backup configuration.

  Fields:
    backupDrPlanRules: Backup rules of the backup plan resource.
  """

  backupDrPlanRules = _messages.MessageField('BackupDrPlanRule', 1, repeated=True)


class BackupDrPlanRule(_messages.Message):
  r"""BackupDrPlanRule has rule specific information of the backup plan
  resource.

  Fields:
    lastSuccessfulBackupTime: Output only. Timestamp of the latest successful
      backup created via this backup rule.
    ruleId: Output only. Unique Id of the backup rule.
  """

  lastSuccessfulBackupTime = _messages.StringField(1)
  ruleId = _messages.StringField(2)


class BackupDrTemplateConfig(_messages.Message):
  r"""BackupDrTemplateConfig has additional information about Backup and DR's
  Template backup configuration.

  Fields:
    firstPartyManagementUri: Output only. The URI of the BackupDr template
      resource for the first party identity users.
    thirdPartyManagementUri: Output only. The URI of the BackupDr template
      resource for the third party identity users.
  """

  firstPartyManagementUri = _messages.StringField(1)
  thirdPartyManagementUri = _messages.StringField(2)


class BackupLocation(_messages.Message):
  r"""BackupLocation represents a cloud location where a backup can be stored.

  Enums:
    TypeValueValuesEnum: Output only. The type of the location.

  Fields:
    locationId: Output only. The id of the cloud location. Example: "us-
      central1"
    type: Output only. The type of the location.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the location.

    Values:
      TYPE_UNSPECIFIED: Location type is unspecified.
      ZONAL: Location type is zonal.
      REGIONAL: Location type is regional.
      MULTI_REGIONAL: Location type is multi regional.
    """
    TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2
    MULTI_REGIONAL = 3

  locationId = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class BackupLock(_messages.Message):
  r"""BackupLock represents a single lock on a Backup resource. An unexpired
  lock on a Backup prevents the Backup from being deleted.

  Fields:
    backupApplianceLockInfo: If the client is a backup and recovery appliance,
      this contains metadata about why the lock exists.
    lockUntilTime: Required. The time after which this lock is not considered
      valid and will no longer protect the Backup from deletion.
    serviceLockInfo: Output only. Contains metadata about the lock exist for
      Google Cloud native backups.
  """

  backupApplianceLockInfo = _messages.MessageField('BackupApplianceLockInfo', 1)
  lockUntilTime = _messages.StringField(2)
  serviceLockInfo = _messages.MessageField('ServiceLockInfo', 3)


class BackupPlan(_messages.Message):
  r"""A `BackupPlan` specifies some common fields, such as `description` as
  well as one or more `BackupRule` messages. Each `BackupRule` has a retention
  policy and defines a schedule by which the system is to perform backup
  workloads.

  Enums:
    StateValueValuesEnum: Output only. The `State` for the `BackupPlan`.

  Messages:
    LabelsValue: Optional. This collection of key/value pairs allows for
      custom labels to be supplied by the user. Example, {"tag": "Weekly"}.

  Fields:
    backupRules: Required. The backup rules for this `BackupPlan`. There must
      be at least one `BackupRule` message.
    backupVault: Required. Resource name of backup vault which will be used as
      storage location for backups. Format:
      projects/{project}/locations/{location}/backupVaults/{backupvault}
    backupVaultServiceAccount: Output only. The Google Cloud Platform Service
      Account to be used by the BackupVault for taking backups. Specify the
      email address of the Backup Vault Service Account.
    createTime: Output only. When the `BackupPlan` was created.
    description: Optional. The description of the `BackupPlan` resource. The
      description allows for additional details about `BackupPlan` and its use
      cases to be provided. An example description is the following: "This is
      a backup plan that performs a daily backup at 6pm and retains data for 3
      months". The description must be at most 2048 characters.
    etag: Optional. `etag` is returned from the service in the response. As a
      user of the service, you may provide an etag value in this field to
      prevent stale resources.
    labels: Optional. This collection of key/value pairs allows for custom
      labels to be supplied by the user. Example, {"tag": "Weekly"}.
    logRetentionDays: Optional. Required for CloudSQL resource_type Configures
      how long logs will be stored. It is defined in "days". This value should
      be greater than or equal to minimum enforced log retention duration of
      the backup vault.
    name: Output only. Identifier. The resource name of the `BackupPlan`.
      Format:
      `projects/{project}/locations/{location}/backupPlans/{backup_plan}`
    resourceType: Required.
    revisionId: Output only. The user friendly revision ID of the
      `BackupPlanRevision`. Example: v0, v1, v2, etc.
    revisionName: Output only. The resource id of the `BackupPlanRevision`.
      Format: `projects/{project}/locations/{location}/backupPlans/{backup_pla
      n}/revisions/{revision_id}`
    state: Output only. The `State` for the `BackupPlan`.
    supportedResourceTypes: Output only. All resource types to which
      backupPlan can be applied.
    updateTime: Output only. When the `BackupPlan` was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The `State` for the `BackupPlan`.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The resource is being created.
      ACTIVE: The resource has been created and is fully usable.
      DELETING: The resource is being deleted.
      INACTIVE: The resource has been created but is not usable.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. This collection of key/value pairs allows for custom labels
    to be supplied by the user. Example, {"tag": "Weekly"}.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupRules = _messages.MessageField('BackupRule', 1, repeated=True)
  backupVault = _messages.StringField(2)
  backupVaultServiceAccount = _messages.StringField(3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  logRetentionDays = _messages.IntegerField(8)
  name = _messages.StringField(9)
  resourceType = _messages.StringField(10)
  revisionId = _messages.StringField(11)
  revisionName = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  supportedResourceTypes = _messages.StringField(14, repeated=True)
  updateTime = _messages.StringField(15)


class BackupPlanAssociation(_messages.Message):
  r"""A BackupPlanAssociation represents a single BackupPlanAssociation which
  contains details like workload, backup plan etc

  Enums:
    StateValueValuesEnum: Output only. The BackupPlanAssociation resource
      state.

  Fields:
    backupPlan: Required. Resource name of backup plan which needs to be
      applied on workload. Format:
      projects/{project}/locations/{location}/backupPlans/{backupPlanId}
    cloudSqlInstanceBackupPlanAssociationProperties: Cloud SQL instance's
      backup plan association properties.
    createTime: Output only. The time when the instance was created.
    dataSource: Output only. Resource name of data source which will be used
      as storage location for backups taken. Format : projects/{project}/locat
      ions/{location}/backupVaults/{backupvault}/dataSources/{datasource}
    name: Output only. Identifier. The resource name of BackupPlanAssociation
      in below format Format : projects/{project}/locations/{location}/backupP
      lanAssociations/{backupPlanAssociationId}
    resource: Required. Immutable. Resource name of workload on which
      backupplan is applied
    resourceType: Required. Immutable.
    rulesConfigInfo: Output only. The config info related to backup rules.
    state: Output only. The BackupPlanAssociation resource state.
    updateTime: Output only. The time when the instance was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The BackupPlanAssociation resource state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The resource is being created.
      ACTIVE: The resource has been created and is fully usable.
      DELETING: The resource is being deleted.
      INACTIVE: The resource has been created but is not usable.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4
    UPDATING = 5

  backupPlan = _messages.StringField(1)
  cloudSqlInstanceBackupPlanAssociationProperties = _messages.MessageField('CloudSqlInstanceBackupPlanAssociationProperties', 2)
  createTime = _messages.StringField(3)
  dataSource = _messages.StringField(4)
  name = _messages.StringField(5)
  resource = _messages.StringField(6)
  resourceType = _messages.StringField(7)
  rulesConfigInfo = _messages.MessageField('RuleConfigInfo', 8, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class BackupPlanRevision(_messages.Message):
  r"""`BackupPlanRevision` represents a snapshot of a `BackupPlan` at a point
  in time.

  Enums:
    StateValueValuesEnum: Output only. Resource State

  Fields:
    backupPlanSnapshot: The Backup Plan being encompassed by this revision.
    createTime: Output only. The timestamp that the revision was created.
    name: Output only. Identifier. The resource name of the
      `BackupPlanRevision`. Format: `projects/{project}/locations/{location}/b
      ackupPlans/{backup_plan}/revisions/{revision}`
    revisionId: Output only. The user friendly revision ID of the
      `BackupPlanRevision`. Example: v0, v1, v2, etc.
    state: Output only. Resource State
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Resource State

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The resource is being created.
      ACTIVE: The resource has been created and is fully usable.
      DELETING: The resource is being deleted.
      INACTIVE: The resource has been created but is not usable.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4

  backupPlanSnapshot = _messages.MessageField('BackupPlan', 1)
  createTime = _messages.StringField(2)
  name = _messages.StringField(3)
  revisionId = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class BackupRule(_messages.Message):
  r"""`BackupRule` binds the backup schedule to a retention policy.

  Fields:
    backupRetentionDays: Required. Configures the duration for which backup
      data will be kept. It is defined in "days". The value should be greater
      than or equal to minimum enforced retention of the backup vault. Minimum
      value is 1 and maximum value is 36159 for custom retention on-demand
      backup. Minimum value is 1 and maximum value is 90 for hourly backups.
      Minimum value is 1 and maximum value is 186 for daily backups. Minimum
      value is 7 and maximum value is 366 for weekly backups. Minimum value is
      30 and maximum value is 732 for monthly backups. Minimum value is 365
      and maximum value is 36159 for yearly backups.
    ruleId: Required. Immutable. The unique id of this `BackupRule`. The
      `rule_id` is unique per `BackupPlan`.The `rule_id` must start with a
      lowercase letter followed by up to 62 lowercase letters, numbers, or
      hyphens. Pattern, /a-z{,62}/.
    standardSchedule: Optional. Defines a schedule that runs within the
      confines of a defined window of time.
  """

  backupRetentionDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  ruleId = _messages.StringField(2)
  standardSchedule = _messages.MessageField('StandardSchedule', 3)


class BackupVault(_messages.Message):
  r"""Message describing a BackupVault object.

  Enums:
    AccessRestrictionValueValuesEnum: Optional. Note: This field is added for
      future use case and will not be supported in the current release. Access
      restriction for the backup vault. Default value is WITHIN_ORGANIZATION
      if not provided during creation.
    BackupRetentionInheritanceValueValuesEnum: Optional. Setting for how a
      backup's enforced retention end time is inherited.
    StateValueValuesEnum: Output only. The BackupVault resource instance
      state.

  Messages:
    AnnotationsValue: Optional. User annotations. See
      https://google.aip.dev/128#annotations Stores small amounts of arbitrary
      data.
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. No labels currently defined:

  Fields:
    accessRestriction: Optional. Note: This field is added for future use case
      and will not be supported in the current release. Access restriction for
      the backup vault. Default value is WITHIN_ORGANIZATION if not provided
      during creation.
    annotations: Optional. User annotations. See
      https://google.aip.dev/128#annotations Stores small amounts of arbitrary
      data.
    backupCount: Output only. The number of backups in this backup vault.
    backupMinimumEnforcedRetentionDuration: Required. The default and minimum
      enforced retention for each backup within the backup vault. The enforced
      retention for each backup can be extended.
    backupRetentionInheritance: Optional. Setting for how a backup's enforced
      retention end time is inherited.
    createTime: Output only. The time when the instance was created.
    deletable: Output only. Set to true when there are no backups nested under
      this resource.
    description: Optional. The description of the BackupVault instance (2048
      characters or less).
    effectiveTime: Optional. Time after which the BackupVault resource is
      locked.
    etag: Optional. Server specified ETag for the backup vault resource to
      prevent simultaneous updates from overwiting each other.
    labels: Optional. Resource labels to represent user provided metadata. No
      labels currently defined:
    logMinimumEnforcedRetentionDuration: Optional. The default and minimum
      enforced retention for Database logs within the backup vault. The
      enforced retention for logs can be extended.
    name: Output only. Identifier. Name of the backup vault to create. It must
      have the format`"projects/{project}/locations/{location}/backupVaults/{b
      ackupvault}"`. `{backupvault}` cannot be changed after creation. It must
      be between 3-63 characters long and must be unique within the project
      and location.
    serviceAccount: Output only. Service account used by the BackupVault
      Service for this BackupVault. The user should grant this account
      permissions in their workload project to enable the service to run
      backups and restores there.
    state: Output only. The BackupVault resource instance state.
    totalStoredBytes: Output only. Total size of the storage used by all
      backup resources.
    uid: Output only. Immutable after resource creation until resource
      deletion.
    updateTime: Output only. The time when the instance was updated.
  """

  class AccessRestrictionValueValuesEnum(_messages.Enum):
    r"""Optional. Note: This field is added for future use case and will not
    be supported in the current release. Access restriction for the backup
    vault. Default value is WITHIN_ORGANIZATION if not provided during
    creation.

    Values:
      ACCESS_RESTRICTION_UNSPECIFIED: Access restriction not set. If user does
        not provide any value or pass this value, it will be changed to
        WITHIN_ORGANIZATION.
      WITHIN_PROJECT: Access to or from resources outside your current project
        will be denied.
      WITHIN_ORGANIZATION: Access to or from resources outside your current
        organization will be denied.
      UNRESTRICTED: No access restriction.
      WITHIN_ORG_BUT_UNRESTRICTED_FOR_BA: Access to or from resources outside
        your current organization will be denied except for backup appliance.
    """
    ACCESS_RESTRICTION_UNSPECIFIED = 0
    WITHIN_PROJECT = 1
    WITHIN_ORGANIZATION = 2
    UNRESTRICTED = 3
    WITHIN_ORG_BUT_UNRESTRICTED_FOR_BA = 4

  class BackupRetentionInheritanceValueValuesEnum(_messages.Enum):
    r"""Optional.

    Setting for how a backup's enforced retention end time is inherited.

    Values:
      BACKUP_RETENTION_INHERITANCE_UNSPECIFIED: Inheritance behavior not set.
        This will default to `INHERIT_VAULT_RETENTION`.
      INHERIT_VAULT_RETENTION: The enforced retention end time of a backup
        will be inherited from the backup vault's
        `backup_minimum_enforced_retention_duration` field. This is the
        default behavior.
      MATCH_BACKUP_EXPIRE_TIME: The enforced retention end time of a backup
        will always match the expire time of the backup. If this is set, the
        backup's enforced retention end time will be set to match the expire
        time during creation of the backup. When updating, the ERET and expire
        time must be updated together and have the same value. Invalid update
        requests will be rejected by the server.
    """

    BACKUP_RETENTION_INHERITANCE_UNSPECIFIED = 0
    INHERIT_VAULT_RETENTION = 1
    MATCH_BACKUP_EXPIRE_TIME = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The BackupVault resource instance state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The backup vault is being created.
      ACTIVE: The backup vault has been created and is fully usable.
      DELETING: The backup vault is being deleted.
      ERROR: The backup vault is experiencing an issue and might be unusable.
      UPDATING: The backup vault is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ERROR = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. See https://google.aip.dev/128#annotations
    Stores small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. No
    labels currently defined:

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessRestriction = _messages.EnumField('AccessRestrictionValueValuesEnum', 1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  backupCount = _messages.IntegerField(3)
  backupMinimumEnforcedRetentionDuration = _messages.StringField(4)
  backupRetentionInheritance = _messages.EnumField(
      'BackupRetentionInheritanceValueValuesEnum', 5
  )
  createTime = _messages.StringField(6)
  deletable = _messages.BooleanField(7)
  description = _messages.StringField(8)
  effectiveTime = _messages.StringField(9)
  etag = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  logMinimumEnforcedRetentionDuration = _messages.StringField(12)
  name = _messages.StringField(13)
  serviceAccount = _messages.StringField(14)
  state = _messages.EnumField('StateValueValuesEnum', 15)
  totalStoredBytes = _messages.IntegerField(16)
  uid = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class BackupWindow(_messages.Message):
  r"""`BackupWindow` defines a window of the day during which backup jobs will
  run.

  Fields:
    endHourOfDay: Required. The hour of day (1-24) when the window end for
      e.g. if value of end hour of day is 10 that mean backup window end time
      is 10:00. End hour of day should be greater than start hour of day. 0 <=
      start_hour_of_day < end_hour_of_day <= 24 End hour of day is not include
      in backup window that mean if end_hour_of_day= 10 jobs should start
      before 10:00.
    startHourOfDay: Required. The hour of day (0-23) when the window starts
      for e.g. if value of start hour of day is 6 that mean backup window
      start at 6:00.
  """

  endHourOfDay = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  startHourOfDay = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BackupdrProjectsLocationsBackupPlanAssociationsCreateRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsCreateRequest object.

  Fields:
    backupPlanAssociation: A BackupPlanAssociation resource to be passed as
      the request body.
    backupPlanAssociationId: Required. The name of the backup plan association
      to create. The name must be unique for the specified project and
      location.
    parent: Required. The backup plan association project and location in the
      format `projects/{project_id}/locations/{location}`. In Cloud BackupDR
      locations map to GCP regions, for example **us-central1**.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  backupPlanAssociation = _messages.MessageField('BackupPlanAssociation', 1)
  backupPlanAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BackupdrProjectsLocationsBackupPlanAssociationsDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsDeleteRequest object.

  Fields:
    name: Required. Name of the backup plan association resource, in the
      format `projects/{project}/locations/{location}/backupPlanAssociations/{
      backupPlanAssociationId}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BackupdrProjectsLocationsBackupPlanAssociationsFetchForResourceTypeRequest(_messages.Message):
  r"""A
  BackupdrProjectsLocationsBackupPlanAssociationsFetchForResourceTypeRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the results fetched in
      the response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. Supported
      fields: * resource * backup_plan * state * data_source * cloud_sql_insta
      nce_backup_plan_association_properties.instance_create_time
    orderBy: Optional. A comma-separated list of fields to order by, sorted in
      ascending order. Use "desc" after a field name for descending. Supported
      fields: * name
    pageSize: Optional. The maximum number of BackupPlanAssociations to
      return. The service may return fewer than this value. If unspecified, at
      most 50 BackupPlanAssociations will be returned. The maximum value is
      100; values above 100 will be coerced to 100.
    pageToken: Optional. A page token, received from a previous call of
      `FetchBackupPlanAssociationsForResourceType`. Provide this to retrieve
      the subsequent page. When paginating, all other parameters provided to
      `FetchBackupPlanAssociationsForResourceType` must match the call that
      provided the page token.
    parent: Required. The parent resource name. Format:
      projects/{project}/locations/{location}
    resourceType: Required. The type of the GCP resource. Ex:
      sql.googleapis.com/Instance
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  resourceType = _messages.StringField(6)


class BackupdrProjectsLocationsBackupPlanAssociationsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsGetRequest object.

  Fields:
    name: Required. Name of the backup plan association resource, in the
      format `projects/{project}/locations/{location}/backupPlanAssociations/{
      backupPlanAssociationId}`
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsBackupPlanAssociationsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsListRequest object.

  Fields:
    filter: Optional. Filtering results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve backup
      Plan Associations information, in the format
      `projects/{project_id}/locations/{location}`. In Cloud BackupDR,
      locations map to GCP regions, for example **us-central1**. To retrieve
      backup plan associations for all locations, use "-" for the `{location}`
      value.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class BackupdrProjectsLocationsBackupPlanAssociationsPatchRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsPatchRequest object.

  Fields:
    backupPlanAssociation: A BackupPlanAssociation resource to be passed as
      the request body.
    name: Output only. Identifier. The resource name of BackupPlanAssociation
      in below format Format : projects/{project}/locations/{location}/backupP
      lanAssociations/{backupPlanAssociationId}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. The list of fields to update. Field mask is used to
      specify the fields to be overwritten in the BackupPlanAssociation
      resource by the update. The fields specified in the update_mask are
      relative to the resource, not the full request. A field will be
      overwritten if it is in the mask. If the user does not provide a mask
      then the request will fail. Currently
      backup_plan_association.backup_plan is the only supported field.
  """

  backupPlanAssociation = _messages.MessageField('BackupPlanAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BackupdrProjectsLocationsBackupPlanAssociationsTriggerBackupRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlanAssociationsTriggerBackupRequest
  object.

  Fields:
    name: Required. Name of the backup plan association resource, in the
      format `projects/{project}/locations/{location}/backupPlanAssociations/{
      backupPlanAssociationId}`
    triggerBackupRequest: A TriggerBackupRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  triggerBackupRequest = _messages.MessageField('TriggerBackupRequest', 2)


class BackupdrProjectsLocationsBackupPlansCreateRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansCreateRequest object.

  Fields:
    backupPlan: A BackupPlan resource to be passed as the request body.
    backupPlanId: Required. The name of the `BackupPlan` to create. The name
      must be unique for the specified project and location.The name must
      start with a lowercase letter followed by up to 62 lowercase letters,
      numbers, or hyphens. Pattern, /a-z{,62}/.
    parent: Required. The `BackupPlan` project and location in the format
      `projects/{project}/locations/{location}`. In Cloud BackupDR locations
      map to GCP regions, for example **us-central1**.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  backupPlan = _messages.MessageField('BackupPlan', 1)
  backupPlanId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BackupdrProjectsLocationsBackupPlansDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansDeleteRequest object.

  Fields:
    name: Required. The resource name of the `BackupPlan` to delete. Format:
      `projects/{project}/locations/{location}/backupPlans/{backup_plan}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BackupdrProjectsLocationsBackupPlansGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansGetRequest object.

  Fields:
    name: Required. The resource name of the `BackupPlan` to retrieve. Format:
      `projects/{project}/locations/{location}/backupPlans/{backup_plan}`
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsBackupPlansListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansListRequest object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The maximum number of `BackupPlans` to return in a
      single response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListBackupPlans` call. Provide this to retrieve the subsequent page in
      a multi-page list of results. When paginating, all other parameters
      provided to `ListBackupPlans` must match the call that provided the page
      token.
    parent: Required. The project and location for which to retrieve
      `BackupPlans` information. Format:
      `projects/{project}/locations/{location}`. In Cloud BackupDR, locations
      map to GCP regions, for e.g. **us-central1**. To retrieve backup plans
      for all locations, use "-" for the `{location}` value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BackupdrProjectsLocationsBackupPlansPatchRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansPatchRequest object.

  Fields:
    backupPlan: A BackupPlan resource to be passed as the request body.
    name: Output only. Identifier. The resource name of the `BackupPlan`.
      Format:
      `projects/{project}/locations/{location}/backupPlans/{backup_plan}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. The list of fields to update. Field mask is used to
      specify the fields to be overwritten in the BackupPlan resource by the
      update. The fields specified in the update_mask are relative to the
      resource, not the full request. A field will be overwritten if it is in
      the mask. If the user does not provide a mask then the request will
      fail. Currently, these fields are supported in update: description,
      schedules, retention period, adding and removing Backup Rules.
  """

  backupPlan = _messages.MessageField('BackupPlan', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BackupdrProjectsLocationsBackupPlansRevisionsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansRevisionsGetRequest object.

  Fields:
    name: Required. The resource name of the `BackupPlanRevision` to retrieve.
      Format: `projects/{project}/locations/{location}/backupPlans/{backup_pla
      n}/revisions/{revision}`
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsBackupPlansRevisionsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupPlansRevisionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of `BackupPlans` to return in a
      single response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListBackupPlans` call. Provide this to retrieve the subsequent page in
      a multi-page list of results. When paginating, all other parameters
      provided to `ListBackupPlans` must match the call that provided the page
      token.
    parent: Required. The project and location for which to retrieve
      `BackupPlanRevisions` information. Format:
      `projects/{project}/locations/{location}/backupPlans/{backup_plan}`. In
      Cloud BackupDR, locations map to GCP regions, for e.g. **us-central1**.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BackupdrProjectsLocationsBackupVaultsCreateRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsCreateRequest object.

  Fields:
    backupVault: A BackupVault resource to be passed as the request body.
    backupVaultId: Required. ID of the requesting object If auto-generating ID
      server-side, remove this field and backup_vault_id from the
      method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is 'false'.
  """

  backupVault = _messages.MessageField('BackupVault', 1)
  backupVaultId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class BackupdrProjectsLocationsBackupVaultsDataSourcesAbandonBackupRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesAbandonBackupRequest
  object.

  Fields:
    abandonBackupRequest: A AbandonBackupRequest resource to be passed as the
      request body.
    dataSource: Required. The resource name of the instance, in the format
      'projects/*/locations/*/backupVaults/*/dataSources/'.
  """

  abandonBackupRequest = _messages.MessageField('AbandonBackupRequest', 1)
  dataSource = _messages.StringField(2, required=True)


class BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. Reserved for future use to provide a BASIC
      & FULL view of Backup resource.

  Fields:
    name: Required. Name of the data source resource name, in the format 'proj
      ects/{project_id}/locations/{location}/backupVaults/{backupVault}/dataSo
      urces/{datasource}/backups/{backup}'
    view: Optional. Reserved for future use to provide a BASIC & FULL view of
      Backup resource.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Reserved for future use to provide a BASIC & FULL view of
    Backup resource.

    Values:
      BACKUP_VIEW_UNSPECIFIED: If the value is not set, the default 'FULL'
        view is used.
      BACKUP_VIEW_BASIC: Includes basic data about the Backup, but not the
        full contents.
      BACKUP_VIEW_FULL: Includes all data about the Backup. This is the
        default value (for both ListBackups and GetBackup).
    """
    BACKUP_VIEW_UNSPECIFIED = 0
    BACKUP_VIEW_BASIC = 1
    BACKUP_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsListRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. Reserved for future use to provide a BASIC
      & FULL view of Backup resource.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve backup
      information, in the format 'projects/{project_id}/locations/{location}'.
      In Cloud Backup and DR, locations map to Google Cloud regions, for
      example **us-central1**. To retrieve data sources for all locations, use
      "-" for the '{location}' value.
    view: Optional. Reserved for future use to provide a BASIC & FULL view of
      Backup resource.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Reserved for future use to provide a BASIC & FULL view of
    Backup resource.

    Values:
      BACKUP_VIEW_UNSPECIFIED: If the value is not set, the default 'FULL'
        view is used.
      BACKUP_VIEW_BASIC: Includes basic data about the Backup, but not the
        full contents.
      BACKUP_VIEW_FULL: Includes all data about the Backup. This is the
        default value (for both ListBackups and GetBackup).
    """
    BACKUP_VIEW_UNSPECIFIED = 0
    BACKUP_VIEW_BASIC = 1
    BACKUP_VIEW_FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsPatchRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsPatchRequest
  object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only. Identifier. Name of the backup to create. It must have
      the format`"projects//locations//backupVaults//dataSources/{datasource}/
      backups/{backup}"`. `{backup}` cannot be changed after creation. It must
      be between 3-63 characters long and must be unique within the
      datasource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Backup resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then the request will fail.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsRestoreRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesBackupsRestoreRequest
  object.

  Fields:
    name: Required. The resource name of the Backup instance, in the format
      'projects/*/locations/*/backupVaults/*/dataSources/*/backups/'.
    restoreBackupRequest: A RestoreBackupRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  restoreBackupRequest = _messages.MessageField('RestoreBackupRequest', 2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesFetchAccessTokenRequest(_messages.Message):
  r"""A
  BackupdrProjectsLocationsBackupVaultsDataSourcesFetchAccessTokenRequest
  object.

  Fields:
    fetchAccessTokenRequest: A FetchAccessTokenRequest resource to be passed
      as the request body.
    name: Required. The resource name for the location for which static IPs
      should be returned. Must be in the format
      'projects/*/locations/*/backupVaults/*/dataSources'.
  """

  fetchAccessTokenRequest = _messages.MessageField('FetchAccessTokenRequest', 1)
  name = _messages.StringField(2, required=True)


class BackupdrProjectsLocationsBackupVaultsDataSourcesFinalizeBackupRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesFinalizeBackupRequest
  object.

  Fields:
    dataSource: Required. The resource name of the instance, in the format
      'projects/*/locations/*/backupVaults/*/dataSources/'.
    finalizeBackupRequest: A FinalizeBackupRequest resource to be passed as
      the request body.
  """

  dataSource = _messages.StringField(1, required=True)
  finalizeBackupRequest = _messages.MessageField('FinalizeBackupRequest', 2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesGetRequest object.

  Fields:
    name: Required. Name of the data source resource name, in the format 'proj
      ects/{project_id}/locations/{location}/backupVaults/{resource_name}/data
      Source/{resource_name}'
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsBackupVaultsDataSourcesInitiateBackupRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesInitiateBackupRequest
  object.

  Fields:
    dataSource: Required. The resource name of the instance, in the format
      'projects/*/locations/*/backupVaults/*/dataSources/'.
    initiateBackupRequest: A InitiateBackupRequest resource to be passed as
      the request body.
  """

  dataSource = _messages.StringField(1, required=True)
  initiateBackupRequest = _messages.MessageField('InitiateBackupRequest', 2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve data
      sources information, in the format
      'projects/{project_id}/locations/{location}'. In Cloud Backup and DR,
      locations map to Google Cloud regions, for example **us-central1**. To
      retrieve data sources for all locations, use "-" for the '{location}'
      value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BackupdrProjectsLocationsBackupVaultsDataSourcesPatchRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesPatchRequest object.

  Fields:
    allowMissing: Optional. Enable upsert.
    dataSource: A DataSource resource to be passed as the request body.
    name: Output only. Identifier. Name of the datasource to create. It must
      have the format`"projects/{project}/locations/{location}/backupVaults/{b
      ackupvault}/dataSources/{datasource}"`. `{datasource}` cannot be changed
      after creation. It must be between 3-63 characters long and must be
      unique within the backup vault.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the DataSource resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then the request will fail.
  """

  allowMissing = _messages.BooleanField(1)
  dataSource = _messages.MessageField('DataSource', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class BackupdrProjectsLocationsBackupVaultsDataSourcesRemoveRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDataSourcesRemoveRequest object.

  Fields:
    name: Required. Name of the resource.
    removeDataSourceRequest: A RemoveDataSourceRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  removeDataSourceRequest = _messages.MessageField('RemoveDataSourceRequest', 2)


class BackupdrProjectsLocationsBackupVaultsDataSourcesSetInternalStatusRequest(_messages.Message):
  r"""A
  BackupdrProjectsLocationsBackupVaultsDataSourcesSetInternalStatusRequest
  object.

  Fields:
    dataSource: Required. The resource name of the instance, in the format
      'projects/*/locations/*/backupVaults/*/dataSources/'.
    setInternalStatusRequest: A SetInternalStatusRequest resource to be passed
      as the request body.
  """

  dataSource = _messages.StringField(1, required=True)
  setInternalStatusRequest = _messages.MessageField('SetInternalStatusRequest', 2)


class BackupdrProjectsLocationsBackupVaultsDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsDeleteRequest object.

  Fields:
    allowMissing: Optional. If true and the BackupVault is not found, the
      request will succeed but no action will be taken.
    etag: The current etag of the backup vault. If an etag is provided and
      does not match the current etag of the connection, deletion will be
      blocked.
    force: Optional. If set to true, any data source from this backup vault
      will also be deleted.
    ignoreBackupPlanReferences: Optional. If set to true, backupvault deletion
      will proceed even if there are backup plans referencing the backupvault.
      The default is 'false'.
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is 'false'.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  ignoreBackupPlanReferences = _messages.BooleanField(4)
  name = _messages.StringField(5, required=True)
  requestId = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class BackupdrProjectsLocationsBackupVaultsFetchUsableRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsFetchUsableRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve
      backupvault stores information, in the format
      'projects/{project_id}/locations/{location}'. In Cloud Backup and DR,
      locations map to Google Cloud regions, for example **us-central1**. To
      retrieve backupvault stores for all locations, use "-" for the
      '{location}' value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BackupdrProjectsLocationsBackupVaultsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Reserved for future use to provide a BASIC
      & FULL view of Backup Vault

  Fields:
    name: Required. Name of the backupvault store resource name, in the format
      'projects/{project_id}/locations/{location}/backupVaults/{resource_name}
      '
    view: Optional. Reserved for future use to provide a BASIC & FULL view of
      Backup Vault
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Reserved for future use to provide a BASIC & FULL view of
    Backup Vault

    Values:
      BACKUP_VAULT_VIEW_UNSPECIFIED: If the value is not set, the default
        'FULL' view is used.
      BACKUP_VAULT_VIEW_BASIC: Includes basic data about the Backup Vault, but
        not the full contents.
      BACKUP_VAULT_VIEW_FULL: Includes all data about the Backup Vault. This
        is the default value (for both ListBackupVaults and GetBackupVault).
    """
    BACKUP_VAULT_VIEW_UNSPECIFIED = 0
    BACKUP_VAULT_VIEW_BASIC = 1
    BACKUP_VAULT_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BackupdrProjectsLocationsBackupVaultsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsListRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Reserved for future use to provide a BASIC
      & FULL view of Backup Vault.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve
      backupvault stores information, in the format
      'projects/{project_id}/locations/{location}'. In Cloud Backup and DR,
      locations map to Google Cloud regions, for example **us-central1**. To
      retrieve backupvault stores for all locations, use "-" for the
      '{location}' value.
    view: Optional. Reserved for future use to provide a BASIC & FULL view of
      Backup Vault.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Reserved for future use to provide a BASIC & FULL view of
    Backup Vault.

    Values:
      BACKUP_VAULT_VIEW_UNSPECIFIED: If the value is not set, the default
        'FULL' view is used.
      BACKUP_VAULT_VIEW_BASIC: Includes basic data about the Backup Vault, but
        not the full contents.
      BACKUP_VAULT_VIEW_FULL: Includes all data about the Backup Vault. This
        is the default value (for both ListBackupVaults and GetBackupVault).
    """
    BACKUP_VAULT_VIEW_UNSPECIFIED = 0
    BACKUP_VAULT_VIEW_BASIC = 1
    BACKUP_VAULT_VIEW_FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class BackupdrProjectsLocationsBackupVaultsPatchRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsPatchRequest object.

  Fields:
    backupVault: A BackupVault resource to be passed as the request body.
    force: Optional. If set to true, will not check plan duration against
      backup vault enforcement duration.
    forceUpdateAccessRestriction: Optional. If set to true, we will force
      update access restriction even if some non compliant data sources are
      present. The default is 'false'.
    name: Output only. Identifier. Name of the backup vault to create. It must
      have the format`"projects/{project}/locations/{location}/backupVaults/{b
      ackupvault}"`. `{backupvault}` cannot be changed after creation. It must
      be between 3-63 characters long and must be unique within the project
      and location.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BackupVault resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then the request will fail.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is 'false'.
  """

  backupVault = _messages.MessageField('BackupVault', 1)
  force = _messages.BooleanField(2)
  forceUpdateAccessRestriction = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  updateMask = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class BackupdrProjectsLocationsBackupVaultsTestIamPermissionsRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsBackupVaultsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BackupdrProjectsLocationsDataSourceReferencesFetchForResourceTypeRequest(_messages.Message):
  r"""A
  BackupdrProjectsLocationsDataSourceReferencesFetchForResourceTypeRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the results fetched in
      the response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. Supported
      fields: * data_source * data_source_gcp_resource_info.gcp_resourcename *
      data_source_backup_config_state * data_source_backup_count *
      data_source_backup_config_info.last_backup_state *
      data_source_gcp_resource_info.gcp_resourcename *
      data_source_gcp_resource_info.type *
      data_source_gcp_resource_info.location * data_source_gcp_resource_info.c
      loud_sql_instance_properties.instance_create_time
    orderBy: Optional. A comma-separated list of fields to order by, sorted in
      ascending order. Use "desc" after a field name for descending. Supported
      fields: * name
    pageSize: Optional. The maximum number of DataSourceReferences to return.
      The service may return fewer than this value. If unspecified, at most 50
      DataSourceReferences will be returned. The maximum value is 100; values
      above 100 will be coerced to 100.
    pageToken: Optional. A page token, received from a previous call of
      `FetchDataSourceReferencesForResourceType`. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `FetchDataSourceReferencesForResourceType` must match the call that
      provided the page token.
    parent: Required. The parent resource name. Format:
      projects/{project}/locations/{location}
    resourceType: Required. The type of the GCP resource. Ex:
      sql.googleapis.com/Instance
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  resourceType = _messages.StringField(6)


class BackupdrProjectsLocationsDataSourceReferencesGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsDataSourceReferencesGetRequest object.

  Fields:
    name: Required. The name of the DataSourceReference to retrieve. Format: p
      rojects/{project}/locations/{location}/dataSourceReferences/{data_source
      _reference}
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class BackupdrProjectsLocationsManagementServersCreateRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersCreateRequest object.

  Fields:
    managementServer: A ManagementServer resource to be passed as the request
      body.
    managementServerId: Required. The name of the management server to create.
      The name must be unique for the specified project and location.
    parent: Required. The management server project and location in the format
      'projects/{project_id}/locations/{location}'. In Cloud Backup and DR
      locations map to Google Cloud regions, for example **us-central1**.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  managementServer = _messages.MessageField('ManagementServer', 1)
  managementServerId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BackupdrProjectsLocationsManagementServersDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class BackupdrProjectsLocationsManagementServersGetIamPolicyRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class BackupdrProjectsLocationsManagementServersGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersGetRequest object.

  Fields:
    name: Required. Name of the management server resource name, in the format
      'projects/{project_id}/locations/{location}/managementServers/{resource_
      name}'
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsManagementServersListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve
      management servers information, in the format
      'projects/{project_id}/locations/{location}'. In Cloud BackupDR,
      locations map to Google Cloud regions, for example **us-central1**. To
      retrieve management servers for all locations, use "-" for the
      '{location}' value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BackupdrProjectsLocationsManagementServersSetIamPolicyRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BackupdrProjectsLocationsManagementServersTestIamPermissionsRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsManagementServersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BackupdrProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BackupdrProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BackupdrProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BackupdrProjectsLocationsResourceBackupConfigsListRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsResourceBackupConfigsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will use 100 as default. Maximum
      value is 500 and values above 500 will be coerced to 500.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve resource
      backup configs. Format: 'projects/{project_id}/locations/{location}'. In
      Cloud Backup and DR, locations map to Google Cloud regions, for example
      **us-central1**.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BackupdrProjectsLocationsServiceConfigInitializeRequest(_messages.Message):
  r"""A BackupdrProjectsLocationsServiceConfigInitializeRequest object.

  Fields:
    initializeServiceRequest: A InitializeServiceRequest resource to be passed
      as the request body.
    name: Required. The resource name of the serviceConfig used to initialize
      the service. Format:
      `projects/{project_id}/locations/{location}/serviceConfig`.
  """

  initializeServiceRequest = _messages.MessageField('InitializeServiceRequest', 1)
  name = _messages.StringField(2, required=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudSqlInstanceBackupPlanAssociationProperties(_messages.Message):
  r"""Cloud SQL instance's BPA properties.

  Fields:
    instanceCreateTime: Output only. The time when the instance was created.
  """

  instanceCreateTime = _messages.StringField(1)


class CloudSqlInstanceBackupProperties(_messages.Message):
  r"""CloudSqlInstanceBackupProperties represents Cloud SQL Instance Backup
  properties. .

  Fields:
    databaseInstalledVersion: The installed database version of the Cloud SQL
      instance when the backup was taken.
    description: An optional text description for the backup. DEPRECATED: Use
      the description field in the Backup resource instead.
    finalBackup: Whether the backup is a final backup.
    sourceInstance: The source instance of the backup.
  """

  databaseInstalledVersion = _messages.StringField(1)
  description = _messages.StringField(2)
  finalBackup = _messages.BooleanField(3)
  sourceInstance = _messages.StringField(4)


class CloudSqlInstanceDataSourceProperties(_messages.Message):
  r"""CloudSqlInstanceDataSourceProperties represents the properties of a
  Cloud SQL resource that are stored in the DataSource. .

  Fields:
    databaseInstalledVersion: The installed database version of the Cloud SQL
      instance.
    instanceCreateTime: The instance creation timestamp.
    name: Name of the Cloud SQL instance backed up by the datasource.
    pitrWindows: Point in time recovery windows. This is not intended to be
      exposed to the customers yet.
  """

  databaseInstalledVersion = _messages.StringField(1)
  instanceCreateTime = _messages.StringField(2)
  name = _messages.StringField(3)
  pitrWindows = _messages.MessageField('PitrWindow', 4, repeated=True)


class CloudSqlInstanceDataSourceReferenceProperties(_messages.Message):
  r"""CloudSqlInstanceDataSourceReferenceProperties represents the properties
  of a Cloud SQL resource that are stored in the DataSourceReference. .

  Fields:
    databaseInstalledVersion: The installed database version of the Cloud SQL
      instance.
    instanceCreateTime: The instance creation timestamp.
    name: Name of the Cloud SQL instance backed up by the datasource.
    pitrWindows: Point in time recovery windows. This is not intended to be
      exposed to the customers yet.
  """

  databaseInstalledVersion = _messages.StringField(1)
  instanceCreateTime = _messages.StringField(2)
  name = _messages.StringField(3)
  pitrWindows = _messages.MessageField('PitrWindow', 4, repeated=True)


class ComputeInstanceBackupProperties(_messages.Message):
  r"""ComputeInstanceBackupProperties represents Compute Engine instance
  backup properties.

  Enums:
    KeyRevocationActionTypeValueValuesEnum: KeyRevocationActionType of the
      instance. Supported options are "STOP" and "NONE". The default value is
      "NONE" if it is not specified.

  Messages:
    LabelsValue: Labels to apply to instances that are created from these
      properties.

  Fields:
    canIpForward: Enables instances created based on these properties to send
      packets with source IP addresses other than their own and receive
      packets with destination IP addresses other than their own. If these
      instances will be used as an IP gateway or it will be set as the next-
      hop in a Route resource, specify `true`. If unsure, leave this set to
      `false`. See the https://cloud.google.com/vpc/docs/using-
      routes#canipforward documentation for more information.
    description: An optional text description for the instances that are
      created from these properties.
    disk: An array of disks that are associated with the instances that are
      created from these properties.
    guestAccelerator: A list of guest accelerator cards' type and count to use
      for instances created from these properties.
    keyRevocationActionType: KeyRevocationActionType of the instance.
      Supported options are "STOP" and "NONE". The default value is "NONE" if
      it is not specified.
    labels: Labels to apply to instances that are created from these
      properties.
    machineType: The machine type to use for instances that are created from
      these properties.
    metadata: The metadata key/value pairs to assign to instances that are
      created from these properties. These pairs can consist of custom
      metadata or predefined keys. See
      https://cloud.google.com/compute/docs/metadata/overview for more
      information.
    minCpuPlatform: Minimum cpu/platform to be used by instances. The instance
      may be scheduled on the specified or newer cpu/platform. Applicable
      values are the friendly names of CPU platforms, such as `minCpuPlatform:
      Intel Haswell` or `minCpuPlatform: Intel Sandy Bridge`. For more
      information, read
      https://cloud.google.com/compute/docs/instances/specify-min-cpu-
      platform.
    networkInterface: An array of network access configurations for this
      interface.
    scheduling: Specifies the scheduling options for the instances that are
      created from these properties.
    serviceAccount: A list of service accounts with specified scopes. Access
      tokens for these service accounts are available to the instances that
      are created from these properties. Use metadata queries to obtain the
      access tokens for these instances.
    sourceInstance: The source instance used to create this backup. This can
      be a partial or full URL to the resource. For example, the following are
      valid values: -https://www.googleapis.com/compute/v1/projects/project/zo
      nes/zone/instances/instance
      -projects/project/zones/zone/instances/instance
    tags: A list of tags to apply to the instances that are created from these
      properties. The tags identify valid sources or targets for network
      firewalls. The setTags method can modify this list of tags. Each tag
      within the list must comply with RFC1035
      (https://www.ietf.org/rfc/rfc1035.txt).
  """

  class KeyRevocationActionTypeValueValuesEnum(_messages.Enum):
    r"""KeyRevocationActionType of the instance. Supported options are "STOP"
    and "NONE". The default value is "NONE" if it is not specified.

    Values:
      KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED: Default value. This value is
        unused.
      NONE: Indicates user chose no operation.
      STOP: Indicates user chose to opt for VM shutdown on key revocation.
    """
    KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED = 0
    NONE = 1
    STOP = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to instances that are created from these properties.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canIpForward = _messages.BooleanField(1)
  description = _messages.StringField(2)
  disk = _messages.MessageField('AttachedDisk', 3, repeated=True)
  guestAccelerator = _messages.MessageField('AcceleratorConfig', 4, repeated=True)
  keyRevocationActionType = _messages.EnumField('KeyRevocationActionTypeValueValuesEnum', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  machineType = _messages.StringField(7)
  metadata = _messages.MessageField('Metadata', 8)
  minCpuPlatform = _messages.StringField(9)
  networkInterface = _messages.MessageField('NetworkInterface', 10, repeated=True)
  scheduling = _messages.MessageField('Scheduling', 11)
  serviceAccount = _messages.MessageField('ServiceAccount', 12, repeated=True)
  sourceInstance = _messages.StringField(13)
  tags = _messages.MessageField('Tags', 14)


class ComputeInstanceDataSourceProperties(_messages.Message):
  r"""ComputeInstanceDataSourceProperties represents the properties of a
  ComputeEngine resource that are stored in the DataSource.

  Fields:
    description: The description of the Compute Engine instance.
    machineType: The machine type of the instance.
    name: Name of the compute instance backed up by the datasource.
    totalDiskCount: The total number of disks attached to the Instance.
    totalDiskSizeGb: The sum of all the disk sizes.
  """

  description = _messages.StringField(1)
  machineType = _messages.StringField(2)
  name = _messages.StringField(3)
  totalDiskCount = _messages.IntegerField(4)
  totalDiskSizeGb = _messages.IntegerField(5)


class ComputeInstanceRestoreProperties(_messages.Message):
  r"""ComputeInstanceRestoreProperties represents Compute Engine instance
  properties to be overridden during restore.

  Enums:
    KeyRevocationActionTypeValueValuesEnum: Optional. KeyRevocationActionType
      of the instance.
    PrivateIpv6GoogleAccessValueValuesEnum: Optional. The private IPv6 google
      access type for the VM. If not specified, use INHERIT_FROM_SUBNETWORK as
      default.

  Messages:
    LabelsValue: Optional. Labels to apply to this instance.

  Fields:
    advancedMachineFeatures: Optional. Controls for advanced machine-related
      behavior features.
    canIpForward: Optional. Allows this instance to send and receive packets
      with non-matching destination or source IPs.
    confidentialInstanceConfig: Optional. Controls Confidential compute
      options on the instance
    deletionProtection: Optional. Whether the resource should be protected
      against deletion.
    description: Optional. An optional description of this resource. Provide
      this property when you create the resource.
    disks: Optional. Array of disks associated with this instance. Persistent
      disks must be created before you can assign them.
    displayDevice: Optional. Enables display device for the instance.
    guestAccelerators: Optional. A list of the type and count of accelerator
      cards attached to the instance.
    hostname: Optional. Specifies the hostname of the instance. The specified
      hostname must be RFC1035 compliant. If hostname is not specified, the
      default hostname is [INSTANCE_NAME].c.[PROJECT_ID].internal when using
      the global DNS, and [INSTANCE_NAME].[ZONE].c.[PROJECT_ID].internal when
      using zonal DNS.
    instanceEncryptionKey: Optional. Encrypts suspended data for an instance
      with a customer-managed encryption key.
    keyRevocationActionType: Optional. KeyRevocationActionType of the
      instance.
    labels: Optional. Labels to apply to this instance.
    machineType: Optional. Full or partial URL of the machine type resource to
      use for this instance.
    metadata: Optional. This includes custom metadata and predefined keys.
    minCpuPlatform: Optional. Minimum CPU platform to use for this instance.
    name: Required. Name of the compute instance.
    networkInterfaces: Optional. An array of network configurations for this
      instance. These specify how interfaces are configured to interact with
      other network services, such as connecting to the internet. Multiple
      interfaces are supported per instance.
    networkPerformanceConfig: Optional. Configure network performance such as
      egress bandwidth tier.
    params: Input only. Additional params passed with the request, but not
      persisted as part of resource payload.
    privateIpv6GoogleAccess: Optional. The private IPv6 google access type for
      the VM. If not specified, use INHERIT_FROM_SUBNETWORK as default.
    reservationAffinity: Optional. Specifies the reservations that this
      instance can consume from.
    resourcePolicies: Optional. Resource policies applied to this instance.
    scheduling: Optional. Sets the scheduling options for this instance.
    serviceAccounts: Optional. A list of service accounts, with their
      specified scopes, authorized for this instance. Only one service account
      per VM instance is supported.
    tags: Optional. Tags to apply to this instance. Tags are used to identify
      valid sources or targets for network firewalls and are specified by the
      client during instance creation.
  """

  class KeyRevocationActionTypeValueValuesEnum(_messages.Enum):
    r"""Optional. KeyRevocationActionType of the instance.

    Values:
      KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED: Default value. This value is
        unused.
      NONE: Indicates user chose no operation.
      STOP: Indicates user chose to opt for VM shutdown on key revocation.
    """
    KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED = 0
    NONE = 1
    STOP = 2

  class PrivateIpv6GoogleAccessValueValuesEnum(_messages.Enum):
    r"""Optional. The private IPv6 google access type for the VM. If not
    specified, use INHERIT_FROM_SUBNETWORK as default.

    Values:
      INSTANCE_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED: Default value. This
        value is unused.
      INHERIT_FROM_SUBNETWORK: Each network interface inherits
        PrivateIpv6GoogleAccess from its subnetwork.
      ENABLE_OUTBOUND_VM_ACCESS_TO_GOOGLE: Outbound private IPv6 access from
        VMs in this subnet to Google services. If specified, the subnetwork
        who is attached to the instance's default network interface will be
        assigned an internal IPv6 prefix if it doesn't have before.
      ENABLE_BIDIRECTIONAL_ACCESS_TO_GOOGLE: Bidirectional private IPv6 access
        to/from Google services. If specified, the subnetwork who is attached
        to the instance's default network interface will be assigned an
        internal IPv6 prefix if it doesn't have before.
    """
    INSTANCE_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED = 0
    INHERIT_FROM_SUBNETWORK = 1
    ENABLE_OUTBOUND_VM_ACCESS_TO_GOOGLE = 2
    ENABLE_BIDIRECTIONAL_ACCESS_TO_GOOGLE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels to apply to this instance.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  advancedMachineFeatures = _messages.MessageField('AdvancedMachineFeatures', 1)
  canIpForward = _messages.BooleanField(2)
  confidentialInstanceConfig = _messages.MessageField('ConfidentialInstanceConfig', 3)
  deletionProtection = _messages.BooleanField(4)
  description = _messages.StringField(5)
  disks = _messages.MessageField('AttachedDisk', 6, repeated=True)
  displayDevice = _messages.MessageField('DisplayDevice', 7)
  guestAccelerators = _messages.MessageField('AcceleratorConfig', 8, repeated=True)
  hostname = _messages.StringField(9)
  instanceEncryptionKey = _messages.MessageField('CustomerEncryptionKey', 10)
  keyRevocationActionType = _messages.EnumField('KeyRevocationActionTypeValueValuesEnum', 11)
  labels = _messages.MessageField('LabelsValue', 12)
  machineType = _messages.StringField(13)
  metadata = _messages.MessageField('Metadata', 14)
  minCpuPlatform = _messages.StringField(15)
  name = _messages.StringField(16)
  networkInterfaces = _messages.MessageField('NetworkInterface', 17, repeated=True)
  networkPerformanceConfig = _messages.MessageField('NetworkPerformanceConfig', 18)
  params = _messages.MessageField('InstanceParams', 19)
  privateIpv6GoogleAccess = _messages.EnumField('PrivateIpv6GoogleAccessValueValuesEnum', 20)
  reservationAffinity = _messages.MessageField('AllocationAffinity', 21)
  resourcePolicies = _messages.StringField(22, repeated=True)
  scheduling = _messages.MessageField('Scheduling', 23)
  serviceAccounts = _messages.MessageField('ServiceAccount', 24, repeated=True)
  tags = _messages.MessageField('Tags', 25)


class ComputeInstanceTargetEnvironment(_messages.Message):
  r"""ComputeInstanceTargetEnvironment represents Compute Engine target
  environment to be used during restore.

  Fields:
    project: Required. Target project for the Compute Engine instance.
    zone: Required. The zone of the Compute Engine instance.
  """

  project = _messages.StringField(1)
  zone = _messages.StringField(2)


class ConfidentialInstanceConfig(_messages.Message):
  r"""A set of Confidential Instance options.

  Fields:
    enableConfidentialCompute: Optional. Defines whether the instance should
      have confidential compute enabled.
  """

  enableConfidentialCompute = _messages.BooleanField(1)


class CustomerEncryptionKey(_messages.Message):
  r"""A customer-supplied encryption key.

  Fields:
    kmsKeyName: Optional. The name of the encryption key that is stored in
      Google Cloud KMS.
    kmsKeyServiceAccount: Optional. The service account being used for the
      encryption request for the given KMS key. If absent, the Compute Engine
      default service account is used.
    rawKey: Optional. Specifies a 256-bit customer-supplied encryption key.
    rsaEncryptedKey: Optional. RSA-wrapped 2048-bit customer-supplied
      encryption key to either encrypt or decrypt this resource.
  """

  kmsKeyName = _messages.StringField(1)
  kmsKeyServiceAccount = _messages.StringField(2)
  rawKey = _messages.StringField(3)
  rsaEncryptedKey = _messages.StringField(4)


class DataSource(_messages.Message):
  r"""Message describing a DataSource object. Datasource object used to
  represent Datasource details for both admin and basic view.

  Enums:
    ConfigStateValueValuesEnum: Output only. The backup configuration state.
    StateValueValuesEnum: Output only. The DataSource resource instance state.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. No labels currently defined:

  Fields:
    backupBlockedByVaultAccessRestriction: Output only. This field is set to
      true if the backup is blocked by vault access restriction.
    backupConfigInfo: Output only. Details of how the resource is configured
      for backup.
    backupCount: Number of backups in the data source.
    configState: Output only. The backup configuration state.
    createTime: Output only. The time when the instance was created.
    dataSourceBackupApplianceApplication: The backed up resource is a backup
      appliance application.
    dataSourceGcpResource: The backed up resource is a Google Cloud resource.
      The word 'DataSource' was included in the names to indicate that this is
      the representation of the Google Cloud resource used within the
      DataSource object.
    etag: Server specified ETag for the ManagementServer resource to prevent
      simultaneous updates from overwiting each other.
    labels: Optional. Resource labels to represent user provided metadata. No
      labels currently defined:
    name: Output only. Identifier. Name of the datasource to create. It must
      have the format`"projects/{project}/locations/{location}/backupVaults/{b
      ackupvault}/dataSources/{datasource}"`. `{datasource}` cannot be changed
      after creation. It must be between 3-63 characters long and must be
      unique within the backup vault.
    state: Output only. The DataSource resource instance state.
    totalStoredBytes: The number of bytes (metadata and data) stored in this
      datasource.
    updateTime: Output only. The time when the instance was updated.
  """

  class ConfigStateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup configuration state.

    Values:
      BACKUP_CONFIG_STATE_UNSPECIFIED: The possible states of backup
        configuration. Status not set.
      ACTIVE: The data source is actively protected (i.e. there is a
        BackupPlanAssociation or Appliance SLA pointing to it)
      PASSIVE: The data source is no longer protected (but may have backups
        under it)
    """
    BACKUP_CONFIG_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PASSIVE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The DataSource resource instance state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The data source is being created.
      ACTIVE: The data source has been created and is fully usable.
      DELETING: The data source is being deleted.
      ERROR: The data source is experiencing an issue and might be unusable.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ERROR = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. No
    labels currently defined:

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupBlockedByVaultAccessRestriction = _messages.BooleanField(1)
  backupConfigInfo = _messages.MessageField('BackupConfigInfo', 2)
  backupCount = _messages.IntegerField(3)
  configState = _messages.EnumField('ConfigStateValueValuesEnum', 4)
  createTime = _messages.StringField(5)
  dataSourceBackupApplianceApplication = _messages.MessageField('DataSourceBackupApplianceApplication', 6)
  dataSourceGcpResource = _messages.MessageField('DataSourceGcpResource', 7)
  etag = _messages.StringField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  totalStoredBytes = _messages.IntegerField(12)
  updateTime = _messages.StringField(13)


class DataSourceBackupApplianceApplication(_messages.Message):
  r"""BackupApplianceApplication describes a Source Resource when it is an
  application backed up by a BackupAppliance.

  Fields:
    applianceId: Appliance Id of the Backup Appliance.
    applicationId: The appid field of the application within the Backup
      Appliance.
    applicationName: The name of the Application as known to the Backup
      Appliance.
    backupAppliance: Appliance name.
    hostId: Hostid of the application host.
    hostname: Hostname of the host where the application is running.
    type: The type of the application. e.g. VMBackup
  """

  applianceId = _messages.IntegerField(1)
  applicationId = _messages.IntegerField(2)
  applicationName = _messages.StringField(3)
  backupAppliance = _messages.StringField(4)
  hostId = _messages.IntegerField(5)
  hostname = _messages.StringField(6)
  type = _messages.StringField(7)


class DataSourceBackupConfigInfo(_messages.Message):
  r"""Information of backup configuration on the DataSource.

  Enums:
    LastBackupStateValueValuesEnum: Output only. The status of the last backup
      in this DataSource

  Fields:
    lastBackupState: Output only. The status of the last backup in this
      DataSource
    lastSuccessfulBackupConsistencyTime: Output only. Timestamp of the last
      successful backup to this DataSource.
  """

  class LastBackupStateValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the last backup in this DataSource

    Values:
      LAST_BACKUP_STATE_UNSPECIFIED: Status not set.
      FIRST_BACKUP_PENDING: The first backup has not yet completed
      SUCCEEDED: The most recent backup was successful
      FAILED: The most recent backup failed
      PERMISSION_DENIED: The most recent backup could not be run/failed
        because of the lack of permissions
    """
    LAST_BACKUP_STATE_UNSPECIFIED = 0
    FIRST_BACKUP_PENDING = 1
    SUCCEEDED = 2
    FAILED = 3
    PERMISSION_DENIED = 4

  lastBackupState = _messages.EnumField('LastBackupStateValueValuesEnum', 1)
  lastSuccessfulBackupConsistencyTime = _messages.StringField(2)


class DataSourceGcpResource(_messages.Message):
  r"""DataSourceGcpResource is used for protected resources that are Google
  Cloud Resources. This name is easeier to understand than
  GcpResourceDataSource or GcpDataSourceResource

  Fields:
    cloudSqlInstanceDatasourceProperties: CloudSqlInstanceDataSourceProperties
      has a subset of Cloud SQL Instance properties that are useful at the
      Datasource level.
    computeInstanceDatasourceProperties: ComputeInstanceDataSourceProperties
      has a subset of Compute Instance properties that are useful at the
      Datasource level.
    diskDatasourceProperties: DiskDataSourceProperties has a subset of Disk
      properties that are useful at the Datasource level.
    gcpResourcename: Output only. Full resource pathname URL of the source
      Google Cloud resource.
    location: Location of the resource: //"global"/"unspecified".
    type: The type of the Google Cloud resource. Use the Unified Resource
      Type, eg. compute.googleapis.com/Instance.
  """

  cloudSqlInstanceDatasourceProperties = _messages.MessageField('CloudSqlInstanceDataSourceProperties', 1)
  computeInstanceDatasourceProperties = _messages.MessageField('ComputeInstanceDataSourceProperties', 2)
  diskDatasourceProperties = _messages.MessageField('DiskDataSourceProperties', 3)
  gcpResourcename = _messages.StringField(4)
  location = _messages.StringField(5)
  type = _messages.StringField(6)


class DataSourceGcpResourceInfo(_messages.Message):
  r"""The GCP resource that the DataSource is associated with.

  Fields:
    cloudSqlInstanceProperties: Output only. The properties of the Cloud SQL
      instance.
    gcpResourcename: Output only. The resource name of the GCP resource. Ex:
      projects/{project}/zones/{zone}/instances/{instance}
    location: Output only. The location of the GCP resource. Ex:
      //"global"/"unspecified"
    type: Output only. The type of the GCP resource. Ex:
      compute.googleapis.com/Instance
  """

  cloudSqlInstanceProperties = _messages.MessageField('CloudSqlInstanceDataSourceReferenceProperties', 1)
  gcpResourcename = _messages.StringField(2)
  location = _messages.StringField(3)
  type = _messages.StringField(4)


class DataSourceReference(_messages.Message):
  r"""DataSourceReference is a reference to a DataSource resource.

  Enums:
    DataSourceBackupConfigStateValueValuesEnum: Output only. The backup
      configuration state of the DataSource.

  Fields:
    createTime: Output only. The time when the DataSourceReference was
      created.
    dataSource: Required. The resource name of the DataSource. Format: project
      s/{project}/locations/{location}/backupVaults/{backupVault}/dataSources/
      {dataSource}
    dataSourceBackupConfigInfo: Output only. Information of backup
      configuration on the DataSource.
    dataSourceBackupConfigState: Output only. The backup configuration state
      of the DataSource.
    dataSourceBackupCount: Output only. Number of backups in the DataSource.
    dataSourceGcpResourceInfo: Output only. The GCP resource that the
      DataSource is associated with.
    name: Identifier. The resource name of the DataSourceReference. Format: pr
      ojects/{project}/locations/{location}/dataSourceReferences/{data_source_
      reference}
  """

  class DataSourceBackupConfigStateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup configuration state of the DataSource.

    Values:
      BACKUP_CONFIG_STATE_UNSPECIFIED: The possible states of backup
        configuration. Status not set.
      ACTIVE: The data source is actively protected (i.e. there is a
        BackupPlanAssociation or Appliance SLA pointing to it)
      PASSIVE: The data source is no longer protected (but may have backups
        under it)
    """
    BACKUP_CONFIG_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PASSIVE = 2

  createTime = _messages.StringField(1)
  dataSource = _messages.StringField(2)
  dataSourceBackupConfigInfo = _messages.MessageField('DataSourceBackupConfigInfo', 3)
  dataSourceBackupConfigState = _messages.EnumField('DataSourceBackupConfigStateValueValuesEnum', 4)
  dataSourceBackupCount = _messages.IntegerField(5)
  dataSourceGcpResourceInfo = _messages.MessageField('DataSourceGcpResourceInfo', 6)
  name = _messages.StringField(7)


class DiskBackupProperties(_messages.Message):
  r"""DiskBackupProperties represents the properties of a Disk backup.

  Enums:
    ArchitectureValueValuesEnum: The architecture of the source disk. Valid
      values are ARM64 or X86_64.

  Fields:
    architecture: The architecture of the source disk. Valid values are ARM64
      or X86_64.
    description: A description of the source disk.
    guestOsFeature: A list of guest OS features that are applicable to this
      backup.
    licenses: A list of publicly available licenses that are applicable to
      this backup. This is applicable if the original image had licenses
      attached, e.g. Windows image.
    region: Region and zone are mutually exclusive fields. The URL of the
      region of the source disk.
    replicaZones: The URL of the Zones where the source disk should be
      replicated.
    sizeGb: Size(in GB) of the source disk.
    sourceDisk: The source disk used to create this backup.
    type: The URL of the type of the disk.
    zone: The URL of the Zone where the source disk.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The architecture of the source disk. Valid values are ARM64 or X86_64.

    Values:
      ARCHITECTURE_UNSPECIFIED: Default value. This value is unused.
      X86_64: Disks with architecture X86_64
      ARM64: Disks with architecture ARM64
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86_64 = 1
    ARM64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  description = _messages.StringField(2)
  guestOsFeature = _messages.MessageField('GuestOsFeature', 3, repeated=True)
  licenses = _messages.StringField(4, repeated=True)
  region = _messages.StringField(5)
  replicaZones = _messages.StringField(6, repeated=True)
  sizeGb = _messages.IntegerField(7)
  sourceDisk = _messages.StringField(8)
  type = _messages.StringField(9)
  zone = _messages.StringField(10)


class DiskDataSourceProperties(_messages.Message):
  r"""DiskDataSourceProperties represents the properties of a Disk resource
  that are stored in the DataSource. .

  Fields:
    description: The description of the disk.
    name: Name of the disk backed up by the datasource.
    sizeGb: The size of the disk in GB.
    type: The type of the disk.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  sizeGb = _messages.IntegerField(3)
  type = _messages.StringField(4)


class DiskRestoreProperties(_messages.Message):
  r"""DiskRestoreProperties represents the properties of a Disk restore.

  Enums:
    AccessModeValueValuesEnum: Optional. The access mode of the disk.
    ArchitectureValueValuesEnum: Optional. The architecture of the source
      disk. Valid values are ARM64 or X86_64.

  Messages:
    LabelsValue: Optional. Labels to apply to this disk. These can be modified
      later using setLabels method. Label values can be empty.
    ResourceManagerTagsValue: Optional. Resource manager tags to be bound to
      the disk.

  Fields:
    accessMode: Optional. The access mode of the disk.
    architecture: Optional. The architecture of the source disk. Valid values
      are ARM64 or X86_64.
    description: Optional. An optional description of this resource. Provide
      this property when you create the resource.
    diskEncryptionKey: Optional. Encrypts the disk using a customer-supplied
      encryption key or a customer-managed encryption key.
    enableConfidentialCompute: Optional. Indicates whether this disk is using
      confidential compute mode. Encryption with a Cloud KMS key is required
      to enable this option.
    guestOsFeature: Optional. A list of features to enable in the guest
      operating system. This is applicable only for bootable images.
    labels: Optional. Labels to apply to this disk. These can be modified
      later using setLabels method. Label values can be empty.
    licenses: Optional. A list of publicly available licenses that are
      applicable to this backup. This is applicable if the original image had
      licenses attached, e.g. Windows image
    name: Required. Name of the disk..
    physicalBlockSizeBytes: Optional. Physical block size of the persistent
      disk, in bytes. If not present in a request, a default value is used.
      Currently, the supported size is 4096.
    provisionedIops: Optional. Indicates how many IOPS to provision for the
      disk. This sets the number of I/O operations per second that the disk
      can handle.
    provisionedThroughput: Optional. Indicates how much throughput to
      provision for the disk. This sets the number of throughput MB per second
      that the disk can handle.
    resourceManagerTags: Optional. Resource manager tags to be bound to the
      disk.
    resourcePolicy: Optional. Resource policies applied to this disk.
    sizeGb: Required. The size of the disk in GB.
    storagePool: Optional. The storage pool in which the new disk is created.
      You can provide this as a partial or full URL to the resource.
    type: Required. URL of the disk type resource describing which disk type
      to use to create the disk.
  """

  class AccessModeValueValuesEnum(_messages.Enum):
    r"""Optional. The access mode of the disk.

    Values:
      READ_WRITE_SINGLE: The default AccessMode, means the disk can be
        attached to single instance in RW mode.
      READ_WRITE_MANY: The AccessMode means the disk can be attached to
        multiple instances in RW mode.
      READ_ONLY_MANY: The AccessMode means the disk can be attached to
        multiple instances in RO mode.
    """
    READ_WRITE_SINGLE = 0
    READ_WRITE_MANY = 1
    READ_ONLY_MANY = 2

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Optional. The architecture of the source disk. Valid values are ARM64
    or X86_64.

    Values:
      ARCHITECTURE_UNSPECIFIED: Default value. This value is unused.
      X86_64: Disks with architecture X86_64
      ARM64: Disks with architecture ARM64
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86_64 = 1
    ARM64 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels to apply to this disk. These can be modified later
    using setLabels method. Label values can be empty.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceManagerTagsValue(_messages.Message):
    r"""Optional. Resource manager tags to be bound to the disk.

    Messages:
      AdditionalProperty: An additional property for a
        ResourceManagerTagsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceManagerTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceManagerTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessMode = _messages.EnumField('AccessModeValueValuesEnum', 1)
  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 2)
  description = _messages.StringField(3)
  diskEncryptionKey = _messages.MessageField('CustomerEncryptionKey', 4)
  enableConfidentialCompute = _messages.BooleanField(5)
  guestOsFeature = _messages.MessageField('GuestOsFeature', 6, repeated=True)
  labels = _messages.MessageField('LabelsValue', 7)
  licenses = _messages.StringField(8, repeated=True)
  name = _messages.StringField(9)
  physicalBlockSizeBytes = _messages.IntegerField(10)
  provisionedIops = _messages.IntegerField(11)
  provisionedThroughput = _messages.IntegerField(12)
  resourceManagerTags = _messages.MessageField('ResourceManagerTagsValue', 13)
  resourcePolicy = _messages.StringField(14, repeated=True)
  sizeGb = _messages.IntegerField(15)
  storagePool = _messages.StringField(16)
  type = _messages.StringField(17)


class DiskTargetEnvironment(_messages.Message):
  r"""DiskTargetEnvironment represents the target environment for the disk.

  Fields:
    project: Required. Target project for the disk.
    zone: Required. Target zone for the disk.
  """

  project = _messages.StringField(1)
  zone = _messages.StringField(2)


class DisplayDevice(_messages.Message):
  r"""A set of Display Device options

  Fields:
    enableDisplay: Optional. Enables display for the Compute Engine VM
  """

  enableDisplay = _messages.BooleanField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """


class Entry(_messages.Message):
  r"""A key/value pair to be used for storing metadata.

  Fields:
    key: Optional. Key for the metadata entry.
    value: Optional. Value for the metadata entry. These are free-form
      strings, and only have meaning as interpreted by the image running in
      the instance. The only restriction placed on values is that their size
      must be less than or equal to 262144 bytes (256 KiB).
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FetchAccessTokenRequest(_messages.Message):
  r"""Request message for FetchAccessToken.

  Fields:
    generationId: Required. The generation of the backup to update.
  """

  generationId = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class FetchAccessTokenResponse(_messages.Message):
  r"""Response message for FetchAccessToken.

  Fields:
    expireTime: The token is valid until this time.
    readLocation: The location in bucket that can be used for reading.
    token: The downscoped token that was created.
    writeLocation: The location in bucket that can be used for writing.
  """

  expireTime = _messages.StringField(1)
  readLocation = _messages.StringField(2)
  token = _messages.StringField(3)
  writeLocation = _messages.StringField(4)


class FetchBackupPlanAssociationsForResourceTypeResponse(_messages.Message):
  r"""Response for the FetchBackupPlanAssociationsForResourceType method.

  Fields:
    backupPlanAssociations: Output only. The BackupPlanAssociations from the
      specified parent.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
  """

  backupPlanAssociations = _messages.MessageField('BackupPlanAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class FetchDataSourceReferencesForResourceTypeResponse(_messages.Message):
  r"""Response for the FetchDataSourceReferencesForResourceType method.

  Fields:
    dataSourceReferences: Output only. The DataSourceReferences from the
      specified parent.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
  """

  dataSourceReferences = _messages.MessageField('DataSourceReference', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class FetchUsableBackupVaultsResponse(_messages.Message):
  r"""Response message for fetching usable BackupVaults.

  Fields:
    backupVaults: The list of BackupVault instances in the project for the
      specified location. If the '{location}' value in the request is "-", the
      response contains a list of instances from all locations. In case any
      location is unreachable, the response will only return backup vaults in
      reachable locations and the 'unreachable' field will be populated with a
      list of unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backupVaults = _messages.MessageField('BackupVault', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class FinalizeBackupRequest(_messages.Message):
  r"""Message for finalizing a Backup.

  Fields:
    backupId: Required. Resource ID of the Backup resource to be finalized.
      This must be the same backup_id that was used in the
      InitiateBackupRequest.
    consistencyTime: The point in time when this backup was captured from the
      source. This will be assigned to the consistency_time field of the newly
      created Backup.
    description: This will be assigned to the description field of the newly
      created Backup.
    recoveryRangeEndTime: The latest timestamp of data available in this
      Backup. This will be set on the newly created Backup.
    recoveryRangeStartTime: The earliest timestamp of data available in this
      Backup. This will set on the newly created Backup.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    retentionDuration: The ExpireTime on the backup will be set to
      FinalizeTime plus this duration. If the resulting ExpireTime is less
      than EnforcedRetentionEndTime, then ExpireTime is set to
      EnforcedRetentionEndTime.
  """

  backupId = _messages.StringField(1)
  consistencyTime = _messages.StringField(2)
  description = _messages.StringField(3)
  recoveryRangeEndTime = _messages.StringField(4)
  recoveryRangeStartTime = _messages.StringField(5)
  requestId = _messages.StringField(6)
  retentionDuration = _messages.StringField(7)


class GCPBackupPlanInfo(_messages.Message):
  r"""GCPBackupPlanInfo captures the plan configuration details of Google
  Cloud resources at the time of backup.

  Fields:
    backupPlan: Resource name of backup plan by which workload is protected at
      the time of the backup. Format:
      projects/{project}/locations/{location}/backupPlans/{backupPlanId}
    backupPlanRuleId: The rule id of the backup plan which triggered this
      backup in case of scheduled backup or used for
  """

  backupPlan = _messages.StringField(1)
  backupPlanRuleId = _messages.StringField(2)


class GcpBackupConfig(_messages.Message):
  r"""GcpBackupConfig captures the Backup configuration details for Google
  Cloud resources. All Google Cloud resources regardless of type are protected
  with backup plan associations.

  Fields:
    backupPlan: The name of the backup plan.
    backupPlanAssociation: The name of the backup plan association.
    backupPlanDescription: The description of the backup plan.
    backupPlanRules: The names of the backup plan rules which point to this
      backupvault
  """

  backupPlan = _messages.StringField(1)
  backupPlanAssociation = _messages.StringField(2)
  backupPlanDescription = _messages.StringField(3)
  backupPlanRules = _messages.StringField(4, repeated=True)


class GcpResource(_messages.Message):
  r"""Minimum details to identify a Google Cloud resource

  Fields:
    gcpResourcename: Name of the Google Cloud resource.
    location: Location of the resource: //"global"/"unspecified".
    type: Type of the resource. Use the Unified Resource Type, eg.
      compute.googleapis.com/Instance.
  """

  gcpResourcename = _messages.StringField(1)
  location = _messages.StringField(2)
  type = _messages.StringField(3)


class GuestOsFeature(_messages.Message):
  r"""Feature type of the Guest OS.

  Enums:
    TypeValueValuesEnum: The ID of a supported feature.

  Fields:
    type: The ID of a supported feature.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The ID of a supported feature.

    Values:
      FEATURE_TYPE_UNSPECIFIED: Default value, which is unused.
      VIRTIO_SCSI_MULTIQUEUE: VIRTIO_SCSI_MULTIQUEUE feature type.
      WINDOWS: WINDOWS feature type.
      MULTI_IP_SUBNET: MULTI_IP_SUBNET feature type.
      UEFI_COMPATIBLE: UEFI_COMPATIBLE feature type.
      SECURE_BOOT: SECURE_BOOT feature type.
      GVNIC: GVNIC feature type.
      SEV_CAPABLE: SEV_CAPABLE feature type.
      BARE_METAL_LINUX_COMPATIBLE: BARE_METAL_LINUX_COMPATIBLE feature type.
      SUSPEND_RESUME_COMPATIBLE: SUSPEND_RESUME_COMPATIBLE feature type.
      SEV_LIVE_MIGRATABLE: SEV_LIVE_MIGRATABLE feature type.
      SEV_SNP_CAPABLE: SEV_SNP_CAPABLE feature type.
      TDX_CAPABLE: TDX_CAPABLE feature type.
      IDPF: IDPF feature type.
      SEV_LIVE_MIGRATABLE_V2: SEV_LIVE_MIGRATABLE_V2 feature type.
    """
    FEATURE_TYPE_UNSPECIFIED = 0
    VIRTIO_SCSI_MULTIQUEUE = 1
    WINDOWS = 2
    MULTI_IP_SUBNET = 3
    UEFI_COMPATIBLE = 4
    SECURE_BOOT = 5
    GVNIC = 6
    SEV_CAPABLE = 7
    BARE_METAL_LINUX_COMPATIBLE = 8
    SUSPEND_RESUME_COMPATIBLE = 9
    SEV_LIVE_MIGRATABLE = 10
    SEV_SNP_CAPABLE = 11
    TDX_CAPABLE = 12
    IDPF = 13
    SEV_LIVE_MIGRATABLE_V2 = 14

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class InitializeParams(_messages.Message):
  r"""Specifies the parameters to initialize this disk.

  Fields:
    diskName: Optional. Specifies the disk name. If not specified, the default
      is to use the name of the instance.
    replicaZones: Optional. URL of the zone where the disk should be created.
      Required for each regional disk associated with the instance.
  """

  diskName = _messages.StringField(1)
  replicaZones = _messages.StringField(2, repeated=True)


class InitializeServiceRequest(_messages.Message):
  r"""Request message for initializing the service.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    resourceType: Required. The resource type to which the default service
      config will be applied. Examples include,
      "compute.googleapis.com/Instance" and "storage.googleapis.com/Bucket".
  """

  requestId = _messages.StringField(1)
  resourceType = _messages.StringField(2)


class InitiateBackupRequest(_messages.Message):
  r"""request message for InitiateBackup.

  Fields:
    backupId: Required. Resource ID of the Backup resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  backupId = _messages.StringField(1)
  requestId = _messages.StringField(2)


class InitiateBackupResponse(_messages.Message):
  r"""Response message for InitiateBackup.

  Fields:
    backup: The name of the backup that was created.
    baseBackupGenerationId: The generation id of the base backup. It is needed
      for the incremental backups.
    newBackupGenerationId: The generation id of the new backup.
  """

  backup = _messages.StringField(1)
  baseBackupGenerationId = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  newBackupGenerationId = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class InstanceParams(_messages.Message):
  r"""Additional instance params.

  Messages:
    ResourceManagerTagsValue: Optional. Resource manager tags to be bound to
      the instance.

  Fields:
    resourceManagerTags: Optional. Resource manager tags to be bound to the
      instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceManagerTagsValue(_messages.Message):
    r"""Optional. Resource manager tags to be bound to the instance.

    Messages:
      AdditionalProperty: An additional property for a
        ResourceManagerTagsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceManagerTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceManagerTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  resourceManagerTags = _messages.MessageField('ResourceManagerTagsValue', 1)


class ListBackupPlanAssociationsResponse(_messages.Message):
  r"""Response message for List BackupPlanAssociation

  Fields:
    backupPlanAssociations: The list of Backup Plan Associations in the
      project for the specified location. If the `{location}` value in the
      request is "-", the response contains a list of instances from all
      locations. In case any location is unreachable, the response will only
      return backup plan associations in reachable locations and the
      'unreachable' field will be populated with a list of unreachable
      locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backupPlanAssociations = _messages.MessageField('BackupPlanAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupPlanRevisionsResponse(_messages.Message):
  r"""The response message for getting a list of `BackupPlanRevision`.

  Fields:
    backupPlanRevisions: The list of `BackupPlanRevisions` in the project for
      the specified location. If the `{location}` value in the request is "-",
      the response contains a list of resources from all locations. In case
      any location is unreachable, the response will only return backup plans
      in reachable locations and the 'unreachable' field will be populated
      with a list of unreachable locations.
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListBackupPlanRevisions` call to retrieve the next page of results. If
      this field is omitted or empty, then there are no more results to
      return.
    unreachable: Locations that could not be reached.
  """

  backupPlanRevisions = _messages.MessageField('BackupPlanRevision', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupPlansResponse(_messages.Message):
  r"""The response message for getting a list of `BackupPlan`.

  Fields:
    backupPlans: The list of `BackupPlans` in the project for the specified
      location. If the `{location}` value in the request is "-", the response
      contains a list of resources from all locations. In case any location is
      unreachable, the response will only return backup plans in reachable
      locations and the 'unreachable' field will be populated with a list of
      unreachable locations. BackupPlan
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListBackupPlans` call to retrieve the next page of results. If this
      field is omitted or empty, then there are no more results to return.
    unreachable: Locations that could not be reached.
  """

  backupPlans = _messages.MessageField('BackupPlan', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupVaultsResponse(_messages.Message):
  r"""Response message for listing BackupVaults.

  Fields:
    backupVaults: The list of BackupVault instances in the project for the
      specified location. If the '{location}' value in the request is "-", the
      response contains a list of instances from all locations. In case any
      location is unreachable, the response will only return backup vaults in
      reachable locations and the 'unreachable' field will be populated with a
      list of unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backupVaults = _messages.MessageField('BackupVault', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""Response message for listing Backups.

  Fields:
    backups: The list of Backup instances in the project for the specified
      location. If the '{location}' value in the request is "-", the response
      contains a list of instances from all locations. In case any location is
      unreachable, the response will only return data sources in reachable
      locations and the 'unreachable' field will be populated with a list of
      unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDataSourcesResponse(_messages.Message):
  r"""Response message for listing DataSources.

  Fields:
    dataSources: The list of DataSource instances in the project for the
      specified location. If the '{location}' value in the request is "-", the
      response contains a list of instances from all locations. In case any
      location is unreachable, the response will only return data sources in
      reachable locations and the 'unreachable' field will be populated with a
      list of unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  dataSources = _messages.MessageField('DataSource', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListManagementServersResponse(_messages.Message):
  r"""Response message for listing management servers.

  Fields:
    managementServers: The list of ManagementServer instances in the project
      for the specified location. If the '{location}' value in the request is
      "-", the response contains a list of instances from all locations. In
      case any location is unreachable, the response will only return
      management servers in reachable locations and the 'unreachable' field
      will be populated with a list of unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  managementServers = _messages.MessageField('ManagementServer', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListResourceBackupConfigsResponse(_messages.Message):
  r"""Response for ListResourceBackupConfigs.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    resourceBackupConfigs: The list of ResourceBackupConfigs for the specified
      scope.
  """

  nextPageToken = _messages.StringField(1)
  resourceBackupConfigs = _messages.MessageField('ResourceBackupConfig', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagementServer(_messages.Message):
  r"""ManagementServer describes a single BackupDR ManagementServer instance.

  Enums:
    StateValueValuesEnum: Output only. The ManagementServer state.
    TypeValueValuesEnum: Optional. The type of the ManagementServer resource.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Labels currently defined: 1. migrate_from_go= If set to true,
      the MS is created in migration ready mode.

  Fields:
    baProxyUri: Output only. The hostname or ip address of the exposed AGM
      endpoints, used by BAs to connect to BA proxy.
    createTime: Output only. The time when the instance was created.
    description: Optional. The description of the ManagementServer instance
      (2048 characters or less).
    etag: Optional. Server specified ETag for the ManagementServer resource to
      prevent simultaneous updates from overwiting each other.
    labels: Optional. Resource labels to represent user provided metadata.
      Labels currently defined: 1. migrate_from_go= If set to true, the MS is
      created in migration ready mode.
    managementUri: Output only. The hostname or ip address of the exposed AGM
      endpoints, used by clients to connect to AGM/RD graphical user interface
      and APIs.
    name: Output only. Identifier. The resource name.
    networks: Optional. VPC networks to which the ManagementServer instance is
      connected. For this version, only a single network is supported. This
      field is optional if MS is created without PSA
    oauth2ClientId: Output only. The OAuth 2.0 client id is required to make
      API calls to the BackupDR instance API of this ManagementServer. This is
      the value that should be provided in the 'aud' field of the OIDC ID
      Token (see openid specification https://openid.net/specs/openid-connect-
      core-1_0.html#IDToken).
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The ManagementServer state.
    type: Optional. The type of the ManagementServer resource.
    updateTime: Output only. The time when the instance was updated.
    workforceIdentityBasedManagementUri: Output only. The hostnames of the
      exposed AGM endpoints for both types of user i.e. 1p and 3p, used to
      connect AGM/RM UI.
    workforceIdentityBasedOauth2ClientId: Output only. The OAuth client IDs
      for both types of user i.e. 1p and 3p.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The ManagementServer state.

    Values:
      INSTANCE_STATE_UNSPECIFIED: State not set.
      CREATING: The instance is being created.
      READY: The instance has been created and is fully usable.
      UPDATING: The instance configuration is being updated. Certain kinds of
        updates may cause the instance to become unusable while the update is
        in progress.
      DELETING: The instance is being deleted.
      REPAIRING: The instance is being repaired and may be unstable.
      MAINTENANCE: Maintenance is being performed on this instance.
      ERROR: The instance is experiencing an issue and might be unusable. You
        can get further details from the statusMessage field of Instance
        resource.
    """
    INSTANCE_STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    REPAIRING = 5
    MAINTENANCE = 6
    ERROR = 7

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the ManagementServer resource.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: Instance type is not mentioned.
      BACKUP_RESTORE: Instance for backup and restore management (i.e., AGM).
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    BACKUP_RESTORE = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Labels
    currently defined: 1. migrate_from_go= If set to true, the MS is created
    in migration ready mode.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  baProxyUri = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  managementUri = _messages.MessageField('ManagementURI', 6)
  name = _messages.StringField(7)
  networks = _messages.MessageField('NetworkConfig', 8, repeated=True)
  oauth2ClientId = _messages.StringField(9)
  satisfiesPzi = _messages.BooleanField(10)
  satisfiesPzs = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  type = _messages.EnumField('TypeValueValuesEnum', 13)
  updateTime = _messages.StringField(14)
  workforceIdentityBasedManagementUri = _messages.MessageField('WorkforceIdentityBasedManagementURI', 15)
  workforceIdentityBasedOauth2ClientId = _messages.MessageField('WorkforceIdentityBasedOAuth2ClientID', 16)


class ManagementURI(_messages.Message):
  r"""ManagementURI for the Management Server resource.

  Fields:
    api: Output only. The ManagementServer AGM/RD API URL.
    webUi: Output only. The ManagementServer AGM/RD WebUI URL.
  """

  api = _messages.StringField(1)
  webUi = _messages.StringField(2)


class Metadata(_messages.Message):
  r"""A metadata key/value entry.

  Fields:
    items: Optional. Array of key/value pairs. The total size of all keys and
      values must be less than 512 KB.
  """

  items = _messages.MessageField('Entry', 1, repeated=True)


class NetworkConfig(_messages.Message):
  r"""Network configuration for ManagementServer instance.

  Enums:
    PeeringModeValueValuesEnum: Optional. The network connect mode of the
      ManagementServer instance. For this version, only PRIVATE_SERVICE_ACCESS
      is supported.

  Fields:
    network: Optional. The resource name of the Google Compute Engine VPC
      network to which the ManagementServer instance is connected.
    peeringMode: Optional. The network connect mode of the ManagementServer
      instance. For this version, only PRIVATE_SERVICE_ACCESS is supported.
  """

  class PeeringModeValueValuesEnum(_messages.Enum):
    r"""Optional. The network connect mode of the ManagementServer instance.
    For this version, only PRIVATE_SERVICE_ACCESS is supported.

    Values:
      PEERING_MODE_UNSPECIFIED: Peering mode not set.
      PRIVATE_SERVICE_ACCESS: Connect using Private Service Access to the
        Management Server. Private services access provides an IP address
        range for multiple Google Cloud services, including Cloud BackupDR.
    """
    PEERING_MODE_UNSPECIFIED = 0
    PRIVATE_SERVICE_ACCESS = 1

  network = _messages.StringField(1)
  peeringMode = _messages.EnumField('PeeringModeValueValuesEnum', 2)


class NetworkInterface(_messages.Message):
  r"""A network interface resource attached to an instance. s

  Enums:
    Ipv6AccessTypeValueValuesEnum: Optional. [Output Only] One of EXTERNAL,
      INTERNAL to indicate whether the IP can be accessed from the Internet.
      This field is always inherited from its subnetwork.
    NicTypeValueValuesEnum: Optional. The type of vNIC to be used on this
      interface. This may be gVNIC or VirtioNet.
    StackTypeValueValuesEnum: The stack type for this network interface.

  Fields:
    accessConfigs: Optional. An array of configurations for this interface.
      Currently, only one access config,ONE_TO_ONE_NAT is supported. If there
      are no accessConfigs specified, then this instance will have no external
      internet access.
    aliasIpRanges: Optional. An array of alias IP ranges for this network
      interface. You can only specify this field for network interfaces in VPC
      networks.
    internalIpv6PrefixLength: Optional. The prefix length of the primary
      internal IPv6 range.
    ipv6AccessConfigs: Optional. An array of IPv6 access configurations for
      this interface. Currently, only one IPv6 access config, DIRECT_IPV6, is
      supported. If there is no ipv6AccessConfig specified, then this instance
      will have no external IPv6 Internet access.
    ipv6AccessType: Optional. [Output Only] One of EXTERNAL, INTERNAL to
      indicate whether the IP can be accessed from the Internet. This field is
      always inherited from its subnetwork.
    ipv6Address: Optional. An IPv6 internal network address for this network
      interface. To use a static internal IP address, it must be unused and in
      the same region as the instance's zone. If not specified, Google Cloud
      will automatically assign an internal IPv6 address from the instance's
      subnetwork.
    name: Output only. [Output Only] The name of the network interface, which
      is generated by the server.
    network: Optional. URL of the VPC network resource for this instance.
    networkAttachment: Optional. The URL of the network attachment that this
      interface should connect to in the following format: projects/{project_n
      umber}/regions/{region_name}/networkAttachments/{network_attachment_name
      }.
    networkIP: Optional. An IPv4 internal IP address to assign to the instance
      for this network interface. If not specified by the user, an unused
      internal IP is assigned by the system.
    nicType: Optional. The type of vNIC to be used on this interface. This may
      be gVNIC or VirtioNet.
    queueCount: Optional. The networking queue count that's specified by users
      for the network interface. Both Rx and Tx queues will be set to this
      number. It'll be empty if not specified by the users.
    stackType: The stack type for this network interface.
    subnetwork: Optional. The URL of the Subnetwork resource for this
      instance.
  """

  class Ipv6AccessTypeValueValuesEnum(_messages.Enum):
    r"""Optional. [Output Only] One of EXTERNAL, INTERNAL to indicate whether
    the IP can be accessed from the Internet. This field is always inherited
    from its subnetwork.

    Values:
      UNSPECIFIED_IPV6_ACCESS_TYPE: IPv6 access type not set. Means this
        network interface hasn't been turned on IPv6 yet.
      INTERNAL: This network interface can have internal IPv6.
      EXTERNAL: This network interface can have external IPv6.
    """
    UNSPECIFIED_IPV6_ACCESS_TYPE = 0
    INTERNAL = 1
    EXTERNAL = 2

  class NicTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of vNIC to be used on this interface. This may be
    gVNIC or VirtioNet.

    Values:
      NIC_TYPE_UNSPECIFIED: Default should be NIC_TYPE_UNSPECIFIED.
      VIRTIO_NET: VIRTIO
      GVNIC: GVNIC
    """
    NIC_TYPE_UNSPECIFIED = 0
    VIRTIO_NET = 1
    GVNIC = 2

  class StackTypeValueValuesEnum(_messages.Enum):
    r"""The stack type for this network interface.

    Values:
      STACK_TYPE_UNSPECIFIED: Default should be STACK_TYPE_UNSPECIFIED.
      IPV4_ONLY: The network interface will be assigned IPv4 address.
      IPV4_IPV6: The network interface can have both IPv4 and IPv6 addresses.
    """
    STACK_TYPE_UNSPECIFIED = 0
    IPV4_ONLY = 1
    IPV4_IPV6 = 2

  accessConfigs = _messages.MessageField('AccessConfig', 1, repeated=True)
  aliasIpRanges = _messages.MessageField('AliasIpRange', 2, repeated=True)
  internalIpv6PrefixLength = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  ipv6AccessConfigs = _messages.MessageField('AccessConfig', 4, repeated=True)
  ipv6AccessType = _messages.EnumField('Ipv6AccessTypeValueValuesEnum', 5)
  ipv6Address = _messages.StringField(6)
  name = _messages.StringField(7)
  network = _messages.StringField(8)
  networkAttachment = _messages.StringField(9)
  networkIP = _messages.StringField(10)
  nicType = _messages.EnumField('NicTypeValueValuesEnum', 11)
  queueCount = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  stackType = _messages.EnumField('StackTypeValueValuesEnum', 13)
  subnetwork = _messages.StringField(14)


class NetworkPerformanceConfig(_messages.Message):
  r"""Network performance configuration.

  Enums:
    TotalEgressBandwidthTierValueValuesEnum: Optional. The tier of the total
      egress bandwidth.

  Fields:
    totalEgressBandwidthTier: Optional. The tier of the total egress
      bandwidth.
  """

  class TotalEgressBandwidthTierValueValuesEnum(_messages.Enum):
    r"""Optional. The tier of the total egress bandwidth.

    Values:
      TIER_UNSPECIFIED: This value is unused.
      DEFAULT: Default network performance config.
      TIER_1: Tier 1 network performance config.
    """
    TIER_UNSPECIFIED = 0
    DEFAULT = 1
    TIER_1 = 2

  totalEgressBandwidthTier = _messages.EnumField('TotalEgressBandwidthTierValueValuesEnum', 1)


class NodeAffinity(_messages.Message):
  r"""Node Affinity: the configuration of desired nodes onto which this
  Instance could be scheduled.

  Enums:
    OperatorValueValuesEnum: Optional. Defines the operation of node
      selection.

  Fields:
    key: Optional. Corresponds to the label key of Node resource.
    operator: Optional. Defines the operation of node selection.
    values: Optional. Corresponds to the label values of Node resource.
  """

  class OperatorValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the operation of node selection.

    Values:
      OPERATOR_UNSPECIFIED: Default value. This value is unused.
      IN: Requires Compute Engine to seek for matched nodes.
      NOT_IN: Requires Compute Engine to avoid certain nodes.
    """
    OPERATOR_UNSPECIFIED = 0
    IN = 1
    NOT_IN = 2

  key = _messages.StringField(1)
  operator = _messages.EnumField('OperatorValueValuesEnum', 2)
  values = _messages.StringField(3, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Messages:
    AdditionalInfoValue: Output only. AdditionalInfo contains additional Info
      related to backup plan association resource.

  Fields:
    additionalInfo: Output only. AdditionalInfo contains additional Info
      related to backup plan association resource.
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      'Code.CANCELLED'.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalInfoValue(_messages.Message):
    r"""Output only. AdditionalInfo contains additional Info related to backup
    plan association resource.

    Messages:
      AdditionalProperty: An additional property for a AdditionalInfoValue
        object.

    Fields:
      additionalProperties: Additional properties of type AdditionalInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalInfo = _messages.MessageField('AdditionalInfoValue', 1)
  apiVersion = _messages.StringField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class PitrSettings(_messages.Message):
  r"""Point in time recovery settings of the backup configuration resource.

  Fields:
    retentionDays: Output only. Number of days to retain the backup.
  """

  retentionDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class PitrWindow(_messages.Message):
  r"""Point in time recovery window for a Cloud SQL instance.

  Fields:
    endTime: The end time of the PITR window.
    logRetentionDays: Log retention days for the PITR window.
    startTime: The start time of the PITR window.
  """

  endTime = _messages.StringField(1)
  logRetentionDays = _messages.IntegerField(2)
  startTime = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class RegionDiskTargetEnvironment(_messages.Message):
  r"""RegionDiskTargetEnvironment represents the target environment for the
  disk.

  Fields:
    project: Required. Target project for the disk.
    region: Required. Target region for the disk.
    replicaZones: Required. Target URLs of the replica zones for the disk.
  """

  project = _messages.StringField(1)
  region = _messages.StringField(2)
  replicaZones = _messages.StringField(3, repeated=True)


class RemoveDataSourceRequest(_messages.Message):
  r"""Message for deleting a DataSource.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class ResourceBackupConfig(_messages.Message):
  r"""ResourceBackupConfig represents a resource along with its backup
  configurations.

  Enums:
    TargetResourceTypeValueValuesEnum: Output only. The type of the target
      resource.

  Messages:
    TargetResourceLabelsValue: Labels associated with the target resource.

  Fields:
    backupConfigsDetails: Backup configurations applying to the target
      resource, including those targeting its related/child resources. For
      example, backup configuration applicable to Compute Engine disks will be
      populated in this field for a Compute Engine VM which has the disk
      associated.
    backupConfigured: Output only. Whether the target resource is configured
      for backup. This is true if the backup_configs_details is not empty.
    name: Identifier. The resource name of the ResourceBackupConfig. Format:
      projects/{project}/locations/{location}/resourceBackupConfigs/{uid}
    targetResource: Output only. The [full resource
      name](https://cloud.google.com/asset-inventory/docs/resource-name-
      format) of the cloud resource that this configuration applies to.
      Supported resource types are ResourceBackupConfig.ResourceType.
    targetResourceDisplayName: Output only. The human friendly name of the
      target resource.
    targetResourceLabels: Labels associated with the target resource.
    targetResourceType: Output only. The type of the target resource.
    uid: Output only. The unique identifier of the resource backup config.
    vaulted: Output only. Whether the target resource is protected by a backup
      vault. This is true if the backup_configs_details is not empty and any
      of the ResourceBackupConfig.backup_configs_details has a backup
      configuration with BackupConfigDetails.backup_vault set. set.
  """

  class TargetResourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the target resource.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Resource type not set.
      CLOUD_SQL_INSTANCE: Cloud SQL instance.
      COMPUTE_ENGINE_VM: Compute Engine VM.
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    CLOUD_SQL_INSTANCE = 1
    COMPUTE_ENGINE_VM = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TargetResourceLabelsValue(_messages.Message):
    r"""Labels associated with the target resource.

    Messages:
      AdditionalProperty: An additional property for a
        TargetResourceLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type
        TargetResourceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TargetResourceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupConfigsDetails = _messages.MessageField('BackupConfigDetails', 1, repeated=True)
  backupConfigured = _messages.BooleanField(2)
  name = _messages.StringField(3)
  targetResource = _messages.StringField(4)
  targetResourceDisplayName = _messages.StringField(5)
  targetResourceLabels = _messages.MessageField('TargetResourceLabelsValue', 6)
  targetResourceType = _messages.EnumField('TargetResourceTypeValueValuesEnum', 7)
  uid = _messages.StringField(8)
  vaulted = _messages.BooleanField(9)


class RestoreBackupRequest(_messages.Message):
  r"""Request message for restoring from a Backup.

  Fields:
    computeInstanceRestoreProperties: Compute Engine instance properties to be
      overridden during restore.
    computeInstanceTargetEnvironment: Compute Engine target environment to be
      used during restore.
    diskRestoreProperties: Disk properties to be overridden during restore.
    diskTargetEnvironment: Disk target environment to be used during restore.
    regionDiskTargetEnvironment: Region disk target environment to be used
      during restore.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  computeInstanceRestoreProperties = _messages.MessageField('ComputeInstanceRestoreProperties', 1)
  computeInstanceTargetEnvironment = _messages.MessageField('ComputeInstanceTargetEnvironment', 2)
  diskRestoreProperties = _messages.MessageField('DiskRestoreProperties', 3)
  diskTargetEnvironment = _messages.MessageField('DiskTargetEnvironment', 4)
  regionDiskTargetEnvironment = _messages.MessageField('RegionDiskTargetEnvironment', 5)
  requestId = _messages.StringField(6)


class RestoreBackupResponse(_messages.Message):
  r"""Response message for restoring from a Backup.

  Fields:
    targetResource: Details of the target resource created/modified as part of
      restore.
  """

  targetResource = _messages.MessageField('TargetResource', 1)


class RuleConfigInfo(_messages.Message):
  r"""Message for rules config info.

  Enums:
    LastBackupStateValueValuesEnum: Output only. The last backup state for
      rule.

  Fields:
    lastBackupError: Output only. google.rpc.Status object to store the last
      backup error.
    lastBackupState: Output only. The last backup state for rule.
    lastSuccessfulBackupConsistencyTime: Output only. The point in time when
      the last successful backup was captured from the source.
    ruleId: Output only. Backup Rule id fetched from backup plan.
  """

  class LastBackupStateValueValuesEnum(_messages.Enum):
    r"""Output only. The last backup state for rule.

    Values:
      LAST_BACKUP_STATE_UNSPECIFIED: State not set.
      FIRST_BACKUP_PENDING: The first backup is pending.
      PERMISSION_DENIED: The most recent backup could not be run/failed
        because of the lack of permissions.
      SUCCEEDED: The last backup operation succeeded.
      FAILED: The last backup operation failed.
    """
    LAST_BACKUP_STATE_UNSPECIFIED = 0
    FIRST_BACKUP_PENDING = 1
    PERMISSION_DENIED = 2
    SUCCEEDED = 3
    FAILED = 4

  lastBackupError = _messages.MessageField('Status', 1)
  lastBackupState = _messages.EnumField('LastBackupStateValueValuesEnum', 2)
  lastSuccessfulBackupConsistencyTime = _messages.StringField(3)
  ruleId = _messages.StringField(4)


class Scheduling(_messages.Message):
  r"""Sets the scheduling options for an Instance.

  Enums:
    InstanceTerminationActionValueValuesEnum: Optional. Specifies the
      termination action for the instance.
    OnHostMaintenanceValueValuesEnum: Optional. Defines the maintenance
      behavior for this instance.
    ProvisioningModelValueValuesEnum: Optional. Specifies the provisioning
      model of the instance.

  Fields:
    automaticRestart: Optional. Specifies whether the instance should be
      automatically restarted if it is terminated by Compute Engine (not
      terminated by a user).
    instanceTerminationAction: Optional. Specifies the termination action for
      the instance.
    localSsdRecoveryTimeout: Optional. Specifies the maximum amount of time a
      Local Ssd Vm should wait while recovery of the Local Ssd state is
      attempted. Its value should be in between 0 and 168 hours with hour
      granularity and the default value being 1 hour.
    minNodeCpus: Optional. The minimum number of virtual CPUs this instance
      will consume when running on a sole-tenant node.
    nodeAffinities: Optional. A set of node affinity and anti-affinity
      configurations. Overrides reservationAffinity.
    onHostMaintenance: Optional. Defines the maintenance behavior for this
      instance.
    preemptible: Optional. Defines whether the instance is preemptible.
    provisioningModel: Optional. Specifies the provisioning model of the
      instance.
  """

  class InstanceTerminationActionValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the termination action for the instance.

    Values:
      INSTANCE_TERMINATION_ACTION_UNSPECIFIED: Default value. This value is
        unused.
      DELETE: Delete the VM.
      STOP: Stop the VM without storing in-memory content. default action.
    """
    INSTANCE_TERMINATION_ACTION_UNSPECIFIED = 0
    DELETE = 1
    STOP = 2

  class OnHostMaintenanceValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the maintenance behavior for this instance.

    Values:
      ON_HOST_MAINTENANCE_UNSPECIFIED: Default value. This value is unused.
      TERMINATE: Tells Compute Engine to terminate and (optionally) restart
        the instance away from the maintenance activity.
      MIGRATE: Default, Allows Compute Engine to automatically migrate
        instances out of the way of maintenance events.
    """
    ON_HOST_MAINTENANCE_UNSPECIFIED = 0
    TERMINATE = 1
    MIGRATE = 2

  class ProvisioningModelValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the provisioning model of the instance.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Default value. This value is not used.
      STANDARD: Standard provisioning with user controlled runtime, no
        discounts.
      SPOT: Heavily discounted, no guaranteed runtime.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2

  automaticRestart = _messages.BooleanField(1)
  instanceTerminationAction = _messages.EnumField('InstanceTerminationActionValueValuesEnum', 2)
  localSsdRecoveryTimeout = _messages.MessageField('SchedulingDuration', 3)
  minNodeCpus = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nodeAffinities = _messages.MessageField('NodeAffinity', 5, repeated=True)
  onHostMaintenance = _messages.EnumField('OnHostMaintenanceValueValuesEnum', 6)
  preemptible = _messages.BooleanField(7)
  provisioningModel = _messages.EnumField('ProvisioningModelValueValuesEnum', 8)


class SchedulingDuration(_messages.Message):
  r"""A SchedulingDuration represents a fixed-length span of time represented
  as a count of seconds and fractions of seconds at nanosecond resolution. It
  is independent of any calendar and concepts like "day" or "month". Range is
  approximately 10,000 years.

  Fields:
    nanos: Optional. Span of time that's a fraction of a second at nanosecond
      resolution.
    seconds: Optional. Span of time at a resolution of a second.
  """

  nanos = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(2)


class ServiceAccount(_messages.Message):
  r"""A service account.

  Fields:
    email: Optional. Email address of the service account.
    scopes: Optional. The list of scopes to be made available for this service
      account.
  """

  email = _messages.StringField(1)
  scopes = _messages.StringField(2, repeated=True)


class ServiceLockInfo(_messages.Message):
  r"""ServiceLockInfo represents the details of a lock taken by the service on
  a Backup resource.

  Fields:
    operation: Output only. The name of the operation that created this lock.
      The lock will automatically be released when the operation completes.
  """

  operation = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SetInternalStatusRequest(_messages.Message):
  r"""Request message for SetStatusInternal method.

  Enums:
    BackupConfigStateValueValuesEnum: Required. Output only. The new
      BackupConfigState to set for the DataSource.

  Fields:
    backupConfigState: Required. Output only. The new BackupConfigState to set
      for the DataSource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (00000000-0000-0000-0000-000000000000).
    value: Required. The value required for this method to work. This field
      must be the 32-byte SHA256 hash of the DataSourceID. The DataSourceID
      used here is only the final piece of the fully qualified resource path
      for this DataSource (i.e. the part after '.../dataSources/'). This field
      exists to make this method difficult to call since it is intended for
      use only by Backup Appliances.
  """

  class BackupConfigStateValueValuesEnum(_messages.Enum):
    r"""Required. Output only. The new BackupConfigState to set for the
    DataSource.

    Values:
      BACKUP_CONFIG_STATE_UNSPECIFIED: The possible states of backup
        configuration. Status not set.
      ACTIVE: The data source is actively protected (i.e. there is a
        BackupPlanAssociation or Appliance SLA pointing to it)
      PASSIVE: The data source is no longer protected (but may have backups
        under it)
    """
    BACKUP_CONFIG_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PASSIVE = 2

  backupConfigState = _messages.EnumField('BackupConfigStateValueValuesEnum', 1)
  requestId = _messages.StringField(2)
  value = _messages.BytesField(3)


class SetInternalStatusResponse(_messages.Message):
  r"""Response message from SetStatusInternal method."""


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StandardSchedule(_messages.Message):
  r"""`StandardSchedule` defines a schedule that run within the confines of a
  defined window of days. We can define recurrence type for schedule as
  HOURLY, DAILY, WEEKLY, MONTHLY or YEARLY.

  Enums:
    DaysOfWeekValueListEntryValuesEnum:
    MonthsValueListEntryValuesEnum:
    RecurrenceTypeValueValuesEnum: Required. Specifies the `RecurrenceType`
      for the schedule.

  Fields:
    backupWindow: Required. A BackupWindow defines the window of day during
      which backup jobs will run. Jobs are queued at the beginning of the
      window and will be marked as `NOT_RUN` if they do not start by the end
      of the window. Note: running jobs will not be cancelled at the end of
      the window.
    daysOfMonth: Optional. Specifies days of months like 1, 5, or 14 on which
      jobs will run. Values for `days_of_month` are only applicable for
      `recurrence_type`, `MONTHLY` and `YEARLY`. A validation error will occur
      if other values are supplied.
    daysOfWeek: Optional. Specifies days of week like, MONDAY or TUESDAY, on
      which jobs will run. This is required for `recurrence_type`, `WEEKLY`
      and is not applicable otherwise. A validation error will occur if a
      value is supplied and `recurrence_type` is not `WEEKLY`.
    hourlyFrequency: Optional. Specifies frequency for hourly backups. A
      hourly frequency of 2 means jobs will run every 2 hours from start time
      till end time defined. This is required for `recurrence_type`, `HOURLY`
      and is not applicable otherwise. A validation error will occur if a
      value is supplied and `recurrence_type` is not `HOURLY`. Value of hourly
      frequency should be between 4 and 23. Reason for limit : We found that
      there is bandwidth limitation of 3GB/S for GMI while taking a backup and
      5GB/S while doing a restore. Given the amount of parallel backups and
      restore we are targeting, this will potentially take the backup time to
      mins and hours (in worst case scenario).
    months: Optional. Specifies the months of year, like `FEBRUARY` and/or
      `MAY`, on which jobs will run. This field is only applicable when
      `recurrence_type` is `YEARLY`. A validation error will occur if other
      values are supplied.
    recurrenceType: Required. Specifies the `RecurrenceType` for the schedule.
    timeZone: Required. The time zone to be used when interpreting the
      schedule. The value of this field must be a time zone name from the IANA
      tz database. See
      https://en.wikipedia.org/wiki/List_of_tz_database_time_zones for the
      list of valid timezone names. For e.g., Europe/Paris.
    weekDayOfMonth: Optional. Specifies a week day of the month like, FIRST
      SUNDAY or LAST MONDAY, on which jobs will run. This will be specified by
      two fields in `WeekDayOfMonth`, one for the day, e.g. `MONDAY`, and one
      for the week, e.g. `LAST`. This field is only applicable for
      `recurrence_type`, `MONTHLY` and `YEARLY`. A validation error will occur
      if other values are supplied.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  class MonthsValueListEntryValuesEnum(_messages.Enum):
    r"""MonthsValueListEntryValuesEnum enum type.

    Values:
      MONTH_UNSPECIFIED: The unspecified month.
      JANUARY: The month of January.
      FEBRUARY: The month of February.
      MARCH: The month of March.
      APRIL: The month of April.
      MAY: The month of May.
      JUNE: The month of June.
      JULY: The month of July.
      AUGUST: The month of August.
      SEPTEMBER: The month of September.
      OCTOBER: The month of October.
      NOVEMBER: The month of November.
      DECEMBER: The month of December.
    """
    MONTH_UNSPECIFIED = 0
    JANUARY = 1
    FEBRUARY = 2
    MARCH = 3
    APRIL = 4
    MAY = 5
    JUNE = 6
    JULY = 7
    AUGUST = 8
    SEPTEMBER = 9
    OCTOBER = 10
    NOVEMBER = 11
    DECEMBER = 12

  class RecurrenceTypeValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the `RecurrenceType` for the schedule.

    Values:
      RECURRENCE_TYPE_UNSPECIFIED: recurrence type not set
      HOURLY: The `BackupRule` is to be applied hourly.
      DAILY: The `BackupRule` is to be applied daily.
      WEEKLY: The `BackupRule` is to be applied weekly.
      MONTHLY: The `BackupRule` is to be applied monthly.
      YEARLY: The `BackupRule` is to be applied yearly.
    """
    RECURRENCE_TYPE_UNSPECIFIED = 0
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    YEARLY = 5

  backupWindow = _messages.MessageField('BackupWindow', 1)
  daysOfMonth = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.INT32)
  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 3, repeated=True)
  hourlyFrequency = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  months = _messages.EnumField('MonthsValueListEntryValuesEnum', 5, repeated=True)
  recurrenceType = _messages.EnumField('RecurrenceTypeValueValuesEnum', 6)
  timeZone = _messages.StringField(7)
  weekDayOfMonth = _messages.MessageField('WeekDayOfMonth', 8)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tags(_messages.Message):
  r"""A set of instance tags.

  Fields:
    items: Optional. An array of tags. Each tag must be 1-63 characters long,
      and comply with RFC1035.
  """

  items = _messages.StringField(1, repeated=True)


class TargetResource(_messages.Message):
  r"""Details of the target resource created/modified as part of restore.

  Fields:
    gcpResource: Details of the native Google Cloud resource created as part
      of restore.
  """

  gcpResource = _messages.MessageField('GcpResource', 1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TriggerBackupRequest(_messages.Message):
  r"""Request message for triggering a backup.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    ruleId: Required. backup rule_id for which a backup needs to be triggered.
  """

  requestId = _messages.StringField(1)
  ruleId = _messages.StringField(2)


class WeekDayOfMonth(_messages.Message):
  r"""`WeekDayOfMonth` defines the week day of the month on which the backups
  will run. The message combines a `WeekOfMonth` and `DayOfWeek` to produce
  values like `FIRST`/`MONDAY` or `LAST`/`FRIDAY`.

  Enums:
    DayOfWeekValueValuesEnum: Required. Specifies the day of the week.
    WeekOfMonthValueValuesEnum: Required. Specifies the week of the month.

  Fields:
    dayOfWeek: Required. Specifies the day of the week.
    weekOfMonth: Required. Specifies the week of the month.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  class WeekOfMonthValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the week of the month.

    Values:
      WEEK_OF_MONTH_UNSPECIFIED: The zero value. Do not use.
      FIRST: The first week of the month.
      SECOND: The second week of the month.
      THIRD: The third week of the month.
      FOURTH: The fourth week of the month.
      LAST: The last week of the month.
    """
    WEEK_OF_MONTH_UNSPECIFIED = 0
    FIRST = 1
    SECOND = 2
    THIRD = 3
    FOURTH = 4
    LAST = 5

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  weekOfMonth = _messages.EnumField('WeekOfMonthValueValuesEnum', 2)


class WorkforceIdentityBasedManagementURI(_messages.Message):
  r"""ManagementURI depending on the Workforce Identity i.e. either 1p or 3p.

  Fields:
    firstPartyManagementUri: Output only. First party Management URI for
      Google Identities.
    thirdPartyManagementUri: Output only. Third party Management URI for
      External Identity Providers.
  """

  firstPartyManagementUri = _messages.StringField(1)
  thirdPartyManagementUri = _messages.StringField(2)


class WorkforceIdentityBasedOAuth2ClientID(_messages.Message):
  r"""OAuth Client ID depending on the Workforce Identity i.e. either 1p or
  3p,

  Fields:
    firstPartyOauth2ClientId: Output only. First party OAuth Client ID for
      Google Identities.
    thirdPartyOauth2ClientId: Output only. Third party OAuth Client ID for
      External Identity Providers.
  """

  firstPartyOauth2ClientId = _messages.StringField(1)
  thirdPartyOauth2ClientId = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    BackupdrProjectsLocationsManagementServersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
