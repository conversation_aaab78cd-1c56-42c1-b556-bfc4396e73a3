"""Generated client library for alloydb version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.alloydb.v1alpha import alloydb_v1alpha_messages as messages


class AlloydbV1alpha(base_api.BaseApiClient):
  """Generated client library for service alloydb version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://alloydb.googleapis.com/'
  MTLS_BASE_URL = 'https://alloydb.mtls.googleapis.com/'

  _PACKAGE = 'alloydb'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AlloydbV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new alloydb handle."""
    url = url or self.BASE_URL
    super(AlloydbV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_backups = self.ProjectsLocationsBackupsService(self)
    self.projects_locations_clusters_instances = self.ProjectsLocationsClustersInstancesService(self)
    self.projects_locations_clusters_users = self.ProjectsLocationsClustersUsersService(self)
    self.projects_locations_clusters = self.ProjectsLocationsClustersService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_supportedDatabaseFlags = self.ProjectsLocationsSupportedDatabaseFlagsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_backups resource."""

    _NAME = 'projects_locations_backups'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Backup in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/backups',
        http_method='POST',
        method_id='alloydb.projects.locations.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/backups',
        request_field='backup',
        request_type_name='AlloydbProjectsLocationsBackupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Backup.

      Args:
        request: (AlloydbProjectsLocationsBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='alloydb.projects.locations.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsBackupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Backup.

      Args:
        request: (AlloydbProjectsLocationsBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}',
        http_method='GET',
        method_id='alloydb.projects.locations.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Backups in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/backups',
        http_method='GET',
        method_id='alloydb.projects.locations.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/backups',
        request_field='',
        request_type_name='AlloydbProjectsLocationsBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Backup.

      Args:
        request: (AlloydbProjectsLocationsBackupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='alloydb.projects.locations.backups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='backup',
        request_type_name='AlloydbProjectsLocationsBackupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsClustersInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_instances resource."""

    _NAME = 'projects_locations_clusters_instances'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsClustersInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Instance in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['instanceId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/instances',
        request_field='instance',
        request_type_name='AlloydbProjectsLocationsClustersInstancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Createsecondary(self, request, global_params=None):
      r"""Creates a new SECONDARY Instance in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Createsecondary')
      return self._RunMethod(
          config, request, global_params=global_params)

    Createsecondary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances:createsecondary',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.instances.createsecondary',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['instanceId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/instances:createsecondary',
        request_field='instance',
        request_type_name='AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Instance.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='alloydb.projects.locations.clusters.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Failover(self, request, global_params=None):
      r"""Forces a Failover for a highly available instance. Failover promotes the HA standby instance as the new primary. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesFailoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Failover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Failover.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:failover',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.instances.failover',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:failover',
        request_field='failoverInstanceRequest',
        request_type_name='AlloydbProjectsLocationsClustersInstancesFailoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Instance.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersInstancesGetRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def GetConnectionInfo(self, request, global_params=None):
      r"""Get instance metadata used for a connection.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConnectionInfo) The response message.
      """
      config = self.GetMethodConfig('GetConnectionInfo')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConnectionInfo.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}/connectionInfo',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.instances.getConnectionInfo',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/connectionInfo',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest',
        response_type_name='ConnectionInfo',
        supports_download=False,
    )

    def InjectFault(self, request, global_params=None):
      r"""Injects fault in an instance. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesInjectFaultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('InjectFault')
      return self._RunMethod(
          config, request, global_params=global_params)

    InjectFault.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:injectFault',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.instances.injectFault',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:injectFault',
        request_field='injectFaultRequest',
        request_type_name='AlloydbProjectsLocationsClustersInstancesInjectFaultRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Instances in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/instances',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersInstancesListRequest',
        response_type_name='ListInstancesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Instance.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}',
        http_method='PATCH',
        method_id='alloydb.projects.locations.clusters.instances.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='instance',
        request_type_name='AlloydbProjectsLocationsClustersInstancesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restart(self, request, global_params=None):
      r"""Restart an Instance in a cluster. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersInstancesRestartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restart')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restart.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:restart',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.instances.restart',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:restart',
        request_field='restartInstanceRequest',
        request_type_name='AlloydbProjectsLocationsClustersInstancesRestartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsClustersUsersService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_users resource."""

    _NAME = 'projects_locations_clusters_users'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsClustersUsersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new User in a given project, location, and cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersUsersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (User) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.users.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'userId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/users',
        request_field='user',
        request_type_name='AlloydbProjectsLocationsClustersUsersCreateRequest',
        response_type_name='User',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single User.

      Args:
        request: (AlloydbProjectsLocationsClustersUsersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}',
        http_method='DELETE',
        method_id='alloydb.projects.locations.clusters.users.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersUsersDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single User.

      Args:
        request: (AlloydbProjectsLocationsClustersUsersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (User) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.users.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersUsersGetRequest',
        response_type_name='User',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Users in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersUsersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUsersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.users.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/users',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersUsersListRequest',
        response_type_name='ListUsersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single User.

      Args:
        request: (AlloydbProjectsLocationsClustersUsersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (User) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}',
        http_method='PATCH',
        method_id='alloydb.projects.locations.clusters.users.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='user',
        request_type_name='AlloydbProjectsLocationsClustersUsersPatchRequest',
        response_type_name='User',
        supports_download=False,
    )

  class ProjectsLocationsClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters resource."""

    _NAME = 'projects_locations_clusters'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Cluster in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clusterId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/clusters',
        request_field='cluster',
        request_type_name='AlloydbProjectsLocationsClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Createsecondary(self, request, global_params=None):
      r"""Creates a cluster of type SECONDARY in the given location using the primary cluster as the source.

      Args:
        request: (AlloydbProjectsLocationsClustersCreatesecondaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Createsecondary')
      return self._RunMethod(
          config, request, global_params=global_params)

    Createsecondary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:createsecondary',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.createsecondary',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clusterId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/clusters:createsecondary',
        request_field='cluster',
        request_type_name='AlloydbProjectsLocationsClustersCreatesecondaryRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='DELETE',
        method_id='alloydb.projects.locations.clusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data from the cluster. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:export',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:export',
        request_field='exportClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersGetRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data to the cluster. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:import',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:import',
        request_field='importClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Clusters in a given project and location.

      Args:
        request: (AlloydbProjectsLocationsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters',
        http_method='GET',
        method_id='alloydb.projects.locations.clusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/clusters',
        request_field='',
        request_type_name='AlloydbProjectsLocationsClustersListRequest',
        response_type_name='ListClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='PATCH',
        method_id='alloydb.projects.locations.clusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='cluster',
        request_type_name='AlloydbProjectsLocationsClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Promote(self, request, global_params=None):
      r"""Promotes a SECONDARY cluster. This turns down replication from the PRIMARY cluster and promotes a secondary cluster into its own standalone cluster. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersPromoteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Promote')
      return self._RunMethod(
          config, request, global_params=global_params)

    Promote.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:promote',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.promote',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:promote',
        request_field='promoteClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersPromoteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Creates a new Cluster in a given project and location, with a volume restored from the provided source, either a backup ID or a point-in-time and a source cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:restore',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.restore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/clusters:restore',
        request_field='restoreClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RestoreFromCloudSQL(self, request, global_params=None):
      r"""Restores an AlloyDB cluster from a CloudSQL resource.

      Args:
        request: (AlloydbProjectsLocationsClustersRestoreFromCloudSQLRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RestoreFromCloudSQL')
      return self._RunMethod(
          config, request, global_params=global_params)

    RestoreFromCloudSQL.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:restoreFromCloudSQL',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.restoreFromCloudSQL',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/clusters:restoreFromCloudSQL',
        request_field='restoreFromCloudSQLRequest',
        request_type_name='AlloydbProjectsLocationsClustersRestoreFromCloudSQLRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switchover(self, request, global_params=None):
      r"""Switches the roles of PRIMARY and SECONDARY clusters without any data loss. This promotes the SECONDARY cluster to PRIMARY and sets up the original PRIMARY cluster to replicate from this newly promoted cluster.

      Args:
        request: (AlloydbProjectsLocationsClustersSwitchoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switchover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switchover.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:switchover',
        http_method='POST',
        method_id='alloydb.projects.locations.clusters.switchover',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:switchover',
        request_field='switchoverClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersSwitchoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Upgrade(self, request, global_params=None):
      r"""Upgrades a single Cluster. Imperative only.

      Args:
        request: (AlloydbProjectsLocationsClustersUpgradeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Upgrade')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upgrade.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:upgrade',
        http_method='PATCH',
        method_id='alloydb.projects.locations.clusters.upgrade',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:upgrade',
        request_field='upgradeClusterRequest',
        request_type_name='AlloydbProjectsLocationsClustersUpgradeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (AlloydbProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='alloydb.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='AlloydbProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AlloydbProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='alloydb.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AlloydbProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='alloydb.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AlloydbProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='alloydb.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='AlloydbProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSupportedDatabaseFlagsService(base_api.BaseApiService):
    """Service class for the projects_locations_supportedDatabaseFlags resource."""

    _NAME = 'projects_locations_supportedDatabaseFlags'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsSupportedDatabaseFlagsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists SupportedDatabaseFlags for a given project and location.

      Args:
        request: (AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSupportedDatabaseFlagsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/supportedDatabaseFlags',
        http_method='GET',
        method_id='alloydb.projects.locations.supportedDatabaseFlags.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'scope'],
        relative_path='v1alpha/{+parent}/supportedDatabaseFlags',
        request_field='',
        request_type_name='AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest',
        response_type_name='ListSupportedDatabaseFlagsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (AlloydbProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='alloydb.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='AlloydbProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (AlloydbProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='alloydb.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='AlloydbProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AlloydbV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
