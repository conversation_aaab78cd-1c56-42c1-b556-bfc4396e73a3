"""Generated message classes for alloydb version v1alpha.

AlloyDB for PostgreSQL is an open source-compatible database service that
provides a powerful option for migrating, modernizing, or building commercial-
grade applications. It offers full compatibility with standard PostgreSQL, and
is more than 4x faster for transactional workloads and up to 100x faster for
analytical queries than standard PostgreSQL in our performance tests. AlloyDB
for PostgreSQL offers a 99.99 percent availability SLA inclusive of
maintenance. AlloyDB is optimized for the most demanding use cases, allowing
you to build new applications that require high transaction throughput, large
database sizes, or multiple read resources; scale existing PostgreSQL
workloads with no application changes; and modernize legacy proprietary
databases.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'alloydb'


class AlloydbProjectsLocationsBackupsCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. ID of the requesting object.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsBackupsDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Backup. If an etag is provided and
      does not match the current etag of the Backup, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. Name of the resource. For the required format, see the
      comment on the Backup.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class AlloydbProjectsLocationsBackupsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsBackupsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListBackupsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsBackupsPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if instance
      is not found. In that case, a new backup is created and `update_mask` is
      ignored.
    backup: A Backup resource to be passed as the request body.
    name: Output only. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id} where the
      cluster and backup ID segments should satisfy the regex expression
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase
      letters, numbers, and dashes, starting with a letter, and ending with a
      letter or number. For more details see https://google.aip.dev/122. The
      prefix of the backup resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Backup resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  allowMissing = _messages.BooleanField(1)
  backup = _messages.MessageField('Backup', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. ID of the requesting object.
    parent: Required. The location of the new cluster. For the required
      format, see the comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersCreatesecondaryRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersCreatesecondaryRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. ID of the requesting object (the secondary cluster).
    parent: Required. The location of the new cluster. For the required
      format, see the comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Cluster. If an etag is provided
      and does not match the current etag of the Cluster, deletion will be
      blocked and an ABORTED error will be returned.
    force: Optional. Whether to cascade delete child instances for given
      cluster.
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersExportRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersExportRequest object.

  Fields:
    exportClusterRequest: A ExportClusterRequest resource to be passed as the
      request body.
    name: Required. The resource name of the cluster.
  """

  exportClusterRequest = _messages.MessageField('ExportClusterRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. The view of the cluster to return. Returns
      all default fields if not set.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field.
    view: Optional. The view of the cluster to return. Returns all default
      fields if not set.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view of the cluster to return. Returns all default
    fields if not set.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: CLUSTER_VIEW_UNSPECIFIED Not specified,
        equivalent to BASIC.
      CLUSTER_VIEW_BASIC: BASIC server responses include all the relevant
        cluster details, excluding
        Cluster.ContinuousBackupInfo.EarliestRestorableTime and other view-
        specific fields. The default value.
      CLUSTER_VIEW_CONTINUOUS_BACKUP: CONTINUOUS_BACKUP response returns all
        the fields from BASIC plus the earliest restorable time if continuous
        backups are enabled. May increase latency.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    CLUSTER_VIEW_BASIC = 1
    CLUSTER_VIEW_CONTINUOUS_BACKUP = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class AlloydbProjectsLocationsClustersImportRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersImportRequest object.

  Fields:
    importClusterRequest: A ImportClusterRequest resource to be passed as the
      request body.
    name: Required. The resource name of the cluster.
  """

  importClusterRequest = _messages.MessageField('ImportClusterRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersInstancesCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. ID of the requesting object.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest
  object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. ID of the requesting object.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersInstancesDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Instance. If an etag is provided
      and does not match the current etag of the Instance, deletion will be
      blocked and an ABORTED error will be returned.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class AlloydbProjectsLocationsClustersInstancesFailoverRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesFailoverRequest object.

  Fields:
    failoverInstanceRequest: A FailoverInstanceRequest resource to be passed
      as the request body.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
  """

  failoverInstanceRequest = _messages.MessageField('FailoverInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest
  object.

  Fields:
    parent: Required. The name of the parent resource. The required format is:
      projects/{project}/locations/{location}/clusters/{cluster}/instances/{in
      stance}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class AlloydbProjectsLocationsClustersInstancesGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesGetRequest object.

  Enums:
    ViewValueValuesEnum: The view of the instance to return.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    view: The view of the instance to return.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view of the instance to return.

    Values:
      INSTANCE_VIEW_UNSPECIFIED: INSTANCE_VIEW_UNSPECIFIED Not specified,
        equivalent to BASIC.
      INSTANCE_VIEW_BASIC: BASIC server responses for a primary or read
        instance include all the relevant instance details, excluding the
        details of each node in the instance. The default value.
      INSTANCE_VIEW_FULL: FULL response is equivalent to BASIC for primary
        instance (for now). For read pool instance, this includes details of
        each node in the pool.
    """
    INSTANCE_VIEW_UNSPECIFIED = 0
    INSTANCE_VIEW_BASIC = 1
    INSTANCE_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class AlloydbProjectsLocationsClustersInstancesInjectFaultRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesInjectFaultRequest object.

  Fields:
    injectFaultRequest: A InjectFaultRequest resource to be passed as the
      request body.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
  """

  injectFaultRequest = _messages.MessageField('InjectFaultRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersInstancesListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field. Additionally, you
      can perform an aggregated list operation by specifying a value with one
      of the following formats: * projects/{project}/locations/-/clusters/- *
      projects/{project}/locations/{region}/clusters/-
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersInstancesPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if instance
      is not found. In that case, a new instance is created and `update_mask`
      is ignored.
    instance: A Instance resource to be passed as the request body.
    name: Output only. The name of the instance resource with the format: * pr
      ojects/{project}/locations/{region}/clusters/{cluster_id}/instances/{ins
      tance_id} where the cluster and instance ID segments should satisfy the
      regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters
      of lowercase letters, numbers, and dashes, starting with a letter, and
      ending with a letter or number. For more details see
      https://google.aip.dev/122. The prefix of the instance resource name is
      the name of the parent resource: *
      projects/{project}/locations/{region}/clusters/{cluster_id}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Instance resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  allowMissing = _messages.BooleanField(1)
  instance = _messages.MessageField('Instance', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersInstancesRestartRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesRestartRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    restartInstanceRequest: A RestartInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restartInstanceRequest = _messages.MessageField('RestartInstanceRequest', 2)


class AlloydbProjectsLocationsClustersListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Cluster.name field. Additionally, you can
      perform an aggregated list operation by specifying a value with the
      following format: * projects/{project}/locations/-
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if cluster is
      not found. In that case, a new cluster is created and `update_mask` is
      ignored.
    cluster: A Cluster resource to be passed as the request body.
    name: Output only. The name of the cluster resource with the format: *
      projects/{project}/locations/{region}/clusters/{cluster_id} where the
      cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For
      more details see https://google.aip.dev/122. The prefix of the cluster
      resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Cluster resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  allowMissing = _messages.BooleanField(1)
  cluster = _messages.MessageField('Cluster', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersPromoteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersPromoteRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field
    promoteClusterRequest: A PromoteClusterRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  promoteClusterRequest = _messages.MessageField('PromoteClusterRequest', 2)


class AlloydbProjectsLocationsClustersRestoreFromCloudSQLRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersRestoreFromCloudSQLRequest object.

  Fields:
    parent: Required. The location of the new cluster. For the required
      format, see the comment on Cluster.name field.
    restoreFromCloudSQLRequest: A RestoreFromCloudSQLRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  restoreFromCloudSQLRequest = _messages.MessageField('RestoreFromCloudSQLRequest', 2)


class AlloydbProjectsLocationsClustersRestoreRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersRestoreRequest object.

  Fields:
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Cluster.name field.
    restoreClusterRequest: A RestoreClusterRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  restoreClusterRequest = _messages.MessageField('RestoreClusterRequest', 2)


class AlloydbProjectsLocationsClustersSwitchoverRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersSwitchoverRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field
    switchoverClusterRequest: A SwitchoverClusterRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  switchoverClusterRequest = _messages.MessageField('SwitchoverClusterRequest', 2)


class AlloydbProjectsLocationsClustersUpgradeRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUpgradeRequest object.

  Fields:
    name: Required. The resource name of the cluster.
    upgradeClusterRequest: A UpgradeClusterRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeClusterRequest = _messages.MessageField('UpgradeClusterRequest', 2)


class AlloydbProjectsLocationsClustersUsersCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    user: A User resource to be passed as the request body.
    userId: Required. ID of the requesting object.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  user = _messages.MessageField('User', 3)
  userId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersUsersDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersDeleteRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the User.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class AlloydbProjectsLocationsClustersUsersGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersGetRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the User.name field.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsClustersUsersListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUsersRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersUsersPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersPatchRequest object.

  Fields:
    allowMissing: Optional. Allow missing fields in the update mask.
    name: Output only. Name of the resource in the form of
      projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the User resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    user: A User resource to be passed as the request body.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  user = _messages.MessageField('User', 5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class AlloydbProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest object.

  Enums:
    ScopeValueValuesEnum: Optional. The scope for which supported flags are
      requested. If not specified, default is DATABASE.

  Fields:
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. The required format is:
      * projects/{project}/locations/{location} Regardless of the parent
      specified here, as long it is contains a valid project and location, the
      service will return a static list of supported flags resources. Note
      that we do not yet support region-specific flags.
    scope: Optional. The scope for which supported flags are requested. If not
      specified, default is DATABASE.
  """

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Optional. The scope for which supported flags are requested. If not
    specified, default is DATABASE.

    Values:
      SCOPE_UNSPECIFIED: The scope of the flag is not specified. Default is
        DATABASE.
      DATABASE: The flag is a database flag.
      CONNECTION_POOL: The flag is a connection pool flag.
    """
    SCOPE_UNSPECIFIED = 0
    DATABASE = 1
    CONNECTION_POOL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  scope = _messages.EnumField('ScopeValueValuesEnum', 4)


class AuthorizedNetwork(_messages.Message):
  r"""AuthorizedNetwork contains metadata for an authorized network.

  Fields:
    cidrRange: CIDR range for one authorzied network of the instance.
  """

  cidrRange = _messages.StringField(1)


class AutomatedBackupPolicy(_messages.Message):
  r"""Message describing the user-specified automated backup policy. All
  fields in the automated backup policy are optional. Defaults for each field
  are provided if they are not set.

  Messages:
    LabelsValue: Labels to apply to backups created using this configuration.

  Fields:
    backupWindow: The length of the time window during which a backup can be
      taken. If a backup does not succeed within this time window, it will be
      canceled and considered failed. The backup window must be at least 5
      minutes long. There is no upper bound on the window. If not set, it
      defaults to 1 hour.
    enabled: Whether automated automated backups are enabled. If not set,
      defaults to true.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the backups with a customer-managed encryption key (CMEK). When
      this field is not specified, the backup will use the cluster's
      encryption config.
    enforcedRetention: If true, backups created by this policy would have
      `enforced_retention` set and cannot be deleted unless they expire (or as
      part of project deletion).
    labels: Labels to apply to backups created using this configuration.
    location: The location where the backup will be stored. Currently, the
      only supported option is to store the backup in the same region as the
      cluster. If empty, defaults to the region of the cluster.
    quantityBasedRetention: Quantity-based Backup retention policy to retain
      recent backups.
    timeBasedRetention: Time-based Backup retention policy.
    weeklySchedule: Weekly schedule for the Backup.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to backups created using this configuration.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupWindow = _messages.StringField(1)
  enabled = _messages.BooleanField(2)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 3)
  enforcedRetention = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  location = _messages.StringField(6)
  quantityBasedRetention = _messages.MessageField('QuantityBasedRetention', 7)
  timeBasedRetention = _messages.MessageField('TimeBasedRetention', 8)
  weeklySchedule = _messages.MessageField('WeeklySchedule', 9)


class Backup(_messages.Message):
  r"""Message describing Backup object

  Enums:
    DatabaseVersionValueValuesEnum: Output only. The database engine major
      version of the cluster this backup was created from. Any restored
      cluster created from this backup will have the same database version.
    StateValueValuesEnum: Output only. The current state of the backup.
    TypeValueValuesEnum: The backup type, which suggests the trigger for the
      backup.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    LabelsValue: Labels as key value pairs
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: ``` "123/environment": "production",
      "123/costCenter": "marketing" ```

  Fields:
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    clusterName: Required. The full resource name of the backup source cluster
      (e.g., projects/{project}/locations/{region}/clusters/{cluster_id}).
    clusterUid: Output only. The system-generated UID of the cluster which was
      used to create this resource.
    createCompletionTime: Output only. Timestamp when the resource finished
      being created.
    createTime: Output only. Create time stamp
    databaseVersion: Output only. The database engine major version of the
      cluster this backup was created from. Any restored cluster created from
      this backup will have the same database version.
    deleteTime: Output only. Delete time stamp
    description: User-provided description of the backup.
    displayName: User-settable and human-readable display name for the Backup.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the backup with a customer-managed encryption key (CMEK). When
      this field is not specified, the backup will then use default encryption
      scheme to protect the user data.
    encryptionInfo: Output only. The encryption information for the backup.
    enforcedRetention: If true, this backup has enforced_retention set and
      cannot be deleted unless it expires (or as part of project deletion).
    etag: For Resource freshness validation (https://google.aip.dev/154)
    expiryQuantity: Output only. The QuantityBasedExpiry of the backup,
      specified by the backup's retention policy. Once the expiry quantity is
      over retention, the backup is eligible to be garbage collected.
    expiryTime: Output only. The time at which after the backup is eligible to
      be garbage collected. It is the duration specified by the backup's
      retention policy, added to the backup's create_time.
    labels: Labels as key value pairs
    name: Output only. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id} where the
      cluster and backup ID segments should satisfy the regex expression
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase
      letters, numbers, and dashes, starting with a letter, and ending with a
      letter or number. For more details see https://google.aip.dev/122. The
      prefix of the backup resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation), if true, indicates that the
      service is actively updating the resource. This can happen due to user-
      triggered updates or system actions like failover or maintenance.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    sizeBytes: Output only. The size of the backup in bytes.
    state: Output only. The current state of the backup.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: ``` "123/environment": "production",
      "123/costCenter": "marketing" ```
    type: The backup type, which suggests the trigger for the backup.
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updateTime: Output only. Update time stamp Users should not infer any
      meaning from this field. Its value is generally unrelated to the timing
      of the backup creation operation.
  """

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""Output only. The database engine major version of the cluster this
    backup was created from. Any restored cluster created from this backup
    will have the same database version.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state of the backup is unknown.
      READY: The backup is ready.
      CREATING: The backup is creating.
      FAILED: The backup failed.
      DELETING: The backup is being deleted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    FAILED = 3
    DELETING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""The backup type, which suggests the trigger for the backup.

    Values:
      TYPE_UNSPECIFIED: Backup Type is unknown.
      ON_DEMAND: ON_DEMAND backups that were triggered by the customer (e.g.,
        not AUTOMATED).
      AUTOMATED: AUTOMATED backups triggered by the automated backups
        scheduler pursuant to an automated backup policy.
      CONTINUOUS: CONTINUOUS backups triggered by the automated backups
        scheduler due to a continuous backup policy.
    """
    TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    AUTOMATED = 2
    CONTINUOUS = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: ``` "123/environment": "production",
    "123/costCenter": "marketing" ```

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  clusterName = _messages.StringField(2)
  clusterUid = _messages.StringField(3)
  createCompletionTime = _messages.StringField(4)
  createTime = _messages.StringField(5)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 6)
  deleteTime = _messages.StringField(7)
  description = _messages.StringField(8)
  displayName = _messages.StringField(9)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 10)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 11)
  enforcedRetention = _messages.BooleanField(12)
  etag = _messages.StringField(13)
  expiryQuantity = _messages.MessageField('QuantityBasedExpiry', 14)
  expiryTime = _messages.StringField(15)
  labels = _messages.MessageField('LabelsValue', 16)
  name = _messages.StringField(17)
  reconciling = _messages.BooleanField(18)
  satisfiesPzi = _messages.BooleanField(19)
  satisfiesPzs = _messages.BooleanField(20)
  sizeBytes = _messages.IntegerField(21)
  state = _messages.EnumField('StateValueValuesEnum', 22)
  tags = _messages.MessageField('TagsValue', 23)
  type = _messages.EnumField('TypeValueValuesEnum', 24)
  uid = _messages.StringField(25)
  updateTime = _messages.StringField(26)


class BackupDrBackupSource(_messages.Message):
  r"""Message describing a BackupDrBackupSource.

  Fields:
    backup: Required. The name of the backup resource with the format: * proje
      cts/{project}/locations/{location}/backupVaults/{backupvault_id}/dataSou
      rces/{datasource_id}/backups/{backup_id}
  """

  backup = _messages.StringField(1)


class BackupDrPitrSource(_messages.Message):
  r"""Message describing a BackupDrPitrSource.

  Fields:
    dataSource: Required. The name of the backup resource with the format: * p
      rojects/{project}/locations/{location}/backupVaults/{backupvault_id}/dat
      aSources/{datasource_id}
    pointInTime: Required. The point in time to restore to.
  """

  dataSource = _messages.StringField(1)
  pointInTime = _messages.StringField(2)


class BackupSource(_messages.Message):
  r"""Message describing a BackupSource.

  Fields:
    backupName: Required. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id}
    backupUid: Output only. The system-generated UID of the backup which was
      used to create this resource. The UID is generated when the backup is
      created, and it is retained until the backup is deleted.
  """

  backupName = _messages.StringField(1)
  backupUid = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClientConnectionConfig(_messages.Message):
  r"""Client connection configuration

  Fields:
    requireConnectors: Optional. Configuration to enforce connectors only (ex:
      AuthProxy) connections to the database.
    sslConfig: Optional. SSL configuration option for this instance.
  """

  requireConnectors = _messages.BooleanField(1)
  sslConfig = _messages.MessageField('SslConfig', 2)


class CloudControl2SharedOperationsReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class CloudSQLBackupRunSource(_messages.Message):
  r"""The source CloudSQL backup resource.

  Fields:
    backupRunId: Required. The CloudSQL backup run ID.
    instanceId: Required. The CloudSQL instance ID.
    project: The project ID of the source CloudSQL instance. This should be
      the same as the AlloyDB cluster's project.
  """

  backupRunId = _messages.IntegerField(1)
  instanceId = _messages.StringField(2)
  project = _messages.StringField(3)


class Cluster(_messages.Message):
  r"""A cluster is a collection of regional AlloyDB resources. It can include
  a primary instance and one or more read pool instances. All cluster
  resources share a storage layer, which scales as needed.

  Enums:
    ClusterTypeValueValuesEnum: Output only. The type of the cluster. This is
      an output-only field and it's populated at the Cluster creation time or
      the Cluster promotion time. The cluster type is determined by which RPC
      was used to create the cluster (i.e. `CreateCluster` vs.
      `CreateSecondaryCluster`
    DatabaseVersionValueValuesEnum: Optional. The database engine major
      version. This is an optional field and it is populated at the Cluster
      creation time. If a database version is not supplied at cluster creation
      time, then a default database version will be used.
    StateValueValuesEnum: Output only. The current serving state of the
      cluster.
    SubscriptionTypeValueValuesEnum: Optional. Subscription type of the
      cluster.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    LabelsValue: Labels as key value pairs
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: ``` "123/environment": "production",
      "123/costCenter": "marketing" ```

  Fields:
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    automatedBackupPolicy: The automated backup policy for this cluster. If no
      policy is provided then the default policy will be used. If backups are
      supported for the cluster, the default policy takes one backup a day,
      has a backup window of 1 hour, and retains backups for 14 days. For more
      information on the defaults, consult the documentation for the message
      type.
    backupSource: Output only. Cluster created from backup.
    cloudsqlBackupRunSource: Output only. Cluster created from CloudSQL
      snapshot.
    clusterType: Output only. The type of the cluster. This is an output-only
      field and it's populated at the Cluster creation time or the Cluster
      promotion time. The cluster type is determined by which RPC was used to
      create the cluster (i.e. `CreateCluster` vs. `CreateSecondaryCluster`
    continuousBackupConfig: Optional. Continuous backup configuration for this
      cluster.
    continuousBackupInfo: Output only. Continuous backup properties for this
      cluster.
    createTime: Output only. Create time stamp
    databaseVersion: Optional. The database engine major version. This is an
      optional field and it is populated at the Cluster creation time. If a
      database version is not supplied at cluster creation time, then a
      default database version will be used.
    deleteTime: Output only. Delete time stamp
    displayName: User-settable and human-readable display name for the
      Cluster.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the data disks and other persistent data resources of a cluster
      with a customer-managed encryption key (CMEK). When this field is not
      specified, the cluster will then use default encryption scheme to
      protect the user data.
    encryptionInfo: Output only. The encryption information for the cluster.
    etag: For Resource freshness validation (https://google.aip.dev/154)
    geminiConfig: Optional. Deprecated and unused. This field will be removed
      in the near future.
    initialUser: Input only. Initial user to setup during cluster creation.
      Required. If used in `RestoreCluster` this is ignored.
    labels: Labels as key value pairs
    maintenanceSchedule: Output only. The maintenance schedule for the
      cluster, generated for a specific rollout if a maintenance window is
      set.
    maintenanceUpdatePolicy: Optional. The maintenance update policy
      determines when to allow or deny updates.
    migrationSource: Output only. Cluster created via DMS migration.
    name: Output only. The name of the cluster resource with the format: *
      projects/{project}/locations/{region}/clusters/{cluster_id} where the
      cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For
      more details see https://google.aip.dev/122. The prefix of the cluster
      resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    network: Required. The resource link for the VPC network in which cluster
      resources are created and from which they are accessible via Private IP.
      The network must belong to the same project as the cluster. It is
      specified in the form:
      `projects/{project}/global/networks/{network_id}`. This is required to
      create a cluster. Deprecated, use network_config.network instead.
    networkConfig: A NetworkConfig attribute.
    primaryConfig: Output only. Cross Region replication config specific to
      PRIMARY cluster.
    pscConfig: Optional. The configuration for Private Service Connect (PSC)
      for the cluster.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Cluster does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    secondaryConfig: Cross Region replication config specific to SECONDARY
      cluster.
    sslConfig: SSL configuration for this AlloyDB cluster.
    state: Output only. The current serving state of the cluster.
    subscriptionType: Optional. Subscription type of the cluster.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: ``` "123/environment": "production",
      "123/costCenter": "marketing" ```
    trialMetadata: Output only. Metadata for free trial clusters
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updateTime: Output only. Update time stamp
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the cluster. This is an output-only field and
    it's populated at the Cluster creation time or the Cluster promotion time.
    The cluster type is determined by which RPC was used to create the cluster
    (i.e. `CreateCluster` vs. `CreateSecondaryCluster`

    Values:
      CLUSTER_TYPE_UNSPECIFIED: The type of the cluster is unknown.
      PRIMARY: Primary cluster that support read and write operations.
      SECONDARY: Secondary cluster that is replicating from another region.
        This only supports read.
    """
    CLUSTER_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    SECONDARY = 2

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""Optional. The database engine major version. This is an optional field
    and it is populated at the Cluster creation time. If a database version is
    not supplied at cluster creation time, then a default database version
    will be used.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current serving state of the cluster.

    Values:
      STATE_UNSPECIFIED: The state of the cluster is unknown.
      READY: The cluster is active and running.
      STOPPED: The cluster is stopped. All instances in the cluster are
        stopped. Customers can start a stopped cluster at any point and all
        their instances will come back to life with same names and IP
        resources. In this state, customer pays for storage. Associated
        backups could also be present in a stopped cluster.
      EMPTY: The cluster is empty and has no associated resources. All
        instances, associated storage and backups have been deleted.
      CREATING: The cluster is being created.
      DELETING: The cluster is being deleted.
      FAILED: The creation of the cluster failed.
      BOOTSTRAPPING: The cluster is bootstrapping with data from some other
        source. Direct mutations to the cluster (e.g. adding read pool) are
        not allowed.
      MAINTENANCE: The cluster is under maintenance. AlloyDB regularly
        performs maintenance and upgrades on customer clusters. Updates on the
        cluster are not allowed while the cluster is in this state.
      PROMOTING: The cluster is being promoted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    STOPPED = 2
    EMPTY = 3
    CREATING = 4
    DELETING = 5
    FAILED = 6
    BOOTSTRAPPING = 7
    MAINTENANCE = 8
    PROMOTING = 9

  class SubscriptionTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Subscription type of the cluster.

    Values:
      SUBSCRIPTION_TYPE_UNSPECIFIED: This is an unknown subscription type. By
        default, the subscription type is STANDARD.
      STANDARD: Standard subscription.
      TRIAL: Trial subscription.
    """
    SUBSCRIPTION_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    TRIAL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: ``` "123/environment": "production",
    "123/costCenter": "marketing" ```

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  automatedBackupPolicy = _messages.MessageField('AutomatedBackupPolicy', 2)
  backupSource = _messages.MessageField('BackupSource', 3)
  cloudsqlBackupRunSource = _messages.MessageField('CloudSQLBackupRunSource', 4)
  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 5)
  continuousBackupConfig = _messages.MessageField('ContinuousBackupConfig', 6)
  continuousBackupInfo = _messages.MessageField('ContinuousBackupInfo', 7)
  createTime = _messages.StringField(8)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 9)
  deleteTime = _messages.StringField(10)
  displayName = _messages.StringField(11)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 12)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 13)
  etag = _messages.StringField(14)
  geminiConfig = _messages.MessageField('GeminiClusterConfig', 15)
  initialUser = _messages.MessageField('UserPassword', 16)
  labels = _messages.MessageField('LabelsValue', 17)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 18)
  maintenanceUpdatePolicy = _messages.MessageField('MaintenanceUpdatePolicy', 19)
  migrationSource = _messages.MessageField('MigrationSource', 20)
  name = _messages.StringField(21)
  network = _messages.StringField(22)
  networkConfig = _messages.MessageField('NetworkConfig', 23)
  primaryConfig = _messages.MessageField('PrimaryConfig', 24)
  pscConfig = _messages.MessageField('PscConfig', 25)
  reconciling = _messages.BooleanField(26)
  satisfiesPzi = _messages.BooleanField(27)
  satisfiesPzs = _messages.BooleanField(28)
  secondaryConfig = _messages.MessageField('SecondaryConfig', 29)
  sslConfig = _messages.MessageField('SslConfig', 30)
  state = _messages.EnumField('StateValueValuesEnum', 31)
  subscriptionType = _messages.EnumField('SubscriptionTypeValueValuesEnum', 32)
  tags = _messages.MessageField('TagsValue', 33)
  trialMetadata = _messages.MessageField('TrialMetadata', 34)
  uid = _messages.StringField(35)
  updateTime = _messages.StringField(36)


class ClusterUpgradeDetails(_messages.Message):
  r"""Upgrade details of a cluster. This cluster can be primary or secondary.

  Enums:
    ClusterTypeValueValuesEnum: Cluster type which can either be primary or
      secondary.
    DatabaseVersionValueValuesEnum: Database version of the cluster after the
      upgrade operation. This will be the target version if the upgrade was
      successful otherwise it remains the same as that before the upgrade
      operation.
    UpgradeStatusValueValuesEnum: Upgrade status of the cluster.

  Fields:
    clusterType: Cluster type which can either be primary or secondary.
    databaseVersion: Database version of the cluster after the upgrade
      operation. This will be the target version if the upgrade was successful
      otherwise it remains the same as that before the upgrade operation.
    instanceUpgradeDetails: Upgrade details of the instances directly
      associated with this cluster.
    name: Normalized name of the cluster
    stageInfo: Array containing stage info associated with this cluster.
    upgradeStatus: Upgrade status of the cluster.
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Cluster type which can either be primary or secondary.

    Values:
      CLUSTER_TYPE_UNSPECIFIED: The type of the cluster is unknown.
      PRIMARY: Primary cluster that support read and write operations.
      SECONDARY: Secondary cluster that is replicating from another region.
        This only supports read.
    """
    CLUSTER_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    SECONDARY = 2

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""Database version of the cluster after the upgrade operation. This will
    be the target version if the upgrade was successful otherwise it remains
    the same as that before the upgrade operation.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  class UpgradeStatusValueValuesEnum(_messages.Enum):
    r"""Upgrade status of the cluster.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 1)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 2)
  instanceUpgradeDetails = _messages.MessageField('InstanceUpgradeDetails', 3, repeated=True)
  name = _messages.StringField(4)
  stageInfo = _messages.MessageField('StageInfo', 5, repeated=True)
  upgradeStatus = _messages.EnumField('UpgradeStatusValueValuesEnum', 6)


class ConnectionInfo(_messages.Message):
  r"""ConnectionInfo singleton resource. https://google.aip.dev/156

  Fields:
    instanceUid: Output only. The unique ID of the Instance.
    ipAddress: Output only. The private network IP address for the Instance.
      This is the default IP for the instance and is always created (even if
      enable_public_ip is set). This is the connection endpoint for an end-
      user application.
    name: The name of the ConnectionInfo singleton resource, e.g.: projects/{p
      roject}/locations/{location}/clusters/*/instances/*/connectionInfo This
      field currently has no semantic meaning.
    pemCertificateChain: Output only. The pem-encoded chain that may be used
      to verify the X.509 certificate. Expected to be in issuer-to-root order
      according to RFC 5246.
    pscDnsName: Output only. The DNS name to use with PSC for the Instance.
    publicIpAddress: Output only. The public IP addresses for the Instance.
      This is available ONLY when enable_public_ip is set. This is the
      connection endpoint for an end-user application.
  """

  instanceUid = _messages.StringField(1)
  ipAddress = _messages.StringField(2)
  name = _messages.StringField(3)
  pemCertificateChain = _messages.StringField(4, repeated=True)
  pscDnsName = _messages.StringField(5)
  publicIpAddress = _messages.StringField(6)


class ConnectionPoolConfig(_messages.Message):
  r"""Configuration for Managed Connection Pool (MCP).

  Enums:
    PoolModeValueValuesEnum: Optional. Deprecated. Use 'flags' instead. The
      pool mode. Defaults to `POOL_MODE_TRANSACTION`.

  Messages:
    FlagsValue: Optional. Connection Pool flags, as a list of "key": "value"
      pairs.

  Fields:
    defaultPoolSize: Optional. Deprecated. Use 'flags' instead. The default
      pool size. Defaults to 20.
    enable: Optional. Deprecated; Prefer 'enabled' as this will be removed
      soon.
    enabled: Optional. Whether to enable Managed Connection Pool (MCP).
    flags: Optional. Connection Pool flags, as a list of "key": "value" pairs.
    ignoreStartupParameters: Optional. Deprecated. Use 'flags' instead. The
      list of startup parameters to ignore. Defaults to ["extra_float_digits"]
    maxClientConn: Optional. Deprecated. Use 'flags' instead. The maximum
      number of client connections allowed.
    maxPreparedStatements: Optional. Deprecated. Use 'flags' instead. The
      maximum number of prepared statements allowed. MCP makes sure that any
      statement prepared by a client, up to this limit, is available on the
      backing server connection in transaction and statement pooling mode.
      Even if the statement was originally prepared on another server
      connection. Defaults to 0.
    minPoolSize: Optional. Deprecated. Use 'flags' instead. The minimum pool
      size. Defaults to 0.
    poolMode: Optional. Deprecated. Use 'flags' instead. The pool mode.
      Defaults to `POOL_MODE_TRANSACTION`.
    queryWaitTimeout: Optional. Deprecated. Use 'flags' instead. The maximum
      number of seconds queries are allowed to spend waiting for execution. If
      the query is not assigned to a server during that time, the client is
      disconnected. 0 disables.
    serverIdleTimeout: Optional. Deprecated. Use 'flags' instead. The maximum
      number of seconds a server is allowed to be idle before it is
      disconnected. 0 disables.
    statsUsers: Optional. Deprecated. Use 'flags' instead. The list of users
      that are allowed to connect to the MCP stats console. The users must
      exist in the database.
  """

  class PoolModeValueValuesEnum(_messages.Enum):
    r"""Optional. Deprecated. Use 'flags' instead. The pool mode. Defaults to
    `POOL_MODE_TRANSACTION`.

    Values:
      POOL_MODE_UNSPECIFIED: The pool mode is not specified. Defaults to
        `POOL_MODE_TRANSACTION`.
      POOL_MODE_SESSION: Server is released back to pool after a client
        disconnects.
      POOL_MODE_TRANSACTION: Server is released back to pool after a
        transaction finishes.
    """
    POOL_MODE_UNSPECIFIED = 0
    POOL_MODE_SESSION = 1
    POOL_MODE_TRANSACTION = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FlagsValue(_messages.Message):
    r"""Optional. Connection Pool flags, as a list of "key": "value" pairs.

    Messages:
      AdditionalProperty: An additional property for a FlagsValue object.

    Fields:
      additionalProperties: Additional properties of type FlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultPoolSize = _messages.StringField(1)
  enable = _messages.BooleanField(2)
  enabled = _messages.BooleanField(3)
  flags = _messages.MessageField('FlagsValue', 4)
  ignoreStartupParameters = _messages.StringField(5, repeated=True)
  maxClientConn = _messages.StringField(6)
  maxPreparedStatements = _messages.StringField(7)
  minPoolSize = _messages.StringField(8)
  poolMode = _messages.EnumField('PoolModeValueValuesEnum', 9)
  queryWaitTimeout = _messages.StringField(10)
  serverIdleTimeout = _messages.StringField(11)
  statsUsers = _messages.StringField(12, repeated=True)


class ContinuousBackupConfig(_messages.Message):
  r"""ContinuousBackupConfig describes the continuous backups recovery
  configurations of a cluster.

  Fields:
    enabled: Whether ContinuousBackup is enabled.
    encryptionConfig: The encryption config can be specified to encrypt the
      backups with a customer-managed encryption key (CMEK). When this field
      is not specified, the backup will use the cluster's encryption config.
    enforcedRetention: If true, backups created by this config would have
      `enforced_retention` set and cannot be deleted unless they expire (or as
      part of project deletion).
    recoveryWindowDays: The number of days that are eligible to restore from
      using PITR. To support the entire recovery window, backups and logs are
      retained for one day more than the recovery window. If not set, defaults
      to 14 days.
  """

  enabled = _messages.BooleanField(1)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 2)
  enforcedRetention = _messages.BooleanField(3)
  recoveryWindowDays = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ContinuousBackupInfo(_messages.Message):
  r"""ContinuousBackupInfo describes the continuous backup properties of a
  cluster.

  Enums:
    ScheduleValueListEntryValuesEnum:

  Fields:
    earliestRestorableTime: Output only. The earliest restorable time that can
      be restored to. If continuous backups and recovery was recently enabled,
      the earliest restorable time is the creation time of the earliest
      eligible backup within this cluster's continuous backup recovery window.
      After a cluster has had continuous backups enabled for the duration of
      its recovery window, the earliest restorable time becomes "now minus the
      recovery window". For example, assuming a point in time recovery is
      attempted at 04/16/2025 3:23:00PM with a 14d recovery window, the
      earliest restorable time would be 04/02/2025 3:23:00PM. This field is
      only visible if the CLUSTER_VIEW_CONTINUOUS_BACKUP cluster view is
      provided.
    enabledTime: Output only. When ContinuousBackup was most recently enabled.
      Set to null if ContinuousBackup is not enabled.
    encryptionInfo: Output only. The encryption information for the WALs and
      backups required for ContinuousBackup.
    schedule: Output only. Days of the week on which a continuous backup is
      taken.
  """

  class ScheduleValueListEntryValuesEnum(_messages.Enum):
    r"""ScheduleValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  earliestRestorableTime = _messages.StringField(1)
  enabledTime = _messages.StringField(2)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 3)
  schedule = _messages.EnumField('ScheduleValueListEntryValuesEnum', 4, repeated=True)


class ContinuousBackupSource(_messages.Message):
  r"""Message describing a ContinuousBackupSource.

  Fields:
    cluster: Required. The source cluster from which to restore. This cluster
      must have continuous backup enabled for this operation to succeed. For
      the required format, see the comment on the Cluster.name field.
    pointInTime: Required. The point in time to restore to.
  """

  cluster = _messages.StringField(1)
  pointInTime = _messages.StringField(2)


class CsvExportOptions(_messages.Message):
  r"""Options for exporting data in CSV format.

  Fields:
    escapeCharacter: Optional. Specifies the character that should appear
      before a data character that needs to be escaped. The default is the
      same as quote character. The value of this argument has to be a
      character in Hex ASCII Code.
    fieldDelimiter: Optional. Specifies the character that separates columns
      within each row (line) of the file. The default is comma. The value of
      this argument has to be a character in Hex ASCII Code.
    quoteCharacter: Optional. Specifies the quoting character to be used when
      a data value is quoted. The default is double-quote. The value of this
      argument has to be a character in Hex ASCII Code.
    selectQuery: Required. The SELECT query used to extract the data.
  """

  escapeCharacter = _messages.StringField(1)
  fieldDelimiter = _messages.StringField(2)
  quoteCharacter = _messages.StringField(3)
  selectQuery = _messages.StringField(4)


class CsvImportOptions(_messages.Message):
  r"""Options for importing data in CSV format.

  Fields:
    columns: Optional. The columns to which CSV data is imported. If not
      specified, all columns of the database table are loaded with CSV data.
    escapeCharacter: Optional. Specifies the character that should appear
      before a data character that needs to be escaped. The default is same as
      quote character. The value of this argument has to be a character in Hex
      ASCII Code.
    fieldDelimiter: Optional. Specifies the character that separates columns
      within each row (line) of the file. The default is comma. The value of
      this argument has to be a character in Hex ASCII Code.
    quoteCharacter: Optional. Specifies the quoting character to be used when
      a data value is quoted. The default is double-quote. The value of this
      argument has to be a character in Hex ASCII Code.
    table: Required. The database table to import CSV file into.
  """

  columns = _messages.StringField(1, repeated=True)
  escapeCharacter = _messages.StringField(2)
  fieldDelimiter = _messages.StringField(3)
  quoteCharacter = _messages.StringField(4)
  table = _messages.StringField(5)


class DenyMaintenancePeriod(_messages.Message):
  r"""DenyMaintenancePeriod definition. Excepting emergencies, maintenance
  will not be scheduled to start within this deny period. The start_date must
  be less than the end_date.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values OR * A month and day value, with a zero year
      for recurring
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values OR * A month and day value, with a zero
      year for recurring
    time: Time in UTC when the deny period starts on start_date and ends on
      end_date. This can be: * Full time OR * All zeros for 00:00:00 UTC
  """

  endDate = _messages.MessageField('GoogleTypeDate', 1)
  startDate = _messages.MessageField('GoogleTypeDate', 2)
  time = _messages.MessageField('GoogleTypeTimeOfDay', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""EncryptionConfig describes the encryption config of a cluster or a
  backup that is encrypted with a CMEK (customer-managed encryption key).

  Fields:
    kmsKeyName: The fully-qualified resource name of the KMS key. Each Cloud
      KMS key is regionalized and has the following format: projects/[PROJECT]
      /locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
  """

  kmsKeyName = _messages.StringField(1)


class EncryptionInfo(_messages.Message):
  r"""EncryptionInfo describes the encryption information of a cluster or a
  backup.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Type of encryption.

  Fields:
    encryptionType: Output only. Type of encryption.
    kmsKeyVersions: Output only. Cloud KMS key versions that are being used to
      protect the database or the backup.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of encryption.

    Values:
      TYPE_UNSPECIFIED: Encryption type not specified. Defaults to
        GOOGLE_DEFAULT_ENCRYPTION.
      GOOGLE_DEFAULT_ENCRYPTION: The data is encrypted at rest with a key that
        is fully managed by Google. No key version will be populated. This is
        the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data is encrypted at rest with a key
        that is managed by the customer. KMS key versions will be populated.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyVersions = _messages.StringField(2, repeated=True)


class ExportClusterRequest(_messages.Message):
  r"""Export cluster request.

  Fields:
    csvExportOptions: Options for exporting data in CSV format. Required field
      to be set for CSV file type.
    database: Required. Name of the database where the export command will be
      executed. Note - Value provided should be the same as expected from
      `SELECT current_database();` and NOT as a resource reference.
    gcsDestination: Required. Option to export data to cloud storage.
    sqlExportOptions: Options for exporting data in SQL format. Required field
      to be set for SQL file type.
  """

  csvExportOptions = _messages.MessageField('CsvExportOptions', 1)
  database = _messages.StringField(2)
  gcsDestination = _messages.MessageField('GcsDestination', 3)
  sqlExportOptions = _messages.MessageField('SqlExportOptions', 4)


class FailoverInstanceRequest(_messages.Message):
  r"""Message for triggering failover on an Instance

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  requestId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class GCAInstanceConfig(_messages.Message):
  r"""Instance level configuration parameters related to the Gemini Cloud
  Assist product.

  Enums:
    GcaEntitlementValueValuesEnum: Output only. Represents the GCA entitlement
      state of the instance.

  Fields:
    gcaEntitlement: Output only. Represents the GCA entitlement state of the
      instance.
  """

  class GcaEntitlementValueValuesEnum(_messages.Enum):
    r"""Output only. Represents the GCA entitlement state of the instance.

    Values:
      GCA_ENTITLEMENT_TYPE_UNSPECIFIED: No GCA entitlement is assigned.
      GCA_STANDARD: The resource is entitled to the GCA Standard Tier.
    """
    GCA_ENTITLEMENT_TYPE_UNSPECIFIED = 0
    GCA_STANDARD = 1

  gcaEntitlement = _messages.EnumField('GcaEntitlementValueValuesEnum', 1)


class GcsDestination(_messages.Message):
  r"""Destination for Export. Export will be done to cloud storage.

  Fields:
    uri: Required. The path to the file in Google Cloud Storage where the
      export will be stored. The URI is in the form
      `gs://bucketName/fileName`.
  """

  uri = _messages.StringField(1)


class GeminiClusterConfig(_messages.Message):
  r"""Deprecated and unused. This field will be removed in the near future.

  Fields:
    entitled: Output only. Deprecated and unused. This field will be removed
      in the near future.
  """

  entitled = _messages.BooleanField(1)


class GeminiInstanceConfig(_messages.Message):
  r"""Deprecated and unused. This field will be removed in the near future.

  Fields:
    entitled: Output only. Deprecated and unused. This field will be removed
      in the near future.
  """

  entitled = _messages.BooleanField(1)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleTypeDate(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleTypeTimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ImportClusterRequest(_messages.Message):
  r"""Import cluster request.

  Fields:
    csvImportOptions: Options for importing data in CSV format.
    database: Optional. Name of the database to which the import will be done.
      For import from SQL file, this is required only if the file does not
      specify a database. Note - Value provided should be the same as expected
      from `SELECT current_database();` and NOT as a resource reference.
    gcsUri: Required. The path to the file in Google Cloud Storage where the
      source file for import will be stored. The URI is in the form
      `gs://bucketName/fileName`.
    password: Optional. The database native user's password.
    sqlImportOptions: Options for importing data in SQL format.
    user: Optional. Database user to be used for importing the data. Note -
      Value provided should be the same as expected from `SELECT
      current_user;` and NOT as a resource reference.
    usernameOptional: Optional. If true, does not require username to be
      provided in the request and does not use password-based authentication
      to connect to the database. If false, username and password are required
      to authenticate to the database.
  """

  csvImportOptions = _messages.MessageField('CsvImportOptions', 1)
  database = _messages.StringField(2)
  gcsUri = _messages.StringField(3)
  password = _messages.StringField(4)
  sqlImportOptions = _messages.MessageField('SqlImportOptions', 5)
  user = _messages.StringField(6)
  usernameOptional = _messages.BooleanField(7)


class InjectFaultRequest(_messages.Message):
  r"""Message for triggering fault injection on an instance

  Enums:
    FaultTypeValueValuesEnum: Required. The type of fault to be injected in an
      instance.

  Fields:
    faultType: Required. The type of fault to be injected in an instance.
    nodeIds: Optional. Full name of the nodes as obtained from
      INSTANCE_VIEW_FULL to subject the fault injection upon. Applicable only
      to read instances, where at least 1 node should be passed.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  class FaultTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of fault to be injected in an instance.

    Values:
      FAULT_TYPE_UNSPECIFIED: The fault type is unknown.
      STOP_VM: Stop the VM
    """
    FAULT_TYPE_UNSPECIFIED = 0
    STOP_VM = 1

  faultType = _messages.EnumField('FaultTypeValueValuesEnum', 1)
  nodeIds = _messages.StringField(2, repeated=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class Instance(_messages.Message):
  r"""An Instance is a computing unit that an end customer can connect to.
  It's the main unit of computing resources in AlloyDB.

  Enums:
    ActivationPolicyValueValuesEnum: Optional. Specifies whether an instance
      needs to spin up. Once the instance is active, the activation policy can
      be updated to the `NEVER` to stop the instance. Likewise, the activation
      policy can be updated to `ALWAYS` to start the instance. There are
      restrictions around when an instance can/cannot be activated (for
      example, a read pool instance should be stopped before stopping primary
      etc.). Please refer to the API documentation for more details.
    AvailabilityTypeValueValuesEnum: Availability type of an Instance. If
      empty, defaults to REGIONAL for primary instances. For read pools,
      availability_type is always UNSPECIFIED. Instances in the read pools are
      evenly distributed across available zones within the region (i.e. read
      pools with more than one node will have a node in at least two zones).
    InstanceTypeValueValuesEnum: Required. The type of the instance. Specified
      at creation time.
    StateValueValuesEnum: Output only. The current serving state of the
      instance.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    DatabaseFlagsValue: Database flags. Set at the instance level. They are
      copied from the primary instance on secondary instance creation. Flags
      that have restrictions default to the value at primary instance on read
      instances during creation. Read instances can set new flags or override
      existing flags that are relevant for reads, for example, for enabling
      columnar cache on a read instance. Flags set on read instance might or
      might not be present on the primary instance. This is a list of "key":
      "value" pairs. "key": The name of the flag. These flags are passed at
      instance setup time, so include both server options and system variables
      for Postgres. Flags are specified with underscores, not hyphens.
      "value": The value of the flag. Booleans are set to **on** for true and
      **off** for false. This field must be omitted if the flag doesn't take a
      value.
    LabelsValue: Labels as key value pairs

  Fields:
    activationPolicy: Optional. Specifies whether an instance needs to spin
      up. Once the instance is active, the activation policy can be updated to
      the `NEVER` to stop the instance. Likewise, the activation policy can be
      updated to `ALWAYS` to start the instance. There are restrictions around
      when an instance can/cannot be activated (for example, a read pool
      instance should be stopped before stopping primary etc.). Please refer
      to the API documentation for more details.
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    availabilityType: Availability type of an Instance. If empty, defaults to
      REGIONAL for primary instances. For read pools, availability_type is
      always UNSPECIFIED. Instances in the read pools are evenly distributed
      across available zones within the region (i.e. read pools with more than
      one node will have a node in at least two zones).
    clientConnectionConfig: Optional. Client connection specific
      configurations
    connectionPoolConfig: Optional. The configuration for Managed Connection
      Pool (MCP).
    createTime: Output only. Create time stamp
    databaseFlags: Database flags. Set at the instance level. They are copied
      from the primary instance on secondary instance creation. Flags that
      have restrictions default to the value at primary instance on read
      instances during creation. Read instances can set new flags or override
      existing flags that are relevant for reads, for example, for enabling
      columnar cache on a read instance. Flags set on read instance might or
      might not be present on the primary instance. This is a list of "key":
      "value" pairs. "key": The name of the flag. These flags are passed at
      instance setup time, so include both server options and system variables
      for Postgres. Flags are specified with underscores, not hyphens.
      "value": The value of the flag. Booleans are set to **on** for true and
      **off** for false. This field must be omitted if the flag doesn't take a
      value.
    deleteTime: Output only. Delete time stamp
    displayName: User-settable and human-readable display name for the
      Instance.
    enablePublicIp: Optional. Enabling public ip for the Instance. Deprecated;
      use network_config.enable_public_ip instead.
    etag: For Resource freshness validation (https://google.aip.dev/154)
    gcaConfig: Output only. Configuration parameters related to Gemini Cloud
      Assist.
    gceZone: The Compute Engine zone that the instance should serve from, per
      https://cloud.google.com/compute/docs/regions-zones This can ONLY be
      specified for ZONAL instances. If present for a REGIONAL instance, an
      error will be thrown. If this is absent for a ZONAL instance, instance
      is created in a random zone with available capacity.
    geminiConfig: Optional. Deprecated and unused. This field will be removed
      in the near future.
    instanceType: Required. The type of the instance. Specified at creation
      time.
    ipAddress: Output only. The IP address for the Instance. This is the
      connection endpoint for an end-user application.
    labels: Labels as key value pairs
    machineConfig: Configurations for the machines that host the underlying
      database engine.
    name: Output only. The name of the instance resource with the format: * pr
      ojects/{project}/locations/{region}/clusters/{cluster_id}/instances/{ins
      tance_id} where the cluster and instance ID segments should satisfy the
      regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters
      of lowercase letters, numbers, and dashes, starting with a letter, and
      ending with a letter or number. For more details see
      https://google.aip.dev/122. The prefix of the instance resource name is
      the name of the parent resource: *
      projects/{project}/locations/{region}/clusters/{cluster_id}
    networkConfig: Optional. Instance-level network configuration.
    nodes: Output only. List of available read-only VMs in this instance,
      including the standby for a PRIMARY instance.
    observabilityConfig: Configuration for observability.
    outboundPublicIpAddresses: Output only. All outbound public IP addresses
      configured for the instance.
    pscInstanceConfig: Optional. The configuration for Private Service Connect
      (PSC) for the instance.
    publicIpAddress: Output only. The public IP addresses for the Instance.
      This is available ONLY when enable_public_ip is set. This is the
      connection endpoint for an end-user application.
    queryInsightsConfig: Configuration for query insights.
    readPoolConfig: Read pool instance configuration. This is required if the
      value of instanceType is READ_POOL.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Instance does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current serving state of the instance.
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updatePolicy: Update policy that will be applied during instance update.
      This field is not persisted when you update the instance. To use a non-
      default update policy, you must specify explicitly specify the value in
      each update request.
    updateTime: Output only. Update time stamp
    writableNode: Output only. This is set for the read-write VM of the
      PRIMARY instance only.
  """

  class ActivationPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies whether an instance needs to spin up. Once the
    instance is active, the activation policy can be updated to the `NEVER` to
    stop the instance. Likewise, the activation policy can be updated to
    `ALWAYS` to start the instance. There are restrictions around when an
    instance can/cannot be activated (for example, a read pool instance should
    be stopped before stopping primary etc.). Please refer to the API
    documentation for more details.

    Values:
      ACTIVATION_POLICY_UNSPECIFIED: The policy is not specified.
      ALWAYS: The instance is running.
      NEVER: The instance is not running.
    """
    ACTIVATION_POLICY_UNSPECIFIED = 0
    ALWAYS = 1
    NEVER = 2

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type of an Instance. If empty, defaults to REGIONAL for
    primary instances. For read pools, availability_type is always
    UNSPECIFIED. Instances in the read pools are evenly distributed across
    available zones within the region (i.e. read pools with more than one node
    will have a node in at least two zones).

    Values:
      AVAILABILITY_TYPE_UNSPECIFIED: This is an unknown Availability type.
      ZONAL: Zonal available instance.
      REGIONAL: Regional (or Highly) available instance.
    """
    AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the instance. Specified at creation time.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: The type of the instance is unknown.
      PRIMARY: PRIMARY instances support read and write operations.
      READ_POOL: READ POOL instances support read operations only. Each read
        pool instance consists of one or more homogeneous nodes. * Read pool
        of size 1 can only have zonal availability. * Read pools with node
        count of 2 or more can have regional availability (nodes are present
        in 2 or more zones in a region).
      SECONDARY: SECONDARY instances support read operations only. SECONDARY
        instance is a cross-region read replica
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    READ_POOL = 2
    SECONDARY = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current serving state of the instance.

    Values:
      STATE_UNSPECIFIED: The state of the instance is unknown.
      READY: The instance is active and running.
      STOPPED: The instance is stopped. Instance name and IP resources are
        preserved.
      CREATING: The instance is being created.
      DELETING: The instance is being deleted.
      MAINTENANCE: The instance is down for maintenance.
      FAILED: The creation of the instance failed or a fatal error occurred
        during an operation on the instance. Note: Instances in this state
        would tried to be auto-repaired. And Customers should be able to
        restart, update or delete these instances.
      BOOTSTRAPPING: Index 7 is used in the producer apis for ROLLED_BACK
        state. Keeping that index unused in case that state also needs to
        exposed via consumer apis in future. The instance has been configured
        to sync data from some other source.
      PROMOTING: The instance is being promoted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    STOPPED = 2
    CREATING = 3
    DELETING = 4
    MAINTENANCE = 5
    FAILED = 6
    BOOTSTRAPPING = 7
    PROMOTING = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DatabaseFlagsValue(_messages.Message):
    r"""Database flags. Set at the instance level. They are copied from the
    primary instance on secondary instance creation. Flags that have
    restrictions default to the value at primary instance on read instances
    during creation. Read instances can set new flags or override existing
    flags that are relevant for reads, for example, for enabling columnar
    cache on a read instance. Flags set on read instance might or might not be
    present on the primary instance. This is a list of "key": "value" pairs.
    "key": The name of the flag. These flags are passed at instance setup
    time, so include both server options and system variables for Postgres.
    Flags are specified with underscores, not hyphens. "value": The value of
    the flag. Booleans are set to **on** for true and **off** for false. This
    field must be omitted if the flag doesn't take a value.

    Messages:
      AdditionalProperty: An additional property for a DatabaseFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DatabaseFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DatabaseFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationPolicy = _messages.EnumField('ActivationPolicyValueValuesEnum', 1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 3)
  clientConnectionConfig = _messages.MessageField('ClientConnectionConfig', 4)
  connectionPoolConfig = _messages.MessageField('ConnectionPoolConfig', 5)
  createTime = _messages.StringField(6)
  databaseFlags = _messages.MessageField('DatabaseFlagsValue', 7)
  deleteTime = _messages.StringField(8)
  displayName = _messages.StringField(9)
  enablePublicIp = _messages.BooleanField(10)
  etag = _messages.StringField(11)
  gcaConfig = _messages.MessageField('GCAInstanceConfig', 12)
  gceZone = _messages.StringField(13)
  geminiConfig = _messages.MessageField('GeminiInstanceConfig', 14)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 15)
  ipAddress = _messages.StringField(16)
  labels = _messages.MessageField('LabelsValue', 17)
  machineConfig = _messages.MessageField('MachineConfig', 18)
  name = _messages.StringField(19)
  networkConfig = _messages.MessageField('InstanceNetworkConfig', 20)
  nodes = _messages.MessageField('Node', 21, repeated=True)
  observabilityConfig = _messages.MessageField('ObservabilityInstanceConfig', 22)
  outboundPublicIpAddresses = _messages.StringField(23, repeated=True)
  pscInstanceConfig = _messages.MessageField('PscInstanceConfig', 24)
  publicIpAddress = _messages.StringField(25)
  queryInsightsConfig = _messages.MessageField('QueryInsightsInstanceConfig', 26)
  readPoolConfig = _messages.MessageField('ReadPoolConfig', 27)
  reconciling = _messages.BooleanField(28)
  satisfiesPzi = _messages.BooleanField(29)
  satisfiesPzs = _messages.BooleanField(30)
  state = _messages.EnumField('StateValueValuesEnum', 31)
  uid = _messages.StringField(32)
  updatePolicy = _messages.MessageField('UpdatePolicy', 33)
  updateTime = _messages.StringField(34)
  writableNode = _messages.MessageField('Node', 35)


class InstanceNetworkConfig(_messages.Message):
  r"""Metadata related to instance-level network configuration.

  Fields:
    allocatedIpRangeOverride: Optional. Name of the allocated IP range for the
      private IP AlloyDB instance, for example: "google-managed-services-
      default". If set, the instance IPs will be created from this allocated
      range and will override the IP range used by the parent cluster. The
      range name must comply with [RFC 1035](http://go/rfc/1035).
      Specifically, the name must be 1-63 characters long and match the
      regular expression [a-z]([-a-z0-9]*[a-z0-9])?.
    authorizedExternalNetworks: Optional. A list of external network
      authorized to access this instance.
    enableOutboundPublicIp: Optional. Enabling an outbound public IP address
      to support a database server sending requests out into the internet.
    enablePublicIp: Optional. Enabling public ip for the instance.
    network: Output only. The resource link for the VPC network in which
      instance resources are created and from which they are accessible via
      Private IP. This will be the same value as the parent cluster's network.
      It is specified in the form: //
      `projects/{project_number}/global/networks/{network_id}`.
  """

  allocatedIpRangeOverride = _messages.StringField(1)
  authorizedExternalNetworks = _messages.MessageField('AuthorizedNetwork', 2, repeated=True)
  enableOutboundPublicIp = _messages.BooleanField(3)
  enablePublicIp = _messages.BooleanField(4)
  network = _messages.StringField(5)


class InstanceUpgradeDetails(_messages.Message):
  r"""Details regarding the upgrade of instaces associated with a cluster.

  Enums:
    InstanceTypeValueValuesEnum: Instance type.
    UpgradeStatusValueValuesEnum: Upgrade status of the instance.

  Fields:
    instanceType: Instance type.
    name: Normalized name of the instance.
    upgradeStatus: Upgrade status of the instance.
  """

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""Instance type.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: The type of the instance is unknown.
      PRIMARY: PRIMARY instances support read and write operations.
      READ_POOL: READ POOL instances support read operations only. Each read
        pool instance consists of one or more homogeneous nodes. * Read pool
        of size 1 can only have zonal availability. * Read pools with node
        count of 2 or more can have regional availability (nodes are present
        in 2 or more zones in a region).
      SECONDARY: SECONDARY instances support read operations only. SECONDARY
        instance is a cross-region read replica
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    READ_POOL = 2
    SECONDARY = 3

  class UpgradeStatusValueValuesEnum(_messages.Enum):
    r"""Upgrade status of the instance.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 1)
  name = _messages.StringField(2)
  upgradeStatus = _messages.EnumField('UpgradeStatusValueValuesEnum', 3)


class IntegerRestrictions(_messages.Message):
  r"""Restrictions on INTEGER type values.

  Fields:
    maxValue: The maximum value that can be specified, if applicable.
    minValue: The minimum value that can be specified, if applicable.
  """

  maxValue = _messages.IntegerField(1)
  minValue = _messages.IntegerField(2)


class ListBackupsResponse(_messages.Message):
  r"""Message for response to listing Backups

  Fields:
    backups: The list of Backup
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListClustersResponse(_messages.Message):
  r"""Message for response to listing Clusters

  Fields:
    clusters: The list of Cluster
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Message for response to listing Instances

  Fields:
    instances: The list of Instance
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSupportedDatabaseFlagsResponse(_messages.Message):
  r"""Message for response to listing SupportedDatabaseFlags.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    supportedDatabaseFlags: The list of SupportedDatabaseFlags.
  """

  nextPageToken = _messages.StringField(1)
  supportedDatabaseFlags = _messages.MessageField('SupportedDatabaseFlag', 2, repeated=True)


class ListUsersResponse(_messages.Message):
  r"""Message for response to listing Users

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    users: The list of User
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  users = _messages.MessageField('User', 3, repeated=True)


class MachineConfig(_messages.Message):
  r"""MachineConfig describes the configuration of a machine.

  Fields:
    cpuCount: The number of CPU's in the VM instance.
    machineType: Machine type of the VM instance. E.g. "n2-highmem-4",
      "n2-highmem-8", "c4a-highmem-4-lssd". cpu_count must match the number of
      vCPUs in the machine type.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  machineType = _messages.StringField(2)


class MaintenanceSchedule(_messages.Message):
  r"""MaintenanceSchedule stores the maintenance schedule generated from the
  MaintenanceUpdatePolicy, once a maintenance rollout is triggered, if
  MaintenanceWindow is set, and if there is no conflicting DenyPeriod. The
  schedule is cleared once the update takes place. This field cannot be
  manually changed; modify the MaintenanceUpdatePolicy instead.

  Fields:
    startTime: Output only. The scheduled start time for the maintenance.
  """

  startTime = _messages.StringField(1)


class MaintenanceUpdatePolicy(_messages.Message):
  r"""MaintenanceUpdatePolicy defines the policy for system updates.

  Fields:
    denyMaintenancePeriods: Periods to deny maintenance. Currently limited to
      1.
    maintenanceWindows: Preferred windows to perform maintenance. Currently
      limited to 1.
  """

  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 1, repeated=True)
  maintenanceWindows = _messages.MessageField('MaintenanceWindow', 2, repeated=True)


class MaintenanceWindow(_messages.Message):
  r"""MaintenanceWindow specifies a preferred day and time for maintenance.

  Enums:
    DayValueValuesEnum: Preferred day of the week for maintenance, e.g.
      MONDAY, TUESDAY, etc.

  Fields:
    day: Preferred day of the week for maintenance, e.g. MONDAY, TUESDAY, etc.
    startTime: Preferred time to start the maintenance operation on the
      specified day. Maintenance will start within 1 hour of this time.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Preferred day of the week for maintenance, e.g. MONDAY, TUESDAY, etc.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  startTime = _messages.MessageField('GoogleTypeTimeOfDay', 2)


class MigrationSource(_messages.Message):
  r"""Subset of the source instance configuration that is available when
  reading the cluster resource.

  Enums:
    SourceTypeValueValuesEnum: Output only. Type of migration source.

  Fields:
    hostPort: Output only. The host and port of the on-premises instance in
      host:port format
    referenceId: Output only. Place holder for the external source
      identifier(e.g DMS job name) that created the cluster.
    sourceType: Output only. Type of migration source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of migration source.

    Values:
      MIGRATION_SOURCE_TYPE_UNSPECIFIED: Migration source is unknown.
      DMS: DMS source means the cluster was created via DMS migration job.
    """
    MIGRATION_SOURCE_TYPE_UNSPECIFIED = 0
    DMS = 1

  hostPort = _messages.StringField(1)
  referenceId = _messages.StringField(2)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 3)


class NetworkConfig(_messages.Message):
  r"""Metadata related to network configuration.

  Fields:
    allocatedIpRange: Optional. Name of the allocated IP range for the private
      IP AlloyDB cluster, for example: "google-managed-services-default". If
      set, the instance IPs for this cluster will be created in the allocated
      range. The range name must comply with RFC 1035. Specifically, the name
      must be 1-63 characters long and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])?`. Field name is intended to be consistent
      with Cloud SQL.
    network: Optional. The resource link for the VPC network in which cluster
      resources are created and from which they are accessible via Private IP.
      The network must belong to the same project as the cluster. It is
      specified in the form:
      `projects/{project_number}/global/networks/{network_id}`. This is
      required to create a cluster.
  """

  allocatedIpRange = _messages.StringField(1)
  network = _messages.StringField(2)


class Node(_messages.Message):
  r"""Details of a single node in the instance. Nodes in an AlloyDB instance
  are ephemeral, they can change during update, failover, autohealing and
  resize operations.

  Fields:
    id: Output only. The identifier of the VM e.g. "test-read-0601-407e52be-
      ms3l".
    ip: Output only. The private IP address of the VM e.g. "**********".
    state: Output only. Determined by state of the compute VM and postgres-
      service health. Compute VM state can have values listed in
      https://cloud.google.com/compute/docs/instances/instance-life-cycle and
      postgres-service health can have values: HEALTHY and UNHEALTHY.
    zoneId: Output only. The Compute Engine zone of the VM e.g. "us-
      central1-b".
  """

  id = _messages.StringField(1)
  ip = _messages.StringField(2)
  state = _messages.StringField(3)
  zoneId = _messages.StringField(4)


class ObservabilityInstanceConfig(_messages.Message):
  r"""Observability Instance specific configuration.

  Fields:
    assistiveExperiencesEnabled: Whether assistive experiences are enabled for
      this AlloyDB instance.
    enabled: Observability feature status for an instance. This flag is turned
      "off" by default.
    maxQueryStringLength: Query string length. The default value is 10k.
    preserveComments: Preserve comments in query string for an instance. This
      flag is turned "off" by default.
    queryPlansPerMinute: Number of query execution plans captured by Insights
      per minute for all queries combined. The default value is 200. Any
      integer between 0 to 200 is considered valid.
    recordApplicationTags: Record application tags for an instance. This flag
      is turned "off" by default.
    trackActiveQueries: Track actively running queries on the instance. If not
      set, this flag is "off" by default.
    trackWaitEventTypes: Output only. Track wait event types during query
      execution for an instance. This flag is turned "on" by default but
      tracking is enabled only after observability enabled flag is also turned
      on. This is read-only flag and only modifiable by internal API.
    trackWaitEvents: Track wait events during query execution for an instance.
      This flag is turned "on" by default but tracking is enabled only after
      observability enabled flag is also turned on.
  """

  assistiveExperiencesEnabled = _messages.BooleanField(1)
  enabled = _messages.BooleanField(2)
  maxQueryStringLength = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  preserveComments = _messages.BooleanField(4)
  queryPlansPerMinute = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  recordApplicationTags = _messages.BooleanField(6)
  trackActiveQueries = _messages.BooleanField(7)
  trackWaitEventTypes = _messages.BooleanField(8)
  trackWaitEvents = _messages.BooleanField(9)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    upgradeClusterStatus: Output only. UpgradeClusterStatus related metadata.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  upgradeClusterStatus = _messages.MessageField('UpgradeClusterStatus', 7)
  verb = _messages.StringField(8)


class PrimaryConfig(_messages.Message):
  r"""Configuration for the primary cluster. It has the list of clusters that
  are replicating from this cluster. This should be set if and only if the
  cluster is of type PRIMARY.

  Fields:
    secondaryClusterNames: Output only. Names of the clusters that are
      replicating from this cluster.
  """

  secondaryClusterNames = _messages.StringField(1, repeated=True)


class PromoteClusterRequest(_messages.Message):
  r"""Message for promoting a Cluster

  Fields:
    etag: Optional. The current etag of the Cluster. If an etag is provided
      and does not match the current etag of the Cluster, deletion will be
      blocked and an ABORTED error will be returned.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class PscAutoConnectionConfig(_messages.Message):
  r"""Configuration for setting up PSC service automation. Consumer projects
  in the configs will be allowlisted automatically for the instance.

  Fields:
    consumerNetwork: The consumer network for the PSC service automation,
      example: "projects/vpc-host-project/global/networks/default". The
      consumer network might be hosted a different project than the consumer
      project.
    consumerNetworkStatus: Output only. The status of the service connection
      policy. Possible values: "STATE_UNSPECIFIED" - Default state, when
      Connection Map is created initially. "VALID" - Set when policy and map
      configuration is valid, and their matching can lead to allowing creation
      of PSC Connections subject to other constraints like connections limit.
      "CONNECTION_POLICY_MISSING" - No Service Connection Policy found for
      this network and Service Class "POLICY_LIMIT_REACHED" - Service
      Connection Policy limit reached for this network and Service Class
      "CONSUMER_INSTANCE_PROJECT_NOT_ALLOWLISTED" - The consumer instance
      project is not in AllowedGoogleProducersResourceHierarchyLevels of the
      matching ServiceConnectionPolicy.
    consumerProject: The consumer project to which the PSC service automation
      endpoint will be created.
    ipAddress: Output only. The IP address of the PSC service automation
      endpoint.
    status: Output only. The status of the PSC service automation connection.
      Possible values: "STATE_UNSPECIFIED" - An invalid state as the default
      case. "ACTIVE" - The connection has been created successfully. "FAILED"
      - The connection is not functional since some resources on the
      connection fail to be created. "CREATING" - The connection is being
      created. "DELETING" - The connection is being deleted.
      "CREATE_REPAIRING" - The connection is being repaired to complete
      creation. "DELETE_REPAIRING" - The connection is being repaired to
      complete deletion.
  """

  consumerNetwork = _messages.StringField(1)
  consumerNetworkStatus = _messages.StringField(2)
  consumerProject = _messages.StringField(3)
  ipAddress = _messages.StringField(4)
  status = _messages.StringField(5)


class PscConfig(_messages.Message):
  r"""PscConfig contains PSC related configuration at a cluster level.

  Fields:
    pscEnabled: Optional. Create an instance that allows connections from
      Private Service Connect endpoints to the instance.
    serviceOwnedProjectNumber: Output only. The project number that needs to
      be allowlisted on the network attachment to enable outbound
      connectivity.
  """

  pscEnabled = _messages.BooleanField(1)
  serviceOwnedProjectNumber = _messages.IntegerField(2)


class PscInstanceConfig(_messages.Message):
  r"""PscInstanceConfig contains PSC related configuration at an instance
  level.

  Fields:
    allowedConsumerProjects: Optional. List of consumer projects that are
      allowed to create PSC endpoints to service-attachments to this instance.
    pscAutoConnections: Optional. Configurations for setting up PSC service
      automation.
    pscDnsName: Output only. The DNS name of the instance for PSC
      connectivity. Name convention: ...alloydb-psc.goog
    pscInterfaceConfigs: Optional. Configurations for setting up PSC
      interfaces attached to the instance which are used for outbound
      connectivity. Only primary instances can have PSC interface attached.
      Currently we only support 0 or 1 PSC interface.
    serviceAttachmentLink: Output only. The service attachment created when
      Private Service Connect (PSC) is enabled for the instance. The name of
      the resource will be in the format of
      `projects//regions//serviceAttachments/`
  """

  allowedConsumerProjects = _messages.StringField(1, repeated=True)
  pscAutoConnections = _messages.MessageField('PscAutoConnectionConfig', 2, repeated=True)
  pscDnsName = _messages.StringField(3)
  pscInterfaceConfigs = _messages.MessageField('PscInterfaceConfig', 4, repeated=True)
  serviceAttachmentLink = _messages.StringField(5)


class PscInterfaceConfig(_messages.Message):
  r"""Configuration for setting up a PSC interface to enable outbound
  connectivity.

  Fields:
    networkAttachmentResource: The network attachment resource created in the
      consumer network to which the PSC interface will be linked. This is of
      the format: "projects/${CONSUMER_PROJECT}/regions/${REGION}/networkAttac
      hments/${NETWORK_ATTACHMENT_NAME}". The network attachment must be in
      the same region as the instance.
  """

  networkAttachmentResource = _messages.StringField(1)


class QuantityBasedExpiry(_messages.Message):
  r"""A backup's position in a quantity-based retention queue, of backups with
  the same source cluster and type, with length, retention, specified by the
  backup's retention policy. Once the position is greater than the retention,
  the backup is eligible to be garbage collected. Example: 5 backups from the
  same source cluster and type with a quantity-based retention of 3 and
  denoted by backup_id (position, retention). Safe: backup_5 (1, 3), backup_4,
  (2, 3), backup_3 (3, 3). Awaiting garbage collection: backup_2 (4, 3),
  backup_1 (5, 3)

  Fields:
    retentionCount: Output only. The backup's position among its backups with
      the same source cluster and type, by descending chronological order
      create time(i.e. newest first).
    totalRetentionCount: Output only. The length of the quantity-based queue,
      specified by the backup's retention policy.
  """

  retentionCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  totalRetentionCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class QuantityBasedRetention(_messages.Message):
  r"""A quantity based policy specifies that a certain number of the most
  recent successful backups should be retained.

  Fields:
    count: The number of backups to retain.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class QueryInsightsInstanceConfig(_messages.Message):
  r"""QueryInsights Instance specific configuration.

  Fields:
    queryPlansPerMinute: Number of query execution plans captured by Insights
      per minute for all queries combined. The default value is 5. Any integer
      between 0 and 20 is considered valid.
    queryStringLength: Query string length. The default value is 1024. Any
      integer between 256 and 4500 is considered valid.
    recordApplicationTags: Record application tags for an instance. This flag
      is turned "on" by default.
    recordClientAddress: Record client address for an instance. Client address
      is PII information. This flag is turned "on" by default.
  """

  queryPlansPerMinute = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  queryStringLength = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  recordApplicationTags = _messages.BooleanField(3)
  recordClientAddress = _messages.BooleanField(4)


class ReadPoolConfig(_messages.Message):
  r"""Configuration for a read pool instance.

  Fields:
    nodeCount: Read capacity, i.e. number of nodes in a read pool instance.
  """

  nodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ReadPoolInstancesUpgradeStageStatus(_messages.Message):
  r"""Read pool instances upgrade specific status.

  Fields:
    upgradeStats: Read pool instances upgrade statistics.
  """

  upgradeStats = _messages.MessageField('Stats', 1)


class RestartInstanceRequest(_messages.Message):
  r"""A RestartInstanceRequest object.

  Fields:
    nodeIds: Optional. Full name of the nodes as obtained from
      INSTANCE_VIEW_FULL to restart upon. Applicable only to read instances.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  nodeIds = _messages.StringField(1, repeated=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class RestoreClusterRequest(_messages.Message):
  r"""Message for restoring a Cluster from a backup or another cluster at a
  given point in time. NEXT_ID: 11

  Fields:
    backupSource: Backup source.
    backupdrBackupSource: BackupDR backup source.
    backupdrPitrSource: BackupDR source used for point in time recovery.
    cluster: Required. The resource being created
    clusterId: Required. ID of the requesting object.
    continuousBackupSource: ContinuousBackup source. Continuous backup needs
      to be enabled in the source cluster for this operation to succeed.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  backupSource = _messages.MessageField('BackupSource', 1)
  backupdrBackupSource = _messages.MessageField('BackupDrBackupSource', 2)
  backupdrPitrSource = _messages.MessageField('BackupDrPitrSource', 3)
  cluster = _messages.MessageField('Cluster', 4)
  clusterId = _messages.StringField(5)
  continuousBackupSource = _messages.MessageField('ContinuousBackupSource', 6)
  requestId = _messages.StringField(7)
  validateOnly = _messages.BooleanField(8)


class RestoreFromCloudSQLRequest(_messages.Message):
  r"""Message for registering Restoring from CloudSQL resource.

  Fields:
    cloudsqlBackupRunSource: Cluster created from CloudSQL backup run.
    cluster: Required. The resource being created
    clusterId: Required. ID of the requesting object.
  """

  cloudsqlBackupRunSource = _messages.MessageField('CloudSQLBackupRunSource', 1)
  cluster = _messages.MessageField('Cluster', 2)
  clusterId = _messages.StringField(3)


class SecondaryConfig(_messages.Message):
  r"""Configuration information for the secondary cluster. This should be set
  if and only if the cluster is of type SECONDARY.

  Fields:
    primaryClusterName: The name of the primary cluster name with the format:
      * projects/{project}/locations/{region}/clusters/{cluster_id}
  """

  primaryClusterName = _messages.StringField(1)


class SqlExportOptions(_messages.Message):
  r"""Options for exporting data in SQL format.

  Fields:
    cleanTargetObjects: Optional. If true, output commands to DROP all the
      dumped database objects prior to outputting the commands for creating
      them.
    ifExistTargetObjects: Optional. If true, use DROP ... IF EXISTS commands
      to check for the object's existence before dropping it in
      clean_target_objects mode.
    schemaOnly: Optional. If true, only export the schema.
    tables: Optional. Tables to export from.
  """

  cleanTargetObjects = _messages.BooleanField(1)
  ifExistTargetObjects = _messages.BooleanField(2)
  schemaOnly = _messages.BooleanField(3)
  tables = _messages.StringField(4, repeated=True)


class SqlImportOptions(_messages.Message):
  r"""Options for importing data in SQL format."""


class SslConfig(_messages.Message):
  r"""SSL configuration.

  Enums:
    CaSourceValueValuesEnum: Optional. Certificate Authority (CA) source. Only
      CA_SOURCE_MANAGED is supported currently, and is the default value.
    SslModeValueValuesEnum: Optional. SSL mode. Specifies client-server
      SSL/TLS connection behavior.

  Fields:
    caSource: Optional. Certificate Authority (CA) source. Only
      CA_SOURCE_MANAGED is supported currently, and is the default value.
    sslMode: Optional. SSL mode. Specifies client-server SSL/TLS connection
      behavior.
  """

  class CaSourceValueValuesEnum(_messages.Enum):
    r"""Optional. Certificate Authority (CA) source. Only CA_SOURCE_MANAGED is
    supported currently, and is the default value.

    Values:
      CA_SOURCE_UNSPECIFIED: Certificate Authority (CA) source not specified.
        Defaults to CA_SOURCE_MANAGED.
      CA_SOURCE_MANAGED: Certificate Authority (CA) managed by the AlloyDB
        Cluster.
    """
    CA_SOURCE_UNSPECIFIED = 0
    CA_SOURCE_MANAGED = 1

  class SslModeValueValuesEnum(_messages.Enum):
    r"""Optional. SSL mode. Specifies client-server SSL/TLS connection
    behavior.

    Values:
      SSL_MODE_UNSPECIFIED: SSL mode is not specified. Defaults to
        ENCRYPTED_ONLY.
      SSL_MODE_ALLOW: SSL connections are optional. CA verification not
        enforced.
      SSL_MODE_REQUIRE: SSL connections are required. CA verification not
        enforced. Clients may use locally self-signed certificates (default
        psql client behavior).
      SSL_MODE_VERIFY_CA: SSL connections are required. CA verification
        enforced. Clients must have certificates signed by a Cluster CA, for
        example, using GenerateClientCertificate.
      ALLOW_UNENCRYPTED_AND_ENCRYPTED: SSL connections are optional. CA
        verification not enforced.
      ENCRYPTED_ONLY: SSL connections are required. CA verification not
        enforced.
    """
    SSL_MODE_UNSPECIFIED = 0
    SSL_MODE_ALLOW = 1
    SSL_MODE_REQUIRE = 2
    SSL_MODE_VERIFY_CA = 3
    ALLOW_UNENCRYPTED_AND_ENCRYPTED = 4
    ENCRYPTED_ONLY = 5

  caSource = _messages.EnumField('CaSourceValueValuesEnum', 1)
  sslMode = _messages.EnumField('SslModeValueValuesEnum', 2)


class StageInfo(_messages.Message):
  r"""Stage information for different stages in the upgrade process.

  Enums:
    StageValueValuesEnum: The stage.
    StatusValueValuesEnum: Status of the stage.

  Fields:
    logsUrl: logs_url is the URL for the logs associated with a stage if that
      stage has logs. Right now, only three stages have logs:
      ALLOYDB_PRECHECK, PG_UPGRADE_CHECK, PRIMARY_INSTANCE_UPGRADE.
    stage: The stage.
    status: Status of the stage.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""The stage.

    Values:
      STAGE_UNSPECIFIED: Unspecified stage.
      ALLOYDB_PRECHECK: Pre-upgrade custom checks, not covered by pg_upgrade.
      PG_UPGRADE_CHECK: Pre-upgrade pg_upgrade checks.
      PREPARE_FOR_UPGRADE: Clone the original cluster.
      PRIMARY_INSTANCE_UPGRADE: Upgrade the primary instance(downtime).
      READ_POOL_INSTANCES_UPGRADE: This stage is read pool upgrade.
      ROLLBACK: Rollback in case of critical failures.
      CLEANUP: Cleanup.
    """
    STAGE_UNSPECIFIED = 0
    ALLOYDB_PRECHECK = 1
    PG_UPGRADE_CHECK = 2
    PREPARE_FOR_UPGRADE = 3
    PRIMARY_INSTANCE_UPGRADE = 4
    READ_POOL_INSTANCES_UPGRADE = 5
    ROLLBACK = 6
    CLEANUP = 7

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the stage.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  logsUrl = _messages.StringField(1)
  stage = _messages.EnumField('StageValueValuesEnum', 2)
  status = _messages.EnumField('StatusValueValuesEnum', 3)


class StageStatus(_messages.Message):
  r"""Status of an upgrade stage.

  Enums:
    StageValueValuesEnum: Upgrade stage.
    StateValueValuesEnum: State of this stage.

  Fields:
    readPoolInstancesUpgrade: Read pool instances upgrade metadata.
    stage: Upgrade stage.
    state: State of this stage.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Upgrade stage.

    Values:
      STAGE_UNSPECIFIED: Unspecified stage.
      ALLOYDB_PRECHECK: Pre-upgrade custom checks, not covered by pg_upgrade.
      PG_UPGRADE_CHECK: Pre-upgrade pg_upgrade checks.
      PREPARE_FOR_UPGRADE: Clone the original cluster.
      PRIMARY_INSTANCE_UPGRADE: Upgrade the primary instance(downtime).
      READ_POOL_INSTANCES_UPGRADE: This stage is read pool upgrade.
      ROLLBACK: Rollback in case of critical failures.
      CLEANUP: Cleanup.
    """
    STAGE_UNSPECIFIED = 0
    ALLOYDB_PRECHECK = 1
    PG_UPGRADE_CHECK = 2
    PREPARE_FOR_UPGRADE = 3
    PRIMARY_INSTANCE_UPGRADE = 4
    READ_POOL_INSTANCES_UPGRADE = 5
    ROLLBACK = 6
    CLEANUP = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""State of this stage.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  readPoolInstancesUpgrade = _messages.MessageField('ReadPoolInstancesUpgradeStageStatus', 1)
  stage = _messages.EnumField('StageValueValuesEnum', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Stats(_messages.Message):
  r"""Upgrade stats for read pool instances.

  Fields:
    failed: Number of read pool instances which failed to upgrade.
    notStarted: Number of read pool instances for which upgrade has not
      started.
    ongoing: Number of read pool instances undergoing upgrade.
    success: Number of read pool instances successfully upgraded.
  """

  failed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  notStarted = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ongoing = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  success = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration(_messages.Message):
  r"""Configuration for availability of database instance

  Enums:
    AvailabilityTypeValueValuesEnum: Availability type. Potential values: *
      `ZONAL`: The instance serves data from only one zone. Outages in that
      zone affect data accessibility. * `REGIONAL`: The instance can serve
      data from more than one zone in a region (it is highly available).

  Fields:
    automaticFailoverRoutingConfigured: Checks for existence of (multi-
      cluster) routing configuration that allows automatic failover to a
      different zone/region in case of an outage. Applicable to Bigtable
      resources.
    availabilityType: Availability type. Potential values: * `ZONAL`: The
      instance serves data from only one zone. Outages in that zone affect
      data accessibility. * `REGIONAL`: The instance can serve data from more
      than one zone in a region (it is highly available).
    crossRegionReplicaConfigured: Checks for resources that are configured to
      have redundancy, and ongoing replication across regions
    externalReplicaConfigured: A boolean attribute.
    promotableReplicaConfigured: A boolean attribute.
  """

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type. Potential values: * `ZONAL`: The instance serves
    data from only one zone. Outages in that zone affect data accessibility. *
    `REGIONAL`: The instance can serve data from more than one zone in a
    region (it is highly available).

    Values:
      AVAILABILITY_TYPE_UNSPECIFIED: <no description>
      ZONAL: Zonal available instance.
      REGIONAL: Regional available instance.
      MULTI_REGIONAL: Multi regional instance
      AVAILABILITY_TYPE_OTHER: For rest of the other category
    """
    AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2
    MULTI_REGIONAL = 3
    AVAILABILITY_TYPE_OTHER = 4

  automaticFailoverRoutingConfigured = _messages.BooleanField(1)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 2)
  crossRegionReplicaConfigured = _messages.BooleanField(3)
  externalReplicaConfigured = _messages.BooleanField(4)
  promotableReplicaConfigured = _messages.BooleanField(5)


class StorageDatabasecenterPartnerapiV1mainBackupConfiguration(_messages.Message):
  r"""Configuration for automatic backups

  Fields:
    automatedBackupEnabled: Whether customer visible automated backups are
      enabled on the instance.
    backupRetentionSettings: Backup retention settings.
    pointInTimeRecoveryEnabled: Whether point-in-time recovery is enabled.
      This is optional field, if the database service does not have this
      feature or metadata is not available in control plane, this can be
      omitted.
  """

  automatedBackupEnabled = _messages.BooleanField(1)
  backupRetentionSettings = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainRetentionSettings', 2)
  pointInTimeRecoveryEnabled = _messages.BooleanField(3)


class StorageDatabasecenterPartnerapiV1mainBackupRun(_messages.Message):
  r"""A backup run.

  Enums:
    StatusValueValuesEnum: The status of this run. REQUIRED

  Fields:
    endTime: The time the backup operation completed. REQUIRED
    error: Information about why the backup operation failed. This is only
      present if the run has the FAILED status. OPTIONAL
    startTime: The time the backup operation started. REQUIRED
    status: The status of this run. REQUIRED
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of this run. REQUIRED

    Values:
      STATUS_UNSPECIFIED: <no description>
      SUCCESSFUL: The backup was successful.
      FAILED: The backup was unsuccessful.
    """
    STATUS_UNSPECIFIED = 0
    SUCCESSFUL = 1
    FAILED = 2

  endTime = _messages.StringField(1)
  error = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainOperationError', 2)
  startTime = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)


class StorageDatabasecenterPartnerapiV1mainCompliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  standard = _messages.StringField(1)
  version = _messages.StringField(2)


class StorageDatabasecenterPartnerapiV1mainCustomMetadataData(_messages.Message):
  r"""Any custom metadata associated with the resource. e.g. A spanner
  instance can have multiple databases with its own unique metadata.
  Information for these individual databases can be captured in custom
  metadata data

  Fields:
    internalResourceMetadata: Metadata for individual internal resources in an
      instance. e.g. spanner instance can have multiple databases with unique
      configuration.
  """

  internalResourceMetadata = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainInternalResourceMetadata', 1, repeated=True)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceFeed(_messages.Message):
  r"""DatabaseResourceFeed is the top level proto to be used to ingest
  different database resource level events into Condor platform.

  Enums:
    FeedTypeValueValuesEnum: Required. Type feed to be ingested into condor

  Fields:
    feedTimestamp: Required. Timestamp when feed is generated.
    feedType: Required. Type feed to be ingested into condor
    observabilityMetricData: A
      StorageDatabasecenterPartnerapiV1mainObservabilityMetricData attribute.
    recommendationSignalData: A StorageDatabasecenterPartnerapiV1mainDatabaseR
      esourceRecommendationSignalData attribute.
    resourceHealthSignalData: A
      StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData
      attribute.
    resourceId: Primary key associated with the Resource. resource_id is
      available in individual feed level as well.
    resourceMetadata: A
      StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata attribute.
  """

  class FeedTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type feed to be ingested into condor

    Values:
      FEEDTYPE_UNSPECIFIED: <no description>
      RESOURCE_METADATA: Database resource metadata feed from control plane
      OBSERVABILITY_DATA: Database resource monitoring data
      SECURITY_FINDING_DATA: Database resource security health signal data
      RECOMMENDATION_SIGNAL_DATA: Database resource recommendation signal data
    """
    FEEDTYPE_UNSPECIFIED = 0
    RESOURCE_METADATA = 1
    OBSERVABILITY_DATA = 2
    SECURITY_FINDING_DATA = 3
    RECOMMENDATION_SIGNAL_DATA = 4

  feedTimestamp = _messages.StringField(1)
  feedType = _messages.EnumField('FeedTypeValueValuesEnum', 2)
  observabilityMetricData = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainObservabilityMetricData', 3)
  recommendationSignalData = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceRecommendationSignalData', 4)
  resourceHealthSignalData = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData', 5)
  resourceId = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 6)
  resourceMetadata = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata', 7)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData(_messages.Message):
  r"""Common model for database resource health signal data.

  Enums:
    ProviderValueValuesEnum: Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged
    SignalClassValueValuesEnum: Required. The class of the signal, such as if
      it's a THREAT or VULNERABILITY.
    SignalSeverityValueValuesEnum: The severity of the signal, such as if it's
      a HIGH or LOW severity.
    SignalTypeValueValuesEnum: Required. Type of signal, for example,
      `AVAILABLE_IN_MULTIPLE_ZONES`, `LOGGING_MOST_ERRORS`, etc.
    StateValueValuesEnum:

  Messages:
    AdditionalMetadataValue: Any other additional metadata

  Fields:
    additionalMetadata: Any other additional metadata
    compliance: Industry standards associated with this signal; if this signal
      is an issue, that could be a violation of the associated industry
      standard(s). For example, AUTO_BACKUP_DISABLED signal is associated with
      CIS GCP 1.1, CIS GCP 1.2, CIS GCP 1.3, NIST 800-53 and ISO-27001
      compliance standards. If a database resource does not have automated
      backup enable, it will violate these following industry standards.
    description: Description associated with signal
    eventTime: Required. The last time at which the event described by this
      signal took place
    externalUri: The external-uri of the signal, using which more information
      about this signal can be obtained. In GCP, this will take user to SCC
      page to get more details about signals.
    name: Required. The name of the signal, ex: PUBLIC_SQL_INSTANCE,
      SQL_LOG_ERROR_VERBOSITY etc.
    provider: Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged
    resourceContainer: Closest parent container of this resource. In GCP,
      'container' refers to a Cloud Resource Manager project. It must be
      resource name of a Cloud Resource Manager project with the format of
      "provider//", such as "projects/123". For GCP provided resources, number
      should be project number.
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    signalClass: Required. The class of the signal, such as if it's a THREAT
      or VULNERABILITY.
    signalId: Required. Unique identifier for the signal. This is an unique id
      which would be mainatined by partner to identify a signal.
    signalSeverity: The severity of the signal, such as if it's a HIGH or LOW
      severity.
    signalType: Required. Type of signal, for example,
      `AVAILABLE_IN_MULTIPLE_ZONES`, `LOGGING_MOST_ERRORS`, etc.
    state: A StateValueValuesEnum attribute.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged

    Values:
      PROVIDER_UNSPECIFIED: <no description>
      GCP: Google cloud platform provider
      AWS: Amazon web service
      AZURE: Azure web service
      ONPREM: On-prem database resources.
      SELFMANAGED: Self-managed database provider. These are resources on a
        cloud platform, e.g., database resource installed in a GCE VM, but not
        a managed database service.
      PROVIDER_OTHER: For the rest of the other categories. Other refers to
        the rest of other database service providers, this could be smaller
        cloud provider. This needs to be provided when the provider is known,
        but it is not present in the existing set of enum values.
    """
    PROVIDER_UNSPECIFIED = 0
    GCP = 1
    AWS = 2
    AZURE = 3
    ONPREM = 4
    SELFMANAGED = 5
    PROVIDER_OTHER = 6

  class SignalClassValueValuesEnum(_messages.Enum):
    r"""Required. The class of the signal, such as if it's a THREAT or
    VULNERABILITY.

    Values:
      CLASS_UNSPECIFIED: Unspecified signal class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      ERROR: Describes an error that prevents some SCC functionality.
    """
    CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    ERROR = 5

  class SignalSeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the signal, such as if it's a HIGH or LOW severity.

    Values:
      SIGNAL_SEVERITY_UNSPECIFIED: This value is used for findings when a
        source doesn't write a severity value.
      CRITICAL: A critical vulnerability is easily discoverable by an external
        actor, exploitable.
      HIGH: A high risk vulnerability can be easily discovered and exploited
        in combination with other vulnerabilities.
      MEDIUM: A medium risk vulnerability could be used by an actor to gain
        access to resources or privileges that enable them to eventually gain
        access and the ability to execute arbitrary code or exfiltrate data.
      LOW: A low risk vulnerability hampers a security organization's ability
        to detect vulnerabilities or active threats in their deployment.
    """
    SIGNAL_SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class SignalTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of signal, for example, `AVAILABLE_IN_MULTIPLE_ZONES`,
    `LOGGING_MOST_ERRORS`, etc.

    Values:
      SIGNAL_TYPE_UNSPECIFIED: Unspecified.
      SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER: Represents if a
        resource is protected by automatic failover. Checks for resources that
        are configured to have redundancy within a region that enables
        automatic failover.
      SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS: Represents if a group
        is replicating across regions. Checks for resources that are
        configured to have redundancy, and ongoing replication, across
        regions.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES: Represents if the resource
        is available in multiple zones or not.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS: Represents if a resource
        is available in multiple regions.
      SIGNAL_TYPE_NO_PROMOTABLE_REPLICA: Represents if a resource has a
        promotable replica.
      SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY: Represents if a resource has an
        automated backup policy.
      SIGNAL_TYPE_SHORT_BACKUP_RETENTION: Represents if a resources has a
        short backup retention period.
      SIGNAL_TYPE_LAST_BACKUP_FAILED: Represents if the last backup of a
        resource failed.
      SIGNAL_TYPE_LAST_BACKUP_OLD: Represents if the last backup of a resource
        is older than some threshold value.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0: Represents if a resource
        violates CIS GCP Foundation 2.0.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3: Represents if a resource
        violates CIS GCP Foundation 1.3.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2: Represents if a resource
        violates CIS GCP Foundation 1.2.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1: Represents if a resource
        violates CIS GCP Foundation 1.1.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0: Represents if a resource
        violates CIS GCP Foundation 1.0.
      SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0: Represents if a resource
        violates CIS Controls 8.0.
      SIGNAL_TYPE_VIOLATES_NIST_800_53: Represents if a resource violates NIST
        800-53.
      SIGNAL_TYPE_VIOLATES_NIST_800_53_R5: Represents if a resource violates
        NIST 800-53 R5.
      SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0: Represents if a
        resource violates NIST Cybersecurity Framework 1.0.
      SIGNAL_TYPE_VIOLATES_ISO_27001: Represents if a resource violates
        ISO-27001.
      SIGNAL_TYPE_VIOLATES_ISO_27001_V2022: Represents if a resource violates
        ISO 27001 2022.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1: Represents if a resource violates
        PCI-DSS v3.2.1.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0: Represents if a resource violates
        PCI-DSS v4.0.
      SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4: Represents if a resource
        violates Cloud Controls Matrix v4.0.
      SIGNAL_TYPE_VIOLATES_HIPAA: Represents if a resource violates HIPAA.
      SIGNAL_TYPE_VIOLATES_SOC2_V2017: Represents if a resource violates SOC2
        v2017.
      SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING: Represents if
        log_checkpoints database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED: Represents if the log_duration
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        on.
      SIGNAL_TYPE_VERBOSE_ERROR_LOGGING: Represents if the log_error_verbosity
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        default or stricter (default or terse).
      SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED: Represents if the
        log_lock_waits database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_LOGGING_MOST_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance is not set appropriately.
      SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance does not have an appropriate severity level.
      SIGNAL_TYPE_MINIMAL_ERROR_LOGGING: Represents if the log_min_messages
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        warning or another recommended value.
      SIGNAL_TYPE_QUERY_STATISTICS_LOGGED: Represents if the databaseFlags
        property of instance metadata for the log_executor_status field is set
        to on.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME: Represents if the
        log_hostname database flag for a Cloud SQL for PostgreSQL instance is
        not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS: Represents if the
        log_parser_stats database flag for a Cloud SQL for PostgreSQL instance
        is not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS: Represents if the
        log_planner_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS: Represents if the
        log_statement database flag for a Cloud SQL for PostgreSQL instance is
        not set to DDL (all data definition statements).
      SIGNAL_TYPE_LOGGING_QUERY_STATISTICS: Represents if the
        log_statement_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES: Represents if the
        log_temp_files database flag for a Cloud SQL for PostgreSQL instance
        is not set to "0". (NOTE: 0 = ON)
      SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED: Represents if the user
        connections database flag for a Cloud SQL for SQL Server instance is
        configured.
      SIGNAL_TYPE_USER_OPTIONS_CONFIGURED: Represents if the user options
        database flag for Cloud SQL SQL Server instance is configured or not.
      SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS: Represents if a resource is
        exposed to public access.
      SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS: Represents if a resources requires
        all incoming connections to use SSL or not.
      SIGNAL_TYPE_NO_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        password configured for the root account or not.
      SIGNAL_TYPE_WEAK_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        weak password configured for the root account.
      SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED: Represents if a SQL
        database instance is not encrypted with customer-managed encryption
        keys (CMEK).
      SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED: Represents if The
        contained database authentication database flag for a Cloud SQL for
        SQL Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING: Represents if the
        cross_db_ownership_chaining database flag for a Cloud SQL for SQL
        Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS: Represents if he external
        scripts enabled database flag for a Cloud SQL for SQL Server instance
        is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS: Represents if the local_infile
        database flag for a Cloud SQL for MySQL instance is not set to off.
      SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED: Represents if the
        log_connections database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED: Represents if the
        log_disconnections database flag for a Cloud SQL for PostgreSQL
        instance is not set to on.
      SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO: Represents if the
        log_min_duration_statement database flag for a Cloud SQL for
        PostgreSQL instance is not set to -1.
      SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS: Represents if the remote access
        database flag for a Cloud SQL for SQL Server instance is not set to
        off.
      SIGNAL_TYPE_DATABASE_NAMES_EXPOSED: Represents if the skip_show_database
        database flag for a Cloud SQL for MySQL instance is not set to on.
      SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED: Represents if the 3625
        (trace flag) database flag for a Cloud SQL for SQL Server instance is
        not set to on.
      SIGNAL_TYPE_PUBLIC_IP_ENABLED: Represents if public IP is enabled.
      SIGNAL_TYPE_IDLE: Represents Idle instance helps to reduce costs.
      SIGNAL_TYPE_OVERPROVISIONED: Represents instances that are unnecessarily
        large for given workload.
      SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES: Represents high number of
        concurrently opened tables.
      SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES: Represents high table count close to
        SLA limit.
      SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION: Represents high number of
        unvacuumed transactions
      SIGNAL_TYPE_UNDERPROVISIONED: Represents need for more CPU and/or memory
      SIGNAL_TYPE_OUT_OF_DISK: Represents out of disk.
      SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY: Represents server
        certificate is near expiry.
      SIGNAL_TYPE_DATABASE_AUDITING_DISABLED: Represents database auditing is
        disabled.
      SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS: Represents not restricted to
        authorized networks.
      SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP: Represents violate org
        policy restrict public ip.
      SIGNAL_TYPE_QUOTA_LIMIT: Cluster nearing quota limit
      SIGNAL_TYPE_NO_PASSWORD_POLICY: No password policy set on resources
      SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT: Performance impact of
        connections settings
      SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT: Performance impact of
        temporary tables settings
      SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT: Performance impact of
        transaction logs settings
      SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES: Performance impact of high joins
        without indexes
      SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES: Detects events where a
        Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL
        users) writes to non-system tables.
      SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS: Detects events where a
        database user or role has been granted all privileges to a database,
        or to all tables, procedures, or functions in a schema.
      SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket outside of
        the organization.
      SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket that is
        owned by the organization and is publicly accessible.
      SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM: Detects if a database instance
        is using a weak password hash algorithm.
      SIGNAL_TYPE_NO_USER_PASSWORD_POLICY: Detects if a database instance has
        no user password policy set.
      SIGNAL_TYPE_HOT_NODE: Detects if a database instance/cluster has a hot
        node.
      SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY: Detects if a database instance
        has no point in time recovery enabled.
      SIGNAL_TYPE_RESOURCE_SUSPENDED: Detects if a database instance/cluster
        is suspended.
      SIGNAL_TYPE_EXPENSIVE_COMMANDS: Detects that expensive commands are
        being run on a database instance impacting overall performance.
      SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED: Indicates that the
        instance does not have a maintenance policy configured.
      SIGNAL_TYPE_NO_DELETION_PROTECTION: Deletion Protection Disabled for the
        resource
      SIGNAL_TYPE_INEFFICIENT_QUERY: Indicates that the instance has
        inefficient queries detected.
      SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD: Indicates that the instance has
        read intensive workload.
      SIGNAL_TYPE_MEMORY_LIMIT: Indicates that the instance is nearing memory
        limit.
      SIGNAL_TYPE_MAX_SERVER_MEMORY: Indicates that the instance's max server
        memory is configured higher than the recommended value.
      SIGNAL_TYPE_LARGE_ROWS: Indicates that the database has large rows
        beyond the recommended limit.
      SIGNAL_TYPE_HIGH_WRITE_PRESSURE: Heavy write pressure on the database
        rows.
      SIGNAL_TYPE_HIGH_READ_PRESSURE: Heavy read pressure on the database
        rows.
      SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED: Encryption org policy
        not satisfied.
      SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED: Location org policy not
        satisfied.
    """
    SIGNAL_TYPE_UNSPECIFIED = 0
    SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER = 1
    SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS = 2
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES = 3
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS = 4
    SIGNAL_TYPE_NO_PROMOTABLE_REPLICA = 5
    SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY = 6
    SIGNAL_TYPE_SHORT_BACKUP_RETENTION = 7
    SIGNAL_TYPE_LAST_BACKUP_FAILED = 8
    SIGNAL_TYPE_LAST_BACKUP_OLD = 9
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0 = 10
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3 = 11
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2 = 12
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1 = 13
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0 = 14
    SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0 = 15
    SIGNAL_TYPE_VIOLATES_NIST_800_53 = 16
    SIGNAL_TYPE_VIOLATES_NIST_800_53_R5 = 17
    SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0 = 18
    SIGNAL_TYPE_VIOLATES_ISO_27001 = 19
    SIGNAL_TYPE_VIOLATES_ISO_27001_V2022 = 20
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1 = 21
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0 = 22
    SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4 = 23
    SIGNAL_TYPE_VIOLATES_HIPAA = 24
    SIGNAL_TYPE_VIOLATES_SOC2_V2017 = 25
    SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING = 26
    SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED = 27
    SIGNAL_TYPE_VERBOSE_ERROR_LOGGING = 28
    SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED = 29
    SIGNAL_TYPE_LOGGING_MOST_ERRORS = 30
    SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS = 31
    SIGNAL_TYPE_MINIMAL_ERROR_LOGGING = 32
    SIGNAL_TYPE_QUERY_STATISTICS_LOGGED = 33
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME = 34
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS = 35
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS = 36
    SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS = 37
    SIGNAL_TYPE_LOGGING_QUERY_STATISTICS = 38
    SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES = 39
    SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED = 40
    SIGNAL_TYPE_USER_OPTIONS_CONFIGURED = 41
    SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS = 42
    SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS = 43
    SIGNAL_TYPE_NO_ROOT_PASSWORD = 44
    SIGNAL_TYPE_WEAK_ROOT_PASSWORD = 45
    SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED = 46
    SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED = 47
    SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING = 48
    SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS = 49
    SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS = 50
    SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED = 51
    SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED = 52
    SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO = 53
    SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS = 54
    SIGNAL_TYPE_DATABASE_NAMES_EXPOSED = 55
    SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED = 56
    SIGNAL_TYPE_PUBLIC_IP_ENABLED = 57
    SIGNAL_TYPE_IDLE = 58
    SIGNAL_TYPE_OVERPROVISIONED = 59
    SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES = 60
    SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES = 61
    SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION = 62
    SIGNAL_TYPE_UNDERPROVISIONED = 63
    SIGNAL_TYPE_OUT_OF_DISK = 64
    SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY = 65
    SIGNAL_TYPE_DATABASE_AUDITING_DISABLED = 66
    SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS = 67
    SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP = 68
    SIGNAL_TYPE_QUOTA_LIMIT = 69
    SIGNAL_TYPE_NO_PASSWORD_POLICY = 70
    SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT = 71
    SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT = 72
    SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT = 73
    SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES = 74
    SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES = 75
    SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS = 76
    SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET = 77
    SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET = 78
    SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM = 79
    SIGNAL_TYPE_NO_USER_PASSWORD_POLICY = 80
    SIGNAL_TYPE_HOT_NODE = 81
    SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY = 82
    SIGNAL_TYPE_RESOURCE_SUSPENDED = 83
    SIGNAL_TYPE_EXPENSIVE_COMMANDS = 84
    SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED = 85
    SIGNAL_TYPE_NO_DELETION_PROTECTION = 86
    SIGNAL_TYPE_INEFFICIENT_QUERY = 87
    SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD = 88
    SIGNAL_TYPE_MEMORY_LIMIT = 89
    SIGNAL_TYPE_MAX_SERVER_MEMORY = 90
    SIGNAL_TYPE_LARGE_ROWS = 91
    SIGNAL_TYPE_HIGH_WRITE_PRESSURE = 92
    SIGNAL_TYPE_HIGH_READ_PRESSURE = 93
    SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED = 94
    SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED = 95

  class StateValueValuesEnum(_messages.Enum):
    r"""StateValueValuesEnum enum type.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The signal requires attention and has not been addressed yet.
      RESOLVED: The signal has been fixed, triaged as a non-issue or otherwise
        addressed and is no longer active.
      MUTED: The signal has been muted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    RESOLVED = 2
    MUTED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalMetadataValue(_messages.Message):
    r"""Any other additional metadata

    Messages:
      AdditionalProperty: An additional property for a AdditionalMetadataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalMetadata = _messages.MessageField('AdditionalMetadataValue', 1)
  compliance = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainCompliance', 2, repeated=True)
  description = _messages.StringField(3)
  eventTime = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  name = _messages.StringField(6)
  provider = _messages.EnumField('ProviderValueValuesEnum', 7)
  resourceContainer = _messages.StringField(8)
  resourceName = _messages.StringField(9)
  signalClass = _messages.EnumField('SignalClassValueValuesEnum', 10)
  signalId = _messages.StringField(11)
  signalSeverity = _messages.EnumField('SignalSeverityValueValuesEnum', 12)
  signalType = _messages.EnumField('SignalTypeValueValuesEnum', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceId(_messages.Message):
  r"""DatabaseResourceId will serve as primary key for any resource ingestion
  event.

  Enums:
    ProviderValueValuesEnum: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged

  Fields:
    provider: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged
    providerDescription: Optional. Needs to be used only when the provider is
      PROVIDER_OTHER.
    resourceType: Required. The type of resource this ID is identifying. Ex
      redis.googleapis.com/Instance, redis.googleapis.com/Cluster,
      alloydb.googleapis.com/Cluster, alloydb.googleapis.com/Instance,
      spanner.googleapis.com/Instance, spanner.googleapis.com/Database,
      firestore.googleapis.com/Database, sqladmin.googleapis.com/Instance,
      bigtableadmin.googleapis.com/Cluster,
      bigtableadmin.googleapis.com/Instance REQUIRED Please refer go/condor-
      common-datamodel
    uniqueId: Required. A service-local token that distinguishes this resource
      from other resources within the same service.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Required. Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged

    Values:
      PROVIDER_UNSPECIFIED: <no description>
      GCP: Google cloud platform provider
      AWS: Amazon web service
      AZURE: Azure web service
      ONPREM: On-prem database resources.
      SELFMANAGED: Self-managed database provider. These are resources on a
        cloud platform, e.g., database resource installed in a GCE VM, but not
        a managed database service.
      PROVIDER_OTHER: For the rest of the other categories. Other refers to
        the rest of other database service providers, this could be smaller
        cloud provider. This needs to be provided when the provider is known,
        but it is not present in the existing set of enum values.
    """
    PROVIDER_UNSPECIFIED = 0
    GCP = 1
    AWS = 2
    AZURE = 3
    ONPREM = 4
    SELFMANAGED = 5
    PROVIDER_OTHER = 6

  provider = _messages.EnumField('ProviderValueValuesEnum', 1)
  providerDescription = _messages.StringField(2)
  resourceType = _messages.StringField(3)
  uniqueId = _messages.StringField(4)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata(_messages.Message):
  r"""Common model for database resource instance metadata. Next ID: 25

  Enums:
    CurrentStateValueValuesEnum: Current state of the instance.
    EditionValueValuesEnum: Optional. Edition represents whether the instance
      is ENTERPRISE or ENTERPRISE_PLUS. This information is core to Cloud SQL
      only and is used to identify the edition of the instance.
    ExpectedStateValueValuesEnum: The state that the instance is expected to
      be in. For example, an instance state can transition to UNHEALTHY due to
      wrong patch update, while the expected state will remain at the HEALTHY.
    InstanceTypeValueValuesEnum: The type of the instance. Specified at
      creation time.
    SuspensionReasonValueValuesEnum: Optional. Suspension reason for the
      resource.

  Fields:
    availabilityConfiguration: Availability configuration for this instance
    backupConfiguration: Backup configuration for this instance
    backupRun: Latest backup run information for this instance
    creationTime: The creation time of the resource, i.e. the time when
      resource is created and recorded in partner service.
    currentState: Current state of the instance.
    customMetadata: Any custom metadata associated with the resource
    edition: Optional. Edition represents whether the instance is ENTERPRISE
      or ENTERPRISE_PLUS. This information is core to Cloud SQL only and is
      used to identify the edition of the instance.
    entitlements: Entitlements associated with the resource
    expectedState: The state that the instance is expected to be in. For
      example, an instance state can transition to UNHEALTHY due to wrong
      patch update, while the expected state will remain at the HEALTHY.
    gcbdrConfiguration: GCBDR configuration for the resource.
    id: Required. Unique identifier for a Database resource
    instanceType: The type of the instance. Specified at creation time.
    location: The resource location. REQUIRED
    machineConfiguration: Machine configuration for this resource.
    primaryResourceId: Identifier for this resource's immediate parent/primary
      resource if the current resource is a replica or derived form of another
      Database resource. Else it would be NULL. REQUIRED if the immediate
      parent exists when first time resource is getting ingested, otherwise
      optional.
    primaryResourceLocation: Primary resource location. REQUIRED if the
      immediate parent exists when first time resource is getting ingested,
      otherwise optional.
    product: The product this resource represents.
    resourceContainer: Closest parent Cloud Resource Manager container of this
      resource. It must be resource name of a Cloud Resource Manager project
      with the format of "/", such as "projects/123". For GCP provided
      resources, number should be project number.
    resourceName: Required. Different from DatabaseResourceId.unique_id, a
      resource name can be reused over time. That is, after a resource named
      "ABC" is deleted, the name "ABC" can be used to to create a new resource
      within the same source. Resource name to follow CAIS resource_name
      format as noted here go/condor-common-datamodel
    suspensionReason: Optional. Suspension reason for the resource.
    tagsSet: Optional. Tags associated with this resources.
    updationTime: The time at which the resource was updated and recorded at
      partner service.
    userLabelSet: User-provided labels associated with the resource
  """

  class CurrentStateValueValuesEnum(_messages.Enum):
    r"""Current state of the instance.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      SUSPENDED: When instance is suspended
      DELETED: Instance is deleted.
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    SUSPENDED = 3
    DELETED = 4
    STATE_OTHER = 5

  class EditionValueValuesEnum(_messages.Enum):
    r"""Optional. Edition represents whether the instance is ENTERPRISE or
    ENTERPRISE_PLUS. This information is core to Cloud SQL only and is used to
    identify the edition of the instance.

    Values:
      EDITION_UNSPECIFIED: Default, to make it consistent with instance
        edition enum.
      EDITION_ENTERPRISE: Represents the enterprise edition.
      EDITION_ENTERPRISE_PLUS: Represents the enterprise plus edition.
    """
    EDITION_UNSPECIFIED = 0
    EDITION_ENTERPRISE = 1
    EDITION_ENTERPRISE_PLUS = 2

  class ExpectedStateValueValuesEnum(_messages.Enum):
    r"""The state that the instance is expected to be in. For example, an
    instance state can transition to UNHEALTHY due to wrong patch update,
    while the expected state will remain at the HEALTHY.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      SUSPENDED: When instance is suspended
      DELETED: Instance is deleted.
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    SUSPENDED = 3
    DELETED = 4
    STATE_OTHER = 5

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the instance. Specified at creation time.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: Unspecified.
      SUB_RESOURCE_TYPE_UNSPECIFIED: For rest of the other categories.
      PRIMARY: A regular primary database instance.
      SECONDARY: A cluster or an instance acting as a secondary.
      READ_REPLICA: An instance acting as a read-replica.
      OTHER: For rest of the other categories.
      SUB_RESOURCE_TYPE_PRIMARY: A regular primary database instance.
      SUB_RESOURCE_TYPE_SECONDARY: A cluster or an instance acting as a
        secondary.
      SUB_RESOURCE_TYPE_READ_REPLICA: An instance acting as a read-replica.
      SUB_RESOURCE_TYPE_EXTERNAL_PRIMARY: An instance acting as an external
        primary.
      SUB_RESOURCE_TYPE_OTHER: For rest of the other categories.
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    SUB_RESOURCE_TYPE_UNSPECIFIED = 1
    PRIMARY = 2
    SECONDARY = 3
    READ_REPLICA = 4
    OTHER = 5
    SUB_RESOURCE_TYPE_PRIMARY = 6
    SUB_RESOURCE_TYPE_SECONDARY = 7
    SUB_RESOURCE_TYPE_READ_REPLICA = 8
    SUB_RESOURCE_TYPE_EXTERNAL_PRIMARY = 9
    SUB_RESOURCE_TYPE_OTHER = 10

  class SuspensionReasonValueValuesEnum(_messages.Enum):
    r"""Optional. Suspension reason for the resource.

    Values:
      SUSPENSION_REASON_UNSPECIFIED: Suspension reason is unspecified.
      WIPEOUT_HIDE_EVENT: Wipeout hide event.
      WIPEOUT_PURGE_EVENT: Wipeout purge event.
      BILLING_DISABLED: Billing disabled for project
      ABUSER_DETECTED: Abuse detected for resource
      ENCRYPTION_KEY_INACCESSIBLE: Encryption key inaccessible.
      REPLICATED_CLUSTER_ENCRYPTION_KEY_INACCESSIBLE: Replicated cluster
        encryption key inaccessible.
    """
    SUSPENSION_REASON_UNSPECIFIED = 0
    WIPEOUT_HIDE_EVENT = 1
    WIPEOUT_PURGE_EVENT = 2
    BILLING_DISABLED = 3
    ABUSER_DETECTED = 4
    ENCRYPTION_KEY_INACCESSIBLE = 5
    REPLICATED_CLUSTER_ENCRYPTION_KEY_INACCESSIBLE = 6

  availabilityConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration', 1)
  backupConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupConfiguration', 2)
  backupRun = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupRun', 3)
  creationTime = _messages.StringField(4)
  currentState = _messages.EnumField('CurrentStateValueValuesEnum', 5)
  customMetadata = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainCustomMetadataData', 6)
  edition = _messages.EnumField('EditionValueValuesEnum', 7)
  entitlements = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainEntitlement', 8, repeated=True)
  expectedState = _messages.EnumField('ExpectedStateValueValuesEnum', 9)
  gcbdrConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainGCBDRConfiguration', 10)
  id = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 11)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 12)
  location = _messages.StringField(13)
  machineConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainMachineConfiguration', 14)
  primaryResourceId = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 15)
  primaryResourceLocation = _messages.StringField(16)
  product = _messages.MessageField('StorageDatabasecenterProtoCommonProduct', 17)
  resourceContainer = _messages.StringField(18)
  resourceName = _messages.StringField(19)
  suspensionReason = _messages.EnumField('SuspensionReasonValueValuesEnum', 20)
  tagsSet = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainTags', 21)
  updationTime = _messages.StringField(22)
  userLabelSet = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainUserLabels', 23)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceRecommendationSignalData(_messages.Message):
  r"""Common model for database resource recommendation signal data.

  Enums:
    RecommendationStateValueValuesEnum: Required. Recommendation state
    SignalTypeValueValuesEnum: Required. Type of signal, for example,
      `SIGNAL_TYPE_IDLE`, `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.

  Messages:
    AdditionalMetadataValue: Optional. Any other additional metadata specific
      to recommendation

  Fields:
    additionalMetadata: Optional. Any other additional metadata specific to
      recommendation
    lastRefreshTime: Required. last time recommendationw as refreshed
    recommendationState: Required. Recommendation state
    recommender: Required. Name of recommendation. Examples:
      organizations/1234/locations/us-central1/recommenders/google.cloudsql.in
      stance.PerformanceRecommender/recommendations/9876
    recommenderId: Required. ID of recommender. Examples:
      "google.cloudsql.instance.PerformanceRecommender"
    recommenderSubtype: Required. Contains an identifier for a subtype of
      recommendations produced for the same recommender. Subtype is a function
      of content and impact, meaning a new subtype might be added when
      significant changes to `content` or `primary_impact.category` are
      introduced. See the Recommenders section to see a list of subtypes for a
      given Recommender. Examples: For recommender =
      "google.cloudsql.instance.PerformanceRecommender", recommender_subtype
      can be "MYSQL_HIGH_NUMBER_OF_OPEN_TABLES_BEST_PRACTICE"/"POSTGRES_HIGH_T
      RANSACTION_ID_UTILIZATION_BEST_PRACTICE"
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    signalType: Required. Type of signal, for example, `SIGNAL_TYPE_IDLE`,
      `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.
  """

  class RecommendationStateValueValuesEnum(_messages.Enum):
    r"""Required. Recommendation state

    Values:
      UNSPECIFIED: <no description>
      ACTIVE: Recommendation is active and can be applied. ACTIVE
        recommendations can be marked as CLAIMED, SUCCEEDED, or FAILED.
      CLAIMED: Recommendation is in claimed state. Recommendations content is
        immutable and cannot be updated by Google. CLAIMED recommendations can
        be marked as CLAIMED, SUCCEEDED, or FAILED.
      SUCCEEDED: Recommendation is in succeeded state. Recommendations content
        is immutable and cannot be updated by Google. SUCCEEDED
        recommendations can be marked as SUCCEEDED, or FAILED.
      FAILED: Recommendation is in failed state. Recommendations content is
        immutable and cannot be updated by Google. FAILED recommendations can
        be marked as SUCCEEDED, or FAILED.
      DISMISSED: Recommendation is in dismissed state. Recommendation content
        can be updated by Google. DISMISSED recommendations can be marked as
        ACTIVE.
    """
    UNSPECIFIED = 0
    ACTIVE = 1
    CLAIMED = 2
    SUCCEEDED = 3
    FAILED = 4
    DISMISSED = 5

  class SignalTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of signal, for example, `SIGNAL_TYPE_IDLE`,
    `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.

    Values:
      SIGNAL_TYPE_UNSPECIFIED: Unspecified.
      SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER: Represents if a
        resource is protected by automatic failover. Checks for resources that
        are configured to have redundancy within a region that enables
        automatic failover.
      SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS: Represents if a group
        is replicating across regions. Checks for resources that are
        configured to have redundancy, and ongoing replication, across
        regions.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES: Represents if the resource
        is available in multiple zones or not.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS: Represents if a resource
        is available in multiple regions.
      SIGNAL_TYPE_NO_PROMOTABLE_REPLICA: Represents if a resource has a
        promotable replica.
      SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY: Represents if a resource has an
        automated backup policy.
      SIGNAL_TYPE_SHORT_BACKUP_RETENTION: Represents if a resources has a
        short backup retention period.
      SIGNAL_TYPE_LAST_BACKUP_FAILED: Represents if the last backup of a
        resource failed.
      SIGNAL_TYPE_LAST_BACKUP_OLD: Represents if the last backup of a resource
        is older than some threshold value.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0: Represents if a resource
        violates CIS GCP Foundation 2.0.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3: Represents if a resource
        violates CIS GCP Foundation 1.3.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2: Represents if a resource
        violates CIS GCP Foundation 1.2.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1: Represents if a resource
        violates CIS GCP Foundation 1.1.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0: Represents if a resource
        violates CIS GCP Foundation 1.0.
      SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0: Represents if a resource
        violates CIS Controls 8.0.
      SIGNAL_TYPE_VIOLATES_NIST_800_53: Represents if a resource violates NIST
        800-53.
      SIGNAL_TYPE_VIOLATES_NIST_800_53_R5: Represents if a resource violates
        NIST 800-53 R5.
      SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0: Represents if a
        resource violates NIST Cybersecurity Framework 1.0.
      SIGNAL_TYPE_VIOLATES_ISO_27001: Represents if a resource violates
        ISO-27001.
      SIGNAL_TYPE_VIOLATES_ISO_27001_V2022: Represents if a resource violates
        ISO 27001 2022.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1: Represents if a resource violates
        PCI-DSS v3.2.1.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0: Represents if a resource violates
        PCI-DSS v4.0.
      SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4: Represents if a resource
        violates Cloud Controls Matrix v4.0.
      SIGNAL_TYPE_VIOLATES_HIPAA: Represents if a resource violates HIPAA.
      SIGNAL_TYPE_VIOLATES_SOC2_V2017: Represents if a resource violates SOC2
        v2017.
      SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING: Represents if
        log_checkpoints database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED: Represents if the log_duration
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        on.
      SIGNAL_TYPE_VERBOSE_ERROR_LOGGING: Represents if the log_error_verbosity
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        default or stricter (default or terse).
      SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED: Represents if the
        log_lock_waits database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_LOGGING_MOST_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance is not set appropriately.
      SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance does not have an appropriate severity level.
      SIGNAL_TYPE_MINIMAL_ERROR_LOGGING: Represents if the log_min_messages
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        warning or another recommended value.
      SIGNAL_TYPE_QUERY_STATISTICS_LOGGED: Represents if the databaseFlags
        property of instance metadata for the log_executor_status field is set
        to on.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME: Represents if the
        log_hostname database flag for a Cloud SQL for PostgreSQL instance is
        not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS: Represents if the
        log_parser_stats database flag for a Cloud SQL for PostgreSQL instance
        is not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS: Represents if the
        log_planner_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS: Represents if the
        log_statement database flag for a Cloud SQL for PostgreSQL instance is
        not set to DDL (all data definition statements).
      SIGNAL_TYPE_LOGGING_QUERY_STATISTICS: Represents if the
        log_statement_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES: Represents if the
        log_temp_files database flag for a Cloud SQL for PostgreSQL instance
        is not set to "0". (NOTE: 0 = ON)
      SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED: Represents if the user
        connections database flag for a Cloud SQL for SQL Server instance is
        configured.
      SIGNAL_TYPE_USER_OPTIONS_CONFIGURED: Represents if the user options
        database flag for Cloud SQL SQL Server instance is configured or not.
      SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS: Represents if a resource is
        exposed to public access.
      SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS: Represents if a resources requires
        all incoming connections to use SSL or not.
      SIGNAL_TYPE_NO_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        password configured for the root account or not.
      SIGNAL_TYPE_WEAK_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        weak password configured for the root account.
      SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED: Represents if a SQL
        database instance is not encrypted with customer-managed encryption
        keys (CMEK).
      SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED: Represents if The
        contained database authentication database flag for a Cloud SQL for
        SQL Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING: Represents if the
        cross_db_ownership_chaining database flag for a Cloud SQL for SQL
        Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS: Represents if he external
        scripts enabled database flag for a Cloud SQL for SQL Server instance
        is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS: Represents if the local_infile
        database flag for a Cloud SQL for MySQL instance is not set to off.
      SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED: Represents if the
        log_connections database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED: Represents if the
        log_disconnections database flag for a Cloud SQL for PostgreSQL
        instance is not set to on.
      SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO: Represents if the
        log_min_duration_statement database flag for a Cloud SQL for
        PostgreSQL instance is not set to -1.
      SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS: Represents if the remote access
        database flag for a Cloud SQL for SQL Server instance is not set to
        off.
      SIGNAL_TYPE_DATABASE_NAMES_EXPOSED: Represents if the skip_show_database
        database flag for a Cloud SQL for MySQL instance is not set to on.
      SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED: Represents if the 3625
        (trace flag) database flag for a Cloud SQL for SQL Server instance is
        not set to on.
      SIGNAL_TYPE_PUBLIC_IP_ENABLED: Represents if public IP is enabled.
      SIGNAL_TYPE_IDLE: Represents Idle instance helps to reduce costs.
      SIGNAL_TYPE_OVERPROVISIONED: Represents instances that are unnecessarily
        large for given workload.
      SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES: Represents high number of
        concurrently opened tables.
      SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES: Represents high table count close to
        SLA limit.
      SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION: Represents high number of
        unvacuumed transactions
      SIGNAL_TYPE_UNDERPROVISIONED: Represents need for more CPU and/or memory
      SIGNAL_TYPE_OUT_OF_DISK: Represents out of disk.
      SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY: Represents server
        certificate is near expiry.
      SIGNAL_TYPE_DATABASE_AUDITING_DISABLED: Represents database auditing is
        disabled.
      SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS: Represents not restricted to
        authorized networks.
      SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP: Represents violate org
        policy restrict public ip.
      SIGNAL_TYPE_QUOTA_LIMIT: Cluster nearing quota limit
      SIGNAL_TYPE_NO_PASSWORD_POLICY: No password policy set on resources
      SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT: Performance impact of
        connections settings
      SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT: Performance impact of
        temporary tables settings
      SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT: Performance impact of
        transaction logs settings
      SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES: Performance impact of high joins
        without indexes
      SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES: Detects events where a
        Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL
        users) writes to non-system tables.
      SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS: Detects events where a
        database user or role has been granted all privileges to a database,
        or to all tables, procedures, or functions in a schema.
      SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket outside of
        the organization.
      SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket that is
        owned by the organization and is publicly accessible.
      SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM: Detects if a database instance
        is using a weak password hash algorithm.
      SIGNAL_TYPE_NO_USER_PASSWORD_POLICY: Detects if a database instance has
        no user password policy set.
      SIGNAL_TYPE_HOT_NODE: Detects if a database instance/cluster has a hot
        node.
      SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY: Detects if a database instance
        has no point in time recovery enabled.
      SIGNAL_TYPE_RESOURCE_SUSPENDED: Detects if a database instance/cluster
        is suspended.
      SIGNAL_TYPE_EXPENSIVE_COMMANDS: Detects that expensive commands are
        being run on a database instance impacting overall performance.
      SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED: Indicates that the
        instance does not have a maintenance policy configured.
      SIGNAL_TYPE_NO_DELETION_PROTECTION: Deletion Protection Disabled for the
        resource
      SIGNAL_TYPE_INEFFICIENT_QUERY: Indicates that the instance has
        inefficient queries detected.
      SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD: Indicates that the instance has
        read intensive workload.
      SIGNAL_TYPE_MEMORY_LIMIT: Indicates that the instance is nearing memory
        limit.
      SIGNAL_TYPE_MAX_SERVER_MEMORY: Indicates that the instance's max server
        memory is configured higher than the recommended value.
      SIGNAL_TYPE_LARGE_ROWS: Indicates that the database has large rows
        beyond the recommended limit.
      SIGNAL_TYPE_HIGH_WRITE_PRESSURE: Heavy write pressure on the database
        rows.
      SIGNAL_TYPE_HIGH_READ_PRESSURE: Heavy read pressure on the database
        rows.
      SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED: Encryption org policy
        not satisfied.
      SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED: Location org policy not
        satisfied.
    """
    SIGNAL_TYPE_UNSPECIFIED = 0
    SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER = 1
    SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS = 2
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES = 3
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS = 4
    SIGNAL_TYPE_NO_PROMOTABLE_REPLICA = 5
    SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY = 6
    SIGNAL_TYPE_SHORT_BACKUP_RETENTION = 7
    SIGNAL_TYPE_LAST_BACKUP_FAILED = 8
    SIGNAL_TYPE_LAST_BACKUP_OLD = 9
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0 = 10
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3 = 11
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2 = 12
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1 = 13
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0 = 14
    SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0 = 15
    SIGNAL_TYPE_VIOLATES_NIST_800_53 = 16
    SIGNAL_TYPE_VIOLATES_NIST_800_53_R5 = 17
    SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0 = 18
    SIGNAL_TYPE_VIOLATES_ISO_27001 = 19
    SIGNAL_TYPE_VIOLATES_ISO_27001_V2022 = 20
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1 = 21
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0 = 22
    SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4 = 23
    SIGNAL_TYPE_VIOLATES_HIPAA = 24
    SIGNAL_TYPE_VIOLATES_SOC2_V2017 = 25
    SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING = 26
    SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED = 27
    SIGNAL_TYPE_VERBOSE_ERROR_LOGGING = 28
    SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED = 29
    SIGNAL_TYPE_LOGGING_MOST_ERRORS = 30
    SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS = 31
    SIGNAL_TYPE_MINIMAL_ERROR_LOGGING = 32
    SIGNAL_TYPE_QUERY_STATISTICS_LOGGED = 33
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME = 34
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS = 35
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS = 36
    SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS = 37
    SIGNAL_TYPE_LOGGING_QUERY_STATISTICS = 38
    SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES = 39
    SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED = 40
    SIGNAL_TYPE_USER_OPTIONS_CONFIGURED = 41
    SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS = 42
    SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS = 43
    SIGNAL_TYPE_NO_ROOT_PASSWORD = 44
    SIGNAL_TYPE_WEAK_ROOT_PASSWORD = 45
    SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED = 46
    SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED = 47
    SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING = 48
    SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS = 49
    SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS = 50
    SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED = 51
    SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED = 52
    SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO = 53
    SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS = 54
    SIGNAL_TYPE_DATABASE_NAMES_EXPOSED = 55
    SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED = 56
    SIGNAL_TYPE_PUBLIC_IP_ENABLED = 57
    SIGNAL_TYPE_IDLE = 58
    SIGNAL_TYPE_OVERPROVISIONED = 59
    SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES = 60
    SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES = 61
    SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION = 62
    SIGNAL_TYPE_UNDERPROVISIONED = 63
    SIGNAL_TYPE_OUT_OF_DISK = 64
    SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY = 65
    SIGNAL_TYPE_DATABASE_AUDITING_DISABLED = 66
    SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS = 67
    SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP = 68
    SIGNAL_TYPE_QUOTA_LIMIT = 69
    SIGNAL_TYPE_NO_PASSWORD_POLICY = 70
    SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT = 71
    SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT = 72
    SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT = 73
    SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES = 74
    SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES = 75
    SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS = 76
    SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET = 77
    SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET = 78
    SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM = 79
    SIGNAL_TYPE_NO_USER_PASSWORD_POLICY = 80
    SIGNAL_TYPE_HOT_NODE = 81
    SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY = 82
    SIGNAL_TYPE_RESOURCE_SUSPENDED = 83
    SIGNAL_TYPE_EXPENSIVE_COMMANDS = 84
    SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED = 85
    SIGNAL_TYPE_NO_DELETION_PROTECTION = 86
    SIGNAL_TYPE_INEFFICIENT_QUERY = 87
    SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD = 88
    SIGNAL_TYPE_MEMORY_LIMIT = 89
    SIGNAL_TYPE_MAX_SERVER_MEMORY = 90
    SIGNAL_TYPE_LARGE_ROWS = 91
    SIGNAL_TYPE_HIGH_WRITE_PRESSURE = 92
    SIGNAL_TYPE_HIGH_READ_PRESSURE = 93
    SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED = 94
    SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED = 95

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalMetadataValue(_messages.Message):
    r"""Optional. Any other additional metadata specific to recommendation

    Messages:
      AdditionalProperty: An additional property for a AdditionalMetadataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalMetadata = _messages.MessageField('AdditionalMetadataValue', 1)
  lastRefreshTime = _messages.StringField(2)
  recommendationState = _messages.EnumField('RecommendationStateValueValuesEnum', 3)
  recommender = _messages.StringField(4)
  recommenderId = _messages.StringField(5)
  recommenderSubtype = _messages.StringField(6)
  resourceName = _messages.StringField(7)
  signalType = _messages.EnumField('SignalTypeValueValuesEnum', 8)


class StorageDatabasecenterPartnerapiV1mainEntitlement(_messages.Message):
  r"""Proto representing the access that a user has to a specific
  feature/service. NextId: 3.

  Enums:
    EntitlementStateValueValuesEnum: The current state of user's accessibility
      to a feature/benefit.
    TypeValueValuesEnum: An enum that represents the type of this entitlement.

  Fields:
    entitlementState: The current state of user's accessibility to a
      feature/benefit.
    type: An enum that represents the type of this entitlement.
  """

  class EntitlementStateValueValuesEnum(_messages.Enum):
    r"""The current state of user's accessibility to a feature/benefit.

    Values:
      ENTITLEMENT_STATE_UNSPECIFIED: <no description>
      ENTITLED: User is entitled to a feature/benefit, but whether it has been
        successfully provisioned is decided by provisioning state.
      REVOKED: User is entitled to a feature/benefit, but it was requested to
        be revoked. Whether the revoke has been successful is decided by
        provisioning state.
    """
    ENTITLEMENT_STATE_UNSPECIFIED = 0
    ENTITLED = 1
    REVOKED = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""An enum that represents the type of this entitlement.

    Values:
      ENTITLEMENT_TYPE_UNSPECIFIED: The entitlement type is unspecified.
      GEMINI: The root entitlement representing Gemini package ownership.This
        will no longer be supported in the future.
      NATIVE: The entitlement representing Native Tier, This will be the
        default Entitlement going forward with GCA Enablement.
      GCA_STANDARD: The entitlement representing GCA-Standard Tier.
    """
    ENTITLEMENT_TYPE_UNSPECIFIED = 0
    GEMINI = 1
    NATIVE = 2
    GCA_STANDARD = 3

  entitlementState = _messages.EnumField('EntitlementStateValueValuesEnum', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class StorageDatabasecenterPartnerapiV1mainGCBDRConfiguration(_messages.Message):
  r"""GCBDR Configuration for the resource.

  Fields:
    gcbdrManaged: Whether the resource is managed by GCBDR.
  """

  gcbdrManaged = _messages.BooleanField(1)


class StorageDatabasecenterPartnerapiV1mainInternalResourceMetadata(_messages.Message):
  r"""Metadata for individual internal resources in an instance. e.g. spanner
  instance can have multiple databases with unique configuration settings.
  Similarly bigtable can have multiple clusters within same bigtable instance.

  Fields:
    backupConfiguration: Backup configuration for this database
    backupRun: Information about the last backup attempt for this database
    isDeletionProtectionEnabled: Whether deletion protection is enabled for
      this internal resource.
    product: A StorageDatabasecenterProtoCommonProduct attribute.
    resourceId: A StorageDatabasecenterPartnerapiV1mainDatabaseResourceId
      attribute.
    resourceName: Required. internal resource name for spanner this will be
      database name e.g."spanner.googleapis.com/projects/123/abc/instances/ins
      t1/databases/db1"
  """

  backupConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupConfiguration', 1)
  backupRun = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupRun', 2)
  isDeletionProtectionEnabled = _messages.BooleanField(3)
  product = _messages.MessageField('StorageDatabasecenterProtoCommonProduct', 4)
  resourceId = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 5)
  resourceName = _messages.StringField(6)


class StorageDatabasecenterPartnerapiV1mainMachineConfiguration(_messages.Message):
  r"""MachineConfiguration describes the configuration of a machine specific
  to Database Resource.

  Fields:
    cpuCount: The number of CPUs. Deprecated. Use vcpu_count instead.
      TODO(b/342344482) add proto validations again after bug fix.
    memorySizeInBytes: Memory size in bytes. TODO(b/342344482) add proto
      validations again after bug fix.
    shardCount: Optional. Number of shards (if applicable).
    vcpuCount: Optional. The number of vCPUs. TODO(b/342344482) add proto
      validations again after bug fix.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  memorySizeInBytes = _messages.IntegerField(2)
  shardCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vcpuCount = _messages.FloatField(4)


class StorageDatabasecenterPartnerapiV1mainObservabilityMetricData(_messages.Message):
  r"""A StorageDatabasecenterPartnerapiV1mainObservabilityMetricData object.

  Enums:
    AggregationTypeValueValuesEnum: Required. Type of aggregation performed on
      the metric.
    MetricTypeValueValuesEnum: Required. Type of metric like CPU, Memory, etc.

  Fields:
    aggregationType: Required. Type of aggregation performed on the metric.
    metricType: Required. Type of metric like CPU, Memory, etc.
    observationTime: Required. The time the metric value was observed.
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    value: Required. Value of the metric type.
  """

  class AggregationTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of aggregation performed on the metric.

    Values:
      AGGREGATION_TYPE_UNSPECIFIED: Unspecified aggregation type.
      PEAK: PEAK aggregation type.
      P99: P99 aggregation type.
      P95: P95 aggregation type.
      CURRENT: current aggregation type.
    """
    AGGREGATION_TYPE_UNSPECIFIED = 0
    PEAK = 1
    P99 = 2
    P95 = 3
    CURRENT = 4

  class MetricTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of metric like CPU, Memory, etc.

    Values:
      METRIC_TYPE_UNSPECIFIED: Unspecified metric type.
      CPU_UTILIZATION: CPU utilization for a resource. The value is a fraction
        between 0.0 and 1.0 (may momentarily exceed 1.0 in some cases).
      MEMORY_UTILIZATION: Memory utilization for a resource. The value is a
        fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some
        cases).
      NETWORK_CONNECTIONS: Number of network connections for a resource.
      STORAGE_UTILIZATION: Storage utilization for a resource. The value is a
        fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some
        cases).
      STORAGE_USED_BYTES: Sotrage used by a resource.
      NODE_COUNT: Node count for a resource. It represents the number of node
        units in a bigtable/spanner instance.
      MEMORY_USED_BYTES: Memory used by a resource (in bytes).
      PROCESSING_UNIT_COUNT: Processing units used by a resource. It
        represents the number of processing units in a spanner instance.
    """
    METRIC_TYPE_UNSPECIFIED = 0
    CPU_UTILIZATION = 1
    MEMORY_UTILIZATION = 2
    NETWORK_CONNECTIONS = 3
    STORAGE_UTILIZATION = 4
    STORAGE_USED_BYTES = 5
    NODE_COUNT = 6
    MEMORY_USED_BYTES = 7
    PROCESSING_UNIT_COUNT = 8

  aggregationType = _messages.EnumField('AggregationTypeValueValuesEnum', 1)
  metricType = _messages.EnumField('MetricTypeValueValuesEnum', 2)
  observationTime = _messages.StringField(3)
  resourceName = _messages.StringField(4)
  value = _messages.MessageField('StorageDatabasecenterProtoCommonTypedValue', 5)


class StorageDatabasecenterPartnerapiV1mainOperationError(_messages.Message):
  r"""An error that occurred during a backup creation operation.

  Enums:
    ErrorTypeValueValuesEnum:

  Fields:
    code: Identifies the specific error that occurred. REQUIRED
    errorType: A ErrorTypeValueValuesEnum attribute.
    message: Additional information about the error encountered. REQUIRED
  """

  class ErrorTypeValueValuesEnum(_messages.Enum):
    r"""ErrorTypeValueValuesEnum enum type.

    Values:
      OPERATION_ERROR_TYPE_UNSPECIFIED: UNSPECIFIED means product type is not
        known or available.
      KMS_KEY_ERROR: key destroyed, expired, not found, unreachable or
        permission denied.
      DATABASE_ERROR: Database is not accessible
      STOCKOUT_ERROR: The zone or region does not have sufficient resources to
        handle the request at the moment
      CANCELLATION_ERROR: User initiated cancellation
      SQLSERVER_ERROR: SQL server specific error
      INTERNAL_ERROR: Any other internal error.
    """
    OPERATION_ERROR_TYPE_UNSPECIFIED = 0
    KMS_KEY_ERROR = 1
    DATABASE_ERROR = 2
    STOCKOUT_ERROR = 3
    CANCELLATION_ERROR = 4
    SQLSERVER_ERROR = 5
    INTERNAL_ERROR = 6

  code = _messages.StringField(1)
  errorType = _messages.EnumField('ErrorTypeValueValuesEnum', 2)
  message = _messages.StringField(3)


class StorageDatabasecenterPartnerapiV1mainRetentionSettings(_messages.Message):
  r"""A StorageDatabasecenterPartnerapiV1mainRetentionSettings object.

  Enums:
    RetentionUnitValueValuesEnum: The unit that 'retained_backups' represents.

  Fields:
    durationBasedRetention: Duration based retention period i.e. 172800
      seconds (2 days)
    quantityBasedRetention: A integer attribute.
    retentionUnit: The unit that 'retained_backups' represents.
    timeBasedRetention: A string attribute.
    timestampBasedRetentionTime: Timestamp based retention period i.e.
      2024-05-01T00:00:00Z
  """

  class RetentionUnitValueValuesEnum(_messages.Enum):
    r"""The unit that 'retained_backups' represents.

    Values:
      RETENTION_UNIT_UNSPECIFIED: Backup retention unit is unspecified, will
        be treated as COUNT.
      COUNT: Retention will be by count, eg. "retain the most recent 7
        backups".
      TIME: Retention will be by Time, eg. "retain backups till a specific
        time" i.e. till 2024-05-01T00:00:00Z.
      DURATION: Retention will be by duration, eg. "retain the backups for
        172800 seconds (2 days)".
      RETENTION_UNIT_OTHER: For rest of the other category
    """
    RETENTION_UNIT_UNSPECIFIED = 0
    COUNT = 1
    TIME = 2
    DURATION = 3
    RETENTION_UNIT_OTHER = 4

  durationBasedRetention = _messages.StringField(1)
  quantityBasedRetention = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  retentionUnit = _messages.EnumField('RetentionUnitValueValuesEnum', 3)
  timeBasedRetention = _messages.StringField(4)
  timestampBasedRetentionTime = _messages.StringField(5)


class StorageDatabasecenterPartnerapiV1mainTags(_messages.Message):
  r"""Message type for storing tags. Tags provide a way to create annotations
  for resources, and in some cases conditionally allow or deny policies based
  on whether a resource has a specific tag.

  Messages:
    TagsValue: The Tag key/value mappings.

  Fields:
    tags: The Tag key/value mappings.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""The Tag key/value mappings.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tags = _messages.MessageField('TagsValue', 1)


class StorageDatabasecenterPartnerapiV1mainUserLabels(_messages.Message):
  r"""Message type for storing user labels. User labels are used to tag App
  Engine resources, allowing users to search for resources matching a set of
  labels and to aggregate usage data by labels.

  Messages:
    LabelsValue: A LabelsValue object.

  Fields:
    labels: A LabelsValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A LabelsValue object.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class StorageDatabasecenterProtoCommonProduct(_messages.Message):
  r"""Product specification for Condor resources.

  Enums:
    EngineValueValuesEnum: The specific engine that the underlying database is
      running.
    TypeValueValuesEnum: Type of specific database product. It could be
      CloudSQL, AlloyDB etc..

  Fields:
    engine: The specific engine that the underlying database is running.
    type: Type of specific database product. It could be CloudSQL, AlloyDB
      etc..
    version: Version of the underlying database engine. Example values: For
      MySQL, it could be "8.0", "5.7" etc.. For Postgres, it could be "14",
      "15" etc..
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""The specific engine that the underlying database is running.

    Values:
      ENGINE_UNSPECIFIED: UNSPECIFIED means engine type is not known or
        available.
      ENGINE_MYSQL: MySQL binary running as an engine in the database
        instance.
      MYSQL: MySQL binary running as engine in database instance.
      ENGINE_POSTGRES: Postgres binary running as engine in database instance.
      POSTGRES: Postgres binary running as engine in database instance.
      ENGINE_SQL_SERVER: SQLServer binary running as engine in database
        instance.
      SQL_SERVER: SQLServer binary running as engine in database instance.
      ENGINE_NATIVE: Native database binary running as engine in instance.
      NATIVE: Native database binary running as engine in instance.
      ENGINE_CLOUD_SPANNER_WITH_POSTGRES_DIALECT: Cloud Spanner with
        PostgreSQL dialect.
      ENGINE_CLOUD_SPANNER_WITH_GOOGLESQL_DIALECT: Cloud Spanner with Google
        SQL dialect.
      ENGINE_MEMORYSTORE_FOR_REDIS: Memorystore with Redis dialect.
      ENGINE_MEMORYSTORE_FOR_REDIS_CLUSTER: Memorystore with Redis cluster
        dialect.
      ENGINE_OTHER: Other refers to rest of other database engine. This is to
        be when engine is known, but it is not present in this enum.
      ENGINE_FIRESTORE_WITH_NATIVE_MODE: Firestore with native mode.
      ENGINE_FIRESTORE_WITH_DATASTORE_MODE: Firestore with datastore mode.
    """
    ENGINE_UNSPECIFIED = 0
    ENGINE_MYSQL = 1
    MYSQL = 2
    ENGINE_POSTGRES = 3
    POSTGRES = 4
    ENGINE_SQL_SERVER = 5
    SQL_SERVER = 6
    ENGINE_NATIVE = 7
    NATIVE = 8
    ENGINE_CLOUD_SPANNER_WITH_POSTGRES_DIALECT = 9
    ENGINE_CLOUD_SPANNER_WITH_GOOGLESQL_DIALECT = 10
    ENGINE_MEMORYSTORE_FOR_REDIS = 11
    ENGINE_MEMORYSTORE_FOR_REDIS_CLUSTER = 12
    ENGINE_OTHER = 13
    ENGINE_FIRESTORE_WITH_NATIVE_MODE = 14
    ENGINE_FIRESTORE_WITH_DATASTORE_MODE = 15

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of specific database product. It could be CloudSQL, AlloyDB etc..

    Values:
      PRODUCT_TYPE_UNSPECIFIED: UNSPECIFIED means product type is not known or
        available.
      PRODUCT_TYPE_CLOUD_SQL: Cloud SQL product area in GCP
      CLOUD_SQL: Cloud SQL product area in GCP
      PRODUCT_TYPE_ALLOYDB: AlloyDB product area in GCP
      ALLOYDB: AlloyDB product area in GCP
      PRODUCT_TYPE_SPANNER: Spanner product area in GCP
      PRODUCT_TYPE_ON_PREM: On premises database product.
      ON_PREM: On premises database product.
      PRODUCT_TYPE_MEMORYSTORE: Memorystore product area in GCP
      PRODUCT_TYPE_BIGTABLE: Bigtable product area in GCP
      PRODUCT_TYPE_FIRESTORE: Firestore product area in GCP.
      PRODUCT_TYPE_COMPUTE_ENGINE: Compute Engine self managed databases
      PRODUCT_TYPE_OTHER: Other refers to rest of other product type. This is
        to be when product type is known, but it is not present in this enum.
    """
    PRODUCT_TYPE_UNSPECIFIED = 0
    PRODUCT_TYPE_CLOUD_SQL = 1
    CLOUD_SQL = 2
    PRODUCT_TYPE_ALLOYDB = 3
    ALLOYDB = 4
    PRODUCT_TYPE_SPANNER = 5
    PRODUCT_TYPE_ON_PREM = 6
    ON_PREM = 7
    PRODUCT_TYPE_MEMORYSTORE = 8
    PRODUCT_TYPE_BIGTABLE = 9
    PRODUCT_TYPE_FIRESTORE = 10
    PRODUCT_TYPE_COMPUTE_ENGINE = 11
    PRODUCT_TYPE_OTHER = 12

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)
  version = _messages.StringField(3)


class StorageDatabasecenterProtoCommonTypedValue(_messages.Message):
  r"""TypedValue represents the value of a metric type. It can either be a
  double, an int64, a string or a bool.

  Fields:
    boolValue: For boolean value
    doubleValue: For double value
    int64Value: For integer value
    stringValue: For string value
  """

  boolValue = _messages.BooleanField(1)
  doubleValue = _messages.FloatField(2)
  int64Value = _messages.IntegerField(3)
  stringValue = _messages.StringField(4)


class StringRestrictions(_messages.Message):
  r"""Restrictions on STRING type values

  Fields:
    allowedValues: The list of allowed values, if bounded. This field will be
      empty if there is a unbounded number of allowed values.
  """

  allowedValues = _messages.StringField(1, repeated=True)


class SupportedDatabaseFlag(_messages.Message):
  r"""SupportedDatabaseFlag gives general information about a database flag,
  like type and allowed values. This is a static value that is defined on the
  server side, and it cannot be modified by callers. To set the Database flags
  on a particular Instance, a caller should modify the Instance.database_flags
  field.

  Enums:
    ScopeValueValuesEnum: The scope of the flag.
    SupportedDbVersionsValueListEntryValuesEnum:
    ValueTypeValueValuesEnum:

  Fields:
    acceptsMultipleValues: Whether the database flag accepts multiple values.
      If true, a comma-separated list of stringified values may be specified.
    flagName: The name of the database flag, e.g. "max_allowed_packets". The
      is a possibly key for the Instance.database_flags map field.
    integerRestrictions: Restriction on INTEGER type value.
    name: The name of the flag resource, following Google Cloud conventions,
      e.g.: * projects/{project}/locations/{location}/flags/{flag} This field
      currently has no semantic meaning.
    recommendedIntegerValue: The recommended value for an INTEGER flag.
    recommendedStringValue: The recommended value for a STRING flag.
    requiresDbRestart: Whether setting or updating this flag on an Instance
      requires a database restart. If a flag that requires database restart is
      set, the backend will automatically restart the database (making sure to
      satisfy any availability SLO's).
    scope: The scope of the flag.
    stringRestrictions: Restriction on STRING type value.
    supportedDbVersions: Major database engine versions for which this flag is
      supported.
    valueType: A ValueTypeValueValuesEnum attribute.
  """

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The scope of the flag.

    Values:
      SCOPE_UNSPECIFIED: The scope of the flag is not specified. Default is
        DATABASE.
      DATABASE: The flag is a database flag.
      CONNECTION_POOL: The flag is a connection pool flag.
    """
    SCOPE_UNSPECIFIED = 0
    DATABASE = 1
    CONNECTION_POOL = 2

  class SupportedDbVersionsValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedDbVersionsValueListEntryValuesEnum enum type.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""ValueTypeValueValuesEnum enum type.

    Values:
      VALUE_TYPE_UNSPECIFIED: This is an unknown flag type.
      STRING: String type flag.
      INTEGER: Integer type flag.
      FLOAT: Float type flag.
      NONE: Denotes that the flag does not accept any values.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    STRING = 1
    INTEGER = 2
    FLOAT = 3
    NONE = 4

  acceptsMultipleValues = _messages.BooleanField(1)
  flagName = _messages.StringField(2)
  integerRestrictions = _messages.MessageField('IntegerRestrictions', 3)
  name = _messages.StringField(4)
  recommendedIntegerValue = _messages.IntegerField(5)
  recommendedStringValue = _messages.StringField(6)
  requiresDbRestart = _messages.BooleanField(7)
  scope = _messages.EnumField('ScopeValueValuesEnum', 8)
  stringRestrictions = _messages.MessageField('StringRestrictions', 9)
  supportedDbVersions = _messages.EnumField('SupportedDbVersionsValueListEntryValuesEnum', 10, repeated=True)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 11)


class SwitchoverClusterRequest(_messages.Message):
  r"""Message for switching over to a cluster

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
  """

  requestId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class TimeBasedRetention(_messages.Message):
  r"""A time based retention policy specifies that all backups within a
  certain time period should be retained.

  Fields:
    retentionPeriod: The retention period.
  """

  retentionPeriod = _messages.StringField(1)


class TrialMetadata(_messages.Message):
  r"""Contains information and all metadata related to TRIAL clusters.

  Fields:
    endTime: End time of the trial cluster.
    graceEndTime: grace end time of the cluster.
    startTime: start time of the trial cluster.
    upgradeTime: Upgrade time of trial cluster to Standard cluster.
  """

  endTime = _messages.StringField(1)
  graceEndTime = _messages.StringField(2)
  startTime = _messages.StringField(3)
  upgradeTime = _messages.StringField(4)


class UpdatePolicy(_messages.Message):
  r"""Policy to be used while updating the instance.

  Enums:
    ModeValueValuesEnum: Mode for updating the instance.

  Fields:
    mode: Mode for updating the instance.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Mode for updating the instance.

    Values:
      MODE_UNSPECIFIED: Mode is unknown.
      DEFAULT: Least disruptive way to apply the update.
      FORCE_APPLY: Performs a forced update when applicable. This will be fast
        but may incur a downtime.
    """
    MODE_UNSPECIFIED = 0
    DEFAULT = 1
    FORCE_APPLY = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class UpgradeClusterRequest(_messages.Message):
  r"""Upgrades a cluster.

  Enums:
    VersionValueValuesEnum: Required. The version the cluster is going to be
      upgraded to.

  Fields:
    etag: Optional. The current etag of the Cluster. If an etag is provided
      and does not match the current etag of the Cluster, upgrade will be
      blocked and an ABORTED error will be returned.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      ignores the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation, for example,
      permission checks and any other type of validation, but does not
      actually execute the create request.
    version: Required. The version the cluster is going to be upgraded to.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""Required. The version the cluster is going to be upgraded to.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)
  version = _messages.EnumField('VersionValueValuesEnum', 4)


class UpgradeClusterResponse(_messages.Message):
  r"""UpgradeClusterResponse contains the response for upgrade cluster
  operation.

  Enums:
    StatusValueValuesEnum: Status of upgrade operation.

  Fields:
    clusterUpgradeDetails: Array of upgrade details for the current cluster
      and all the secondary clusters associated with this cluster.
    message: A user friendly message summarising the upgrade operation details
      and the next steps for the user if there is any.
    status: Status of upgrade operation.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of upgrade operation.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  clusterUpgradeDetails = _messages.MessageField('ClusterUpgradeDetails', 1, repeated=True)
  message = _messages.StringField(2)
  status = _messages.EnumField('StatusValueValuesEnum', 3)


class UpgradeClusterStatus(_messages.Message):
  r"""Message for current status of the Major Version Upgrade operation.

  Enums:
    SourceVersionValueValuesEnum: Source database major version.
    StateValueValuesEnum: Cluster Major Version Upgrade state.
    TargetVersionValueValuesEnum: Target database major version.

  Fields:
    cancellable: Whether the operation is cancellable.
    sourceVersion: Source database major version.
    stages: Status of all upgrade stages.
    state: Cluster Major Version Upgrade state.
    targetVersion: Target database major version.
  """

  class SourceVersionValueValuesEnum(_messages.Enum):
    r"""Source database major version.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Cluster Major Version Upgrade state.

    Values:
      STATUS_UNSPECIFIED: Unspecified status.
      NOT_STARTED: Not started.
      IN_PROGRESS: In progress.
      SUCCESS: Operation succeeded.
      FAILED: Operation failed.
      PARTIAL_SUCCESS: Operation partially succeeded.
      CANCEL_IN_PROGRESS: Cancel is in progress.
      CANCELLED: Cancellation complete.
    """
    STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    SUCCESS = 3
    FAILED = 4
    PARTIAL_SUCCESS = 5
    CANCEL_IN_PROGRESS = 6
    CANCELLED = 7

  class TargetVersionValueValuesEnum(_messages.Enum):
    r"""Target database major version.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
      POSTGRES_16: The database version is Postgres 16.
      POSTGRES_17: The database version is Postgres 17.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3
    POSTGRES_16 = 4
    POSTGRES_17 = 5

  cancellable = _messages.BooleanField(1)
  sourceVersion = _messages.EnumField('SourceVersionValueValuesEnum', 2)
  stages = _messages.MessageField('StageStatus', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  targetVersion = _messages.EnumField('TargetVersionValueValuesEnum', 5)


class User(_messages.Message):
  r"""Message describing User object.

  Enums:
    UserTypeValueValuesEnum: Optional. Type of this user.

  Fields:
    databaseRoles: Optional. List of database roles this user has. The
      database role strings are subject to the PostgreSQL naming conventions.
    keepExtraRoles: Input only. If the user already exists and it has
      additional roles, keep them granted.
    name: Output only. Name of the resource in the form of
      projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.
    password: Input only. Password for the user.
    userType: Optional. Type of this user.
  """

  class UserTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of this user.

    Values:
      USER_TYPE_UNSPECIFIED: Unspecified user type.
      ALLOYDB_BUILT_IN: The default user type that authenticates via password-
        based authentication.
      ALLOYDB_IAM_USER: Database user that can authenticate via IAM-Based
        authentication.
    """
    USER_TYPE_UNSPECIFIED = 0
    ALLOYDB_BUILT_IN = 1
    ALLOYDB_IAM_USER = 2

  databaseRoles = _messages.StringField(1, repeated=True)
  keepExtraRoles = _messages.BooleanField(2)
  name = _messages.StringField(3)
  password = _messages.StringField(4)
  userType = _messages.EnumField('UserTypeValueValuesEnum', 5)


class UserPassword(_messages.Message):
  r"""The username/password for a database user. Used for specifying initial
  users at cluster creation time.

  Fields:
    password: The initial password for the user.
    user: The database username.
  """

  password = _messages.StringField(1)
  user = _messages.StringField(2)


class WeeklySchedule(_messages.Message):
  r"""A weekly schedule starts a backup at prescribed start times within a
  day, for the specified days of the week. The weekly schedule message is
  flexible and can be used to create many types of schedules. For example, to
  have a daily backup that starts at 22:00, configure the `start_times` field
  to have one element "22:00" and the `days_of_week` field to have all seven
  days of the week.

  Enums:
    DaysOfWeekValueListEntryValuesEnum:

  Fields:
    daysOfWeek: The days of the week to perform a backup. If this field is
      left empty, the default of every day of the week is used.
    startTimes: The times during the day to start a backup. The start times
      are assumed to be in UTC and to be an exact hour (e.g., 04:00:00). If no
      start times are provided, a single fixed start time is chosen
      arbitrarily.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 1, repeated=True)
  startTimes = _messages.MessageField('GoogleTypeTimeOfDay', 2, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
