project:
  name: project
  collection: vmwareengine.projects
  attributes:
  - &project
    parameter_name: projectsId
    attribute_name: project
    help: Project ID.
    property: core/project
  disable_auto_completers: false

location:
  name: location
  collection: vmwareengine.projects.locations
  attributes:
  - *project
  - &location
    parameter_name: locationsId
    attribute_name: location
    help: The resource name of the location.
    fallthroughs:
    - hook: googlecloudsdk.command_lib.vmware.networks.util:DefaultToGlobal
      hint: set location as 'global' (default)
  disable_auto_completers: false

network_peering:
  name: VMware Engine VPC network peering
  collection: vmwareengine.projects.locations.networkPeerings
  attributes:
  - *project
  - *location
  - &network_peering
    parameter_name: networkPeeringsId
    attribute_name: network-peering
    help: VMware Engine VPC network peering
  disable_auto_completers: false

network_peering_routes:
  name: VMware Engine VPC network peering routes
  collection: vmwareengine.projects.locations.networkPeerings.peeringRoutes
  attributes:
  - *project
  - *location
  - *network_peering
  - &network_peering_routes
    parameter_name: networkPeeringsRoutesId
    attribute_name: network-peering-routes
    help: VMware Engine VPC network peering routes
  disable_auto_completers: false
