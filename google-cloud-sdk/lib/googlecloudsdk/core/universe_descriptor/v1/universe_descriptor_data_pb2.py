# -*- coding: utf-8 -*- #
# Copyright 2024 Google LLC. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: universe_descriptor_data/v1/universe_descriptor_data.proto
"""Generated protocol buffer code."""
from cloudsdk.google.protobuf import descriptor as _descriptor
from cloudsdk.google.protobuf import descriptor_pool as _descriptor_pool
from cloudsdk.google.protobuf import symbol_database as _symbol_database
from cloudsdk.google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:universe_descriptor_data/v1/universe_descriptor_data.proto\x12\x1buniverse_descriptor_data.v1\"\xe6\x03\n\x16UniverseDescriptorData\x12\x1c\n\x0funiverse_domain\x18\x01 \x01(\tH\x00\x88\x01\x01\x12 \n\x13universe_short_name\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\"\n\x15\x61uthentication_domain\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x1b\n\x0eproject_prefix\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x1d\n\x10\x63loud_web_domain\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x14\n\x07version\x18\x06 \x01(\tH\x05\x88\x01\x01\x12!\n\x14\x64ocumentation_domain\x18\x07 \x01(\tH\x06\x88\x01\x01\x12\x12\n\x05state\x18\x08 \x01(\tH\x07\x88\x01\x01\x12%\n\x18\x61rtifact_registry_domain\x18\t \x01(\tH\x08\x88\x01\x01\x42\x12\n\x10_universe_domainB\x16\n\x14_universe_short_nameB\x18\n\x16_authentication_domainB\x11\n\x0f_project_prefixB\x13\n\x11_cloud_web_domainB\n\n\x08_versionB\x17\n\x15_documentation_domainB\x08\n\x06_stateB\x1b\n\x19_artifact_registry_domainb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'universe_descriptor_data.v1.universe_descriptor_data_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_UNIVERSEDESCRIPTORDATA']._serialized_start=92
  _globals['_UNIVERSEDESCRIPTORDATA']._serialized_end=578
# @@protoc_insertion_point(module_scope)
