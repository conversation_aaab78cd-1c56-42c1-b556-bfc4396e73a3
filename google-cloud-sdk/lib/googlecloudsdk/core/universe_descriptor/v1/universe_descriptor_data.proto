// DO NOT EDIT THIS FILE BY HAND. This file is generated.

syntax = "proto3";

package universe_descriptor_data.v1;

message UniverseDescriptorData {
  // API domain for the universe.
  optional string universe_domain = 1;

  // Short name for the universe that is prepended to each project ID.
  optional string universe_short_name = 2;

  // Authentication domain.
  optional string authentication_domain = 3;

  // Project prefix.
  optional string project_prefix = 4;

  // Cloud console documentation domain.
  optional string cloud_web_domain = 5;

  // For clients to leverage in order to understand proto changes (ex: 1.0.0)
  optional string version = 6;

  // Documentation domain.
  optional string documentation_domain = 7;

  // State of the universe.
  optional string state = 8;

  // Artifact Registry domain.
  optional string artifact_registry_domain = 9;
}
