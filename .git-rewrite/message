AS-105 WIP Basic Fine Tuning (#269)

* Cleint Data Refactor: turned selected prefix into a context

* UX Fix: make things look a bit nicer

* Client Fix: reply to should be undefined or prefix, not a string

* Client Refactor: move selected prefix and usable models to context

* Model Fix: removed default unique index and added unique index if whitelableVersion is not null

* Model Fix: replyto check should be before assistantName check

* Code Clean: whitespace

* Endpoint Feature: list fine tuned models for client to choose from

* UX Fix: let default error handler handle errors

* Cod eClean: remove unused var

* Endpoint Fix WIP: messenger info must include or be exclusively custom when using a whitelable release

* Code Cleanup: whitespace

* Deps Chore: package.json files

* UX Feature: able to see the assistants name

* UX Feature: if release a chat is based off is deprecated, notify the user

* UX Feature: make sure user has saved and changed before finalizing release

* Endpiint Feature: return release when chat is requested

* Endpoint Refactor: moving the mesengerInfo to server-models

* AI update: whitelabels can use 4o and is the default for whitelabels

* Endpoint Fix: releaseId not releastId

* Code Clenaup: whitespace, return if fail

* UX Fix: If a chat is not replying to anyone, it's the start

* UX Fix: if deprecate fail, display error

* UX Feature: display version when applicable

* UX Fix: some styling

* Code Fix: whitespace

* Client Util Feature: tabs component that creates route

* UX Feature: remove anchor tags, using button and react-router Link

* UX Fix: styling

* UX Feature WIP: can choose a prefix for your qa tests to run on

* Client Refactor: moving fine tuned models to the whitelabel item root instead of in release

* Util Refactor: move matchesPath to ./util. Util Fix:  params might have undefined value

* Router Refactor: moved the route config to the QA Testing

* Deps Chore: packagge.json files

* Code Cleanup: whitespace

* Model Endpoint Fix: update paths, make sure prompts are retrieved correctly

* Model Fix: created location for whitelabel release

* Endpoint Refactor: move create chat logic to the model

* Endpoint Refactor: move the add message related to text messages to the Chat model

* Endpoint Fix: use messenger and custom assistant

* Dev Fix: apiName required in typing

* Endpoint Fix: countDocuments() not count()

* Endpoint Refactor: moving the whitelabel add message logic to the model

* Endpoint Fix: whitelabel no longer has release status, now create releases based off whitelabel

* Code Refactor: moving message assistants to the transcript router

* Dev Cleanup: remove unused variables

* Launch Fix: remove repeated files in the docker-compose files

* Dev Fix: eslint no trailing spaces

* Deps Chore: update pnpm lock files

* Dev Fix: remove unnecessary postinstall script

* Model Fix: FineTune now has category so that the messengers can use the same assistant for different categories

* Model Fix: importing locally rather than from the dependency name

* Client Refactor: remove unnecessary props from footer

* Client Type Fix: transcript and replyTo are optional but not category or assistantName

* Import Fix: RenderContent not exported from transcript

* Client Refactor, Fix: client updates whitelabel chats from server. Updates when deletes final

* Type Fix: whitelabel has no releaseStatus, now whitelabel can create releases

* Client Type Fix: owned list has doc and other assorted data instead of just the document

* Client Import Cleanup: remove unused imports

* UX fix: for categorizing text, if no assistant set, use default assistant

* Client Fetch Cleanup: minimize the amount of fetching done for notification settings

* UX Fix WIP: supporting retrieving prefix from more than one whitelabel transcript

* Client Refactor: moved hover into a context so it doesn't polute the props of every component

* Endpoint Feature: created "use" method for partial path matching in websocket router

* Dev Cleanup: whitespace

* UX Fix: removed second title on whitelabel list item

* Dev Fix: update.sh updates and installs on named resource package

* Endpoint Refactor: Using dedicated job poller

* UX Fix: working on whitelabel releases

* UX Refactor: Display Prefix uses the base Transcript Display

* Dev Fix: renamed target to more accurate rag name

* Endpoint Fix: prefix list may have transcriptId

* Endpoint Feature: whitelabel has finetune models available as ai assistants

* Client Util Fix: track and close websocket without state

* Client Fix: readd dforce feedback

* UX Refatcor: use auth0 user pictures

* Client Endpoint Fix: remove ref for starting job tracking

* Ux Fix: retrieve transcript base on selected

* Client Fix: added some classes and keys

* Ux Fix: set current item when there were none and switch to page when new item selected

* UX Fix: track and display error

* UX Fix: redirect to first transcript if none chosen

* UX Fix: Threads are meant for threads

* Deps Fix: shouldn't reverence self

* UX Fix: use auth0 picture rather than message uploaded picture

* UX Fix: use auth0 picture rather than message uploaded picture for whitelabel related transcripts

* UX Fix: when specifying specific message to reply to, don't remove it when using category suggestion

* UX Fix: get transcript graph working again

* Dev Cleanup: whitespace, semicolons and removing console.logs

* Client Refactor: seperated the chat and whitelabel left drawer

* UX Fix: redirect to transcript page when hitting whitelabel item index

* UX Feature: display your chats using a whitelabel release on it's index page

* UX Fix: Thread Display was too small

* UX Fix: going to thread pages should not change the menu

* Dev Cleanup: exporting qa from the qa root

* Deps Fix: event should be exported in utils

* Deps Fix: should not import own package

* Client Refactor: Attempting to reuse message prefix setting for both release and qa testing

* Client Fix: update whitelabel after adding or removing chats

* Endpoint Fix: stronger type casting done in the server and provided to the model.

* Endpoint Fix Temp: chats and whitelabels use specified rag context

* Dev Clenaup: remove a few console.logs

* Client Data Refactor: Decouple the current transcript from the chosen prefix

* UX Fix: some styling updates

* UX Feature: QA runs are retained between qa page changes

* Client Data Fix: missing QAPromptRun

* Dev Fix: cleanup a bunch of eslint errors

* Client Data Fix: the fine tune items come with baseModel not baseModelName

* UX Fix: tabs no longer disappear when scorlling

* UX Fix WIP: attempt to redirect to origial location for logouts

* UX Style Fix: added some styling classes

* UX Link Feature: release editor can go to the transcript that the prefix is based off

* Client Dev Fix: React likes keys for array items

* UX Nav Fix: create whitelabel transcript response doesn't have transcript but does have _id

* UX Style Fix: added "block-list" class

* Dev Feature: proof that a model is working properly

* Endpoint Fix: no title means error

* Dev Cleanup: whitespace

* Dev Cleanup: front end can create the url, backend doesn't have to

* Model Refactor: seperating and exporting the message and category schemas in case another model wants to use them

* Model Refactor: strip and save prefix's transcript so that the whitelabel's transcript can be cleaned up with less fear

* Deps Fix: the client shouldn't have access to server-models

* Release Prefix Feature: each prefix has a unique title

* UX Feature: able to see and click on a chat's whitelabel

* UX Refactor: each prefix has a list of messages

* UX Feature: store messages and prefix target on the release prefix

* Code Fix: add missing type

* Code Fix: JSON_Extra doesn't want to turn into value

* Code Fix: add missing variable

* Endpoint Feature: populate chat's whitelabelRelease to display in the owner's list

* Dev Cleanup: whitspace, unused var

* Dev Refactor and Fix: turned update file status and running into a single function and updated variables to make sure they're available when needed

* Dev Fix: constant error message rather than from the error

* Endpoint Fix: vector file endpoint based on whitelabel

* Model Refactor: remove messages property from release prefix, instead retrieve transcript and trim messages

* Dev Model Fix: made context a mandatory property to solve typescript errors

* Dev Feature: cretaed build script and added "prepare" scripts on clients to catch typescript errors.

* UX Feature WIP: user can create multiple vector indexes within a single whitelabel project

* Dev Cleanup: Whitespace

* Model Endpoint Fix: Throw error if doc has been removed

* Model Feature: try to retain meta data related to file being added or removed

* Model Cleanup: textObjectKey removed

* Model Feature: RagVetcor Document

* Model Endpoint Fix: provide the raw updated doc and a promise for when the work ends for http 202 responses

* Model Schema Fix: added some validators for number of context messages and minimum similarity

* Model Endpoint Refactor: simplified the file input so I don't have to worry multer vs busboy

* Model Fix: findOrCreateVertexIndex was not added to the model

* Model Endpoint Fix: types for adding and removing file. Provides updated document and work promise

* Model Dep Fix: exporting vector

* Endpoint Feature: rag-vector endpoint now available

* Client Feature WIP: Rag vector pages and routes, still untested

* Util Feature: trys to cast an error or throw it

* Dev Type Fix: ensure message target exists on prefix

* Dev Error Fix: either use error message or default string

* Model Endpoint Fix: need to use \$set when setting property in mongodb

* Dev Type Fix: file may not exist

* Model Fix: empty string considered not set for required

* Model Fix: vectorFiles doesn't exist, files does

* Endpoint Fix: needs to save new rag document

* Endpoint Fix: name doesn't exist, title does

* Client Feature: rag vector now available

* Endpoint Fix: rag vector files returns a list of files

* Client Cleanup: styling and text updates

* Client Refatcor: moving the new file form to it's own component

* Client Refactor: changed some variable names

* Dev Cleanup: name not needed since we're not serverside rendering

* Client Feature: poll current rag vector index so long as it has pending files

* UX Feature: can clear all vectors

* Model Endpoint Fix: $nin doesn't exist, using $not: {  } ionstead

* Dev Cleanup: remove unused var and not logging entire document, just files

* Deps Chore: pnpm lock file

* Dev Feature: logging previous location and next location for copying

* Client Feature: I'm using user info in a variety of places so it may be a good idea to isolate the form and display

* UX Cleanup: Added File Status and Settings Page when a vector is selected

* UX Feature: able to update context settings like min similarity and max context per message

* Endpoint Fix: wasn;t waiting for update to finish

* Client Feature: able to fade in and out

* UX Fix: when submitting, previous error should be erased

* Client Fix: change some text and styling

* Dev Client Fix: change location of variables, whitespace, remove unused imports

* Client Fix: file header can be clicked to remove

* UX Feature: Able to add rag vector indexes to release drafts

* UX Feature: Able to set rag index for releases, select a rag for whitelabel transcript and select a rag for qa

* UX Fix: vector list no longer used, create a rag vector and use that with releases

* Dev Cleanup: whitespace

* Model Fix: can only update a rag index when it isn't used

* Stack Fix: readd web-client.env to the env_files

* Deps Cleanup: removing unused deps

* Dev Fix: removing mocha and jest because they were giving false negatives relating to typescript errors

* Dev Cleanup: remove unused empty file

* Client UX Fix: feedback provider added back in again for the chats

* Model Endpoint Fix: if no release, no rag vertex is retrievable

* Client Endpoint Cleanup: user picture no longer used when adding message

* Dev Cleanup: whitespace

* Client UX Fix: used files toggled viewable by default

* Client Refactor: use replaceParams instead of manually creating link

* Client UX Fix: form was submitting when clicking the reset button

* Client UX Feature: if file failed client side, can't send serverside

* Client Refactor: use replaceParams instead of manually setting the link for each menu item

* Dev Cleanup: remove unused empty file

* Client UX Fix: Need prefix notice was showing even when prefix was available

* Dev Refactor: moving tab menu to components

* Client UX Fix: if no rag vertex indexes available, create a button that will direct them to upload

* Server Endpoint Fix: release draft no longer cares about the vertexIndexTarget

* Client UX Fix: display the category of a prefix

* Client UX Fix: duplicat actegory display

* Client UX Feature: clicking on rag vector index title takes user to that rag's page

* Client Refactor: use replaceParams instead of hardcoded location

* Client UX Fix: make the release list nicer looking

* Client Endpoint Fix: redirect using replaceParams

* Client Refactor: use replaceParams instead of hardcode the link

* Client Route Refactor: move the fine tune router to it's folder

* Dev Client Refactor: rename qa route and reorganize the routes on the router

* Model Feature: model organization able to be set on finetuneai

* Model Refactor: change file name to file title

* UX Fix: change file name to file title

* Endpoint Fix: check if e is null

* Dev Endpoint Fix: ragId may be undefined

* UX Cleanup: No longer using vector-index so removing it and references to it

* Dev Cleanup: release can't be undefined at that location

* Model Endpoint Fix: make sure release is avaialbel before creating chat

* Model Cleanup: get rid of userPicture from message models

* Dev Clenaup: whitespace

* UX Feature: able to use gpt 4o and gpt 4o mini for fine tunes

* UX Feature: added item tabs in fine tunes

* Tool Refactor: only use the assistant's info rather than the whole chat completion messenger for fine tunes

* Dev Feature: name the model name that isn't available. IT should all be internal so we shouldn't have to worry about bad actors

* Util Fix: should clean maps

* Client Fix: missing fine tune router outlet

* Model Endpoint Fix: should dave fork regardless if the old vector is clearing or not

* Model Endpoint Fix: vector file list can't clear

* Dev Cleanup: unused file

* Model Endpoint Refatcor: getting rid of deletes and cloning

* Model Endpoint Fix: user count not added to whitelabel meta data

* Client UX Feature: display he number of users using a whitelabel

* Endpoint Fix: when forking should also include the whitelabel as well as the release

* Endpoint Fix: adding some casting errors to fine tune creation and user info updating

* Endpoint Feature: Added some validation when setting rag's user info

* Endpoint Fix: userPicture no longer used when adding message

* Endpoint Fix: need to await finding models

* Dev Cleanup: remove unused file

* Dev Cleanup: whitespace and remove console.log

* Model Endpoint Refactor: add each property for forking instead of cloneAndClean

* UX Feature: can fork an ai chat

* UX Feature: able to fork and change title of fine tune